<cfoutput>
	<section class="clinet-testimonials">
		<div class="container">
			<div class="row">
				<div class="span6 client">
					<div class="client-inner">
						#application.objCMS.renderZone(zone='N',event=event)#
					</div>
				</div>

				<cfsavecontent variable="local.items">
					#application.objCMS.renderZone(zone='O',event=event, mode='div')#
				</cfsavecontent>
				<cfset local.items = replaceNoCase(local.items, '<div id="zoneO" class="zonewrapper">', "", "All")>
				<cfset local.items = replaceNoCase(local.items, "zoneresource", "item", "All")>
				<cfset local.items = trim(replaceNoCase(local.items, "item", "active item", "One"))>
				<cfif len(local.items) gt len("</div>")>
					<cfset local.items = left(local.items, len(local.items) - len("</div>"))>
				</cfif>
				<div class="span6 testimonials">
					<div class="owl-carousel owl-theme">
						#local.items#
					</div>
				</div>
			</div>
		</div>
	</section>
</cfoutput>