ALTER PROC dbo.site_getSiteInfo
@environmentName varchar(50),
@internalURL varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @environmentID int, @defaultTemplateID int;
	select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

	select @defaultTemplateID = templateID
	from dbo.cms_pageTemplates 
	where siteID is null 
	and templateFilename = 'DefaultTemplate'
	and [status] = 'A';
	
	select s.siteid, s.GA4MeasurementID, s.GTMContainerID, o.orgid, s.defaultLanguageID, s.defaultTimeZoneId, s.allowGuestAccounts, s.forceLoginPage, 
		s.affiliationRequired, s.immediateMemberUpdates, s.emailMemberUpdates,
		s.siteCode, s.siteName, s.defaultPostalState, s.joinURL, s.alternateGuestAccountCreationLink, 
		s.alternateGuestAccountPopup, s.alternateForgotPasswordLink, s.defaultAdminEmails,
		o.orgcode, oi.organizationName as orgname, oi.organizationShortName as orgshortname, oi.XUserName, o.useBatches, o.defaultPending, o.memNumPrefixGuest, o.memNumPrefixUser, o.accountingEmail, 
		o.sysMemberID, o.publicGroupPrintID, o.notifyBadCOF, o.notifyBadCOFMessage, o.userTokenSecret, o.useAccrualAcct,
		s.noRightsContentID, s.noRightsNotLoggedInContentID, s.firstTimeLoginContentID, 
		s.siteAgreementContentID, l.languageCode as defaultLanguageCode, s.enforceSiteAgreement, s.inactiveUserContentID,
		s.defaultCurrencyTypeID, ct.CurrencyType as defaultCurrencyType, s.showCurrencyType, s.siteResourceID as siteSiteResourceID, 
		s.memberAdminSiteResourceID, s.subscriptionAdminSiteResourceID, s.subscriptionsSiteResourceID,
		s.alternateUpdateMemberLink, sh.hostname as mainHostname, s.UserWayAccountCode,
		sh.hasssl, scheme = case when sh.hasssl = 1 then 'https' else 'http' end,
		n.networkID as loginNetworkID, n.networkName as loginNetworkName, n.networkCode as loginNetworkCode,
		n.supportProviderEmail, n.emailFrom as networkEmailFrom, n.supportProviderPhone, n.supportProviderName, 
		s.defaultCountryID, s.enableMobile, s.enableDeviceDetection, s.enableAdd2Home, s.defaultOrgIdentityID, s.loginOrgIdentityID,
		ps.ovTemplateID as defaultTemplateID, ps.ovTemplateIDMobile as defaultTemplateIDMobile,
		pt.siteResourceID as defaultTemplateSiteResourceID, ptmobile.siteResourceID as defaultTemplateSiteResourceIDMobile,
		s.showHomepageWarning,s.homePageWarningContentID, homePageWarning.rawContent as homePageWarningContent,
		s.customHeadContentID, customHead.rawContent as customHeadContent,
		s.deliveryPolicyURL, s.privacyPolicyURL, s.rrPolicyURL, s.tcURL,
		(select top 1 ai.siteResourceID
			from dbo.cms_applicationInstances as ai
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID
			where ai.siteID = s.siteid
			and at.applicationTypeName = 'Ajax') as ajaxAppInstanceID,
		(select top 1 ai.siteResourceID
			from dbo.cms_applicationInstances as ai
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID
			where ai.siteID = s.siteid
			and at.applicationTypeName = 'appProxy') as appProxyAppInstanceID,
		(select top 1 groupID
			from dbo.ams_groups
			where orgID = s.orgID
			and groupCode = 'SiteAdmins') as adminGroupID,
		usesMenuSystem = case when exists (
							select mu.usedBySiteResourceID
							from dbo.cms_menus as menu
							inner join dbo.cms_menuUsages mu on mu.menuID = menu.menuID
							inner join dbo.cms_siteResources sr on sr.siteResourceID = mu.usedBySiteResourceID and sr.siteID = s.siteID
							where menu.siteID = s.siteID
							)
							then 1
						else 0
						end,
		case when swp.isSWL = 1 then swp.brandSWLTab else '' end as swlBrand,
		case when swp.isSWOD = 1 then swp.brandSWODTab else '' end as swodBrand,
		isnull(swp.isSWCP,0) as isSWCP,
		case when swp.isSWCP = 1 then 'Certificate Programs' else '' end as swcpBrand,
		swp.brandBundleTab as swbBrand,
		internalAssetsURL = replace(@internalURL,'*SITECODE*',s.siteCode) + 'assets/' + o.orgcode + '/' + s.siteCode + '/',
		sllm.loginLimitModeCode, s.useRemoteLogin, s.useRemoteLoginForm, s.remoteLoginFormURL, s.dropboxAppKey,
		sf.memberDocuments as sf_memberDocuments, sf.tasks as sf_tasks, sf.relationships as sf_relationships, 
		sf.memberHistory as sf_memberHistory, sf.subscriptions as sf_subscriptions, sf.contributions as sf_contributions,
		sf.mcAPI as sf_mcAPI, sf.badgePrinters as sf_badgePrinters,
		sf.sitePasswords as sf_sitePasswords,
		hasReferrals = case 
			when exists (
				select referralID 
				from dbo.ref_referrals rr
				inner join dbo.cms_applicationInstances AS ai on rr.applicationInstanceID = ai.applicationInstanceID and ai.siteID = s.siteID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = s.siteID and sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			) then 1
			else 0
			end,
		s.defaultConsentListID, s.subscriptionIssuesEmail, sf.referralsSMS, sf.recurringEvents as sf_recurringEvents
	from dbo.sites as s
	inner join dbo.siteFeatures as sf on sf.siteID = s.siteID
	inner join dbo.cms_pageSections ps on ps.siteID = s.siteID and ps.sectionCode = 'root' and ps.parentSectionID is null
	inner join dbo.cms_pageTemplates pt on pt.templateID = isnull(ps.ovTemplateID,@defaultTemplateID)
	inner join dbo.organizations as o on o.orgid = s.orgid
	inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = s.defaultOrgIdentityID
	inner join dbo.cms_languages as l on l.languageID = s.defaultLanguageID
	inner join dbo.currencyTypes as ct on ct.currencyTypeID = s.defaultCurrencyTypeID
	inner join dbo.networkSites as ns on ns.siteID = s.siteID and ns.isLoginNetwork = 1
	inner join dbo.networks as n on n.networkID = ns.networkID
	inner join dbo.siteEnvironments se on se.siteID = s.siteID and se.environmentID = @environmentID
	inner join dbo.siteHostnames as sh on sh.hostnameID = se.mainHostnameID
	inner join dbo.siteLoginLimitModes sllm on sllm.loginLimitModeID = s.loginLimitModeID
	left outer join dbo.cms_pageTemplates as ptmobile on ptmobile.templateID = ps.ovTemplateIDMobile
	left outer join seminarweb.dbo.tblParticipants as swp on swp.orgcode = s.sitecode
	CROSS APPLY dbo.fn_getContent(s.homePageWarningContentID,1) as homePageWarning
	CROSS APPLY dbo.fn_getContent(s.customHeadContentID,1) as customHead;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
