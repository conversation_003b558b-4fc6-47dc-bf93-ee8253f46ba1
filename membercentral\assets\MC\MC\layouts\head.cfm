<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
<cfoutput>
	<head>
		<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
		<link rel="apple-touch-icon" sizes="180x180" href="/images/apple-icon-180x180.png">
		<link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
		<link name="favicon" type="image/x-icon" href="/images/favicon.ico" rel="shortcut icon" />
		#application.objCMS.getBootstrapHeadHTML()#
		
		<link rel="stylesheet" type="text/css" href="/css/main.css" />
	  <!-- STYLES -->
		<link type="text/css" rel="stylesheet" href="/assets/common/javascript/owlCarousel/221/owl.carousel.min.css" />
		<link type="text/css" rel="stylesheet" href="/css/layout.css" />
		<link type="text/css" rel="stylesheet" href="/css/responsive.css" />
		#application.objCMS.getSiteCustomCSS(siteID=arguments.event.getValue('mc_siteInfo.siteID'))#
	  	<link rel="stylesheet" type="text/css" href="/css/slideshow.css" />
 		<link href="/css/jquery.lightbox-0.5.css" media="screen" rel="stylesheet" type="text/css" />
		<script type="text/javascript" src="/javascript/custom.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/owlCarousel/221/owl.carousel.min.js"></script>
	   <cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
	   	<script type="text/javascript" src="/javascript/jquery.slideshow.js"></script>
			<script type="text/javascript" src="/javascript/jquery.lightbox-0.5.js"></script>	  			
			<script type="text/javascript">if (window.event+''=='undefined')event=0;</script>	
		</cfif>
		<script src="//load.sumome.com/" data-sumo-site-id="****************************************************************" async="async"></script>	
		#application.objCMS.getResponsiveHeadHTML()#	
		<!-- Start of trialsmith Zendesk Widget script -->
		<script>/*<![CDATA[*/window.zE||(function(e,t,s){var n=window.zE=window.zEmbed=function(){n._.push(arguments)}, a=n.s=e.createElement(t),r=e.getElementsByTagName(t)[0];n.set=function(e){ n.set._.push(e)},n._=[],n.set._=[],a.async=true,a.setAttribute("charset","utf-8"), a.src="https://static.zdassets.com/ekr/asset_composer.js?key="+s, n.t=+new Date,a.type="text/javascript",r.parentNode.insertBefore(a,r)})(document,"script","9d931583-b689-4d41-89c9-602c23e3f86e");/*]]>*/</script>
		<!-- End of trialsmith Zendesk Widget script -->
		<script type="text/javascript">(function(d,s){var DID="608a456d-3403-4e98-b4ba-e8d38deebf78";var js,fjs=d.getElementsByTagName(s)[0];js=d.createElement(s);js.async=1;js.src="https://track.cbdatatracker.com/Home?v=3&id='"+DID+"'";fjs.parentNode.insertBefore(js,fjs);}(document,'script'));</script>
	</head>
</cfoutput>