/*** ESSENTIAL STYLES ***/
.sf-menu, .sf-menu * {
  margin: 0;
  padding: 0;
  list-style: none;
}
.sf-menu li {
  position: relative;
}
.sf-menu ul {
  position: absolute;
  display: none;
  top: 100%;
  left: 0;
  z-index: 99;
  padding-top: 8px;
}
.sf-menu > li {
  float: left;
}
.sf-menu li:hover > ul,
.sf-menu li.sfHover > ul {
  display: block;
}

.sf-menu a {
  display: block;
  position: relative;
  letter-spacing: 1px;
}
.sf-menu ul ul {
  top: 0;
  left: 100%;
}


/*** DEMO SKIN ***/
.sf-menu {
  padding: 35px 0 0 20px;
  width: 100%;
  float: left;
 /* margin-bottom: 1em;*/
 width: 100%;
 background: rgba(0, 0, 0, 0) url("../images/mainNavBG.png") no-repeat scroll 0 0;
}
.sf-menu ul {
  box-shadow: 2px 2px 6px rgba(0,0,0,.2);
  min-width: 12em; /* allow long menu items to determine submenu width */
  *width: 12em; /* no auto sub width for IE7, see white-space comment below */
}
.sf-menu a{color: #fff;}
.sf-menu li li a{padding: 5px 10px;font-size: 14px;font-weight: normal;border-top: 1px solid #bebabb;}

.sf-menu > li a {
  /*border-right: 1px solid #000;
  border-top: 1px solid #bebabb;*/
  
  letter-spacing: 1;
  font-size: 14pt;
  text-decoration: none;
  font-weight: bold;
  zoom: 1; /* IE7 */
  padding: 0;
}

.sf-menu > li {
  padding: 0 10px 0 0;
  margin: 0 10px 0 0;
  border-right: 2px solid #fff;
  /*background: #746d71;*/
  white-space: nowrap; /* no need for Supersubs plugin */
  *white-space: normal; /* ...unless you support IE7 (let it wrap) */
  -webkit-transition: background .2s;
  transition: background .2s;
}

.sf-menu > li:last-of-type {
	border: none;
}

.sf-menu ul li {
  background: #746d71;
}
.sf-menu ul ul li {
  background: #746d71;
}
.sf-menu li li:hover,
.sf-menu li li.sfHover {
  background: #bebabb;
  /* only transition out, not in */
  -webkit-transition: none;
  transition: none;
}

/*** arrows (for all except IE7) **/
.sf-arrows .sf-with-ul {
 /* padding-right: 2.5em;
  *padding-right: 1em;*/ /* no CSS arrows for IE7 (lack pseudo-elements) */
}
/* styling for both css and generated arrows */
.sf-arrows .sf-with-ul:after {
  /*content: '';*/
  position: absolute;
  top: 50%;
  right: 1em;
  margin-top: -3px;
  height: 0;
  width: 0;
  /* order of following 3 rules important for fallbacks to work */
  border: 5px solid transparent;
  border-top-color: #dFeEFF; /* edit this to suit design (no rgba in IE8) */
  border-top-color: rgba(255,255,255,.5);
}
.sf-arrows > li > .sf-with-ul:focus:after,
.sf-arrows > li:hover > .sf-with-ul:after,
.sf-arrows > .sfHover > .sf-with-ul:after {
  border-top-color: white; /* IE8 fallback colour */
}
/* styling for right-facing arrows */
.sf-arrows ul .sf-with-ul:after {
  margin-top: -5px;
  margin-right: -3px;
  border-color: transparent;
  border-left-color: #dFeEFF; /* edit this to suit design (no rgba in IE8) */
  border-left-color: rgba(255,255,255,.5);
}
.sf-arrows ul li > .sf-with-ul:focus:after,
.sf-arrows ul li:hover > .sf-with-ul:after,
.sf-arrows ul .sfHover > .sf-with-ul:after {
  border-left-color: white;
}
