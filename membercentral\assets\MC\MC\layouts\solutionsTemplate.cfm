<cfoutput>
	<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
		<cfinclude template="head.cfm" />
		<body>
			<cfinclude template="jssalesscript.cfm">
			<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct">
				<div class="bodyText" style="margin:6px;">#application.objCMS.renderZone(zone='Main',event=event)#</div>
			<cfelse>
				<cfinclude template="headerContent.cfm">
				<section class="banner">
					#application.objCMS.renderZone(zone='F',event=event)#
				</section>
					#application.objCMS.renderZone(zone='Main',event=event)#
				<cfinclude template="innerFooterContent.cfm">
				<section class="ams-soft">
					#application.objCMS.renderZone(zone='G',event=event)#
				</section>

				<cfinclude template="footerContent.cfm">
			</cfif>
			<cfinclude template="toolBar.cfm" />
			<cfinclude template="adRoll.cfm" />
		</body>
	</html>
</cfoutput>
