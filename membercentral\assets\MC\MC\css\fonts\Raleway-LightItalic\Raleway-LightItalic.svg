<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20120731 at Mon Apr 11 17:49:06 2016
 By ,,,
Copyright (c) 2010 - 2012, <PERSON> (<EMAIL>), <PERSON> (<EMAIL>), <PERSON> (<EMAIL>) with Reserved Font Name "Raleway"
</metadata>
<defs>
<font id="Raleway-LightItalic" horiz-adv-x="724" >
  <font-face 
    font-family="Raleway Light"
    font-weight="300"
    font-style="italic"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 4 3 3 1 1 6 0 3"
    ascent="800"
    descent="-200"
    x-height="520"
    cap-height="709"
    bbox="-266 -224 1339 936"
    underline-thickness="50"
    underline-position="-50"
    slope="-12"
    unicode-range="U+000D-FB06"
  />
<missing-glyph horiz-adv-x="0" 
 />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="546" 
d="M133 479h-69l9 41h70l1 8q7 32 22 63t38.5 55.5t54 39.5t69.5 15q33 0 57.5 -8.5t38.5 -18.5q26 30 59.5 48t78.5 18q23 0 38 -4.5t25 -10t15.5 -11t8.5 -7.5l-26 -36q-6 7 -18 15.5t-44 8.5q-33 0 -59.5 -14.5t-45.5 -37t-31 -50.5t-17 -55l-4 -18h163l-9 -41h-163
l-102 -479h-50l102 479h-162l-101 -479h-50zM354 520q7 36 18 64t27 51q-14 13 -35.5 17t-37.5 4q-31 0 -53.5 -12t-38.5 -31t-25.5 -41t-14.5 -43l-2 -9h162z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="676" 
d="M133 479h-69l9 41h69l2 8q6 31 21 61.5t38.5 55.5t54.5 40.5t70 15.5q33 0 57.5 -8.5t38.5 -18.5q28 30 63.5 48t82.5 18q24 0 44.5 -5t36.5 -11.5t27 -14t16 -11.5l-33 -35q-10 12 -37 22t-59 10q-35 0 -61.5 -14.5t-46 -37t-32 -50.5t-17.5 -55l-4 -18h243l-111 -520
h-50l102 479h-193l-102 -479h-50l102 479h-162l-101 -479h-50zM354 520q7 32 18 62t27 53q-14 13 -35.5 17t-37.5 4q-31 0 -54 -12.5t-39 -31t-25.5 -41t-13.5 -43.5l-2 -8h162z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="752" 
d="M609 -6q-39 0 -57 20t-18 49q0 14 4 33l105 491q3 13 3 28q0 33 -19.5 56.5t-64.5 23.5q-33 0 -58 -14t-43.5 -36.5t-31 -51.5t-18.5 -59l-3 -14h108l-9 -41h-108l-102 -479h-50l102 479h-166l-101 -479h-50l101 479h-69l9 41h69l2 7q8 36 25 67.5t41 55t55 37.5t69 14
q35 0 55.5 -8.5t34.5 -17.5q26 31 62 48t82 17q66 0 98 -31t32 -79q0 -18 -4 -35l-104 -493q-2 -10 -2 -16q0 -17 10 -31.5t34 -14.5q17 0 37 5t22 5v-42q0 -1 -8 -3t-20 -4.5t-26.5 -4.5t-27.5 -2zM358 520q8 36 17.5 66t22.5 50q-13 13 -31 16.5t-34 3.5
q-30 0 -53.5 -11.5t-41 -29.5t-29 -41t-15.5 -46l-2 -8h166z" />
    <glyph glyph-name="f_i" unicode="fi" horiz-adv-x="464" 
d="M133 479h-69l9 41h70l3 17q7 34 24 70.5t43.5 65.5t62.5 48t81 19q26 0 47 -5t37 -11.5t26 -14t15 -11.5l-33 -35q-11 13 -37.5 22.5t-61.5 9.5q-32 0 -58 -14.5t-45.5 -37t-32 -50.5t-18.5 -55l-3 -18h242l-111 -520h-50l102 479h-193l-101 -479h-50z" />
    <glyph glyph-name="f_l" unicode="fl" horiz-adv-x="536" 
d="M391 -6q-36 0 -54.5 20t-18.5 49q0 14 4 30l105 494q2 7 3 14t1 14q0 16 -4.5 30.5t-15 25.5t-27.5 17.5t-42 6.5q-30 0 -54.5 -13.5t-42.5 -35.5t-30.5 -50.5t-18.5 -59.5l-3 -16h108l-9 -41h-109l-101 -479h-50l101 479h-69l9 41h70l4 20q9 43 26 79.5t43 63.5t59.5 42
t75.5 15q67 0 99.5 -30.5t32.5 -78.5q0 -19 -4 -36l-105 -493q-2 -10 -2 -15q0 -18 10 -32.5t33 -14.5q9 0 19.5 1.5t19.5 3.5t15.5 3.5l6.5 1.5v-42q0 -1 -8 -3t-20 -4.5t-27 -4.5t-30 -2z" />
    <glyph glyph-name=".notdef" horiz-adv-x="0" 
 />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294z" />
    <glyph glyph-name="A.sc" horiz-adv-x="561" 
d="M316 545h40l121 -545h-49l-39 175h-263l-113 -175h-50zM384 216l-61 270l-176 -270h237z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="922" 
d="M506 710h452l-11 -46h-387l-58 -278h335l-10 -47h-336l-62 -293h395l-9 -46h-446l50 234h-253l-191 -234h-57zM426 280l77 366l-303 -366h226z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="922" 
d="M558 816l-31 16l76 85h57zM506 710h452l-11 -46h-387l-58 -278h335l-10 -47h-336l-62 -293h395l-9 -46h-446l50 234h-253l-191 -234h-57zM426 280l77 366l-303 -366h226z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="203" 
d="M66 629l-31 16l76 85h57z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM430 816l-31 16l76 85h57z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM438 853q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="257" 
d="M129 666q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM331 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="305" 
d="M54 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM346 828l18 88h44l-19 -88h-43zM477 828l19 88h43l-18 -88h-44z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="284" 
d="M55 640l18 88h44l-19 -88h-43zM186 640l19 88h43l-18 -88h-44z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM366 918h55l41 -85l-37 -16z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="204" 
d="M63 730h55l41 -85l-37 -16z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM307 858l8 37h272l-8 -37h-272z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="372" 
d="M62 671l8 37h272l-8 -37h-272z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="649" 
d="M488 -169q-26 6 -38.5 21t-12.5 35q0 29 21 55t72 58h-13l-44 234h-319l-142 -234h-55l437 710h44l133 -710q-56 -34 -77.5 -56.5t-21.5 -45.5q0 -31 39 -37zM469 276l-66 372l-228 -372h294z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="216" 
d="M78 -169q-26 6 -38.5 21t-12.5 35q0 32 27 61t92 68l15 -16q-56 -34 -77.5 -56.5t-21.5 -45.5q0 -31 39 -37l-23 -30v0z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM378 843q0 32 24 53t56 21q24 0 38 -14t14 -36q0 -32 -23.5 -53.5t-55.5 -21.5q-25 0 -39 14t-14 37zM452 894q-18 0 -32.5 -13t-14.5 -34q0 -14 8.5 -23t22.5 -9q16 0 32 13.5
t16 33.5q0 15 -9 23.5t-23 8.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="232" 
d="M63 656q0 32 24 53t56 21q24 0 38 -14t14 -36q0 -32 -23.5 -53.5t-55.5 -21.5q-25 0 -39 14t-14 37zM137 707q-18 0 -32.5 -13t-14.5 -34q0 -14 8.5 -23t22.5 -9q16 0 32 13.5t16 33.5q0 15 -9 23.5t-23 8.5z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="650" 
d="M358 738q0 32 24 53t56 21q24 0 38 -14t14 -36q0 -24 -13.5 -43t-35.5 -27l130 -692h-54l-44 234h-319l-142 -234h-55l426 693q-25 12 -25 45zM469 276l-66 372l-228 -372h294zM432 789q-18 0 -32.5 -13t-14.5 -34q0 -14 8.5 -23t22.5 -9q16 0 32 13.5t16 33.5
q0 15 -9 23.5t-23 8.5zM439 819l-31 16l76 83h57z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM496 834q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6
q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="378" 
d="M247 647q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z
" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="636" 
d="M178 710h316q30 0 52.5 -12.5t37.5 -32.5t22.5 -46t7.5 -54q0 -32 -10 -63t-28.5 -57t-43.5 -47t-55 -34q45 -15 68.5 -54.5t23.5 -90.5q0 -46 -19.5 -86t-51 -69.5t-72 -46.5t-82.5 -17h-317zM353 46q31 0 60.5 15t53 39t37.5 55t14 64q0 24 -6.5 45t-19 37.5t-30.5 26
t-40 9.5h-272l-62 -291h265zM404 380q31 0 59.5 14.5t50.5 38.5t35 55t13 64q0 47 -24 79.5t-68 32.5h-251l-60 -284h245z" />
    <glyph glyph-name="B.sc" horiz-adv-x="552" 
d="M138 545h266q25 0 44 -9.5t31.5 -25.5t19 -36.5t6.5 -41.5q0 -50 -30.5 -91.5t-79.5 -60.5q38 -12 58 -43t20 -70q0 -36 -16 -66.5t-42 -53t-59.5 -35t-69.5 -12.5h-264zM294 41q25 0 49 10.5t42.5 28.5t30 40.5t11.5 46.5q0 35 -21.5 61.5t-59.5 26.5h-223l-45 -214h216
zM333 295q24 0 46.5 10.5t40 27.5t27.5 39.5t10 46.5q0 35 -20.5 59.5t-55.5 24.5h-206l-43 -208h201z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="659" 
d="M295 -6q-59 0 -104.5 25t-77 67t-48 96t-16.5 111q0 73 30.5 147.5t84.5 135.5t128 99.5t161 38.5q45 0 81 -11.5t63 -32t45 -49t26 -62.5l-47 -24q-9 37 -28 62t-43.5 40.5t-53 22.5t-57.5 7q-49 0 -93.5 -15.5t-82 -42.5t-68 -63.5t-51.5 -77.5t-32.5 -85.5
t-11.5 -86.5q0 -52 14.5 -98t41.5 -81t65.5 -55.5t86.5 -20.5q30 0 64 8t67 25t63.5 43.5t55.5 62.5l40 -21q-26 -40 -61.5 -71t-76.5 -52t-83.5 -31.5t-81.5 -10.5z" />
    <glyph glyph-name="C.sc" horiz-adv-x="571" 
d="M246 -5q-50 0 -89 20t-66 52.5t-41 74.5t-14 87q0 55 24.5 112t69 103t105 75t132.5 29q75 0 121.5 -33.5t61.5 -85.5l-43 -23q-8 28 -24 47t-36.5 30.5t-44 16.5t-47.5 5q-60 0 -110 -25t-85.5 -64.5t-55.5 -88t-20 -95.5q0 -39 12 -74t34.5 -62t55 -42.5t72.5 -15.5
q50 0 106 24.5t97 79.5l35 -20q-21 -31 -50.5 -54.5t-63 -39.5t-69 -24.5t-67.5 -8.5z" />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="659" 
d="M472 816l-31 16l76 85h57zM295 -6q-59 0 -104.5 25t-77 67t-48 96t-16.5 111q0 73 30.5 147.5t84.5 135.5t128 99.5t161 38.5q45 0 81 -11.5t63 -32t45 -49t26 -62.5l-47 -24q-9 37 -28 62t-43.5 40.5t-53 22.5t-57.5 7q-49 0 -93.5 -15.5t-82 -42.5t-68 -63.5
t-51.5 -77.5t-32.5 -85.5t-11.5 -86.5q0 -52 14.5 -98t41.5 -81t65.5 -55.5t86.5 -20.5q30 0 64 8t67 25t63.5 43.5t55.5 62.5l40 -21q-26 -40 -61.5 -71t-76.5 -52t-83.5 -31.5t-81.5 -10.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="659" 
d="M384 900l33 17l68 -63l94 63l26 -17l-109 -81h-37zM295 -6q-59 0 -104.5 25t-77 67t-48 96t-16.5 111q0 73 30.5 147.5t84.5 135.5t128 99.5t161 38.5q45 0 81 -11.5t63 -32t45 -49t26 -62.5l-47 -24q-9 37 -28 62t-43.5 40.5t-53 22.5t-57.5 7q-49 0 -93.5 -15.5
t-82 -42.5t-68 -63.5t-51.5 -77.5t-32.5 -85.5t-11.5 -86.5q0 -52 14.5 -98t41.5 -81t65.5 -55.5t86.5 -20.5q30 0 64 8t67 25t63.5 43.5t55.5 62.5l40 -21q-26 -40 -61.5 -71t-76.5 -52t-83.5 -31.5t-81.5 -10.5z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="305" 
d="M60 713l33 17l68 -63l94 63l26 -17l-109 -81h-37z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="655" 
d="M189 -125q13 -7 34 -13t45 -6q65 0 65 45q0 16 -11.5 22t-29.5 6q-20 0 -43 -5t-36 -9l59 80q-54 4 -96.5 30.5t-71 67.5t-43.5 93t-15 107q0 73 30.5 147.5t84.5 135.5t128 99.5t161 38.5q45 0 81 -11.5t63 -32t45 -49t26 -62.5l-47 -24q-9 37 -28 62t-43.5 40.5
t-53 22.5t-57.5 7q-49 0 -93.5 -15.5t-82 -42.5t-68 -63.5t-51.5 -77.5t-32.5 -85.5t-11.5 -86.5q0 -52 14.5 -98t41.5 -81t65.5 -55.5t86.5 -20.5q30 0 64 8t67 25t63.5 43.5t55.5 62.5l40 -21q-25 -39 -59 -68.5t-73 -50.5t-80 -32.5t-79 -12.5l-37 -48q9 4 19.5 5t18.5 1
q58 0 58 -46q0 -38 -27 -58t-78 -20q-26 0 -48.5 5.5t-39.5 13.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="258" 
d="M8 -125q13 -7 34 -13t45 -6q65 0 65 45q0 16 -11.5 22t-29.5 6q-20 0 -43 -5t-36 -9l74 100l24 -12l-44 -56q9 4 19.5 5t18.5 1q58 0 58 -46q0 -38 -27 -58t-78 -20q-26 0 -48.5 5.5t-39.5 13.5z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="659" 
d="M377 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM295 -6q-59 0 -104.5 25t-77 67t-48 96t-16.5 111q0 73 30.5 147.5t84.5 135.5t128 99.5t161 38.5q45 0 81 -11.5t63 -32t45 -49t26 -62.5l-47 -24q-9 37 -28 62t-43.5 40.5t-53 22.5t-57.5 7q-49 0 -93.5 -15.5
t-82 -42.5t-68 -63.5t-51.5 -77.5t-32.5 -85.5t-11.5 -86.5q0 -52 14.5 -98t41.5 -81t65.5 -55.5t86.5 -20.5q30 0 64 8t67 25t63.5 43.5t55.5 62.5l40 -21q-26 -40 -61.5 -71t-76.5 -52t-83.5 -31.5t-81.5 -10.5z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="659" 
d="M295 -6q-59 0 -104.5 25t-77 67t-48 96t-16.5 111q0 73 30.5 147.5t84.5 135.5t128 99.5t161 38.5q45 0 81 -11.5t63 -32t45 -49t26 -62.5l-47 -24q-9 37 -28 62t-43.5 40.5t-53 22.5t-57.5 7q-49 0 -93.5 -15.5t-82 -42.5t-68 -63.5t-51.5 -77.5t-32.5 -85.5
t-11.5 -86.5q0 -52 14.5 -98t41.5 -81t65.5 -55.5t86.5 -20.5q30 0 64 8t67 25t63.5 43.5t55.5 62.5l40 -21q-26 -40 -61.5 -71t-76.5 -52t-83.5 -31.5t-81.5 -10.5zM462 827l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="140" 
d="M47 640l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="688" 
d="M178 710h225q66 0 114.5 -21t80 -57.5t47 -86t15.5 -106.5q0 -95 -30.5 -175t-85 -139t-129.5 -92t-163 -33h-225zM262 46q80 0 144 31.5t108.5 84.5t68.5 122.5t24 145.5q0 51 -13.5 94t-40.5 74t-67 48.5t-93 17.5h-174l-131 -618h174z" />
    <glyph glyph-name="D.sc" horiz-adv-x="596" 
d="M138 545h189q56 0 97.5 -16.5t68.5 -45t40 -67.5t13 -84q0 -72 -25 -133t-70 -105t-106 -69t-133 -25h-190zM220 42q65 0 116.5 23t87.5 62t55 90.5t19 107.5q0 78 -45 127.5t-135 49.5h-143l-97 -460h142z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="688" 
d="M340 900l33 17l68 -63l94 63l26 -17l-109 -81h-37zM178 710h225q66 0 114.5 -21t80 -57.5t47 -86t15.5 -106.5q0 -95 -30.5 -175t-85 -139t-129.5 -92t-163 -33h-225zM262 46q80 0 144 31.5t108.5 84.5t68.5 122.5t24 145.5q0 51 -13.5 94t-40.5 74t-67 48.5t-93 17.5
h-174l-131 -618h174z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="695" 
d="M54 378h60l71 332h225q66 0 114.5 -21t80 -57.5t47 -86t15.5 -106.5q0 -95 -30.5 -175t-85 -139t-129.5 -92t-163 -33h-225l71 336h-60zM269 46q80 0 144 31.5t108.5 84.5t68.5 122.5t24 145.5q0 51 -13.5 94t-40.5 74t-67 48.5t-93 17.5h-174l-61 -286h169l-9 -42h-169
l-61 -290h174z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="584" 
d="M178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450z" />
    <glyph glyph-name="E.sc" horiz-adv-x="509" 
d="M138 545h370l-9 -43h-324l-43 -204h283l-9 -40h-282l-46 -216h330l-9 -42h-377z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="584" 
d="M413 816l-31 16l76 85h57zM178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="584" 
d="M178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450zM423 853q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="584" 
d="M323 900l33 17l68 -63l94 63l26 -17l-109 -81h-37zM178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="584" 
d="M323 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="584" 
d="M336 828l18 88h44l-19 -88h-43zM467 828l19 88h43l-18 -88h-44zM178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="584" 
d="M178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450zM404 827l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="584" 
d="M178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450zM361 918h55l41 -85l-37 -16z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="584" 
d="M178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450zM294 858l8 37h272l-8 -37h-272z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="739" 
d="M405 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36 7.5t34 21t28.5 32t17.5 40.5l8 34l-325 609l-131 -613h-51l151 710h39l333 -623l133 622h50l-156 -738q-7 -33 -25 -60t-42 -46.5t-51.5 -30.5t-53.5 -11z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="584" 
d="M178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-196q-56 -34 -77.5 -56.5t-21.5 -45.5q0 -31 39 -37l-23 -30q-26 6 -38.5 21t-12.5 35q0 29 21 55t72 58h-213z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="695" 
d="M54 378h60l71 332h225q66 0 114.5 -21t80 -57.5t47 -86t15.5 -106.5q0 -95 -30.5 -175t-85 -139t-129.5 -92t-163 -33h-225l71 336h-60zM269 46q80 0 144 31.5t108.5 84.5t68.5 122.5t24 145.5q0 51 -13.5 94t-40.5 74t-67 48.5t-93 17.5h-174l-61 -286h169l-9 -42h-169
l-61 -290h174z" />
    <glyph glyph-name="Etilde" unicode="&#x1ebc;" horiz-adv-x="584" 
d="M178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450zM476 834q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7
t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="768" 
d="M157 410q19 59 55.5 114t86 97t111.5 67.5t132 25.5q45 0 81.5 -11.5t63.5 -32t45 -49t26 -62.5l-47 -24q-10 37 -28.5 62t-43 40.5t-53 22.5t-57.5 7q-58 0 -108.5 -21.5t-92 -57t-72 -82t-47.5 -96.5h342l-22 -34h-329q-4 -17 -6.5 -33t-3.5 -32h304l-22 -34h-282
q3 -49 18.5 -92t42.5 -75t64 -50.5t83 -18.5q30 0 63.5 8t66.5 25t64 43.5t57 62.5l38 -21q-25 -40 -61 -71t-76.5 -52t-83.5 -31.5t-82 -10.5q-57 0 -101.5 23.5t-76 63t-49 90.5t-19.5 106h-110l22 34h88q1 16 3.5 32t6.5 33h-69l23 34h55z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="578" 
d="M178 710h440l-10 -46h-389l-59 -282h329l-9 -44h-330l-72 -338h-51z" />
    <glyph glyph-name="F.sc" horiz-adv-x="500" 
d="M138 545h368l-9 -43h-322l-43 -206h273l-9 -41h-273l-54 -255h-47z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="687" 
d="M546 110q-121 -115 -248 -115q-59 0 -105 25t-78 66.5t-49 95t-17 110.5q0 79 32.5 155t87.5 135.5t126.5 96t148.5 36.5q51 0 89 -11.5t65 -32t44.5 -49t26.5 -62.5l-46 -25q-20 70 -69 101.5t-121 31.5q-69 0 -129.5 -33.5t-105.5 -87t-71.5 -120t-26.5 -132.5
q0 -51 15 -97t42.5 -80.5t66 -55t86.5 -20.5q61 0 122 29.5t126 94.5l30 138h-152l9 41h196l-73 -345h-45z" />
    <glyph glyph-name="G.alt" horiz-adv-x="702" 
d="M299 -5q-59 0 -105.5 24t-78.5 64.5t-49 94t-17 111.5q0 81 33.5 157.5t89.5 136t128.5 96t151.5 36.5q85 0 141.5 -43t82.5 -112l-46 -33q-10 28 -27 54t-41 45t-55 30.5t-68 11.5q-69 0 -130.5 -33.5t-108 -87t-73.5 -120.5t-27 -134q0 -50 13.5 -95t40 -80t65 -55.5
t88.5 -20.5q52 0 100.5 21t89.5 56.5t72.5 81t48.5 94.5h-209l10 46h264l-8 -32q-14 -55 -49.5 -111t-86 -101t-112.5 -73.5t-128 -28.5z" />
    <glyph glyph-name="G.sc" horiz-adv-x="592" 
d="M449 82q-96 -86 -203 -86q-49 0 -88 19.5t-66 52t-41.5 74t-14.5 86.5q0 60 26.5 117.5t71 102.5t103 72.5t123.5 27.5q83 0 127.5 -33t61.5 -85l-43 -24q-17 52 -58 76t-99 24q-56 0 -104.5 -24.5t-84.5 -64t-56.5 -88.5t-20.5 -98q0 -39 12.5 -74t35.5 -61.5t55 -41.5
t72 -15q100 0 202 93l21 97h-126l7 37h167l-56 -266h-41z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="687" 
d="M546 110q-121 -115 -248 -115q-59 0 -105 25t-78 66.5t-49 95t-17 110.5q0 79 32.5 155t87.5 135.5t126.5 96t148.5 36.5q51 0 89 -11.5t65 -32t44.5 -49t26.5 -62.5l-46 -25q-20 70 -69 101.5t-121 31.5q-69 0 -129.5 -33.5t-105.5 -87t-71.5 -120t-26.5 -132.5
q0 -51 15 -97t42.5 -80.5t66 -55t86.5 -20.5q61 0 122 29.5t126 94.5l30 138h-152l9 41h196l-73 -345h-45zM468 853q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z
" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="687" 
d="M360 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM546 110q-121 -115 -248 -115q-59 0 -105 25t-78 66.5t-49 95t-17 110.5q0 79 32.5 155t87.5 135.5t126.5 96t148.5 36.5q51 0 89 -11.5t65 -32t44.5 -49t26.5 -62.5l-46 -25q-20 70 -69 101.5t-121 31.5
q-69 0 -129.5 -33.5t-105.5 -87t-71.5 -120t-26.5 -132.5q0 -51 15 -97t42.5 -80.5t66 -55t86.5 -20.5q61 0 122 29.5t126 94.5l30 138h-152l9 41h196l-73 -345h-45z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="687" 
d="M232 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM546 110q-121 -115 -248 -115q-59 0 -105 25t-78 66.5t-49 95t-17 110.5q0 79 32.5 155t87.5 135.5t126.5 96t148.5 36.5q51 0 89 -11.5t65 -32t44.5 -49t26.5 -62.5l-46 -25q-20 70 -69 101.5t-121 31.5
q-69 0 -129.5 -33.5t-105.5 -87t-71.5 -120t-26.5 -132.5q0 -51 15 -97t42.5 -80.5t66 -55t86.5 -20.5q61 0 122 29.5t126 94.5l30 138h-152l9 41h196l-73 -345h-45z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="136" 
d="M15 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="687" 
d="M546 110q-121 -115 -248 -115q-59 0 -105 25t-78 66.5t-49 95t-17 110.5q0 79 32.5 155t87.5 135.5t126.5 96t148.5 36.5q51 0 89 -11.5t65 -32t44.5 -49t26.5 -62.5l-46 -25q-20 70 -69 101.5t-121 31.5q-69 0 -129.5 -33.5t-105.5 -87t-71.5 -120t-26.5 -132.5
q0 -51 15 -97t42.5 -80.5t66 -55t86.5 -20.5q61 0 122 29.5t126 94.5l30 138h-152l9 41h196l-73 -345h-45zM449 827l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="706" 
d="M178 710h51l-69 -324h422l69 324h51l-151 -710h-51l72 340h-421l-73 -340h-51z" />
    <glyph glyph-name="H.sc" horiz-adv-x="612" 
d="M138 545h47l-52 -245h347l51 245h47l-116 -545h-46l54 257h-347l-54 -257h-47z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="715" 
d="M109 583h47l27 127h51l-27 -127h422l27 127h51l-27 -127h47l-7 -32h-47l-117 -551h-51l72 340h-422l-72 -340h-51l117 551h-47zM587 386l35 165h-422l-35 -165h422z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="706" 
d="M355 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM178 710h51l-69 -324h422l69 324h51l-151 -710h-51l72 340h-421l-73 -340h-51z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51z" />
    <glyph glyph-name="I.sc" horiz-adv-x="219" 
d="M138 544h46l-115 -544h-47z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="698" 
d="M178 709h51l-151 -709h-51zM335 -11q-85 0 -137 44l25 41q19 -17 50 -27.5t72 -10.5q54 0 91 21t62.5 61t42.5 98.5t33 132.5l77 360h51l-77 -360q-17 -81 -38 -147.5t-53 -113.5t-80 -73t-119 -26z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="464" 
d="M102 -11q-85 0 -137 44l25 41q19 -17 50 -27.5t72 -10.5q54 0 91 21t62.5 61t42.5 98.5t33 132.5l77 360h51l-77 -360q-17 -81 -38 -147.5t-53 -113.5t-80 -73t-119 -26z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM218 816l-31 16l76 85h57z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM223 853q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM119 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM140 828l18 88h44l-19 -88h-43zM271 828l19 88h43l-18 -88h-44z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM205 827l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM162 918h55l41 -85l-37 -16z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM99 858l8 37h272l-8 -37h-272z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="234" 
d="M179 709h51l-151 -709q-56 -34 -77.5 -56.5t-21.5 -45.5q0 -31 39 -37l-23 -30q-26 6 -38.5 21t-12.5 35q0 29 21.5 55t71.5 58h-10z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM285 834q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5
t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="J.sc" horiz-adv-x="409" 
d="M84 -8q-73 0 -115 33l22 38q16 -12 42 -20.5t60 -8.5q45 0 75 15.5t50 45.5t33.5 73.5t25.5 99.5l59 276h46l-59 -276q-13 -62 -29.5 -113t-43 -87t-66.5 -56t-100 -20z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="464" 
d="M357 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM102 -11q-85 0 -137 44l25 41q19 -17 50 -27.5t72 -10.5q54 0 91 21t62.5 61t42.5 98.5t33 132.5l77 360h51l-77 -360q-17 -81 -38 -147.5t-53 -113.5t-80 -73t-119 -26z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="606" 
d="M178 709h51l-92 -430l482 431h59l-346 -311l209 -399h-58l-190 368l-168 -149l-47 -219h-51z" />
    <glyph glyph-name="K.sc" horiz-adv-x="530" 
d="M138 544h46l-68 -323l389 324h54l-284 -239l180 -306h-52l-164 277l-134 -109l-36 -168h-47z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="606" 
d="M191 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM178 709h51l-92 -430l482 431h59l-346 -311l209 -399h-58l-190 368l-168 -149l-47 -219h-51z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="555" 
d="M178 710h51l-141 -664h401l-10 -46h-452z" />
    <glyph glyph-name="L.sc" horiz-adv-x="471" 
d="M138 545h47l-107 -503h323l-8 -42h-371z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="555" 
d="M226 816l-31 16l76 85h57zM178 710h51l-141 -664h401l-10 -46h-452z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="555" 
d="M282 645l76 85h57l-102 -101zM178 710h51l-141 -664h401l-10 -46h-452z" />
    <glyph glyph-name="caron.alt" horiz-adv-x="203" 
d="M48 645l76 85h57l-102 -101z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="555" 
d="M191 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM178 710h51l-141 -664h401l-10 -46h-452z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="555" 
d="M178 710h51l-141 -664h401l-10 -46h-452zM331 353l22 101h43l-21 -101h-44z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="174" 
d="M53 248l22 101h43l-21 -101h-44z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="562" 
d="M14 257l91 70l81 383h51l-72 -336l189 148l12 -30l-211 -165l-60 -281h401l-9 -46h-452l59 279l-68 -53z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="842" 
d="M178 710h51l171 -508l388 508h50l-151 -710h-51l131 614l-365 -479h-32l-161 479l-131 -614h-51z" />
    <glyph glyph-name="M.sc" 
d="M138 545h47l149 -381l309 381h47l-116 -545h-46l97 459l-290 -357h-29l-140 357l-97 -459h-47z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="741" 
d="M178 710h40l336 -625l132 624h51l-151 -709h-45l-332 614l-131 -614h-51z" />
    <glyph glyph-name="N.sc" horiz-adv-x="641" 
d="M138 545h36l287 -465l99 464h46l-115 -544h-42l-283 457l-97 -457h-47z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="741" 
d="M479 816l-31 16l76 85h57zM178 710h40l336 -625l132 624h51l-151 -709h-45l-332 614l-131 -614h-51z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="741" 
d="M385 900l33 17l68 -63l94 63l26 -17l-109 -81h-37zM178 710h40l336 -625l132 624h51l-151 -709h-45l-332 614l-131 -614h-51z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="741" 
d="M249 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM178 710h40l336 -625l132 624h51l-151 -709h-45l-332 614l-131 -614h-51z" />
    <glyph glyph-name="Ndotaccent" unicode="&#x1e44;" horiz-adv-x="741" 
d="M464 827l19 90h46l-19 -90h-46zM178 710h40l336 -625l132 624h51l-151 -709h-45l-332 614l-131 -614h-51z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="741" 
d="M178 710h40l336 -625l132 624h51l-151 -709h-45l-332 614l-131 -614h-51zM547 834q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5
t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="O" unicode="O" 
d="M297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55
t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="O.sc" horiz-adv-x="623" 
d="M247 -4q-51 0 -90.5 19t-66.5 50.5t-41 73t-14 86.5q0 61 27.5 119.5t73.5 103.5t105.5 72.5t123.5 27.5q51 0 90 -19.5t65.5 -51.5t40.5 -73.5t14 -85.5q0 -62 -27.5 -120t-73 -103t-104.5 -72t-123 -27zM257 39q56 0 105.5 24.5t86 64t58 88.5t21.5 98q0 38 -12 72.5
t-34 61t-54.5 42t-72.5 15.5q-56 0 -105 -24.5t-86.5 -64t-59 -88.5t-21.5 -98q0 -38 11.5 -72.5t34 -61t55 -42t73.5 -15.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1117" 
d="M298 -5q-60 0 -106.5 24.5t-78.5 65t-48.5 93.5t-16.5 110q0 81 34 158t90 137t129 96t150 36q47 0 85 -16.5t65.5 -44.5t45.5 -65.5t24 -79.5l43 201h439l-11 -46h-387l-58 -278h335l-10 -47h-336l-62 -293h395l-9 -46h-447l43 200q-26 -43 -60 -80.5t-74 -65
t-84.5 -43.5t-89.5 -16zM307 42q69 0 130.5 33.5t108 87t73.5 120.5t27 134q0 50 -14 95.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" 
d="M469 816l-31 16l76 85h57zM297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87t73 120.5t27 133
q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" 
d="M297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55
t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20zM473 853q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35
t28.5 -15z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" 
d="M371 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87
t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" 
d="M388 828l18 88h44l-19 -88h-43zM519 828l19 88h43l-18 -88h-44zM297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5
t108 87t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" 
d="M297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55
t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20zM424 918h55l41 -85l-37 -16z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" 
d="M402 817l-26 16l75 85h51zM514 817l-26 16l74 85h52zM297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87
t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="302" 
d="M67 629l-26 16l75 85h51zM179 629l-26 16l74 85h52z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" 
d="M345 858l8 37h272l-8 -37h-272zM297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87t73 120.5t27 133
q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Oogonek" unicode="&#x1ea;" horiz-adv-x="723" 
d="M234 -171q-26 6 -38.5 21t-12.5 35q0 28 20.5 53.5t69.5 57.5q-54 5 -96 31t-70.5 65.5t-43.5 90t-15 104.5q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -80 -32 -154.5t-85.5 -133.5t-123 -96t-144.5 -41q-54 -33 -74.5 -55t-20.5 -45
q0 -31 39 -37zM307 42q69 0 131 33.5t108 87t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" 
d="M701 710l-70 -83q33 -42 50 -95.5t17 -110.5q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36q-46 0 -83.5 14t-67.5 39l-40 -48h-57l67 80q-34 41 -51 94.5t-17 112.5q0 82 34 159t90 137t129 96t150 36q46 0 83.5 -15t66.5 -41l43 51h57zM100 293q0 -101 51 -172
l416 497q-25 23 -57 36.5t-70 13.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134zM307 42q69 0 131 33.5t108 87t73 120.5t27 133q0 48 -13 91.5t-37 77.5l-416 -496q25 -23 57 -35t70 -12z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="574" 
d="M626 710l-595 -710h-57l595 710h57z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" 
d="M701 710l-70 -83q33 -42 50 -95.5t17 -110.5q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36q-46 0 -83.5 14t-67.5 39l-40 -48h-57l67 80q-34 41 -51 94.5t-17 112.5q0 82 34 159t90 137t129 96t150 36q46 0 83.5 -15t66.5 -41l43 51h57zM100 293q0 -101 51 -172
l416 497q-25 23 -57 36.5t-70 13.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134zM307 42q69 0 131 33.5t108 87t73 120.5t27 133q0 48 -13 91.5t-37 77.5l-416 -496q25 -23 57 -35t70 -12zM465 816l-31 16l76 85h57z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" 
d="M297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55
t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20zM534 834q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14
t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="598" 
d="M178 710h278q37 0 65.5 -15.5t48.5 -41t30.5 -58t10.5 -67.5q0 -47 -19.5 -93.5t-52.5 -83.5t-77.5 -60t-93.5 -23h-233l-57 -268h-51zM376 315q38 0 71.5 18.5t58 48.5t39 67t14.5 74q0 29 -8.5 54.5t-23.5 45t-36.5 30.5t-47.5 11h-224l-74 -349h231z" />
    <glyph glyph-name="P.sc" horiz-adv-x="520" 
d="M138 545h232q31 0 56 -12t42 -32.5t26 -46t9 -52.5q0 -36 -16 -72t-43 -64t-63.5 -45.5t-77.5 -17.5h-191l-43 -203h-47zM310 245q31 0 57.5 13.5t46 35t30.5 48.5t11 54q0 45 -26 75.5t-70 30.5h-184l-54 -257h189z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="723" 
d="M502 64q-47 -32 -99 -50.5t-106 -18.5q-60 0 -106 24t-78 64.5t-48.5 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -47 -11.5 -92.5t-32.5 -88t-50.5 -80t-65.5 -68.5l55 -92h-53zM307 42q48 0 91.5 16t81.5 44l-60 104
h52l44 -75q29 28 53 61.5t41 70.5t26.5 76t9.5 77q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Q.sc" horiz-adv-x="622" 
d="M417 48q-38 -25 -81.5 -38.5t-88.5 -13.5q-51 0 -90.5 19t-66.5 50.5t-41 73t-14 86.5q0 61 27.5 119.5t73.5 103.5t105.5 72.5t123.5 27.5q51 0 90 -19.5t65.5 -51.5t40.5 -73.5t14 -85.5q0 -70 -34.5 -134.5t-91.5 -111.5l47 -72h-48zM257 39q38 0 72.5 12t65.5 32
l-52 81h47l36 -57q46 41 73.5 96t27.5 111q0 38 -11.5 73t-34 61.5t-54.5 42t-72 15.5q-56 0 -105.5 -24.5t-86.5 -64t-58.5 -89t-21.5 -98.5q0 -38 11.5 -72.5t34 -61t55 -42t73.5 -15.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="624" 
d="M27 0l151 710h282q36 0 65 -15.5t49 -41t30.5 -58t10.5 -67.5q0 -44 -16.5 -86.5t-45 -77t-66 -58.5t-80.5 -30l108 -276h-58l-106 268h-216l-57 -268h-51zM380 315q37 0 70.5 18.5t58 48t39 66.5t14.5 74q0 28 -8.5 54t-23.5 45.5t-36 31t-47 11.5h-228l-74 -349h235z
" />
    <glyph glyph-name="R.sc" horiz-adv-x="544" 
d="M138 545h236q31 0 56 -12t42 -32.5t26 -46t9 -52.5q0 -33 -13 -65t-36 -58t-53.5 -44t-65.5 -24l95 -211h-54l-91 203h-177l-43 -203h-47zM313 245q30 0 56.5 13.5t46.5 35t31.5 48.5t11.5 54q0 43 -26 74.5t-71 31.5h-187l-54 -257h192z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="624" 
d="M400 819l-31 16l76 85h57zM27 0l151 710h282q36 0 65 -15.5t49 -41t30.5 -58t10.5 -67.5q0 -44 -16.5 -86.5t-45 -77t-66 -58.5t-80.5 -30l108 -276h-58l-106 268h-216l-57 -268h-51zM380 315q37 0 70.5 18.5t58 48t39 66.5t14.5 74q0 28 -8.5 54t-23.5 45.5t-36 31
t-47 11.5h-228l-74 -349h235z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="624" 
d="M327 903l33 17l68 -63l94 63l26 -17l-109 -81h-37zM27 0l151 710h282q36 0 65 -15.5t49 -41t30.5 -58t10.5 -67.5q0 -44 -16.5 -86.5t-45 -77t-66 -58.5t-80.5 -30l108 -276h-58l-106 268h-216l-57 -268h-51zM380 315q37 0 70.5 18.5t58 48t39 66.5t14.5 74q0 28 -8.5 54
t-23.5 45.5t-36 31t-47 11.5h-228l-74 -349h235z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="624" 
d="M188 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM27 0l151 710h282q36 0 65 -15.5t49 -41t30.5 -58t10.5 -67.5q0 -44 -16.5 -86.5t-45 -77t-66 -58.5t-80.5 -30l108 -276h-58l-106 268h-216l-57 -268h-51zM380 315q37 0 70.5 18.5t58 48t39 66.5t14.5 74
q0 28 -8.5 54t-23.5 45.5t-36 31t-47 11.5h-228l-74 -349h235z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="583" 
d="M240 -6q-158 0 -246 106l36 41q32 -47 87.5 -74.5t133.5 -27.5q101 0 160 42t59 121q0 55 -46.5 83.5t-134.5 48.5q-45 10 -79.5 22t-58.5 28.5t-36.5 40t-12.5 56.5q0 55 22 98.5t60 73.5t90.5 46t113.5 16q66 0 113.5 -20.5t79.5 -62.5l-35 -40q-48 77 -168 77
q-59 0 -100.5 -13.5t-68.5 -36.5t-39.5 -54.5t-12.5 -68.5q0 -26 10.5 -44t31.5 -31t52.5 -23t73.5 -20q45 -11 82.5 -23.5t63.5 -31t40.5 -44t14.5 -62.5q0 -54 -21.5 -96t-60 -70t-91 -42.5t-113.5 -14.5z" />
    <glyph glyph-name="S.sc" horiz-adv-x="507" 
d="M199 -5q-129 0 -206 80l33 38q27 -35 73 -56t111 -21q78 0 125.5 29.5t47.5 88.5q0 42 -38 62.5t-112 36.5q-37 8 -66 17.5t-48.5 23t-29.5 32t-10 44.5q0 42 18 75t49.5 56t74 35t92.5 12q55 0 94.5 -15.5t67.5 -47.5l-30 -37q-22 29 -56 44t-85 15q-47 0 -80 -9.5
t-54.5 -26.5t-31.5 -39.5t-10 -49.5q0 -19 8.5 -32.5t26 -23.5t44 -18t61.5 -16q37 -8 68 -18t52.5 -24t33.5 -34t12 -49q0 -42 -17.5 -74.5t-49 -54t-74.5 -32.5t-94 -11z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="583" 
d="M401 835l-31 16l76 85h57zM240 -6q-158 0 -246 106l36 41q32 -47 87.5 -74.5t133.5 -27.5q101 0 160 42t59 121q0 55 -46.5 83.5t-134.5 48.5q-45 10 -79.5 22t-58.5 28.5t-36.5 40t-12.5 56.5q0 55 22 98.5t60 73.5t90.5 46t113.5 16q66 0 113.5 -20.5t79.5 -62.5
l-35 -40q-48 77 -168 77q-59 0 -100.5 -13.5t-68.5 -36.5t-39.5 -54.5t-12.5 -68.5q0 -26 10.5 -44t31.5 -31t52.5 -23t73.5 -20q45 -11 82.5 -23.5t63.5 -31t40.5 -44t14.5 -62.5q0 -54 -21.5 -96t-60 -70t-91 -42.5t-113.5 -14.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="583" 
d="M317 919l33 17l68 -63l94 63l26 -17l-109 -81h-37zM240 -6q-158 0 -246 106l36 41q32 -47 87.5 -74.5t133.5 -27.5q101 0 160 42t59 121q0 55 -46.5 83.5t-134.5 48.5q-45 10 -79.5 22t-58.5 28.5t-36.5 40t-12.5 56.5q0 55 22 98.5t60 73.5t90.5 46t113.5 16
q66 0 113.5 -20.5t79.5 -62.5l-35 -40q-48 77 -168 77q-59 0 -100.5 -13.5t-68.5 -36.5t-39.5 -54.5t-12.5 -68.5q0 -26 10.5 -44t31.5 -31t52.5 -23t73.5 -20q45 -11 82.5 -23.5t63.5 -31t40.5 -44t14.5 -62.5q0 -54 -21.5 -96t-60 -70t-91 -42.5t-113.5 -14.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="583" 
d="M309 855l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM240 -6q-158 0 -246 106l36 41q32 -47 87.5 -74.5t133.5 -27.5q101 0 160 42t59 121q0 55 -46.5 83.5t-134.5 48.5q-45 10 -79.5 22t-58.5 28.5t-36.5 40t-12.5 56.5q0 55 22 98.5t60 73.5t90.5 46t113.5 16
q66 0 113.5 -20.5t79.5 -62.5l-35 -40q-48 77 -168 77q-59 0 -100.5 -13.5t-68.5 -36.5t-39.5 -54.5t-12.5 -68.5q0 -26 10.5 -44t31.5 -31t52.5 -23t73.5 -20q45 -11 82.5 -23.5t63.5 -31t40.5 -44t14.5 -62.5q0 -54 -21.5 -96t-60 -70t-91 -42.5t-113.5 -14.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="579" 
d="M339 664h-246l9 46h544l-10 -46h-246l-142 -664h-50z" />
    <glyph glyph-name="T.sc" horiz-adv-x="504" 
d="M271 502h-205l9 43h455l-10 -43h-202l-107 -502h-47z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="616" 
d="M88 412h216l54 252h-246l9 46h544l-10 -46h-246l-54 -252h216l-7 -32h-216l-80 -380h-51l80 380h-215z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="579" 
d="M297 900l33 17l68 -63l94 63l26 -17l-109 -81h-37zM339 664h-246l9 46h544l-10 -46h-246l-142 -664h-50z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="577" 
d="M178 710h51l-31 -144h209q37 0 65.5 -15.5t48.5 -41t30.5 -58t10.5 -67.5q0 -47 -19.5 -93.5t-52.5 -83.5t-77.5 -60t-93.5 -23h-215l-26 -124h-51zM327 171q39 0 72.5 18.5t58 48.5t39 67t14.5 73q0 28 -8.5 54t-23.5 45t-36.5 30.5t-47.5 11.5h-206l-74 -348h212z" />
    <glyph glyph-name="U" unicode="U" 
d="M298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29
z" />
    <glyph glyph-name="U.sc" horiz-adv-x="623" 
d="M248 -4q-52 0 -88 14.5t-58 40t-31.5 59t-9.5 72.5q0 22 2.5 44t7.5 45l58 274h47l-58 -274q-9 -41 -9 -81q0 -32 7.5 -59.5t24.5 -47.5t45.5 -32t69.5 -12q55 0 96 20t69.5 53t46 74.5t26.5 84.5l58 274h46l-58 -274q-12 -55 -34.5 -105t-58.5 -87.5t-85.5 -60
t-113.5 -22.5z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" 
d="M453 816l-31 16l76 85h57zM298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5
t-72.5 -114t-103.5 -78.5t-137.5 -29z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" 
d="M298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29
zM470 853q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" 
d="M364 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357
q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" 
d="M383 828l18 88h44l-19 -88h-43zM514 828l19 88h43l-18 -88h-44zM298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51
l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" 
d="M298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29
zM416 918h55l41 -85l-37 -16z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" 
d="M298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29
zM394 817l-26 16l75 85h51zM506 817l-26 16l74 85h52z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" 
d="M298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29
zM340 858l8 37h272l-8 -37h-272z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" 
d="M234 -171q-26 6 -38.5 21t-12.5 35q0 28 20.5 53.5t68.5 57.5q-54 4 -91 23.5t-60 50.5t-33 72t-10 87q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357
q-15 -70 -42.5 -133.5t-69.5 -112.5t-99.5 -78.5t-131.5 -32.5q-53 -33 -73.5 -55t-20.5 -45q0 -31 39 -37l-23 -30v0z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" 
d="M411 843q0 32 24 53t56 21q24 0 38 -14t14 -36q0 -32 -23.5 -53.5t-55.5 -21.5q-25 0 -39 14t-14 37zM485 894q-18 0 -32.5 -13t-14.5 -34q0 -14 8.5 -23t22.5 -9q16 0 32 13.5t16 33.5q0 15 -9 23.5t-23 8.5zM298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5
q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" 
d="M298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29
zM528 834q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="647" 
d="M98 710h54l118 -646l391 646h54l-436 -710h-47z" />
    <glyph glyph-name="V.sc" horiz-adv-x="559" 
d="M70 545h50l107 -481l310 481h50l-354 -545h-41z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="993" 
d="M97 710h56l118 -650l213 346l-56 299h50l45 -258l155 258h50l-182 -299l66 -346l394 650h56l-439 -710h-46l-70 365l-227 -365h-46z" />
    <glyph glyph-name="W.alt" horiz-adv-x="995" 
d="M103 710h56l54 -637l335 637h55l71 -637l325 637h57l-367 -710h-54l-77 627l-336 -627h-54z" />
    <glyph glyph-name="W.sc" horiz-adv-x="850" 
d="M70 545h51l109 -488l165 252l-50 232h45l40 -196l125 196h45l-148 -232l58 -252l317 488h51l-356 -545h-42l-64 274l-180 -274h-42z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="993" 
d="M588 816l-31 16l76 85h57zM97 710h56l118 -650l213 346l-56 299h50l45 -258l155 258h50l-182 -299l66 -346l394 650h56l-439 -710h-46l-70 365l-227 -365h-46z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="993" 
d="M495 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM97 710h56l118 -650l213 346l-56 299h50l45 -258l155 258h50l-182 -299l66 -346l394 650h56l-439 -710h-46l-70 365l-227 -365h-46z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="993" 
d="M509 828l18 88h44l-19 -88h-43zM640 828l19 88h43l-18 -88h-44zM97 710h56l118 -650l213 346l-56 299h50l45 -258l155 258h50l-182 -299l66 -346l394 650h56l-439 -710h-46l-70 365l-227 -365h-46z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="993" 
d="M97 710h56l118 -650l213 346l-56 299h50l45 -258l155 258h50l-182 -299l66 -346l394 650h56l-439 -710h-46l-70 365l-227 -365h-46zM543 918h55l41 -85l-37 -16z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="598" 
d="M281 350l-187 360h58l166 -323l302 323h58l-340 -360l181 -350h-58l-159 313l-293 -313h-58z" />
    <glyph glyph-name="X.sc" horiz-adv-x="523" 
d="M228 269l-160 276h53l141 -242l243 242h53l-278 -276l156 -269h-52l-137 234l-235 -234h-53z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="602" 
d="M264 270l-171 440h57l150 -393l318 393h56l-359 -442l-58 -268h-50z" />
    <glyph glyph-name="Y.sc" horiz-adv-x="522" 
d="M213 205l-147 340h51l128 -298l256 298h52l-294 -341l-43 -204h-47z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="602" 
d="M264 270l-171 440h57l150 -393l318 393h56l-359 -442l-58 -268h-50zM387 816l-31 16l76 85h57z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="602" 
d="M264 270l-171 440h57l150 -393l318 393h56l-359 -442l-58 -268h-50zM296 836l109 81h37l75 -81l-33 -17l-68 63l-94 -63z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="602" 
d="M264 270l-171 440h57l150 -393l318 393h56l-359 -442l-58 -268h-50zM318 828l18 88h44l-19 -88h-43zM449 828l19 88h43l-18 -88h-44z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="602" 
d="M264 270l-171 440h57l150 -393l318 393h56l-359 -442l-58 -268h-50zM344 918h55l41 -85l-37 -16z" />
    <glyph glyph-name="Ytilde" unicode="&#x1ef8;" horiz-adv-x="602" 
d="M264 270l-171 440h57l150 -393l318 393h56l-359 -442l-58 -268h-50zM453 834q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16
t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="593" 
d="M-28 42l608 622h-470l10 46h531l-10 -42l-605 -622h471l-9 -46h-535z" />
    <glyph glyph-name="Z.sc" horiz-adv-x="520" 
d="M-21 38l493 464h-388l9 43h443l-8 -38l-489 -465h388l-9 -42h-447z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="593" 
d="M384 816l-31 16l76 85h57zM-28 42l608 622h-470l10 46h531l-10 -42l-605 -622h471l-9 -46h-535z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="593" 
d="M306 900l33 17l68 -63l94 63l26 -17l-109 -81h-37zM-28 42l608 622h-470l10 46h531l-10 -42l-605 -622h471l-9 -46h-535z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="593" 
d="M-28 42l608 622h-470l10 46h531l-10 -42l-605 -622h471l-9 -46h-535zM381 827l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5z" />
    <glyph glyph-name="a.alt" horiz-adv-x="590" 
d="M211 -10q-44 0 -78.5 17.5t-58 47.5t-36 69.5t-12.5 82.5q0 63 25.5 121.5t67 103t94.5 71t109 26.5q63 0 105 -34.5t59 -88.5l24 111h50l-110 -517h-50l24 110q-41 -54 -96 -87t-117 -33zM230 35q32 0 62 11t56.5 29.5t49 44t39.5 54.5l36 168q-5 30 -17.5 56t-31.5 45
t-44.5 30t-55.5 11q-49 0 -93.5 -24.5t-78 -63.5t-53.5 -87t-20 -95q0 -36 10 -68.5t29.5 -57t47.5 -39t64 -14.5z" />
    <glyph glyph-name="a.alt2" horiz-adv-x="512" 
d="M131 -10q-30 0 -54.5 10t-42 28t-27 42t-9.5 52q0 49 23 84t59 57t78.5 32t81.5 10q55 0 92.5 -10t54.5 -18l11 51q3 12 4 23t1 22q0 54 -30.5 85t-87.5 31q-37 0 -80.5 -16t-91.5 -46l-11 34q47 29 96 48.5t99 19.5q79 0 117.5 -41t38.5 -107q0 -27 -6 -57l-69 -324h-48
l17 84q-45 -49 -102.5 -71.5t-113.5 -22.5zM149 30q23 0 48.5 5t50.5 14.5t47.5 24.5t40.5 36q20 23 24 40l19 92q-32 14 -68 21t-72 7q-31 0 -64.5 -7t-62 -23.5t-47 -42.5t-18.5 -65q0 -47 29.5 -74.5t72.5 -27.5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM346 629l-31 16l76 85h57z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM354 666q20 0 40 18.5t28 45.5
h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM249 649l109 81h37l75 -81
l-33 -17l-68 63l-94 -63z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM268 640l18 88h44l-19 -88h-43z
M399 640l19 88h43l-18 -88h-44z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="885" 
d="M135 -10q-33 0 -59 10.5t-43 28.5t-26 42t-9 51q0 40 19 74t51.5 58t77 37.5t95.5 13.5q33 0 65.5 -5.5t59.5 -14.5q8 26 18 48t18 45q0 50 -30 80.5t-86 30.5q-38 0 -81.5 -16t-91.5 -46l-12 34q56 34 104.5 51t93.5 17q59 0 97 -28.5t47 -82.5q42 50 98.5 80.5
t118.5 30.5q44 0 80 -16t61.5 -44t39.5 -66.5t14 -82.5q0 -26 -4.5 -47t-9.5 -33h-436q-1 -9 -1.5 -18t-0.5 -18q0 -38 11.5 -70t32 -55t49 -36t63.5 -13q26 0 52 7.5t50 20.5t44 31.5t34 40.5l42 -12q-17 -28 -42.5 -51.5t-56 -40.5t-65 -26.5t-68.5 -9.5q-65 0 -110.5 32
t-67.5 85q-22 -32 -52.5 -54t-63 -36t-64 -20.5t-56.5 -6.5zM154 30q28 0 60.5 9t62 24.5t52 36.5t30.5 45q-6 22 -6 49q0 29 6 56q-27 10 -58 15t-60 5q-42 0 -77.5 -10t-61.5 -28t-40.5 -43.5t-14.5 -56.5q0 -20 6.5 -38.5t19.5 -32.5t33.5 -22.5t47.5 -8.5zM801 277
q2 11 3 23t1 23q0 34 -10 64t-29.5 52.5t-48.5 35.5t-66 13q-40 0 -77.5 -16t-69.5 -44t-56 -66.5t-36 -84.5h389z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="885" 
d="M489 629l-31 16l76 85h57zM135 -10q-33 0 -59 10.5t-43 28.5t-26 42t-9 51q0 40 19 74t51.5 58t77 37.5t95.5 13.5q33 0 65.5 -5.5t59.5 -14.5q8 26 18 48t18 45q0 50 -30 80.5t-86 30.5q-38 0 -81.5 -16t-91.5 -46l-12 34q56 34 104.5 51t93.5 17q59 0 97 -28.5
t47 -82.5q42 50 98.5 80.5t118.5 30.5q44 0 80 -16t61.5 -44t39.5 -66.5t14 -82.5q0 -26 -4.5 -47t-9.5 -33h-436q-1 -9 -1.5 -18t-0.5 -18q0 -38 11.5 -70t32 -55t49 -36t63.5 -13q26 0 52 7.5t50 20.5t44 31.5t34 40.5l42 -12q-17 -28 -42.5 -51.5t-56 -40.5t-65 -26.5
t-68.5 -9.5q-65 0 -110.5 32t-67.5 85q-22 -32 -52.5 -54t-63 -36t-64 -20.5t-56.5 -6.5zM154 30q28 0 60.5 9t62 24.5t52 36.5t30.5 45q-6 22 -6 49q0 29 6 56q-27 10 -58 15t-60 5q-42 0 -77.5 -10t-61.5 -28t-40.5 -43.5t-14.5 -56.5q0 -20 6.5 -38.5t19.5 -32.5
t33.5 -22.5t47.5 -8.5zM801 277q2 11 3 23t1 23q0 34 -10 64t-29.5 52.5t-48.5 35.5t-66 13q-40 0 -77.5 -16t-69.5 -44t-56 -66.5t-36 -84.5h389z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM299 730h55l41 -85l-37 -16z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM231 671l8 37h272l-8 -37h-272z
" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="670" 
d="M414 110q-50 -57 -108.5 -88.5t-119.5 -31.5q-37 0 -67.5 12t-52.5 34t-34.5 52.5t-12.5 66.5q0 77 51 135.5t143 113.5q-31 48 -44 80t-13 61q0 35 16.5 66.5t44.5 55.5t64.5 38t77.5 14q54 0 95.5 -27.5t63.5 -77.5l-35 -31q-17 44 -52 70t-80 26q-29 0 -56 -10.5
t-47.5 -28t-32.5 -40.5t-12 -48q0 -24 13.5 -56t48.5 -81l158 -226q30 48 52.5 105t34.5 122h145l-9 -42h-98q-16 -64 -41.5 -119.5t-58.5 -102.5l107 -152h-65zM196 33q52 0 102 29.5t93 81.5l-153 223l-3 3q-81 -49 -123.5 -98.5t-42.5 -110.5q0 -59 35.5 -93.5
t91.5 -34.5z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="591" 
d="M385 -169q-26 6 -38.5 21t-12.5 35q0 29 22 55.5t75 59.5q-19 9 -19 32q0 3 0.5 7.5t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47
l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-56 -34 -77.5 -56.5t-21.5 -45.5q0 -31 39 -37zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69
t31 -57.5t48 -39t61 -14.5z" />
    <glyph glyph-name="apostrophe" unicode="&#x2bc;" horiz-adv-x="537" 
d="M290 645l48 54l7 31h78l-103 -101z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM293 656q0 32 24 53t56 21
q24 0 38 -14t14 -36q0 -32 -23.5 -53.5t-55.5 -21.5q-25 0 -39 14t-14 37zM367 707q-18 0 -32.5 -13t-14.5 -34q0 -14 8.5 -23t22.5 -9q16 0 32 13.5t16 33.5q0 15 -9 23.5t-23 8.5z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM370 773l-31 16l76 85h57z
M293 656q0 32 24 53t56 21q24 0 38 -14t14 -36q0 -32 -23.5 -53.5t-55.5 -21.5q-25 0 -39 14t-14 37zM367 707q-18 0 -32.5 -13t-14.5 -34q0 -14 8.5 -23t22.5 -9q16 0 32 13.5t16 33.5q0 15 -9 23.5t-23 8.5z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="528" 
d="M53 303l198 407h43l197 -407h-46l-172 356l-176 -356h-44z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="473" 
d="M53 245q1 3 8 18.5t20 32.5t33 30.5t46 13.5q22 0 40.5 -9.5t35.5 -20.5t34.5 -20.5t37.5 -9.5q18 0 32 7.5t23 17t13.5 18t4.5 10.5h34q0 -2 -7 -15.5t-20.5 -30t-35 -29.5t-50.5 -13q-24 0 -43.5 9t-37 20.5t-33.5 20.5t-33 9t-29.5 -9t-21 -20t-13 -20t-4.5 -10h-34v0
z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="306" 
d="M138 553l53 60l-59 22l15 29l60 -24l13 72h34l-17 -72l71 24l1 -29l-68 -22l28 -60l-30 -18l-26 61l-54 -61z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="799" 
d="M553 15q32 0 60 27t48.5 69t32.5 92.5t12 97.5q0 57 -18 107t-51.5 86.5t-81 58t-106.5 21.5q-74 0 -145 -33.5t-126.5 -90t-89 -131.5t-33.5 -158q0 -60 18 -110.5t51.5 -88t80 -58.5t103.5 -21q39 0 76 9.5t75 28.5l6 -27q-42 -20 -85 -30t-82 -10q-60 0 -110 23
t-86 63.5t-56.5 96t-20.5 121.5q0 61 17.5 118t47.5 106.5t71.5 90t89 69.5t100 45t104.5 16q63 0 115 -23t89 -62.5t57 -93.5t20 -116q0 -49 -13 -106t-38 -105.5t-61.5 -80.5t-82.5 -32q-28 0 -43 14.5t-15 48.5q0 7 0.5 15.5t2.5 16.5q-34 -32 -78 -52.5t-87 -20.5
q-34 0 -61.5 13.5t-47 37.5t-30 56.5t-10.5 70.5q0 53 19.5 101.5t52.5 86t76.5 59.5t90.5 22q50 0 83.5 -24.5t49.5 -66.5l18 88h36l-75 -351q-4 -17 -5 -27.5t-1 -16.5q0 -40 36 -40zM335 40q24 0 49.5 8.5t48 23.5t40 35t26.5 43l33 169q-14 50 -47 75t-76 25
q-38 0 -73.5 -18.5t-64 -49.5t-45.5 -72t-17 -87q0 -33 9.5 -61t26 -48.5t40 -31.5t50.5 -11z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM410 647q-21 0 -36 6t-27 13.5
t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="586" 
d="M245 -10q-64 0 -106 33t-60 81l-22 -104h-45l155 730h50l-69 -323q47 54 101.5 88t115.5 34q45 0 78 -18.5t55.5 -48.5t33.5 -68.5t11 -78.5q0 -64 -25.5 -123t-67.5 -104t-95.5 -71.5t-109.5 -26.5zM245 35q47 0 91.5 23t78.5 61.5t54.5 87.5t20.5 101q0 34 -9.5 66
t-28.5 56.5t-46 39t-63 14.5q-30 0 -58.5 -11t-54.5 -29t-49.5 -42t-44.5 -51l-40 -192q3 -27 16 -50t33 -39.5t45.5 -25.5t54.5 -9z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="514" 
d="M164 710l262 -710h-57l-263 710h58z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="275" 
d="M120 -130v905h45v-905h-45z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="238" 
d="M222 697l-59 -277q-2 -10 -10.5 -28t-26.5 -32q8 -8 10.5 -19t2.5 -22q0 -5 -0.5 -10.5t-0.5 -8.5l-62 -288h53l-10 -43h-77q-6 0 -11 4t-5 15q0 7 1 11l62 290q1 4 1 8v8q0 14 -5.5 24t-18.5 12l8 40q10 0 18 6t13.5 14t9 16.5t4.5 13.5l59 279q4 20 13.5 25t14.5 5h77
l-9 -43h-52z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="238" 
d="M133 697l9 43h76q4 0 10.5 -3.5t6.5 -17.5q0 -6 -1 -9l-60 -279q-1 -2 -1 -6v-9q0 -12 5.5 -23.5t19.5 -11.5l-9 -40q-18 -2 -29.5 -18t-15.5 -34l-61 -290q-4 -17 -12.5 -23.5t-16.5 -6.5h-76l9 43h52l61 288q2 9 10.5 27.5t25.5 32.5q-7 9 -9.5 19.5t-2.5 20.5
q0 12 2 20l59 277h-52z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="238" 
d="M17 -40l166 780h98l-9 -43h-51l-148 -694h52l-9 -43h-99z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="237" 
d="M-23 -40l9 43h51l148 694h-51l9 43h98l-165 -780h-99z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="269" 
d="M162 280v-410h-45v410h45zM162 775v-410h-45v410h45z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="337" 
d="M260 360q0 -20 -8.5 -38.5t-23 -32.5t-33 -22t-37.5 -8q-29 0 -47 20.5t-18 50.5q0 20 9 38t23.5 32t32.5 22t37 8q30 0 47.5 -20.5t17.5 -49.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="522" 
d="M222 -10q-45 0 -81 17t-61.5 47t-39 70t-13.5 86q0 64 25 121.5t67.5 101.5t98 70t117.5 26q68 0 112.5 -32t58.5 -89l-53 -16q-12 43 -47 67.5t-83 24.5q-47 0 -91 -21.5t-78 -58t-55 -85t-21 -102.5q0 -38 11 -71.5t30.5 -58t47.5 -38.5t61 -14q26 0 53 8t51 21
t42.5 30.5t28.5 36.5l46 -14q-15 -27 -39.5 -50.5t-54.5 -40.5t-64.5 -26.5t-68.5 -9.5z" />
    <glyph glyph-name="c_t" horiz-adv-x="820" 
d="M220 -10q-47 0 -83 18.5t-60.5 49t-37.5 70.5t-13 83q0 58 24 115t65 102t96 73t118 28q47 0 78 -12t53 -33q-8 19 -14.5 45t-6.5 54q0 33 12 63.5t34 53t53.5 36t70.5 13.5q69 0 93.5 -26t24.5 -71q0 -27 -6 -60.5t-14 -71.5h115l-9 -41h-115l-81 -380q-1 -4 -1 -11
q0 -23 15 -35t36 -12q24 0 45 8t37 16l5 -40q-23 -11 -50 -21t-61 -10q-35 0 -58 18t-23 53q0 10 3 22l83 392h-69l8 41h70q7 33 13 63.5t6 56.5q0 35 -17.5 56.5t-66.5 21.5q-31 0 -54 -11.5t-38.5 -30.5t-23.5 -43.5t-8 -49.5q0 -24 5 -50.5t11.5 -50.5t12.5 -43.5
t8 -30.5l-52 -16q-14 48 -49.5 70t-85.5 22q-51 0 -94.5 -24.5t-76 -63t-51 -86t-18.5 -93.5q0 -37 11 -70t30.5 -58t47.5 -39.5t62 -14.5q21 0 46.5 6.5t50.5 19t45.5 30.5t31.5 40l46 -14q-17 -31 -43.5 -54.5t-57.5 -39.5t-64.5 -24.5t-63.5 -8.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="522" 
d="M334 629l-31 16l76 85h57zM222 -10q-45 0 -81 17t-61.5 47t-39 70t-13.5 86q0 64 25 121.5t67.5 101.5t98 70t117.5 26q68 0 112.5 -32t58.5 -89l-53 -16q-12 43 -47 67.5t-83 24.5q-47 0 -91 -21.5t-78 -58t-55 -85t-21 -102.5q0 -38 11 -71.5t30.5 -58t47.5 -38.5
t61 -14q26 0 53 8t51 21t42.5 30.5t28.5 36.5l46 -14q-15 -27 -39.5 -50.5t-54.5 -40.5t-64.5 -26.5t-68.5 -9.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="522" 
d="M261 713l33 17l68 -63l94 63l26 -17l-109 -81h-37zM222 -10q-45 0 -81 17t-61.5 47t-39 70t-13.5 86q0 64 25 121.5t67.5 101.5t98 70t117.5 26q68 0 112.5 -32t58.5 -89l-53 -16q-12 43 -47 67.5t-83 24.5q-47 0 -91 -21.5t-78 -58t-55 -85t-21 -102.5q0 -38 11 -71.5
t30.5 -58t47.5 -38.5t61 -14q26 0 53 8t51 21t42.5 30.5t28.5 36.5l46 -14q-15 -27 -39.5 -50.5t-54.5 -40.5t-64.5 -26.5t-68.5 -9.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="522" 
d="M120 -125q13 -7 34 -13t45 -6q65 0 65 45q0 16 -11.5 22t-29.5 6q-20 0 -43 -5t-36 -9l56 76q-40 4 -72 22.5t-54.5 48t-34.5 67.5t-12 81q0 64 25 121.5t67.5 101.5t98 70t117.5 26q68 0 112.5 -32t58.5 -89l-53 -16q-12 43 -47 67.5t-83 24.5q-47 0 -91 -21.5t-78 -58
t-55 -85t-21 -102.5q0 -38 11 -71.5t30.5 -58t47.5 -38.5t61 -14q26 0 53 8t51 21t42.5 30.5t28.5 36.5l46 -14q-15 -27 -38.5 -49.5t-52 -39t-61 -27t-65.5 -11.5l-34 -43q9 4 19.5 5t18.5 1q58 0 58 -46q0 -38 -27 -58t-78 -20q-26 0 -48.5 5.5t-39.5 13.5z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="522" 
d="M254 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM222 -10q-45 0 -81 17t-61.5 47t-39 70t-13.5 86q0 64 25 121.5t67.5 101.5t98 70t117.5 26q68 0 112.5 -32t58.5 -89l-53 -16q-12 43 -47 67.5t-83 24.5q-47 0 -91 -21.5t-78 -58t-55 -85t-21 -102.5q0 -38 11 -71.5
t30.5 -58t47.5 -38.5t61 -14q26 0 53 8t51 21t42.5 30.5t28.5 36.5l46 -14q-15 -27 -39.5 -50.5t-54.5 -40.5t-64.5 -26.5t-68.5 -9.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="522" 
d="M222 -10q-45 0 -81 17t-61.5 47t-39 70t-13.5 86q0 64 25 121.5t67.5 101.5t98 70t117.5 26q68 0 112.5 -32t58.5 -89l-53 -16q-12 43 -47 67.5t-83 24.5q-47 0 -91 -21.5t-78 -58t-55 -85t-21 -102.5q0 -38 11 -71.5t30.5 -58t47.5 -38.5t61 -14q26 0 53 8t51 21
t42.5 30.5t28.5 36.5l46 -14q-15 -27 -39.5 -50.5t-54.5 -40.5t-64.5 -26.5t-68.5 -9.5zM337 640l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="533" 
d="M175 -120l24 111q-40 3 -71.5 23t-53 50t-33 68t-11.5 78q0 56 22 111t60 99.5t90 73.5t111 34l25 117h30l-25 -117q72 -2 111.5 -34t53.5 -86l-52 -16q-14 45 -45.5 67t-76.5 24l-95 -447q20 0 44.5 7t48.5 19.5t44 30t30 38.5l46 -14q-17 -32 -44.5 -55.5t-58 -39.5
t-62.5 -24t-58 -8l-24 -110h-30zM81 216q0 -33 9 -63.5t25.5 -54.5t40 -40t53.5 -20l94 443q-48 -4 -88.5 -29.5t-70 -63.5t-46.5 -83.5t-17 -88.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="174" 
d="M92 432l19 85h43l-18 -85h-44zM1 0l18 85h43l-18 -85h-43z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="235" 
d="M15 -81l31 79h-18l18 87h51l-19 -87l-36 -79h-27z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="842" 
d="M431 -7q-78 0 -145 27.5t-115.5 76t-76 114.5t-27.5 144q0 77 27.5 143t76 114t115.5 75.5t145 27.5q79 0 146 -27.5t116 -75.5t77 -114t28 -143q0 -78 -28 -144t-77 -114.5t-116 -76t-146 -27.5zM432 21q71 0 132.5 24t106.5 68t71 104.5t26 134.5q0 72 -25.5 133.5
t-70.5 106.5t-106.5 70t-133.5 25t-133.5 -25t-106.5 -69.5t-70.5 -105.5t-25.5 -133q0 -71 25 -132t70 -105.5t106.5 -70t134.5 -25.5zM439 124q-43 0 -84 16t-73 46t-51.5 73t-19.5 96q0 41 13 82.5t40.5 75t69 54.5t98.5 21q62 0 112 -27t72 -80l-48 -16q-11 24 -28 39.5
t-35.5 24t-38 11.5t-34.5 3q-44 0 -76 -17t-53 -44t-31 -60.5t-10 -66.5q0 -45 16 -79.5t41.5 -58t56 -36t59.5 -12.5q20 0 41 5.5t40.5 16t35 26t24.5 34.5l48 -14q-6 -17 -21.5 -36.5t-39.5 -36.5t-55.5 -28.5t-68.5 -11.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="591" 
d="M214 -10q-44 0 -79 18t-59 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t93 72t109.5 27q32 0 58.5 -10t47.5 -26.5t36 -38.5t24 -47l68 323h51l-140 -658q-2 -10 -2 -12q0 -15 17 -15l-10 -45q-7 -1 -12 -1h-10q-34 5 -34 36q0 3 0.5 6.5t1.5 11t3.5 19.5t6.5 32
q-41 -52 -98 -83t-112 -31zM233 35q25 0 56.5 11t60.5 30.5t52.5 44.5t32.5 54l36 171q-4 28 -18 53.5t-34.5 44t-46 29.5t-52.5 11q-49 0 -93 -24t-77 -62.5t-52 -86t-19 -94.5q0 -37 11 -70t31 -58t48.5 -39.5t63.5 -14.5z" />
    <glyph glyph-name="d.alt" horiz-adv-x="587" 
d="M214 -10q-44 0 -79 18t-59 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q32 0 58 -10t46.5 -26.5t36 -38.5t26.5 -47l68 323h50l-155 -730h-50l22 101q-40 -50 -95 -80.5t-110 -30.5zM232 35q26 0 58 11t61.5 30.5t52.5 44.5t31 54l36 171
q-3 29 -17 54t-35 44t-47 29.5t-53 10.5q-49 0 -92.5 -24t-76.5 -62.5t-52 -86t-19 -94.5q0 -37 11 -70t31 -58t48 -39.5t63 -14.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="381" 
d="M75 -130l80 378h-125l10 46h126l102 481h48l-102 -481h124l-9 -46h-125l-80 -378h-49z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="385" 
d="M78 -130l64 305h-125l10 46h125l24 111h-125l9 46h125l85 397h48l-84 -397h124l-10 -46h-124l-24 -111h125l-10 -46h-125l-64 -305h-48z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="591" 
d="M628 645l76 85h57l-102 -101zM214 -10q-44 0 -79 18t-59 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t93 72t109.5 27q32 0 58.5 -10t47.5 -26.5t36 -38.5t24 -47l68 323h51l-140 -658q-2 -10 -2 -12q0 -15 17 -15l-10 -45q-7 -1 -12 -1h-10q-34 5 -34 36
q0 3 0.5 6.5t1.5 11t3.5 19.5t6.5 32q-41 -52 -98 -83t-112 -31zM233 35q25 0 56.5 11t60.5 30.5t52.5 44.5t32.5 54l36 171q-4 28 -18 53.5t-34.5 44t-46 29.5t-52.5 11q-49 0 -93 -24t-77 -62.5t-52 -86t-19 -94.5q0 -37 11 -70t31 -58t48.5 -39.5t63.5 -14.5z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="591" 
d="M430 655h106l16 75h51l-16 -75h74l-8 -36h-74l-116 -547q-2 -10 -2 -12q0 -15 17 -15l-10 -45q-7 -1 -12 -1h-10q-34 5 -34 36q0 3 0.5 6.5t1.5 11t3.5 19.5t6.5 32q-41 -52 -98 -83t-112 -31q-44 0 -79 18t-59 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t93 72
t109.5 27q32 0 58.5 -10t47.5 -26.5t36 -38.5t24 -47l44 212h-105zM233 35q25 0 56.5 11t60.5 30.5t52.5 44.5t32.5 54l36 171q-4 28 -18 53.5t-34.5 44t-46 29.5t-52.5 11q-49 0 -93 -24t-77 -62.5t-52 -86t-19 -94.5q0 -37 11 -70t31 -58t48.5 -39.5t63.5 -14.5z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="254" 
d="M181 568q-27 0 -43.5 17.5t-16.5 43.5q0 18 7.5 35t20 30.5t29.5 21.5t36 8q27 0 43.5 -18.5t16.5 -44.5q0 -19 -7.5 -36t-20.5 -29.5t-30 -20t-35 -7.5zM188 600q18 0 36 16t18 39q0 17 -9.5 26t-25.5 9q-20 0 -36.5 -15t-16.5 -37q0 -17 9 -27.5t25 -10.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="541" 
d="M243 394v81h44v-81h-44zM243 81v80h44v-80h-44zM64 257v43h403v-43h-403z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="592" 
d="M552 592q-48 76 -163 77l-61 -291q46 -11 84 -23.5t64 -31t40.5 -44t14.5 -62.5q0 -54 -21 -95.5t-58.5 -69.5t-89.5 -43t-113 -15l-20 -93h-30l20 94q-139 7 -220 105l36 41q29 -43 78.5 -70t117.5 -32l62 295q-44 10 -78.5 22t-58.5 28.5t-36.5 40t-12.5 56.5
q0 52 20 94t55 72t84 47t106 20l22 101h30l-22 -100q63 -2 107.5 -22.5t76.5 -60.5zM475 202q0 52 -40 79t-118 47l-61 -289q101 0 160 42t59 121zM162 496q0 -48 35 -71t107 -41l60 284q-54 -2 -92 -16t-62.5 -37t-36 -53.5t-11.5 -65.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="201" 
d="M122 520h50l-110 -520h-50z" />
    <glyph glyph-name="dotlessj" unicode="&#x237;" horiz-adv-x="201" 
d="M-116 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36.5 7.5t34.5 21t28 32t17 40.5l117 550h50l-117 -548q-7 -33 -24.5 -60t-41.5 -47t-51.5 -31t-53.5 -11z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="529" 
d="M224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5
q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5
t-44 5.5z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="529" 
d="M336 629l-31 16l76 85h57zM224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17q0 -37 9.5 -70t29 -57.5
t49 -39t68.5 -14.5q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5
t-38.5 17.5t-44 5.5z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="529" 
d="M224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5
q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5
t-44 5.5zM359 666q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="529" 
d="M260 713l33 17l68 -63l94 63l26 -17l-109 -81h-37zM224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17
q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5
t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5t-44 5.5z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="529" 
d="M257 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17
q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5
t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5t-44 5.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="529" 
d="M269 640l18 88h44l-19 -88h-43zM400 640l19 88h43l-18 -88h-44zM224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17
t-0.5 -17q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5
t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5t-44 5.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="529" 
d="M224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5
q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5
t-44 5.5zM337 640l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="529" 
d="M224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5
q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5
t-44 5.5zM309 730h55l41 -85l-37 -16z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="565" 
d="M214 -7q-44 0 -79 13.5t-59.5 36.5t-37.5 54.5t-13 66.5q0 42 17 77.5t43.5 63t59.5 47t65 29.5q-38 16 -63.5 49t-25.5 78q0 48 23 85.5t59.5 63.5t80.5 39.5t86 13.5q33 0 64.5 -10t56 -28.5t39 -46t14.5 -62.5q0 -34 -13.5 -62.5t-35 -51.5t-49.5 -40t-58 -28
q47 -18 77.5 -58.5t30.5 -96.5q0 -52 -24 -95t-64 -73.5t-90.5 -47.5t-103.5 -17zM227 37q36 0 75 13.5t70.5 38t52 59t20.5 76.5q0 32 -13 57t-34.5 42.5t-49 26.5t-55.5 9q-36 0 -74.5 -14t-70 -38.5t-52 -59t-20.5 -76.5q0 -32 13 -56.5t34.5 -42t48.5 -26.5t55 -9z
M303 400q30 0 63.5 10.5t61 30t46 48t18.5 65.5q0 26 -10.5 46.5t-29 34.5t-42.5 21.5t-51 7.5q-34 0 -67.5 -11t-60.5 -31t-43.5 -48t-16.5 -63q0 -27 11.5 -47.5t30.5 -34.5t42.5 -21.5t47.5 -7.5z" />
    <glyph glyph-name="eight.lnum" horiz-adv-x="565" 
d="M496 226q0 -52 -24 -95t-64 -73.5t-90.5 -47.5t-103.5 -17q-44 0 -79 13.5t-59.5 36.5t-37.5 54.5t-13 66.5q0 42 17 77.5t43.5 63t59.5 47t65 29.5q-38 16 -63.5 49t-25.5 78q0 48 23 85.5t59.5 63.5t80.5 39.5t86 13.5q33 0 64.5 -10t56 -28.5t39 -46t14.5 -62.5
q0 -34 -13.5 -62.5t-35 -51.5t-49.5 -40t-58 -28q47 -18 77.5 -58.5t30.5 -96.5zM445 224q0 32 -13 57t-34.5 42.5t-49 26.5t-55.5 9q-36 0 -74.5 -14t-70 -38.5t-52 -59t-20.5 -76.5q0 -32 13 -56.5t34.5 -42t48.5 -26.5t55 -9q36 0 75 13.5t70.5 38t52 59t20.5 76.5z
M171 511q0 -27 11.5 -47.5t30.5 -34.5t42.5 -21.5t47.5 -7.5q30 0 63.5 10.5t61 30t46 48t18.5 65.5q0 26 -10.5 46.5t-29 34.5t-42.5 21.5t-51 7.5q-34 0 -67.5 -11t-60.5 -31t-43.5 -48t-16.5 -63z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="377" 
d="M282 44q0 -29 -15 -52.5t-40 -40t-57 -26t-65 -9.5q-59 0 -92 28.5t-33 69.5q0 23 10 42t26 33.5t36.5 25t40.5 16.5q-25 9 -41.5 27t-16.5 44t14.5 46.5t37 35t50 22t54.5 7.5q22 0 43 -5.5t37 -16.5t25.5 -26.5t9.5 -35.5q0 -37 -28.5 -61.5t-66.5 -36.5q31 -11 51 -33
t20 -54zM246 43q0 17 -8.5 30.5t-22 22.5t-31.5 14t-36 5q-23 0 -46.5 -7t-42.5 -19.5t-31 -30t-12 -38.5q0 -17 8.5 -30t22.5 -22t32 -13.5t36 -4.5q22 0 45.5 6.5t42.5 18.5t31 29.5t12 38.5zM70 203q0 -14 7.5 -24.5t19.5 -18t27.5 -11.5t30.5 -4q19 0 39 5.5t37 15
t28 23.5t11 32q0 28 -26 43.5t-61 15.5q-21 0 -41.5 -5.5t-36 -15.5t-25.5 -24t-10 -32z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="377" 
d="M387 541q0 -29 -15 -52.5t-39.5 -40.5t-56.5 -26.5t-65 -9.5q-59 0 -92.5 29t-33.5 70q0 23 10 41.5t26 33.5t36.5 25.5t40.5 16.5q-24 9 -41 27t-17 44t14.5 46.5t37 35t50 22t54.5 7.5q22 0 43 -5.5t37 -16.5t25.5 -26.5t9.5 -35.5q0 -37 -28 -61.5t-66 -36.5
q31 -11 50.5 -33t19.5 -54zM352 540q0 17 -8.5 30.5t-22 22.5t-31.5 14t-36 5q-23 0 -47 -7t-43 -19.5t-31 -29.5t-12 -38q0 -17 8.5 -30t23 -22t32 -14t35.5 -5q22 0 45.5 6.5t43 18.5t31.5 29.5t12 38.5zM175 700q0 -14 7.5 -24.5t19.5 -18t27.5 -11.5t30.5 -4q19 0 39 5
t37 15t28 24t11 32q0 28 -26 43.5t-60 15.5q-21 0 -41.5 -5.5t-36.5 -15.5t-26 -24t-10 -32z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="409" 
d="M4 0l19 85h43l-18 -85h-44zM120 0l19 85h42l-17 -85h-44zM235 0l19 85h44l-19 -85h-44z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="529" 
d="M224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5
q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5
t-44 5.5zM228 671l8 37h272l-8 -37h-272z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="853" 
d="M54 249l10 46h722l-9 -46h-723z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="537" 
d="M54 249l10 46h406l-9 -46h-407z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="552" 
d="M240 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q17 0 35.5 7.5t35 21t28.5 32t17 40.5l68 320q6 28 8.5 51t2.5 42q0 100 -83 100q-31 0 -64 -12t-63.5 -34t-56.5 -51.5t-43 -63.5l-68 -322h-50l110 520h47l-27 -124q21 30 49 54.5t59.5 42t65 27
t66.5 9.5q60 0 86 -32t26 -93q0 -23 -3.5 -49t-9.5 -56l-69 -327q-7 -33 -25 -60t-42 -47t-51.5 -31t-53.5 -11z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="529" 
d="M172 -176q-26 6 -38.5 21t-12.5 35q0 28 20 53.5t68 57.5q-44 3 -77.5 21t-57 47.5t-35.5 68t-12 82.5q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17
q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-32 -54 -87 -88.5t-118 -41.5q-55 -33 -75.5 -55.5t-20.5 -45.5q0 -31 39 -37zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5
t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5t-44 5.5z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="464" 
d="M77 208v38h298v-38h-298zM77 306v38h298v-38h-298z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="575" 
d="M547 689l-96 -56q47 -63 65.5 -131.5t18.5 -131.5q0 -88 -28 -158t-73 -119.5t-102 -76t-115 -26.5q-43 0 -79 15.5t-61.5 42.5t-39.5 63.5t-14 78.5q0 57 25 108.5t66 90.5t93 62t106 23q67 0 113.5 -35t59.5 -92q0 8 0.5 15.5t0.5 15.5q0 59 -15 117.5t-62 117.5
l-124 -73l-12 25l119 69q-22 24 -49 48t-62 48h70q24 -18 44.5 -37t37.5 -39l101 59zM74 196q0 -34 11 -63.5t31.5 -51t48.5 -34t62 -12.5q45 0 88.5 20t77.5 53.5t54.5 77t20.5 89.5q0 33 -11.5 61.5t-31.5 49.5t-48.5 33t-62.5 12q-45 0 -88 -19t-77 -51.5t-54.5 -75
t-20.5 -89.5z" />
    <glyph glyph-name="etilde" unicode="&#x1ebd;" horiz-adv-x="529" 
d="M224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5
q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5
t-44 5.5zM413 647q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5
t-47.5 -11.5z" />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="239" 
d="M77 218l105 499h51l-106 -499h-50zM30 0l22 102h53l-24 -102h-51z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="239" 
d="M186 498l-106 -499h-50l106 499h50zM232 716l-21 -102h-53l24 102h50z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="311" 
d="M133 479h-69l9 41h70l3 17q19 96 68.5 149.5t115.5 53.5q28 0 52.5 -9t39.5 -24l-26 -36q-10 11 -28.5 18t-38.5 7q-48 0 -84 -40t-49 -118l-3 -18h139l-9 -41h-140l-146 -692h-50z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="525" 
d="M158 -149q-72 0 -121.5 38.5t-64.5 101.5l41 21q15 -52 56 -83.5t98 -31.5q43 0 83.5 17t72 47t50 70.5t18.5 87.5q0 65 -38 105t-101 40q-48 0 -95 -22.5t-83 -64.5h-48q4 10 17.5 43t31 77t37 92t35.5 88.5t27 67l11 26.5h323l-10 -47h-286l-111 -281q35 32 77.5 49.5
t87.5 17.5q39 0 72 -13.5t56 -37.5t36 -57.5t13 -73.5q0 -60 -24.5 -111t-64.5 -88t-91 -57.5t-105 -20.5z" />
    <glyph glyph-name="five.lnum" horiz-adv-x="536" 
d="M193 -10q-72 0 -121 38.5t-65 101.5l41 21q15 -52 56 -83.5t98 -31.5q43 0 83.5 17t72 47t50 70.5t18.5 87.5q0 65 -38 105.5t-101 40.5q-48 0 -95 -22.5t-83 -64.5h-48q3 7 11.5 28t20 50t25.5 63.5t28 69.5q33 82 73 182h324l-10 -47h-286l-111 -281q35 32 77.5 49.5
t87.5 17.5q39 0 72 -13.5t56 -37.5t36 -57.5t13 -73.5q0 -60 -24.5 -111t-64 -88t-91 -57.5t-105.5 -20.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="803" 
d="M244 586q53 0 86 -28.5t33 -74.5q0 -33 -15 -61t-40 -48t-57.5 -31t-67.5 -11q-46 0 -78.5 20.5t-43.5 55.5l28 18q11 -28 38 -44.5t65 -16.5q27 0 51 8.5t42.5 23.5t29.5 36t11 45q0 38 -28 58.5t-64 20.5q-30 0 -60 -13t-50 -35h-33q7 16 21.5 49t30 68.5t28.5 65
t16 38.5h208l-8 -35h-181l-63 -142q21 17 47 25t54 8zM57 21l673 692l23 -23l-673 -693zM549 -4q-58 0 -91.5 28.5t-33.5 69.5q0 23 10 42t26 33.5t36.5 25t40.5 16.5q-25 9 -41.5 27t-16.5 44t14.5 46.5t37 35t50 22t54.5 7.5q22 0 43 -5.5t37 -16.5t25.5 -26.5t9.5 -35.5
q0 -37 -28.5 -61.5t-66.5 -36.5q31 -11 51 -33t20 -54q0 -29 -15 -52.5t-40 -40t-57 -26t-65 -9.5zM559 30q22 0 45.5 6.5t42.5 18.5t31 29.5t12 38.5q0 17 -8.5 30.5t-22 22.5t-31.5 14t-36 5q-23 0 -46.5 -7t-42.5 -19.5t-31 -30t-12 -38.5q0 -17 8.5 -30t22.5 -22
t32 -13.5t36 -4.5zM599 225q19 0 39 5.5t37 15t28 23.5t11 32q0 28 -26 43.5t-61 15.5q-21 0 -41.5 -5.5t-36 -15.5t-25.5 -24t-10 -32q0 -14 7.5 -24.5t19.5 -18t27.5 -11.5t30.5 -4z" />
    <glyph glyph-name="five.numerator" horiz-adv-x="358" 
d="M240 586q53 0 86 -28.5t33 -74.5q0 -33 -15 -61t-40 -48t-57.5 -31t-67.5 -11q-46 0 -78.5 20.5t-43.5 55.5l28 18q11 -28 38 -44.5t65 -16.5q27 0 51 8.5t42.5 23.5t29.5 36t11 45q0 38 -28 58.5t-64 20.5q-30 0 -60 -13t-50 -35h-33q7 16 21.5 49t30 68.5t28.5 65
t16 38.5h208l-8 -35h-181l-63 -142q21 17 47 25t54 8z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="141" 
d="M-266 21l673 692l23 -23l-673 -693z" />
    <glyph glyph-name="eight.denominator" horiz-adv-x="377" 
d="M122 -4q-58 0 -91.5 28.5t-33.5 69.5q0 23 10 42t26 33.5t36.5 25t40.5 16.5q-25 9 -41.5 27t-16.5 44t14.5 46.5t37 35t50 22t54.5 7.5q22 0 43 -5.5t37 -16.5t25.5 -26.5t9.5 -35.5q0 -37 -28.5 -61.5t-66.5 -36.5q31 -11 51 -33t20 -54q0 -29 -15 -52.5t-40 -40
t-57 -26t-65 -9.5zM132 30q22 0 45.5 6.5t42.5 18.5t31 29.5t12 38.5q0 17 -8.5 30.5t-22 22.5t-31.5 14t-36 5q-23 0 -46.5 -7t-42.5 -19.5t-31 -30t-12 -38.5q0 -17 8.5 -30t22.5 -22t32 -13.5t36 -4.5zM172 225q19 0 39 5.5t37 15t28 23.5t11 32q0 28 -26 43.5t-61 15.5
q-21 0 -41.5 -5.5t-36 -15.5t-25.5 -24t-10 -32q0 -14 7.5 -24.5t19.5 -18t27.5 -11.5t30.5 -4z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="359" 
d="M257 666q53 0 86 -28.5t33 -74.5q0 -33 -15 -61t-40 -48t-57.5 -31t-67.5 -11q-47 0 -79 20.5t-43 55.5l28 18q11 -28 38 -44.5t65 -16.5q27 0 51 8.5t42.5 23.5t29.5 36t11 45q0 38 -28 58.5t-64 20.5q-30 0 -60 -13t-50 -35h-33q7 16 21.5 49t30 68.5t28.5 65t16 38.5
h208l-8 -35h-181l-63 -142q21 17 47 25t54 8z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="358" 
d="M152 169q53 0 85.5 -28.5t32.5 -74.5q0 -33 -15 -60.5t-40 -48t-57.5 -31.5t-66.5 -11q-47 0 -79.5 20.5t-43.5 54.5l29 19q11 -28 38 -44.5t65 -16.5q27 0 51 8.5t42.5 23.5t29.5 36t11 45q0 38 -28 58.5t-64 20.5q-30 0 -60 -13t-50 -35h-34q7 16 21.5 49t30 68.5
t28.5 65t16 38.5h209l-8 -35h-181l-63 -142q21 17 47 25t54 8z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="498" 
d="M33 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36.5 7.5t34.5 21t28 32t17 40.5l95 448h-69l8 41h71l16 78q20 96 68.5 149.5t114.5 53.5q29 0 53.5 -9t39.5 -24l-26 -36q-10 11 -28.5 18t-38.5 7q-49 0 -82.5 -40.5t-50.5 -117.5l-16 -79h139
l-9 -41h-140l-95 -447q-7 -33 -25 -60t-42 -46.5t-51 -30.5t-53 -11z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="517" 
d="M299 59h-326l9 44l442 468h34l-99 -466h84l-10 -46h-84l-39 -186h-50zM313 105l82 385l-357 -385h275z" />
    <glyph glyph-name="four.lnum" horiz-adv-x="539" 
d="M305 0l42 198h-342l10 44l456 468h35l-99 -466h84l-10 -46h-84l-42 -198h-50zM361 244l82 386l-372 -386h290z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="363" 
d="M163 -80l22 106h-218l7 32l283 255h24l-54 -253h54l-7 -34h-54l-23 -106h-34zM14 60h179l43 202z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="364" 
d="M269 417l22 105h-218l7 33l283 255h24l-54 -253h55l-8 -35h-54l-22 -105h-35zM120 557h179l43 202z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="586" 
d="M165 -223q-86 0 -130.5 32.5t-63.5 87.5l38 23q18 -53 63 -77.5t102 -24.5q37 0 73 11t65.5 32.5t51 53.5t30.5 75l24 114q-20 -25 -45 -45.5t-52 -35.5t-56 -23t-56 -8q-43 0 -77 18t-57.5 48t-36 69t-12.5 82q0 60 24 117.5t64.5 102.5t93.5 72.5t112 27.5
q64 0 104.5 -35.5t63.5 -86.5l24 113h45l-113 -530q-11 -53 -38.5 -93t-65.5 -66.5t-83 -40t-92 -13.5zM231 35q31 0 63.5 12.5t60.5 32.5t49 45t30 50l36 171q-5 30 -18.5 55.5t-33.5 43.5t-46 28.5t-54 10.5q-51 0 -94.5 -25t-76 -63.5t-51 -86t-18.5 -92.5q0 -37 11 -70
t31 -58t48 -39.5t63 -14.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="586" 
d="M165 -223q-86 0 -130.5 32.5t-63.5 87.5l38 23q18 -53 63 -77.5t102 -24.5q37 0 73 11t65.5 32.5t51 53.5t30.5 75l24 114q-20 -25 -45 -45.5t-52 -35.5t-56 -23t-56 -8q-43 0 -77 18t-57.5 48t-36 69t-12.5 82q0 60 24 117.5t64.5 102.5t93.5 72.5t112 27.5
q64 0 104.5 -35.5t63.5 -86.5l24 113h45l-113 -530q-11 -53 -38.5 -93t-65.5 -66.5t-83 -40t-92 -13.5zM231 35q31 0 63.5 12.5t60.5 32.5t49 45t30 50l36 171q-5 30 -18.5 55.5t-33.5 43.5t-46 28.5t-54 10.5q-51 0 -94.5 -25t-76 -63.5t-51 -86t-18.5 -92.5q0 -37 11 -70
t31 -58t48 -39.5t63 -14.5zM347 666q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="586" 
d="M244 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM165 -223q-86 0 -130.5 32.5t-63.5 87.5l38 23q18 -53 63 -77.5t102 -24.5q37 0 73 11t65.5 32.5t51 53.5t30.5 75l24 114q-20 -25 -45 -45.5t-52 -35.5t-56 -23t-56 -8q-43 0 -77 18t-57.5 48t-36 69t-12.5 82
q0 60 24 117.5t64.5 102.5t93.5 72.5t112 27.5q64 0 104.5 -35.5t63.5 -86.5l24 113h45l-113 -530q-11 -53 -38.5 -93t-65.5 -66.5t-83 -40t-92 -13.5zM231 35q31 0 63.5 12.5t60.5 32.5t49 45t30 50l36 171q-5 30 -18.5 55.5t-33.5 43.5t-46 28.5t-54 10.5q-51 0 -94.5 -25
t-76 -63.5t-51 -86t-18.5 -92.5q0 -37 11 -70t31 -58t48 -39.5t63 -14.5z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="586" 
d="M392 736l-29 -68h16l-15 -70h-48l14 70l35 68h27zM165 -223q-86 0 -130.5 32.5t-63.5 87.5l38 23q18 -53 63 -77.5t102 -24.5q37 0 73 11t65.5 32.5t51 53.5t30.5 75l24 114q-20 -25 -45 -45.5t-52 -35.5t-56 -23t-56 -8q-43 0 -77 18t-57.5 48t-36 69t-12.5 82
q0 60 24 117.5t64.5 102.5t93.5 72.5t112 27.5q64 0 104.5 -35.5t63.5 -86.5l24 113h45l-113 -530q-11 -53 -38.5 -93t-65.5 -66.5t-83 -40t-92 -13.5zM231 35q31 0 63.5 12.5t60.5 32.5t49 45t30 50l36 171q-5 30 -18.5 55.5t-33.5 43.5t-46 28.5t-54 10.5q-51 0 -94.5 -25
t-76 -63.5t-51 -86t-18.5 -92.5q0 -37 11 -70t31 -58t48 -39.5t63 -14.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="586" 
d="M165 -223q-86 0 -130.5 32.5t-63.5 87.5l38 23q18 -53 63 -77.5t102 -24.5q37 0 73 11t65.5 32.5t51 53.5t30.5 75l24 114q-20 -25 -45 -45.5t-52 -35.5t-56 -23t-56 -8q-43 0 -77 18t-57.5 48t-36 69t-12.5 82q0 60 24 117.5t64.5 102.5t93.5 72.5t112 27.5
q64 0 104.5 -35.5t63.5 -86.5l24 113h45l-113 -530q-11 -53 -38.5 -93t-65.5 -66.5t-83 -40t-92 -13.5zM231 35q31 0 63.5 12.5t60.5 32.5t49 45t30 50l36 171q-5 30 -18.5 55.5t-33.5 43.5t-46 28.5t-54 10.5q-51 0 -94.5 -25t-76 -63.5t-51 -86t-18.5 -92.5q0 -37 11 -70
t31 -58t48 -39.5t63 -14.5zM324 640l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="527" 
d="M170 40q57 1 102.5 15t78 39.5t50 62.5t17.5 85q0 62 -36.5 98.5t-101.5 36.5h-18l10 47h17q31 0 59 11.5t49 31t33.5 46t12.5 56.5q0 46 -29.5 73t-76.5 27q-32 0 -60 -12.5t-49.5 -34t-37 -51.5t-22.5 -65l-108 -506h-48l109 513q9 45 30.5 82t52 63.5t67.5 41t78 14.5
q66 0 105.5 -37t39.5 -95q0 -31 -12 -60.5t-32 -54t-45.5 -41.5t-51.5 -23q58 -14 87.5 -55.5t29.5 -100.5t-22 -104.5t-62.5 -78t-98 -50t-127.5 -20.5z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="543" 
d="M495 243l-419 -250v55l346 211l-346 211v54l419 -251v-30z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="532" 
d="M36 273l269 175l-9 -45l-221 -144l160 -143l-10 -45l-196 172zM230 273l270 175l-10 -45l-221 -144l160 -143l-9 -45l-196 172z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="532" 
d="M477 243l-268 -172l9 45l221 143l-160 144l9 45l196 -175zM283 243l-269 -172l10 45l221 143l-160 144l9 45l195 -175z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="337" 
d="M36 273l269 175l-9 -45l-221 -144l160 -143l-10 -45l-196 172z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="337" 
d="M283 243l-269 -172l10 45l221 143l-160 144l9 45l195 -175z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="552" 
d="M167 730h50l-71 -334q43 61 104 97t123 36q63 0 91 -35.5t28 -99.5q0 -21 -2.5 -45t-8.5 -50l-63 -299h-50l61 290q10 47 10 83q0 110 -91 110q-30 0 -61.5 -12t-61 -34t-54.5 -51.5t-41 -63.5l-68 -322h-50z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="552" 
d="M81 655h70l16 75h50l-16 -75h110l-8 -36h-110l-47 -223q43 61 104 97t123 36q63 0 91 -35.5t28 -99.5q0 -21 -2.5 -45t-8.5 -50l-63 -299h-50l61 290q10 46 10 83q0 110 -91 110q-30 0 -61.5 -12t-61 -34t-54.5 -51.5t-41 -63.5l-68 -322h-50l131 619h-69z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="552" 
d="M280 649l106 81h36l72 -81l-32 -17l-66 63l-91 -63zM167 730h50l-71 -334q43 61 104 97t123 36q63 0 91 -35.5t28 -99.5q0 -21 -2.5 -45t-8.5 -50l-63 -299h-50l61 290q10 47 10 83q0 110 -91 110q-30 0 -61.5 -12t-61 -34t-54.5 -51.5t-41 -63.5l-68 -322h-50z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="405" 
d="M54 249l10 46h274l-10 -46h-274z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="201" 
d="M122 520h50l-110 -520h-50zM167 730h50l-19 -92h-50z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="201" 
d="M170 629l-31 16l76 85h57zM122 520h50l-110 -520h-50z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="201" 
d="M122 520h50l-110 -520h-50zM169 666q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="201" 
d="M64 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM122 520h50l-110 -520h-50z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="201" 
d="M84 640l18 88h44l-19 -88h-43zM215 640l19 88h43l-18 -88h-44zM122 520h50l-110 -520h-50z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="201" 
d="M122 520h50l-110 -520h-50zM95 730h55l41 -85l-37 -16z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="410" 
d="M122 520h50l-110 -520h-50zM167 730h50l-19 -92h-50zM92 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36.5 7.5t34.5 21t28 32t17 40.5l117 550h50l-116 -548q-7 -33 -25 -60t-42 -47t-51.5 -31t-53.5 -11zM375 730h50l-20 -92h-50z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="209" 
d="M-109 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36.5 7.5t34.5 21t28 32t17 40.5l117 550h50l-116 -548q-7 -33 -25 -60t-42 -47t-51.5 -31t-53.5 -11zM174 730h50l-20 -92h-50z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="201" 
d="M122 520h50l-110 -520h-50zM35 671l8 37h272l-8 -37h-272z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="202" 
d="M123 520h50l-110 -520q-56 -34 -77.5 -56.5t-21.5 -45.5q0 -31 39 -37l-23 -30q-26 6 -38.5 21t-12.5 35q0 29 21.5 55t71.5 58h-9zM168 730h50l-19 -92h-50z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="201" 
d="M122 520h50l-110 -520h-50zM224 647q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5
t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="j.alt" horiz-adv-x="201" 
d="M122 517h50l-155 -730h-50zM165 719h50l-17 -81h-50z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="201" 
d="M-116 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36.5 7.5t34.5 21t28 32t17 40.5l117 550h50l-117 -548q-7 -33 -24.5 -60t-41.5 -47t-51.5 -31t-53.5 -11zM60 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="530" 
d="M167 730h50l-78 -369q57 78 121.5 124t135.5 46q57 0 86.5 -28t29.5 -77q0 -66 -52.5 -117t-148.5 -77l118 -232h-55l-124 255q105 22 159.5 66t54.5 98q0 35 -21 52.5t-59 17.5q-66 0 -134 -54.5t-128 -152.5l-60 -282h-50z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="530" 
d="M140 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM167 730h50l-78 -369q57 78 121.5 124t135.5 46q57 0 86.5 -28t29.5 -77q0 -66 -52.5 -117t-148.5 -77l118 -232h-55l-124 255q105 22 159.5 66t54.5 98q0 35 -21 52.5t-59 17.5q-66 0 -134 -54.5t-128 -152.5
l-60 -282h-50z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="499" 
d="M122 520h50l-63 -300l360 299h58l-251 -211l148 -308h-55l-133 278l-139 -113l-35 -165h-50z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="249" 
d="M103 -6q-34 0 -53 18.5t-19 50.5q0 11 3 25l136 642h50l-133 -627q-1 -5 -1.5 -9t-0.5 -9q0 -21 12.5 -33t36.5 -12q11 0 26 2.5t28 7.5v-40q-17 -7 -42.5 -11.5t-42.5 -4.5z" />
    <glyph glyph-name="l.alt" horiz-adv-x="215" 
d="M173 730h50l-155 -730h-50z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="249" 
d="M201 775l-31 16l76 85h57zM103 -6q-34 0 -53 18.5t-19 50.5q0 11 3 25l136 642h50l-133 -627q-1 -5 -1.5 -9t-0.5 -9q0 -21 12.5 -33t36.5 -12q11 0 26 2.5t28 7.5v-40q-17 -7 -42.5 -11.5t-42.5 -4.5z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="241" 
d="M256 645l76 85h57l-102 -101zM103 -6q-34 0 -53 18.5t-19 50.5q0 11 3 25l136 642h50l-133 -627q-1 -5 -1.5 -9t-0.5 -9q0 -21 12.5 -33t36.5 -12q11 0 26 2.5t28 7.5v-40q-17 -7 -42.5 -11.5t-42.5 -4.5z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="249" 
d="M35 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM103 -6q-34 0 -53 18.5t-19 50.5q0 11 3 25l136 642h50l-133 -627q-1 -5 -1.5 -9t-0.5 -9q0 -21 12.5 -33t36.5 -12q11 0 26 2.5t28 7.5v-40q-17 -7 -42.5 -11.5t-42.5 -4.5z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="277" 
d="M103 -6q-34 0 -53 18.5t-19 50.5q0 11 3 25l136 642h50l-133 -627q-1 -5 -1.5 -9t-0.5 -9q0 -21 12.5 -33t36.5 -12q11 0 26 2.5t28 7.5v-40q-17 -7 -42.5 -11.5t-42.5 -4.5zM211 259l22 101h43l-21 -101h-44z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="544" 
d="M31 273l418 251v-54l-346 -211l346 -211v-55l-418 250v30z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="554" 
d="M495 354l-48 -223h-44l38 177h-362l10 46h406z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="266" 
d="M18 324l99 74l70 332h50l-61 -287l102 77l14 -30l-126 -94l-60 -285q-4 -13 -4 -26q0 -21 12.5 -33t35.5 -12q11 0 26.5 2.5t29.5 7.5v-40q-18 -7 -43.5 -11.5t-42.5 -4.5q-35 0 -53.5 18t-18.5 50q0 12 3 26l56 263l-76 -56z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="874" 
d="M122 520h47l-27 -124q44 63 101 98t116 35q61 0 89.5 -37.5t28.5 -106.5q42 71 101.5 107.5t122.5 36.5q60 0 87 -33.5t27 -95.5q0 -44 -12 -101l-64 -299h-50l62 290q10 49 10 89q0 104 -86 104q-30 0 -59.5 -12t-56.5 -33t-50.5 -51t-40.5 -66l-67 -321h-50l61 290
q6 27 8.5 49.5t2.5 41.5q0 102 -85 102q-30 0 -60 -11.5t-57 -33t-50 -51t-41 -65.5l-68 -322h-50z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="549" 
d="M70 265v42h403v-42h-403z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="591" 
d="M131 520h50l-62 -293q-11 -50 -11 -85q0 -107 94 -107q30 0 62 11.5t61.5 32t55 49.5t43.5 64l70 328h51l-96 -450q-1 -3 -1 -10q0 -15 17 -15l-11 -45q-35 0 -43.5 11.5t-8.5 28.5q0 2 1.5 12.5t3.5 23.5t4.5 26t4.5 21q-20 -29 -46.5 -53.5t-56 -42t-61 -27.5
t-62.5 -10q-48 0 -73.5 24.5t-31.5 67.5l-60 -294h-50z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="465" 
d="M384 145l-33 -32l-126 126l-127 -126l-32 33l126 125l-126 127l33 32l126 -126l126 127l32 -33l-126 -127z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="552" 
d="M122 520h47l-27 -124q21 30 49 54.5t59.5 42t65 27t66.5 9.5q60 0 86 -32t26 -93q0 -23 -3.5 -49t-9.5 -56l-63 -299h-50l61 290q6 28 8.5 51t2.5 42q0 100 -83 100q-31 0 -64 -12t-63.5 -34t-56.5 -51.5t-43 -63.5l-68 -322h-50z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="552" 
d="M362 629l-31 16l76 85h57zM122 520h47l-27 -124q21 30 49 54.5t59.5 42t65 27t66.5 9.5q60 0 86 -32t26 -93q0 -23 -3.5 -49t-9.5 -56l-63 -299h-50l61 290q6 28 8.5 51t2.5 42q0 100 -83 100q-31 0 -64 -12t-63.5 -34t-56.5 -51.5t-43 -63.5l-68 -322h-50z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="552" 
d="M-31 645l48 54l7 31h78l-103 -101zM122 520h47l-27 -124q21 30 49 54.5t59.5 42t65 27t66.5 9.5q60 0 86 -32t26 -93q0 -23 -3.5 -49t-9.5 -56l-63 -299h-50l61 290q6 28 8.5 51t2.5 42q0 100 -83 100q-31 0 -64 -12t-63.5 -34t-56.5 -51.5t-43 -63.5l-68 -322h-50z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="552" 
d="M252 713l33 17l68 -63l94 63l26 -17l-109 -81h-37zM122 520h47l-27 -124q21 30 49 54.5t59.5 42t65 27t66.5 9.5q60 0 86 -32t26 -93q0 -23 -3.5 -49t-9.5 -56l-63 -299h-50l61 290q6 28 8.5 51t2.5 42q0 100 -83 100q-31 0 -64 -12t-63.5 -34t-56.5 -51.5t-43 -63.5
l-68 -322h-50z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="552" 
d="M153 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM122 520h47l-27 -124q21 30 49 54.5t59.5 42t65 27t66.5 9.5q60 0 86 -32t26 -93q0 -23 -3.5 -49t-9.5 -56l-63 -299h-50l61 290q6 28 8.5 51t2.5 42q0 100 -83 100q-31 0 -64 -12t-63.5 -34t-56.5 -51.5t-43 -63.5
l-68 -322h-50z" />
    <glyph glyph-name="ndotaccent" unicode="&#x1e45;" horiz-adv-x="552" 
d="M324 640l19 90h46l-19 -90h-46zM122 520h47l-27 -124q21 30 49 54.5t59.5 42t65 27t66.5 9.5q60 0 86 -32t26 -93q0 -23 -3.5 -49t-9.5 -56l-63 -299h-50l61 290q6 28 8.5 51t2.5 42q0 100 -83 100q-31 0 -64 -12t-63.5 -34t-56.5 -51.5t-43 -63.5l-68 -322h-50z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="569" 
d="M173 -155q-68 0 -113 36.5t-57 101.5l40 27q8 -54 45.5 -87t94.5 -33q48 0 91 24t79 68.5t63 107t42 139.5q-35 -67 -99 -105.5t-136 -38.5q-42 0 -76 15.5t-58 42t-37.5 62.5t-13.5 78q0 58 24.5 110t65.5 91.5t94 62.5t110 23q87 0 138 -58t51 -160q0 -99 -25.5 -190.5
t-71 -162t-109.5 -112.5t-142 -42zM238 128q44 0 86 19t75 51.5t53 74.5t20 88q0 34 -11 64t-31 52t-48 35t-61 13q-44 0 -86 -19t-75 -51t-53 -74t-20 -89q0 -34 11 -64t31 -52t48 -35t61 -13z" />
    <glyph glyph-name="nine.lnum" horiz-adv-x="585" 
d="M75 428q0 58 25 110t66 91.5t94 62.5t110 23q88 0 138.5 -58t50.5 -160q0 -98 -25.5 -189.5t-71 -162t-109.5 -113t-142 -42.5q-67 0 -112.5 37t-57.5 101l40 27q8 -54 46 -86.5t94 -32.5q44 0 85 22.5t76 66t62.5 106t45.5 143.5q-18 -32 -43 -58.5t-55 -45.5
t-63.5 -29.5t-67.5 -10.5q-42 0 -76 15.5t-58.5 42.5t-38 63t-13.5 77zM360 671q-44 0 -86 -19t-75 -51t-53 -74.5t-20 -88.5q0 -34 11 -64t30.5 -52.5t47.5 -35.5t61 -13q43 0 85.5 19t75.5 51.5t53.5 74.5t20.5 88q0 34 -11 64t-31 52.5t-48 35.5t-61 13z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="389" 
d="M6 160q0 32 15.5 60.5t41 49.5t59 33.5t69.5 12.5q57 0 91 -34t34 -94q0 -57 -15.5 -106.5t-44 -86.5t-67.5 -58.5t-87 -21.5q-43 0 -73.5 19t-39.5 54l28 22q6 -29 31 -45.5t61 -16.5q56 0 100 45t62 126q-22 -32 -60.5 -51.5t-80.5 -19.5q-56 0 -90 32t-34 80zM183 284
q-27 0 -52.5 -9.5t-45 -25.5t-31.5 -37.5t-12 -44.5q0 -37 27.5 -62.5t70.5 -25.5q27 0 52.5 9.5t45.5 25.5t32 37.5t12 44.5q0 36 -28 62t-71 26z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="388" 
d="M111 654q0 32 15 60.5t40.5 49.5t59 33.5t69.5 12.5q57 0 91 -34t34 -94q0 -57 -15.5 -106.5t-44 -87t-67.5 -59t-87 -21.5q-43 0 -73 19.5t-39 54.5l27 21q7 -28 32 -45t61 -17q55 0 99 45t62 127q-21 -32 -60 -52t-80 -20q-56 0 -90 32.5t-34 80.5zM287 777
q-27 0 -52 -9.5t-45 -25.5t-32 -37t-12 -44q0 -37 27.5 -62.5t71.5 -25.5q27 0 52 9.5t45 25.5t32 37t12 44q0 36 -27.5 62t-71.5 26z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="552" 
d="M122 520h47l-27 -124q21 30 49 54.5t59.5 42t65 27t66.5 9.5q60 0 86 -32t26 -93q0 -23 -3.5 -49t-9.5 -56l-63 -299h-50l61 290q6 28 8.5 51t2.5 42q0 100 -83 100q-31 0 -64 -12t-63.5 -34t-56.5 -51.5t-43 -63.5l-68 -322h-50zM401 647q-21 0 -36 6t-27 13.5t-23 14
t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="641" 
d="M638 466h-119l-93 -213h127l-8 -41h-138l-93 -212h-44l95 212h-196l-93 -212h-44l94 212h-107l9 41h118l92 213h-125l9 39h135l90 205h44l-92 -205h196l90 205h44l-92 -205h110zM383 253l93 213h-195l-93 -213h195z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="569" 
d="M218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71
t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="569" 
d="M350 629l-31 16l76 85h57zM218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35q45 0 89.5 22t79 59t56 85.5
t21.5 101.5q0 38 -11 71t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="569" 
d="M218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71
t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5zM356 666q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14
q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="569" 
d="M249 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35
q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="569" 
d="M267 640l18 88h44l-19 -88h-43zM398 640l19 88h43l-18 -88h-44zM218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5z
M227 35q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="991" 
d="M217 -10q-43 0 -78.5 16.5t-60.5 45.5t-38.5 68.5t-13.5 84.5q0 66 26 124.5t69 103t98.5 70.5t114.5 26q75 0 122.5 -47.5t54.5 -128.5q42 83 109.5 129.5t146.5 46.5q90 0 139.5 -60t49.5 -161q0 -33 -3.5 -47.5t-5.5 -20.5h-431q-3 -18 -3 -35q0 -38 11.5 -70
t32.5 -55t50.5 -36.5t65.5 -13.5q27 0 54 7.5t51 20.5t44 31.5t33 40.5l41 -12q-18 -29 -44.5 -52.5t-57.5 -40t-65 -26t-67 -9.5q-37 0 -69 12.5t-57 36t-41.5 55.5t-22.5 71q-23 -40 -52 -72.5t-62 -55t-69 -35t-72 -12.5zM227 35q46 0 90.5 21.5t79 58.5t55.5 86t21 103
q0 38 -10.5 71t-30 57t-47 38t-61.5 14q-46 0 -90 -22t-78.5 -59.5t-56 -87t-21.5 -104.5q0 -38 10.5 -70t30 -55.5t47 -37t61.5 -13.5zM906 277q3 21 3 41q0 38 -11 69t-30.5 53.5t-48 35t-62.5 12.5q-39 0 -76 -16t-68.5 -44t-55 -66.5t-35.5 -84.5h384z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="569" 
d="M218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71
t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5zM304 730h55l41 -85l-37 -16z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="569" 
d="M277 629l-26 16l75 85h51zM389 629l-26 16l74 85h52zM218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35
q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="569" 
d="M225 671l8 37h272l-8 -37h-272zM218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35q45 0 89.5 22t79 59t56 85.5
t21.5 101.5q0 38 -11 71t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="389" 
d="M12 46h135l98 462q-11 -12 -32 -26t-46 -26.5t-50.5 -21t-44.5 -8.5l10 50q24 0 53.5 13.5t56 30.5t45.5 32.5t21 18.5h51l-111 -525h121l-10 -46h-307z" />
    <glyph glyph-name="one.lnum" horiz-adv-x="375" 
d="M9 46h127l130 614q-10 -12 -30.5 -26.5t-44.5 -28t-48.5 -22.5t-43.5 -9l10 47q25 0 54.5 14.5t54.5 32.5t42.5 33.5t18.5 17.5h51l-143 -673h120l-10 -46h-298z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="794" 
d="M38 21l673 692l23 -23l-673 -693zM256 367l-7 -35h-185l7 35h78l68 322q-6 -6 -18.5 -14.5t-27 -16t-29 -12.5t-26.5 -5l7 35q15 0 33 8t33 18t25.5 19t11.5 10h36l-78 -364h72zM542 -4q-58 0 -91.5 28.5t-33.5 69.5q0 23 10 42t26 33.5t36.5 25t40.5 16.5
q-25 9 -41.5 27t-16.5 44t14.5 46.5t37 35t50 22t54.5 7.5q22 0 43 -5.5t37 -16.5t25.5 -26.5t9.5 -35.5q0 -37 -28.5 -61.5t-66.5 -36.5q31 -11 51 -33t20 -54q0 -29 -15 -52.5t-40 -40t-57 -26t-65 -9.5zM552 30q22 0 45.5 6.5t42.5 18.5t31 29.5t12 38.5q0 17 -8.5 30.5
t-22 22.5t-31.5 14t-36 5q-23 0 -46.5 -7t-42.5 -19.5t-31 -30t-12 -38.5q0 -17 8.5 -30t22.5 -22t32 -13.5t36 -4.5zM592 225q19 0 39 5.5t37 15t28 23.5t11 32q0 28 -26 43.5t-61 15.5q-21 0 -41.5 -5.5t-36 -15.5t-25.5 -24t-10 -32q0 -14 7.5 -24.5t19.5 -18t27.5 -11.5
t30.5 -4z" />
    <glyph glyph-name="one.numerator" horiz-adv-x="253" 
d="M247 367l-7 -35h-185l7 35h78l68 322q-6 -6 -18.5 -14.5t-27 -16t-29 -12.5t-26.5 -5l7 35q15 0 33 8t33 18t25.5 19t11.5 10h36l-78 -364h72z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="253" 
d="M264 447l-7 -35h-185l7 35h78l68 322q-6 -6 -18.5 -14.5t-27 -16t-29 -12.5t-26.5 -5l7 35q15 0 33 8t33 18t25.5 19t11.5 10h36l-78 -364h72z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="795" 
d="M407 0q16 76 54.5 116.5t101.5 63.5q25 9 52 18t49 21.5t36 31t14 46.5t-19.5 47.5t-62.5 19.5q-24 0 -44.5 -6t-37 -14.5t-29 -18.5t-21.5 -19l-19 25q4 4 16.5 15t33.5 22.5t49 20.5t63 9q57 0 84 -27t27 -66q0 -33 -13.5 -56t-36 -39t-51.5 -27.5t-61 -22.5
q-35 -12 -58 -28t-37 -33t-21 -34t-10 -31h234l-8 -34h-285zM25 21l673 692l23 -23l-673 -693zM258 367l-7 -35h-185l7 35h78l68 322q-6 -6 -18.5 -14.5t-27 -16t-29 -12.5t-26.5 -5l7 35q15 0 33 8t33 18t25.5 19t11.5 10h36l-78 -364h72z" />
    <glyph glyph-name="two.denominator" horiz-adv-x="368" 
d="M-26 0q16 76 55 116.5t103 63.5q25 9 52.5 18t50 21.5t37 31t14.5 46.5t-20 47.5t-64 19.5q-25 0 -45.5 -6t-37.5 -14.5t-29.5 -18.5t-21.5 -19l-19 25q4 4 17 15t34 22.5t49.5 20.5t63.5 9q59 0 86 -27t27 -66q0 -33 -13.5 -56t-36 -39t-52.5 -27.5t-62 -22.5
q-36 -12 -59.5 -28t-37.5 -33t-21 -34t-10 -31h237l-8 -34h-289v0z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="368" 
d="M-43 -80q16 76 55 116.5t103 63.5q25 9 52.5 18t50 21.5t37 31t14.5 46.5t-20 47.5t-64 19.5q-25 0 -45.5 -6t-37.5 -14.5t-29.5 -18.5t-21.5 -19l-19 25q4 4 17 15t34 22.5t49.5 20.5t63.5 9q59 0 86 -27t27 -66q0 -33 -14 -56t-36.5 -39.5t-52.5 -28t-61 -21.5
q-36 -12 -59.5 -28t-37.5 -33t-21 -34t-10 -31h237l-8 -34h-289z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="263" 
d="M170 -46l-7 -34h-196l7 34h83l68 323q-6 -7 -19 -15t-28 -15.5t-31 -12.5t-28 -5l7 35q16 0 34.5 8t34.5 18t27 19t12 10h36l-77 -365h77z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="758" 
d="M591 106h-218l7 32l283 255h24l-54 -253h54l-7 -34h-54l-23 -106h-34zM599 140l43 202l-222 -202h179zM53 21l673 692l23 -23l-673 -693zM254 367l-7 -35h-185l7 35h78l68 322q-6 -6 -18.5 -14.5t-27 -16t-29 -12.5t-26.5 -5l7 35q15 0 33 8t33 18t25.5 19t11.5 10h36
l-78 -364h72z" />
    <glyph glyph-name="four.denominator" horiz-adv-x="363" 
d="M202 106h-218l7 32l283 255h24l-54 -253h54l-7 -34h-54l-23 -106h-34zM210 140l43 202l-222 -202h179z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="795" 
d="M28 21l673 692l23 -23l-673 -693zM258 367l-7 -35h-185l7 35h78l68 322q-6 -6 -18.5 -14.5t-27 -16t-29 -12.5t-26.5 -5l7 35q15 0 33 8t33 18t25.5 19t11.5 10h36l-78 -364h72zM543 -5q-24 0 -46.5 5.5t-40 17t-28.5 29t-11 40.5l27 20q0 -29 27.5 -53t78.5 -24
q26 0 50 6.5t42 18t28.5 27t10.5 34.5q0 32 -33 50.5t-92 18.5h-22l7 32h21q68 0 107 20.5t39 61.5q0 30 -26 48.5t-65 18.5q-40 0 -72.5 -16t-51.5 -42l-18 26q22 29 62.5 46.5t85.5 17.5q56 0 89.5 -26.5t33.5 -66.5q0 -80 -107 -103q37 -6 57.5 -29t20.5 -55
q0 -28 -14.5 -51t-39 -39t-56 -24.5t-64.5 -8.5z" />
    <glyph glyph-name="three.denominator" horiz-adv-x="371" 
d="M116 -5q-24 0 -46.5 5.5t-40 17t-28.5 29t-11 40.5l27 20q0 -29 27.5 -53t78.5 -24q26 0 50 6.5t42 18t28.5 27t10.5 34.5q0 32 -33 50.5t-92 18.5h-22l7 32h21q68 0 107 20.5t39 61.5q0 30 -26 48.5t-65 18.5q-40 0 -72.5 -16t-51.5 -42l-18 26q22 29 62.5 46.5
t85.5 17.5q56 0 89.5 -26.5t33.5 -66.5q0 -80 -107 -103q37 -6 57.5 -29t20.5 -55q0 -28 -14.5 -51t-39 -39t-56 -24.5t-64.5 -8.5z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="371" 
d="M99 -85q-24 0 -46.5 5t-40 15.5t-28.5 28t-11 43.5l27 20q0 -32 27.5 -54.5t78.5 -22.5q26 0 50 6.5t42 18t28.5 27t10.5 34.5q0 32 -33.5 50.5t-91.5 18.5h-22l7 32h21q68 0 107 20.5t39 61.5q0 30 -26 48.5t-65 18.5q-40 0 -72.5 -16t-51.5 -42l-18 26q22 29 62.5 46.5
t85.5 17.5q56 0 89.5 -26.5t33.5 -66.5q0 -80 -107 -103q37 -6 57.5 -29t20.5 -55q0 -28 -14.5 -51t-39 -39t-56 -24.5t-64.5 -8.5z" />
    <glyph glyph-name="oogonek" unicode="&#x1eb;" horiz-adv-x="569" 
d="M169 -176q-26 6 -38.5 21t-12.5 35q0 28 20 53t68 57q-42 2 -75.5 20t-56.5 47.5t-35.5 68t-12.5 81.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -58 -22.5 -112.5t-60 -97.5t-87 -72t-104.5 -36q-28 -17 -46.5 -30
t-30 -25t-16 -23t-4.5 -23q0 -31 39 -37zM227 35q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="410" 
d="M168 348q-42 0 -67 26t-25 64q0 27 13 50t36 39.5t53 26t65 9.5q27 0 52 -4.5t45 -13.5l6 31q3 13 3 26q0 33 -20 52.5t-57 19.5q-50 0 -118 -41l-10 31q76 46 139 46q54 0 84 -26t30 -74q0 -21 -4 -40l-33 -153q-1 -2 -1 -7q0 -11 13 -13l-9 -43q-29 0 -37.5 10t-8.5 31
v8.5t1 9.5q-30 -32 -69 -48.5t-81 -16.5zM188 386q21 0 43 6t41 15.5t32.5 23t16.5 28.5l12 55q-20 10 -42.5 13.5t-46.5 3.5q-52 0 -86.5 -23t-34.5 -61q0 -26 18 -43.5t47 -17.5z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="431" 
d="M223 348q-32 0 -57 11.5t-42.5 32t-26.5 47t-9 56.5q0 42 17.5 81t46.5 68.5t67.5 47.5t80.5 18q31 0 56.5 -11.5t43.5 -31.5t27.5 -47t9.5 -58q0 -41 -17.5 -79.5t-47 -68.5t-68 -48t-81.5 -18zM231 385q30 0 59 14t52 37.5t37 54.5t14 65q0 49 -28 82t-73 33
q-30 0 -58.5 -13.5t-51.5 -37t-37 -54.5t-14 -66q0 -49 27.5 -82t72.5 -33z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="569" 
d="M81 49q-27 29 -41 70t-14 88q0 63 26 121.5t68.5 103t98 71t114.5 26.5q68 0 115 -41l30 33h38l-48 -52q27 -30 41.5 -71t14.5 -88q0 -62 -25.5 -120t-67.5 -102.5t-97 -71t-115 -26.5q-69 0 -117 39l-28 -30h-38zM227 35q45 0 89.5 22t79 59t56 85.5t21.5 101.5
q0 37 -10.5 69.5t-29.5 56.5l-298 -360q19 -16 42 -25t50 -9zM78 213q0 -76 40 -125l296 361q-18 17 -41 26t-50 9q-45 0 -89 -22t-78.5 -59.5t-56 -86.5t-21.5 -103z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="569" 
d="M81 49q-27 29 -41 70t-14 88q0 63 26 121.5t68.5 103t98 71t114.5 26.5q68 0 115 -41l30 33h38l-48 -52q27 -30 41.5 -71t14.5 -88q0 -62 -25.5 -120t-67.5 -102.5t-97 -71t-115 -26.5q-69 0 -117 39l-28 -30h-38zM227 35q45 0 89.5 22t79 59t56 85.5t21.5 101.5
q0 37 -10.5 69.5t-29.5 56.5l-298 -360q19 -16 42 -25t50 -9zM78 213q0 -76 40 -125l296 361q-18 17 -41 26t-50 9q-45 0 -89 -22t-78.5 -59.5t-56 -86.5t-21.5 -103zM350 629l-31 16l76 85h57z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="569" 
d="M218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71
t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5zM411 647q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5
q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="587" 
d="M123 520h45l-22 -104q41 50 97.5 81.5t113.5 31.5q44 0 78.5 -18t58 -48.5t36 -70.5t12.5 -83q0 -60 -23 -117.5t-62.5 -102.5t-92.5 -72t-112 -27q-63 0 -105 35.5t-61 86.5l-69 -325h-50zM250 35q49 0 92.5 24t76.5 62t52 85.5t19 95.5q0 37 -11 70t-31 58t-48 39.5
t-63 14.5q-26 0 -57 -11.5t-60 -31t-52.5 -44.5t-32.5 -53l-37 -171q6 -29 19.5 -54t33 -44t45 -29.5t54.5 -10.5z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="565" 
d="M64 424q0 64 23 117t65.5 90.5t102.5 58t134 20.5h216l-9 -41h-66l-160 -749h-44l68 317h-76l-68 -317h-44l67 317q-100 2 -154.5 51.5t-54.5 135.5zM109 434q0 -39 13.5 -67.5t36.5 -47t55 -28.5t68 -12l83 390q-61 0 -108.5 -18t-80.5 -49t-50 -74t-17 -94zM485 669
h-75l-83 -390h75z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="250" 
d="M46 258q0 67 17.5 127.5t48 118.5t71.5 114.5t87 115.5l34 -20q-32 -38 -68.5 -90t-68 -111.5t-52.5 -123t-21 -123.5q0 -31 3.5 -66.5t10.5 -71.5t17 -71.5t24 -67.5l-41 -22q-29 68 -45.5 145t-16.5 146z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="250" 
d="M224 433q0 -61 -16 -119.5t-45.5 -116t-70.5 -114.5t-91 -116l-33 22q46 54 84 108t65.5 109t42.5 109.5t15 108.5q0 38 -4.5 79t-12 80t-17.5 73t-20 58l43 20q26 -70 43 -148t17 -153z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="672" 
d="M211 437q-51 0 -81.5 33.5t-30.5 82.5q0 34 14.5 64t38.5 53t55 36.5t64 13.5q51 0 81.5 -33.5t30.5 -83.5q0 -33 -14.5 -63.5t-38.5 -53t-55 -36t-64 -13.5zM218 469q24 0 46.5 11t40 29t28 41.5t10.5 48.5q0 38 -22.5 63.5t-56.5 25.5q-24 0 -46.5 -11t-40 -29
t-28 -41.5t-10.5 -48.5q0 -38 22 -63.5t57 -25.5zM424 -10q-50 0 -81 33.5t-31 83.5q0 33 15 63.5t39 53t55 36t63 13.5q50 0 80.5 -33.5t30.5 -82.5q0 -34 -14.5 -64t-38.5 -53t-54.5 -36.5t-63.5 -13.5zM430 22q24 0 46.5 11t40 29t28.5 41t11 48q0 38 -22.5 64t-56.5 26
q-24 0 -46.5 -11t-40 -29t-28.5 -41.5t-11 -48.5q0 -38 22 -63.5t57 -25.5zM-1 21l673 692l23 -23l-673 -693z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="177" 
d="M4 0l19 85h43l-18 -85h-44z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="995" 
d="M219 437q-52 0 -83 33.5t-31 81.5q0 34 15 64.5t39 53.5t54.5 36.5t62.5 13.5q51 0 82 -33.5t31 -82.5q0 -34 -15 -64t-39 -53t-54.5 -36.5t-61.5 -13.5zM226 469q23 0 45 11t39.5 29t28 41.5t10.5 49.5q0 37 -23 62.5t-58 25.5q-23 0 -45 -11t-39 -29.5t-28 -42
t-11 -48.5q0 -37 22.5 -62.5t58.5 -25.5zM432 -10q-52 0 -83 33.5t-31 82.5q0 34 14.5 64t38.5 53t54.5 36.5t62.5 13.5q52 0 82.5 -33.5t30.5 -82.5q0 -34 -14.5 -64t-38.5 -53t-54.5 -36.5t-61.5 -13.5zM438 22q23 0 45 11t39.5 29t28.5 41.5t11 49.5q0 36 -22.5 62
t-58.5 26q-23 0 -45 -11t-39.5 -29t-28.5 -42t-11 -49q0 -37 22 -62.5t59 -25.5zM760 -10q-52 0 -83 33t-31 83q0 34 15 64t39 53t54.5 36.5t62.5 13.5q51 0 81.5 -33.5t30.5 -82.5q0 -34 -14.5 -64t-38.5 -53t-54.5 -36.5t-61.5 -13.5zM766 22q23 0 45.5 11t40 29t28 41.5
t10.5 48.5q0 37 -22.5 63t-58.5 26q-23 0 -45 -11t-39.5 -29t-28 -42t-10.5 -49q0 -37 22 -62.5t58 -25.5zM5 21l673 692l23 -23l-673 -693z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="413" 
d="M369 376v-42h-130v-141h-46v141h-131v42h131v141h46v-141h130z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="484" 
d="M71 70v43h314v-43h-314zM385 382v-43h-133v-144h-47v144h-134v43h134v144h47v-144h133z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="586" 
d="M202 -10q-43 0 -75.5 17.5t-55 47t-34 68t-11.5 81.5q0 64 26 122.5t68 103.5t96 72t111 27q58 0 101 -32.5t61 -81.5l22 105h46l-140 -661q-2 -8 -2 -10q0 -15 16 -16l-9 -44q-2 0 -4 -0.5t-4 -0.5q-22 0 -37.5 9.5t-15.5 31.5q0 4 2 12l57 271q-102 -122 -218 -122z
M224 35q31 0 60.5 11t56 29t49.5 42.5t42 51.5l38 176q-3 28 -14.5 53.5t-30 44.5t-44 30t-55.5 11q-49 0 -93.5 -24t-79 -62.5t-55 -87t-20.5 -98.5q0 -36 10 -68t29 -56.5t46 -38.5t61 -14z" />
    <glyph glyph-name="q.alt" horiz-adv-x="586" 
d="M420 112q-102 -122 -218 -122q-43 0 -75.5 17.5t-55 47t-34 68t-11.5 81.5q0 64 26 122.5t68 103.5t96 72t111 27q58 0 101 -32.5t61 -81.5l22 105h46l-156 -733h-50zM224 35q31 0 60.5 11t56 29t49.5 42.5t42 51.5l39 180q-2 26 -13 50.5t-30.5 43t-45.5 30t-56 11.5
q-49 0 -93.5 -24t-79 -62.5t-55 -87t-20.5 -98.5q0 -36 10 -68t29 -56.5t46 -38.5t61 -14z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="455" 
d="M131 193q16 77 45 115.5t73 61.5q26 14 56.5 28t56.5 34.5t43 52t17 79.5q0 56 -33 84t-79 28q-53 0 -102.5 -30t-81.5 -78l-32 22q20 31 46.5 55t56 40.5t61.5 25t62 8.5t57 -9.5t47.5 -28t32.5 -46.5t12 -65q0 -52 -15.5 -87t-41 -60t-57.5 -42t-66 -32q-18 -8 -35 -19
t-32 -28t-27 -43.5t-20 -65.5h-44zM91 0l21 98h43l-20 -98h-44z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="450" 
d="M301 313q-8 -39 -19.5 -67t-26 -48.5t-32.5 -35t-40 -26.5q-26 -14 -56.5 -28t-56.5 -34.5t-43 -52t-17 -79.5q0 -56 33 -84t79 -28q53 0 102.5 30t81.5 78l32 -22q-20 -31 -46.5 -55t-56 -40.5t-61.5 -25t-62 -8.5t-57 9.5t-47.5 28t-32.5 46.5t-12 65q0 52 15.5 87
t41 60t57.5 42t66 32q18 8 35 19t32 28t27 43.5t20 65.5h44zM341 506l-21 -98h-43l20 98h44z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="259" 
d="M118 540l37 174h45l-38 -174h-44zM194 540l37 174h45l-37 -174h-45z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="335" 
d="M13 -93l34 93h-19l20 92h51l-20 -92l-39 -93h-27zM110 -93l34 93h-18l19 92h52l-20 -92l-40 -93h-27z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="237" 
d="M13 -93l34 93h-19l20 92h51l-20 -92l-39 -93h-27z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="282" 
d="M202 730l-35 -99h18l-19 -91h-52l20 91l41 99h27zM302 730l-35 -99h18l-19 -91h-52l20 91l41 99h27z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="181" 
d="M202 730l-35 -99h18l-19 -91h-52l20 91l41 99h27z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="277" 
d="M118 540l35 98h-18l19 92h52l-20 -92l-41 -98h-27zM219 540l34 98h-18l19 92h52l-20 -92l-41 -98h-26z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="177" 
d="M118 540l35 99h-18l19 91h52l-20 -91l-41 -99h-27z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="182" 
d="M118 539l37 175h45l-38 -175h-44z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="333" 
d="M122 520h48l-28 -128q42 60 96.5 94.5t105.5 34.5q10 0 14 -1l-10 -45q-67 -2 -124.5 -41.5t-92.5 -108.5l-69 -325h-50z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="333" 
d="M242 629l-31 16l76 85h57zM122 520h48l-28 -128q42 60 96.5 94.5t105.5 34.5q10 0 14 -1l-10 -45q-67 -2 -124.5 -41.5t-92.5 -108.5l-69 -325h-50z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="333" 
d="M153 713l33 17l68 -63l94 63l26 -17l-109 -81h-37zM122 520h48l-28 -128q42 60 96.5 94.5t105.5 34.5q10 0 14 -1l-10 -45q-67 -2 -124.5 -41.5t-92.5 -108.5l-69 -325h-50z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="333" 
d="M-30 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM122 520h48l-28 -128q42 60 96.5 94.5t105.5 34.5q10 0 14 -1l-10 -45q-67 -2 -124.5 -41.5t-92.5 -108.5l-69 -325h-50z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="842" 
d="M432 -7q-79 0 -146 27.5t-115.5 76t-76 114.5t-27.5 144q0 77 27.5 143t76 114t115.5 75.5t146 27.5q78 0 145 -27.5t116 -75.5t77 -114t28 -143q0 -78 -28 -144t-77 -114.5t-116 -76t-145 -27.5zM432 21q71 0 132 24t106 68t71 105.5t26 136.5q0 71 -25 131.5t-70 105
t-106.5 70t-133.5 25.5t-133.5 -25.5t-106.5 -70t-70 -105t-25 -131.5q0 -72 25 -133t69.5 -105.5t106 -70t134.5 -25.5zM282 581h190q29 0 53.5 -12.5t42 -32.5t27.5 -45.5t10 -51.5q0 -25 -8 -48t-22 -41.5t-34.5 -31t-44.5 -15.5l114 -178h-46l-110 173h-131v-173h-41
v456zM473 334q45 0 68.5 31t23.5 74q0 45 -27 74.5t-69 29.5h-146v-209h150z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="461" 
d="M167 -10q-57 0 -105.5 20t-78.5 60l31 34q33 -38 71.5 -56t89.5 -18q36 0 66.5 9t53 25t35 39t12.5 52q0 38 -35 56t-100 35q-35 9 -61 18t-43.5 20.5t-26.5 27.5t-9 39q0 43 18 76.5t48 56t69.5 34t82.5 11.5q58 0 98.5 -20.5t57.5 -52.5l-33 -27q-17 30 -53.5 45
t-81.5 15q-31 0 -59 -7.5t-49.5 -23t-34.5 -39t-13 -55.5q0 -17 7 -28.5t20.5 -20t34.5 -15t49 -13.5q39 -10 69.5 -20t52 -23.5t32.5 -32t11 -44.5q0 -39 -17 -71.5t-47 -56t-71.5 -36.5t-90.5 -13z" />
    <glyph glyph-name="s_t" unicode="&#xfb06;" horiz-adv-x="757" 
d="M172 -10q-62 0 -108.5 23t-73.5 55l31 34q66 -72 160 -72q28 0 57.5 7t54 22t40 37.5t15.5 54.5q0 36 -21 52.5t-53 24.5t-69 12.5t-69 15.5t-53 33t-21 66q0 46 20 79t51.5 54t69.5 31t75 10q33 0 61.5 -7.5t48.5 -23.5q-4 21 -8 41t-4 43q0 29 8.5 59t29.5 54t56 39
t88 15q62 0 84.5 -22.5t22.5 -67.5q0 -27 -6 -61t-14 -78h114l-9 -41h-114l-81 -380q-1 -5 -1.5 -8t-0.5 -5q0 -23 15 -34t38 -11q13 0 26 3t24.5 7.5t19 8t10.5 5.5l6 -40q-5 -2 -15.5 -7.5t-25.5 -10.5t-32.5 -9t-35.5 -4q-36 0 -59.5 18t-23.5 51q0 8 1 16.5t3 16.5
l82 383h-70l9 41h70q9 45 14 77t5 55q0 38 -16.5 52t-54.5 14q-41 0 -68 -12t-42.5 -31.5t-21.5 -45t-6 -51.5q0 -44 10 -76t15 -45l-33 -28q-21 36 -55.5 48t-69.5 12q-29 0 -58 -6.5t-52 -21t-37.5 -37t-14.5 -55.5q0 -38 21.5 -56t53.5 -26t69 -12.5t69 -14.5t53.5 -32
t21.5 -66t-21 -76.5t-53.5 -53.5t-72.5 -31.5t-79 -10.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="461" 
d="M291 629l-31 16l76 85h57zM167 -10q-57 0 -105.5 20t-78.5 60l31 34q33 -38 71.5 -56t89.5 -18q36 0 66.5 9t53 25t35 39t12.5 52q0 38 -35 56t-100 35q-35 9 -61 18t-43.5 20.5t-26.5 27.5t-9 39q0 43 18 76.5t48 56t69.5 34t82.5 11.5q58 0 98.5 -20.5t57.5 -52.5
l-33 -27q-17 30 -53.5 45t-81.5 15q-31 0 -59 -7.5t-49.5 -23t-34.5 -39t-13 -55.5q0 -17 7 -28.5t20.5 -20t34.5 -15t49 -13.5q39 -10 69.5 -20t52 -23.5t32.5 -32t11 -44.5q0 -39 -17 -71.5t-47 -56t-71.5 -36.5t-90.5 -13z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="461" 
d="M206 713l33 17l68 -63l94 63l26 -17l-109 -81h-37zM167 -10q-57 0 -105.5 20t-78.5 60l31 34q33 -38 71.5 -56t89.5 -18q36 0 66.5 9t53 25t35 39t12.5 52q0 38 -35 56t-100 35q-35 9 -61 18t-43.5 20.5t-26.5 27.5t-9 39q0 43 18 76.5t48 56t69.5 34t82.5 11.5
q58 0 98.5 -20.5t57.5 -52.5l-33 -27q-17 30 -53.5 45t-81.5 15q-31 0 -59 -7.5t-49.5 -23t-34.5 -39t-13 -55.5q0 -17 7 -28.5t20.5 -20t34.5 -15t49 -13.5q39 -10 69.5 -20t52 -23.5t32.5 -32t11 -44.5q0 -39 -17 -71.5t-47 -56t-71.5 -36.5t-90.5 -13z" />
    <glyph glyph-name="schwa" unicode="&#x259;" horiz-adv-x="556" 
d="M207 -10q-45 0 -80.5 15.5t-61 43t-39 65.5t-13.5 82q0 19 2.5 41.5t9.5 40.5h432q3 19 3 38q0 37 -11 69t-31 54.5t-48 35.5t-62 13q-26 0 -52.5 -7.5t-50.5 -20.5t-44 -31t-34 -40l-41 12q16 27 41.5 50.5t56.5 41t65.5 27t69.5 9.5q44 0 80 -17t61 -47t38.5 -70.5
t13.5 -86.5q0 -63 -25 -120.5t-67.5 -101.5t-97.5 -70t-115 -26zM216 30q39 0 75.5 16t68 44t55 67t35.5 84h-384q-3 -21 -3 -40q0 -38 11 -69.5t31 -54t48 -35t63 -12.5z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="461" 
d="M205 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM167 -10q-57 0 -105.5 20t-78.5 60l31 34q33 -38 71.5 -56t89.5 -18q36 0 66.5 9t53 25t35 39t12.5 52q0 38 -35 56t-100 35q-35 9 -61 18t-43.5 20.5t-26.5 27.5t-9 39q0 43 18 76.5t48 56t69.5 34t82.5 11.5
q58 0 98.5 -20.5t57.5 -52.5l-33 -27q-17 30 -53.5 45t-81.5 15q-31 0 -59 -7.5t-49.5 -23t-34.5 -39t-13 -55.5q0 -17 7 -28.5t20.5 -20t34.5 -15t49 -13.5q39 -10 69.5 -20t52 -23.5t32.5 -32t11 -44.5q0 -39 -17 -71.5t-47 -56t-71.5 -36.5t-90.5 -13z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="481" 
d="M366 190q29 -21 29 -73q0 -51 -22.5 -89.5t-56.5 -64t-74.5 -38.5t-75.5 -13q-32 0 -58 7.5t-46 20t-35.5 28.5t-26.5 32l40 31q28 -36 60.5 -53.5t74.5 -17.5q28 0 57.5 10t53.5 28.5t39.5 45t15.5 60.5q0 24 -10 39t-26 23.5t-36 12t-40 4.5q-67 3 -110 34.5t-43 93.5
q0 38 16 69.5t34 49.5q-14 11 -22 34.5t-8 52.5q0 45 21 82t53 62.5t71.5 39.5t76.5 14q34 0 59 -7t42.5 -19t29.5 -28t20 -34l-44 -18q-5 18 -17 29.5t-28 18t-34 9.5t-36 3q-28 0 -57 -10t-52.5 -29t-38.5 -45.5t-15 -60.5q0 -48 32 -67t83 -21q30 -1 58.5 -7.5t51 -21.5
t36 -38t13.5 -57q0 -37 -16.5 -70t-38.5 -52zM124 311q0 -41 23 -59t54.5 -25t65 -9.5t55.5 -12.5q21 16 35.5 43t14.5 56q0 25 -11 42t-28 27.5t-37.5 15t-40.5 4.5q-52 0 -86 17q-17 -15 -31 -41.5t-14 -57.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="179" 
d="M94 432l18 85h44l-19 -85h-43zM-12 -81l31 79h-18l19 87h50l-19 -87l-36 -79h-27z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="513" 
d="M461 536h-377l10 47h458l-494 -710h-57z" />
    <glyph glyph-name="seven.lnum" horiz-adv-x="486" 
d="M476 663h-376l9 47h458l-495 -710h-56z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="789" 
d="M-3 21l673 692l23 -23l-673 -693zM537 -4q-58 0 -91.5 28.5t-33.5 69.5q0 23 10 42t26 33.5t36.5 25t40.5 16.5q-25 9 -41.5 27t-16.5 44t14.5 46.5t37 35t50 22t54.5 7.5q22 0 43 -5.5t37 -16.5t25.5 -26.5t9.5 -35.5q0 -37 -28.5 -61.5t-66.5 -36.5q31 -11 51 -33
t20 -54q0 -29 -15 -52.5t-40 -40t-57 -26t-65 -9.5zM547 30q22 0 45.5 6.5t42.5 18.5t31 29.5t12 38.5q0 17 -8.5 30.5t-22 22.5t-31.5 14t-36 5q-23 0 -46.5 -7t-42.5 -19.5t-31 -30t-12 -38.5q0 -17 8.5 -30t22.5 -22t32 -13.5t36 -4.5zM587 225q19 0 39 5.5t37 15
t28 23.5t11 32q0 28 -26 43.5t-61 15.5q-21 0 -41.5 -5.5t-36 -15.5t-25.5 -24t-10 -32q0 -14 7.5 -24.5t19.5 -18t27.5 -11.5t30.5 -4zM364 695h-240l8 35h295l-304 -393h-40z" />
    <glyph glyph-name="seven.numerator" horiz-adv-x="330" 
d="M347 695h-240l8 35h295l-304 -393h-40z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="331" 
d="M365 775h-240l7 35h295l-304 -393h-39z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="331" 
d="M259 278h-240l8 35h295l-304 -393h-40z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="585" 
d="M237 -10q-87 0 -138 58t-51 160q0 99 25.5 190.5t71.5 162t110 112.5t142 42q67 0 112.5 -37t56.5 -101l-40 -27q-8 54 -45.5 87t-94.5 33q-48 0 -91 -24t-79 -68.5t-63 -107t-42 -139.5q18 33 43.5 60t56.5 45.5t65.5 28.5t69.5 10q42 0 76.5 -15.5t58.5 -42t37.5 -62.5
t13.5 -78q0 -58 -25 -110t-66 -91.5t-94 -62.5t-110 -23zM248 35q44 0 86 19t75 51t53 74t20 89q0 34 -11 64t-31 52t-47.5 35t-60.5 13q-44 0 -86 -19t-75 -51.5t-53.5 -74.5t-20.5 -88q0 -34 11 -64t31 -52t48 -35t61 -13z" />
    <glyph glyph-name="six.lnum" horiz-adv-x="585" 
d="M237 -10q-87 0 -138 58t-51 160q0 99 25.5 190.5t71.5 162t110 112.5t142 42q67 0 112.5 -37t56.5 -101l-40 -27q-8 54 -45.5 87t-94.5 33q-44 0 -85 -22.5t-76 -66t-62.5 -106.5t-45.5 -144q18 32 43.5 58.5t55.5 45.5t63 29.5t67 10.5q42 0 76.5 -15.5t58.5 -42
t37.5 -62.5t13.5 -78q0 -58 -25 -110t-66 -91.5t-94 -62.5t-110 -23zM248 35q44 0 86 19t75 51t53 74t20 89q0 34 -11 64t-31 52t-47.5 35t-60.5 13q-44 0 -86 -19t-75 -51.5t-53.5 -74.5t-20.5 -88q0 -34 11 -64t31 -52t48 -35t61 -13z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="387" 
d="M302 71q0 -32 -15.5 -60.5t-41 -49.5t-58.5 -33.5t-69 -12.5q-57 0 -90.5 34t-33.5 94q0 57 15.5 106.5t44 86.5t68 58.5t86.5 21.5q42 0 72.5 -19t39.5 -54l-27 -22q-7 28 -32 45t-60 17q-57 0 -100.5 -44.5t-61.5 -127.5q21 32 60 52t80 20q56 0 89.5 -32t33.5 -80z
M127 -53q27 0 52 9.5t45 25.5t32 37.5t12 44.5q0 36 -27.5 62t-71.5 26q-27 0 -52.5 -9.5t-45.5 -25.5t-32 -37.5t-12 -44.5q0 -36 28 -62t72 -26z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="388" 
d="M408 565q0 -32 -15.5 -60.5t-41 -50t-59 -34t-69.5 -12.5q-57 0 -91 34t-34 94q0 57 16 106.5t44.5 87t68 59t86.5 21.5q42 0 72.5 -19t39.5 -54l-28 -22q-6 28 -31 45t-61 17q-56 0 -99.5 -45t-61.5 -127q20 32 59.5 51.5t80.5 19.5q56 0 90 -31.5t34 -79.5zM231 441
q27 0 52.5 9.5t45.5 25.5t32 37.5t12 44.5q0 36 -27.5 62t-71.5 26q-27 0 -52.5 -9.5t-45.5 -25.5t-32 -37.5t-12 -44.5q0 -37 28 -62.5t71 -25.5z" />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="251" 
 />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="466" 
d="M66 370h94q-11 38 -22.5 73t-11.5 73q0 40 18 76.5t47 63.5t66 43t76 16q51 0 89 -28.5t55 -76.5l-37 -31q-14 43 -46 68t-72 25q-28 0 -54.5 -12t-47.5 -33t-33.5 -49t-12.5 -59q0 -36 12 -72t22 -77h169l-9 -41h-152q1 -8 1.5 -17t0.5 -17q0 -30 -5 -56t-21 -54.5
t-46.5 -63t-80.5 -80.5q37 8 70 8q21 0 37.5 -3t32 -6.5t30.5 -6.5t33 -3q35 0 92 19l4 -40q-56 -24 -108 -24q-21 0 -39 3.5t-35 7.5t-35.5 7.5t-40.5 3.5q-21 0 -49 -4t-52 -10l-7 37q55 49 89.5 84t53 63.5t25 54.5t6.5 58q0 19 -3 39h-111z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="306" 
d="M129 -6q-35 0 -58 18t-23 53q0 5 0.5 10.5t1.5 11.5l84 392h-70l9 41h70l37 178h50l-37 -178h115l-9 -41h-115l-75 -352q-7 -32 -7 -40q0 -23 14.5 -34.5t35.5 -11.5q15 0 28.5 3t24.5 7.5t18.5 8t10.5 5.5l6 -40q-4 -3 -14.5 -8t-25.5 -10.5t-33 -9t-38 -3.5z" />
    <glyph glyph-name="t.alt" horiz-adv-x="293" 
d="M135 474h-72l10 46h72l38 179h51l-38 -179h118l-10 -46h-118l-101 -474h-51z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="307" 
d="M39 355h68l27 124h-69l9 41h69l38 178h50l-38 -178h115l-8 -41h-116l-27 -124h116l-7 -36h-117l-40 -190q-6 -30 -6 -41q0 -24 14 -35.5t36 -11.5q14 0 27.5 3t25 7.5t19 8t10.5 5.5l6 -40q-5 -3 -15.5 -8.5t-25 -10.5t-33 -8.5t-37.5 -3.5q-35 0 -58 18t-23 53
q0 5 0.5 10.5t1.5 11.5l49 232h-69z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="306" 
d="M281 645l76 85h57l-102 -101zM129 -6q-35 0 -58 18t-23 53q0 5 0.5 10.5t1.5 11.5l84 392h-70l9 41h70l37 178h50l-37 -178h115l-9 -41h-115l-75 -352q-7 -32 -7 -40q0 -23 14.5 -34.5t35.5 -11.5q15 0 28.5 3t24.5 7.5t18.5 8t10.5 5.5l6 -40q-4 -3 -14.5 -8
t-25.5 -10.5t-33 -9t-38 -3.5z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="577" 
d="M163 710h51l-62 -289q42 47 91 78t110 31q45 0 79 -17.5t56.5 -47t34 -67t11.5 -77.5q0 -57 -22 -116.5t-61.5 -108t-94 -79.5t-119.5 -31t-102 29.5t-54 71.5l-63 -300h-51zM248 33q45 0 87.5 22.5t75 60.5t52 88t19.5 105q0 37 -9.5 69t-27.5 55.5t-44.5 37t-60.5 13.5
q-30 0 -58.5 -10.5t-54.5 -29t-48.5 -42.5t-41.5 -51l-38 -180q-2 -5 -2 -8.5v-6.5q0 -25 12.5 -47.5t33 -39t48 -26.5t57.5 -10z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="503" 
d="M149 -147q-72 0 -119.5 32t-60.5 88l42 30q9 -48 47 -76t101 -28q44 0 80.5 13.5t63 38t41 59.5t14.5 78q0 57 -36.5 91t-106.5 34h-23l9 44h25q44 0 79.5 12t60 33.5t37.5 51t13 64.5q0 52 -33.5 82t-89.5 30t-104 -27.5t-79 -74.5l-28 28q17 27 41.5 49t54 38
t62.5 24.5t67 8.5q37 0 67.5 -11t51.5 -30.5t32.5 -46.5t11.5 -60q0 -36 -13 -68.5t-35.5 -58t-53 -43t-66.5 -22.5q51 -8 79.5 -47.5t28.5 -96.5q0 -52 -20.5 -96t-55.5 -75.5t-82.5 -49.5t-102.5 -18z" />
    <glyph glyph-name="three.lnum" horiz-adv-x="557" 
d="M210 -9q-43 0 -78.5 11.5t-61 33.5t-39.5 54.5t-14 73.5l38 23q0 -69 44 -109t119 -40q42 0 80.5 13t68.5 36t47.5 54.5t17.5 67.5q0 61 -51 95.5t-142 34.5h-32l9 45h33q110 0 172.5 42t62.5 118q0 60 -38.5 94.5t-103.5 34.5q-61 0 -114 -29.5t-81 -76.5l-30 32
q18 28 44.5 50t57.5 37.5t65.5 24t69.5 8.5q42 0 76 -12.5t58 -34.5t37 -53t13 -68q0 -69 -44 -118.5t-128 -69.5q56 -12 87 -53t31 -96q0 -49 -23.5 -90t-62 -70.5t-88 -46t-100.5 -16.5z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="800" 
d="M191 327q-24 0 -46.5 6t-40 17.5t-28.5 28.5t-11 40l28 21q0 -29 27 -53t78 -24q26 0 50 6.5t42 18t29 27t11 34.5q0 32 -34 50.5t-92 18.5h-21l6 32h22q68 0 106.5 20.5t38.5 61.5q0 30 -26 48t-65 18t-71.5 -15.5t-51.5 -41.5l-19 26q23 29 63.5 46.5t85.5 17.5
q56 0 89 -26.5t33 -66.5q0 -81 -106 -103q36 -7 56.5 -30t20.5 -54q0 -28 -14.5 -51t-39 -39t-56 -25t-64.5 -9zM61 21l673 692l23 -23l-673 -693zM545 -4q-58 0 -91.5 28.5t-33.5 69.5q0 23 10 42t26 33.5t36.5 25t40.5 16.5q-25 9 -41.5 27t-16.5 44t14.5 46.5t37 35
t50 22t54.5 7.5q22 0 43 -5.5t37 -16.5t25.5 -26.5t9.5 -35.5q0 -37 -28.5 -61.5t-66.5 -36.5q31 -11 51 -33t20 -54q0 -29 -15 -52.5t-40 -40t-57 -26t-65 -9.5zM555 30q22 0 45.5 6.5t42.5 18.5t31 29.5t12 38.5q0 17 -8.5 30.5t-22 22.5t-31.5 14t-36 5q-23 0 -46.5 -7
t-42.5 -19.5t-31 -30t-12 -38.5q0 -17 8.5 -30t22.5 -22t32 -13.5t36 -4.5zM595 225q19 0 39 5.5t37 15t28 23.5t11 32q0 28 -26 43.5t-61 15.5q-21 0 -41.5 -5.5t-36 -15.5t-25.5 -24t-10 -32q0 -14 7.5 -24.5t19.5 -18t27.5 -11.5t30.5 -4z" />
    <glyph glyph-name="three.numerator" horiz-adv-x="370" 
d="M186 327q-24 0 -46.5 6t-40 17.5t-28.5 28.5t-11 40l28 21q0 -29 27 -53t78 -24q26 0 50 6.5t42 18t29 27t11 34.5q0 32 -34 50.5t-92 18.5h-21l6 32h22q68 0 106.5 20.5t38.5 61.5q0 30 -26 48t-65 18t-71.5 -15.5t-51.5 -41.5l-19 26q23 29 63.5 46.5t85.5 17.5
q56 0 89 -26.5t33 -66.5q0 -81 -106 -103q36 -7 56.5 -30t20.5 -54q0 -28 -14.5 -51t-39 -39t-56 -25t-64.5 -9z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="370" 
d="M205 407q-59 0 -91 25.5t-37 66.5l28 21q0 -32 26.5 -54.5t79.5 -22.5q26 0 49.5 6.5t41.5 18t29 27t11 34.5q0 32 -33.5 50.5t-92.5 18.5h-21l6 32h22q68 0 106.5 21t38.5 60q0 31 -25.5 49t-65.5 18q-39 0 -71.5 -15.5t-51.5 -41.5l-19 26q23 29 63.5 46.5t85.5 17.5
q56 0 89 -26t33 -68q0 -38 -27 -64.5t-79 -37.5q36 -7 56.5 -29.5t20.5 -54.5q0 -28 -15 -51t-39 -39t-55 -25t-63 -9z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="771" 
d="M189 327q-24 0 -46.5 6t-40 17.5t-28.5 28.5t-11 40l28 21q0 -29 27 -53t78 -24q26 0 50 6.5t42 18t29 27t11 34.5q0 32 -34 50.5t-92 18.5h-21l6 32h22q68 0 106.5 20.5t38.5 61.5q0 30 -26 48t-65 18t-71.5 -15.5t-51.5 -41.5l-19 26q23 29 63.5 46.5t85.5 17.5
q56 0 89 -26.5t33 -66.5q0 -81 -106 -103q36 -7 56.5 -30t20.5 -54q0 -28 -14.5 -51t-39 -39t-56 -25t-64.5 -9zM600 106h-218l7 32l283 255h24l-54 -253h54l-7 -34h-54l-23 -106h-34zM608 140l43 202l-222 -202h179zM86 21l673 692l23 -23l-673 -693z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="670" 
d="M353 674h-90l-52 -247h-38l53 247h-90l8 38h217zM405 710h49l45 -195l129 195h50l-60 -283h-37l48 226l-123 -184h-31l-45 184l-48 -226h-37z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="493" 
d="M-13 0q6 28 18 62.5t36 70t62.5 69.5t97.5 60q49 22 88 40.5t66 38.5t41.5 43.5t14.5 55.5q0 18 -6.5 35t-20.5 30.5t-35.5 21.5t-50.5 8q-32 0 -60 -10t-50.5 -24t-38.5 -28t-24 -22l-26 35q4 4 22.5 19.5t47 32t66 30t80.5 13.5q37 0 65.5 -10.5t47.5 -28.5t28.5 -41.5
t9.5 -48.5q0 -41 -16 -71.5t-44.5 -54.5t-68.5 -44t-87 -41q-40 -17 -73 -42t-58 -52.5t-41.5 -54t-22.5 -46.5h337l-10 -46h-395v0z" />
    <glyph glyph-name="two.lnum" horiz-adv-x="547" 
d="M-15 0q12 69 35.5 120.5t56 89.5t74 65t89.5 48q41 17 85.5 33.5t81 40t60.5 59.5t24 92q0 25 -7.5 47.5t-23 39.5t-40 27t-58.5 10q-39 0 -72.5 -11.5t-61 -28t-48.5 -35t-34 -33.5l-27 31q4 5 24.5 25t54.5 42t79.5 39.5t100.5 17.5q43 0 75 -13t53 -35.5t31 -52
t10 -62.5q0 -61 -25.5 -104t-64.5 -73t-86 -50.5t-89 -37.5q-63 -25 -104 -54.5t-66 -61.5t-37 -65t-18 -64h385l-9 -46h-448v0z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="367" 
d="M62 413q8 38 22 66.5t33.5 49.5t44.5 36t57 27q26 10 53.5 19t50 21.5t37 31t14.5 46.5q0 27 -20.5 47t-64.5 20q-24 0 -45 -6t-37.5 -14.5t-29.5 -18.5t-21 -18l-19 24q3 4 16 15t34.5 22.5t50 20.5t63.5 9q58 0 85.5 -27t27.5 -66q0 -34 -14 -57t-37 -39t-52.5 -27.5
t-60.5 -21.5q-36 -12 -59.5 -28t-37.5 -33t-21 -34t-10 -31h233l-7 -34h-286z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="810" 
d="M49 333q8 38 21.5 66.5t33 49.5t44.5 36t56 27q25 10 52 19t49.5 21.5t36.5 31t14 46.5q0 27 -20 47t-63 20q-24 0 -44.5 -6t-37 -14.5t-29 -18.5t-20.5 -18l-19 24q3 4 16 15t34 22.5t49 20.5t63 9q57 0 84 -27t27 -66q0 -33 -13.5 -56t-36 -39.5t-52 -28t-60.5 -21.5
q-36 -12 -59 -28t-37 -33t-20.5 -34t-9.5 -31h230l-7 -34h-282zM80 21l673 692l23 -23l-673 -693zM557 -5q-24 0 -46.5 5.5t-40 17t-28.5 29t-11 40.5l27 20q0 -29 27.5 -53t78.5 -24q26 0 50 6.5t42 18t28.5 27t10.5 34.5q0 32 -33 50.5t-92 18.5h-22l7 32h21
q68 0 107 20.5t39 61.5q0 30 -26 48.5t-65 18.5q-40 0 -72.5 -16t-51.5 -42l-18 26q22 29 62.5 46.5t85.5 17.5q56 0 89.5 -26.5t33.5 -66.5q0 -80 -107 -103q37 -6 57.5 -29t20.5 -55q0 -28 -14.5 -51t-39 -39t-56 -24.5t-64.5 -8.5z" />
    <glyph glyph-name="two.numerator" horiz-adv-x="367" 
d="M45 333q8 38 22 66.5t33.5 49.5t44.5 36t57 27q26 10 53.5 19t50 21.5t37 31t14.5 46.5q0 27 -20.5 47t-64.5 20q-24 0 -45 -6t-37.5 -14.5t-29.5 -18.5t-21 -18l-19 24q3 4 16 15t34.5 22.5t50 20.5t63.5 9q59 0 86 -27t27 -66q0 -33 -14 -56t-36.5 -39.5t-52.5 -28
t-61 -21.5q-36 -12 -59.5 -28t-37.5 -33t-21 -34t-10 -31h233l-7 -34h-286z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="562" 
d="M162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7
t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35z" />
    <glyph glyph-name="u.alt" horiz-adv-x="559" 
d="M167 -10q-36 0 -60 10t-39 28t-21 42t-6 53q0 23 3.5 47t8.5 50l64 300h50l-63 -293q-5 -23 -8 -45t-3 -42q0 -47 21 -76t76 -29q32 0 64.5 12.5t61.5 34.5t53.5 50t40.5 60l70 328h50l-111 -520h-49l26 121q-21 -30 -48 -54t-57 -41.5t-61.5 -26.5t-62.5 -9z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="562" 
d="M322 629l-31 16l76 85h57zM162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10
q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="562" 
d="M162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7
t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35zM338 666q20 0 40 18.5t28 45.5h32q-4 -19 -14.5 -36t-25 -29.5t-31.5 -19.5t-34 -7q-32 0 -51 20.5t-19 51.5q0 9 3 20h33q-1 -5 -1 -14q0 -20 11.5 -35t28.5 -15z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="562" 
d="M239 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45
q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="562" 
d="M255 640l18 88h44l-19 -88h-43zM386 640l19 88h43l-18 -88h-44zM162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16
l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="562" 
d="M162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7
t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35zM272 730h55l41 -85l-37 -16z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="562" 
d="M162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7
t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35zM258 629l-26 16l75 85h51zM370 629l-26 16l74 85h52z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="562" 
d="M162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7
t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35zM207 671l8 37h272l-8 -37h-272z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="500" 
d="M-9 -46l10 46h366l-10 -46h-366z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="251" 
 />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="537" 
d="M54 249l10 46h406l-9 -46h-407z" />
    <glyph glyph-name="uni015E" unicode="&#x15e;" horiz-adv-x="584" 
d="M148 -125q13 -7 34 -13t45 -6q65 0 65 45q0 16 -11.5 22t-29.5 6q-20 0 -43 -5t-36 -9l58 79q-150 3 -235 106l36 41q32 -47 87.5 -74.5t133.5 -27.5q101 0 160 42t59 121q0 55 -46.5 83.5t-134.5 48.5q-45 10 -79.5 22t-58.5 28.5t-36.5 40t-12.5 56.5q0 55 22 98.5
t60 73.5t90.5 46t113.5 16q66 0 113.5 -20.5t79.5 -62.5l-35 -40q-48 77 -168 77q-59 0 -100.5 -13.5t-68.5 -36.5t-39.5 -54.5t-12.5 -68.5q0 -26 10.5 -44t31.5 -31t52.5 -23t73.5 -20q45 -11 82.5 -23.5t63.5 -31t40.5 -44t14.5 -62.5q0 -52 -19.5 -92.5t-55 -68.5
t-84 -43.5t-105.5 -17.5l-37 -48q9 4 19.5 5t18.5 1q58 0 58 -46q0 -38 -27 -58t-78 -20q-26 0 -48.5 5.5t-39.5 13.5z" />
    <glyph glyph-name="uni015F" unicode="&#x15f;" horiz-adv-x="461" 
d="M69 -125q13 -7 34 -13t45 -6q65 0 65 45q0 16 -11.5 22t-29.5 6q-20 0 -43 -5t-36 -9l56 76q-52 3 -95 22.5t-71 56.5l31 34q33 -38 71.5 -56t89.5 -18q36 0 66.5 9t53 25t35 39t12.5 52q0 38 -35 56t-100 35q-35 9 -61 18t-43.5 20.5t-26.5 27.5t-9 39q0 43 18 76.5
t48 56t69.5 34t82.5 11.5q58 0 98.5 -20.5t57.5 -52.5l-33 -27q-17 30 -53.5 45t-81.5 15q-31 0 -59 -7.5t-49.5 -23t-34.5 -39t-13 -55.5q0 -17 7 -28.5t20.5 -20t34.5 -15t49 -13.5q39 -10 69.5 -20t52 -23.5t32.5 -32t11 -44.5q0 -38 -15.5 -69.5t-44 -55t-67.5 -37
t-85 -15.5l-34 -43q9 4 19.5 5t18.5 1q58 0 58 -46q0 -38 -27 -58t-78 -20q-26 0 -48.5 5.5t-39.5 13.5z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="578" 
d="M338 664h-246l9 46h544l-10 -46h-246l-142 -664h-8l-41 -53q9 4 19.5 5t18.5 1q58 0 58 -46q0 -38 -27 -58t-78 -20q-26 0 -48.5 5.5t-39.5 13.5l19 27q13 -7 34 -13t45 -6q65 0 65 45q0 16 -11.5 22t-29.5 6q-20 0 -43 -5t-36 -9l63 85h-10z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="306" 
d="M30 -125q13 -7 34 -13t45 -6q65 0 65 45q0 16 -11.5 22t-29.5 6q-20 0 -43 -5t-36 -9l59 80q-29 4 -47 21.5t-18 48.5v10.5t2 11.5l84 392h-70l9 41h70l37 178h50l-37 -178h115l-9 -41h-115l-75 -352q-7 -32 -7 -40q0 -23 14.5 -34.5t35.5 -11.5q15 0 28.5 3t24.5 7.5
t18.5 8t10.5 5.5l6 -40q-8 -5 -34 -16t-61 -14l-37 -48q9 4 19.5 5t18.5 1q58 0 58 -46q0 -38 -27 -58t-78 -20q-26 0 -48.5 5.5t-39.5 13.5z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="583" 
d="M177 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM240 -6q-158 0 -246 106l36 41q32 -47 87.5 -74.5t133.5 -27.5q101 0 160 42t59 121q0 55 -46.5 83.5t-134.5 48.5q-45 10 -79.5 22t-58.5 28.5t-36.5 40t-12.5 56.5q0 55 22 98.5t60 73.5t90.5 46t113.5 16
q66 0 113.5 -20.5t79.5 -62.5l-35 -40q-48 77 -168 77q-59 0 -100.5 -13.5t-68.5 -36.5t-39.5 -54.5t-12.5 -68.5q0 -26 10.5 -44t31.5 -31t52.5 -23t73.5 -20q45 -11 82.5 -23.5t63.5 -31t40.5 -44t14.5 -62.5q0 -54 -21.5 -96t-60 -70t-91 -42.5t-113.5 -14.5z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="461" 
d="M110 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM167 -10q-57 0 -105.5 20t-78.5 60l31 34q33 -38 71.5 -56t89.5 -18q36 0 66.5 9t53 25t35 39t12.5 52q0 38 -35 56t-100 35q-35 9 -61 18t-43.5 20.5t-26.5 27.5t-9 39q0 43 18 76.5t48 56t69.5 34t82.5 11.5
q58 0 98.5 -20.5t57.5 -52.5l-33 -27q-17 30 -53.5 45t-81.5 15q-31 0 -59 -7.5t-49.5 -23t-34.5 -39t-13 -55.5q0 -17 7 -28.5t20.5 -20t34.5 -15t49 -13.5q39 -10 69.5 -20t52 -23.5t32.5 -32t11 -44.5q0 -39 -17 -71.5t-47 -56t-71.5 -36.5t-90.5 -13z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="579" 
d="M160 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM339 664h-246l9 46h544l-10 -46h-246l-142 -664h-50z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="306" 
d="M65 -187l29 68h-16l15 70h48l-14 -70l-35 -68h-27zM129 -6q-35 0 -58 18t-23 53q0 5 0.5 10.5t1.5 11.5l84 392h-70l9 41h70l37 178h50l-37 -178h115l-9 -41h-115l-75 -352q-7 -32 -7 -40q0 -23 14.5 -34.5t35.5 -11.5q15 0 28.5 3t24.5 7.5t18.5 8t10.5 5.5l6 -40
q-4 -3 -14.5 -8t-25.5 -10.5t-33 -9t-38 -3.5z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="569" 
d="M123 520h50l-62 -293q-11 -50 -11 -85q0 -107 94 -107q30 0 62 11.5t61.5 32t55 49.5t43.5 64l70 328h51l-96 -450q-1 -3 -1 -10q0 -15 17 -15l-11 -45q-35 0 -43.5 11.5t-8.5 28.5q0 2 1.5 12.5t3.5 23.5t4.5 26t4.5 21q-20 -29 -46.5 -53.5t-56 -42t-61 -27.5
t-62.5 -10q-48 0 -73.5 24.5t-31.5 67.5l-60 -294h-50z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="558" 
d="M-62 23l680 690l26 -26l-680 -690z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="174" 
d="M53 248l22 101h43l-21 -101h-44z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="562" 
d="M162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-56 -34 -77.5 -56.5t-21.5 -45.5q0 -31 39 -37l-23 -30
q-26 6 -38.5 21t-12.5 35q0 29 22 55.5t75 59.5q-18 9 -18 32q0 3 0.5 7t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="562" 
d="M278 656q0 32 24 53t56 21q24 0 38 -14t14 -36q0 -32 -23.5 -53.5t-55.5 -21.5q-25 0 -39 14t-14 37zM352 707q-18 0 -32.5 -13t-14.5 -34q0 -14 8.5 -23t22.5 -9q16 0 32 13.5t16 33.5q0 15 -9 23.5t-23 8.5zM162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49
l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="562" 
d="M162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7
t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35zM398 647q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33
q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="476" 
d="M60 520h52l70 -470q91 112 154.5 235t95.5 235h48q-38 -131 -108.5 -262t-176.5 -258h-52z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="743" 
d="M56 520h52l85 -470q47 53 86 111t71 117l-52 242h52l35 -176q23 46 40 90t30 86h48q-36 -119 -103 -243l46 -227q94 112 158 235t97 235h49q-40 -131 -111 -262t-180 -258h-52l-43 217q-32 -55 -71 -109.5t-86 -107.5h-52z" />
    <glyph glyph-name="w.alt" horiz-adv-x="777" 
d="M66 520h53l53 -456l236 456h49l42 -456l247 456h52l-286 -520h-51l-44 446l-234 -446h-52z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="743" 
d="M420 629l-31 16l76 85h57zM56 520h52l85 -470q47 53 86 111t71 117l-52 242h52l35 -176q23 46 40 90t30 86h48q-36 -119 -103 -243l46 -227q94 112 158 235t97 235h49q-40 -131 -111 -262t-180 -258h-52l-43 217q-32 -55 -71 -109.5t-86 -107.5h-52z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="743" 
d="M328 649l109 81h37l75 -81l-33 -17l-68 63l-94 -63zM56 520h52l85 -470q47 53 86 111t71 117l-52 242h52l35 -176q23 46 40 90t30 86h48q-36 -119 -103 -243l46 -227q94 112 158 235t97 235h49q-40 -131 -111 -262t-180 -258h-52l-43 217q-32 -55 -71 -109.5t-86 -107.5
h-52z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="743" 
d="M339 640l18 88h44l-19 -88h-43zM470 640l19 88h43l-18 -88h-44zM56 520h52l85 -470q47 53 86 111t71 117l-52 242h52l35 -176q23 46 40 90t30 86h48q-36 -119 -103 -243l46 -227q94 112 158 235t97 235h49q-40 -131 -111 -262t-180 -258h-52l-43 217q-32 -55 -71 -109.5
t-86 -107.5h-52z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="743" 
d="M56 520h52l85 -470q47 53 86 111t71 117l-52 242h52l35 -176q23 46 40 90t30 86h48q-36 -119 -103 -243l46 -227q94 112 158 235t97 235h49q-40 -131 -111 -262t-180 -258h-52l-43 217q-32 -55 -71 -109.5t-86 -107.5h-52zM382 730h55l41 -85l-37 -16z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="472" 
d="M198 259l-136 261h55l112 -215l4 -14l10 12l205 217h55l-247 -261l136 -259h-56l-111 214l-5 13l-10 -12l-202 -215h-56z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="478" 
d="M-36 -183q31 5 52.5 13.5t39.5 22.5t34.5 35.5t36.5 52.5l36 54l-111 525h52l94 -471l48 76q45 72 75.5 125.5t52.5 98.5t37 85.5t27 85.5h50q-14 -48 -29 -89.5t-37.5 -88t-56 -103.5t-83.5 -136l-82 -129q-35 -56 -62.5 -92.5t-53.5 -59t-53 -33t-61 -13.5z" />
    <glyph glyph-name="y.alt" horiz-adv-x="500" 
d="M36 -213q0 1 8 13.5t19.5 32t26 43.5t28.5 47.5t26 44t20 32.5l-109 520h52l94 -471l282 471h49l-442 -733h-54v0z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="478" 
d="M-36 -183q31 5 52.5 13.5t39.5 22.5t34.5 35.5t36.5 52.5l36 54l-111 525h52l94 -471l48 76q45 72 75.5 125.5t52.5 98.5t37 85.5t27 85.5h50q-14 -48 -29 -89.5t-37.5 -88t-56 -103.5t-83.5 -136l-82 -129q-35 -56 -62.5 -92.5t-53.5 -59t-53 -33t-61 -13.5zM285 629
l-31 16l76 85h57z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="478" 
d="M-36 -183q31 5 52.5 13.5t39.5 22.5t34.5 35.5t36.5 52.5l36 54l-111 525h52l94 -471l48 76q45 72 75.5 125.5t52.5 98.5t37 85.5t27 85.5h50q-14 -48 -29 -89.5t-37.5 -88t-56 -103.5t-83.5 -136l-82 -129q-35 -56 -62.5 -92.5t-53.5 -59t-53 -33t-61 -13.5zM189 649
l109 81h37l75 -81l-33 -17l-68 63l-94 -63z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="478" 
d="M-36 -183q31 5 52.5 13.5t39.5 22.5t34.5 35.5t36.5 52.5l36 54l-111 525h52l94 -471l48 76q45 72 75.5 125.5t52.5 98.5t37 85.5t27 85.5h50q-14 -48 -29 -89.5t-37.5 -88t-56 -103.5t-83.5 -136l-82 -129q-35 -56 -62.5 -92.5t-53.5 -59t-53 -33t-61 -13.5zM206 640
l18 88h44l-19 -88h-43zM337 640l19 88h43l-18 -88h-44z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="642" 
d="M404 357h83l-8 -38h-104l-42 -51l-5 -26h135l-7 -37h-136l-44 -205h-51l44 205h-135l8 37h135l6 28l-19 49h-105l8 38h84l-139 353h57l149 -393l318 393h57z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="478" 
d="M-36 -183q31 5 52.5 13.5t39.5 22.5t34.5 35.5t36.5 52.5l36 54l-111 525h52l94 -471l48 76q45 72 75.5 125.5t52.5 98.5t37 85.5t27 85.5h50q-14 -48 -29 -89.5t-37.5 -88t-56 -103.5t-83.5 -136l-82 -129q-35 -56 -62.5 -92.5t-53.5 -59t-53 -33t-61 -13.5zM236 730h55
l41 -85l-37 -16z" />
    <glyph glyph-name="ytilde" unicode="&#x1ef9;" horiz-adv-x="478" 
d="M-36 -183q31 5 52.5 13.5t39.5 22.5t34.5 35.5t36.5 52.5l36 54l-111 525h52l94 -471l48 76q45 72 75.5 125.5t52.5 98.5t37 85.5t27 85.5h50q-14 -48 -29 -89.5t-37.5 -88t-56 -103.5t-83.5 -136l-82 -129q-35 -56 -62.5 -92.5t-53.5 -59t-53 -33t-61 -13.5zM351 647
q-21 0 -36 6t-27 13.5t-23 14t-26 6.5q-16 0 -27 -7t-17.5 -15.5t-10 -16t-3.5 -9.5h-34q1 4 7 17.5t18 28t30 26t44 11.5q19 0 32.5 -6.5t25.5 -14t24 -13.5t30 -6q19 0 31 7t19.5 15.5t11 16t3.5 9.5h33q-1 -5 -7 -18.5t-18.5 -27.5t-32 -25.5t-47.5 -11.5z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="463" 
d="M-28 36l440 445h-339l9 39h396l-8 -36l-438 -445h343l-9 -39h-402z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="463" 
d="M277 629l-31 16l76 85h57zM-28 36l440 445h-339l9 39h396l-8 -36l-438 -445h343l-9 -39h-402z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="463" 
d="M199 713l33 17l68 -63l94 63l26 -17l-109 -81h-37zM-28 36l440 445h-339l9 39h396l-8 -36l-438 -445h343l-9 -39h-402z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="463" 
d="M-28 36l440 445h-339l9 39h396l-8 -36l-438 -445h343l-9 -39h-402zM275 640l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="590" 
d="M229 -10q-42 0 -75.5 17t-57.5 48t-36.5 73t-12.5 93q0 78 25.5 146.5t68.5 119.5t99.5 80.5t117.5 29.5q42 0 75.5 -17t57.5 -48t36.5 -73t12.5 -93q0 -78 -25.5 -146.5t-68.5 -119.5t-99.5 -80.5t-117.5 -29.5zM239 37q50 0 95.5 25t80 69t54.5 103t20 127
q0 42 -9.5 77t-28 59.5t-44.5 38.5t-59 14q-51 0 -96 -25.5t-79.5 -69.5t-54.5 -103.5t-20 -126.5q0 -84 37 -136t104 -52z" />
    <glyph glyph-name="zero.lnum" horiz-adv-x="652" 
d="M260 -10q-55 0 -94.5 25.5t-65 66t-37.5 91t-12 101.5q0 49 13 101t37 100t57 91t73.5 75t86.5 50.5t95 18.5q55 0 94.5 -26t65 -67t37 -91.5t11.5 -100.5q0 -75 -28.5 -152t-78 -140t-115 -103t-139.5 -40zM270 37q65 0 120 38t94.5 95.5t62 124.5t22.5 125q0 45 -10 89
t-30.5 78t-52 55t-73.5 21q-44 0 -83.5 -18t-72.5 -49t-59.5 -71t-45 -84.5t-28.5 -89.5t-10 -85q0 -45 9.5 -86.5t29.5 -73t51.5 -50.5t75.5 -19z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="434" 
d="M136 -85q-37 0 -63.5 14.5t-44 38t-25.5 52.5t-8 58q0 40 17.5 82t48.5 76t71.5 55.5t87.5 21.5q37 0 63 -15t43 -38t25 -52.5t8 -57.5q0 -41 -17.5 -82.5t-47.5 -75.5t-71 -55.5t-87 -21.5zM32 82q0 -24 6.5 -48t20 -42.5t34.5 -30t50 -11.5q40 0 73.5 19.5t57 48.5
t36.5 63t13 64q0 25 -7 48.5t-20.5 42.5t-34.5 30.5t-49 11.5q-41 0 -74 -19.5t-56.5 -48.5t-36.5 -63.5t-13 -64.5z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="433" 
d="M241 412q-37 0 -63.5 14.5t-44 37.5t-25.5 52.5t-8 58.5q0 40 17.5 82t48.5 76t71.5 55.5t87.5 21.5q37 0 63 -15t43 -38t25 -52.5t8 -57.5q0 -41 -17.5 -83t-47.5 -75.5t-70.5 -55t-87.5 -21.5zM137 579q0 -24 6.5 -48t20.5 -42.5t34.5 -30t49.5 -11.5q40 0 73.5 19.5
t57 48.5t36.5 63t13 64q0 25 -7 48.5t-20.5 42.5t-34 30.5t-48.5 11.5q-41 0 -74.5 -19.5t-57 -48.5t-36.5 -63.5t-13 -64.5z" />
    <glyph glyph-name="emspace" unicode="&#x2003;" horiz-adv-x="867" 
 />
    <glyph glyph-name="enspace" unicode="&#x2002;" horiz-adv-x="545" 
 />
    <glyph glyph-name="uni030F" unicode="&#x30f;" horiz-adv-x="302" 
d="M171 730h50l39 -85l-32 -16zM59 730h52l37 -85l-32 -16z" />
    <glyph glyph-name="uni0311" unicode="&#x311;" horiz-adv-x="257" 
d="M53 638q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32v0z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="372" 
d="M70 708h272l-8 -37h-272z" />
    <glyph glyph-name="dotbelow" horiz-adv-x="141" 
d="M16 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="558" 
d="M169 475q23 20 52 31.5t63 11.5t63.5 -12t52.5 -32l71 71l26 -26l-71 -71q17 -23 27 -51.5t10 -60.5q0 -63 -39 -113l73 -71l-24 -24l-73 70q-23 -20 -52 -31t-64 -11q-34 0 -63 10.5t-52 30.5l-71 -69l-24 24l71 70q-39 50 -39 114q0 32 10 61t27 52l-69 70l27 26z
M149 335q0 -30 10.5 -55.5t29 -45t43 -30.5t52.5 -11t52.5 11.5t43 31t29.5 45.5t11 56t-11 56t-29.5 45.5t-43 30.5t-52.5 11q-27 0 -51.5 -11.5t-43 -31t-29.5 -46t-11 -56.5z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="524" 
d="M157 0q-61 22 -95.5 79t-34.5 131q0 55 19 105.5t51.5 92t76.5 71t94 42.5l26 124h30l-26 -119q18 3 37 3q7 0 13.5 -0.5t12.5 -1.5l25 118h31l-26 -123q45 -12 74.5 -41t40.5 -73l-52 -16q-13 43 -47.5 67.5t-82.5 24.5q-47 0 -91 -21.5t-78.5 -58t-55 -85t-20.5 -102.5
q0 -38 11 -71.5t30.5 -58t47.5 -38.5t61 -14q25 0 52 8t51 21t43 30.5t28 36.5l47 -14q-25 -44 -71 -76t-100 -44l-25 -117h-30l24 112q-7 -1 -13.5 -1.5t-12.5 -0.5q-10 0 -19 1t-17 3l-25 -114h-29z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="1047" 
d="M52 364h141l-13 69h-113l7 34h99l-46 243h55l44 -243h275l-44 238h50l45 -258l155 258h50l-145 -238h276l148 243h55l-151 -243h100l-8 -34h-113l-42 -69h140l-7 -34h-154l-204 -330h-46l-70 365l-226 -365h-47l-64 330h-154zM641 60l164 270h-216zM300 60l166 270h-216z
M508 433h-276l12 -69h243l26 42zM575 406l8 -42h242l42 69h-276z" />
    <glyph glyph-name="franc" unicode="&#x20a3;" horiz-adv-x="592" 
d="M53 0l30 140h-87l8 42h88l112 528h440l-10 -46h-389l-59 -282h329l-9 -44h-330l-33 -156h138l-8 -42h-139l-30 -140h-51z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="796" 
d="M774 376h-80l-14 -65h80l-7 -34h-80l-59 -277h-46l-149 277h-254l-59 -277h-51l59 277h-81l7 34h81l14 65h-81l7 34h81l64 300h39l161 -300h244l64 299h51l-64 -299h80zM193 410h154l-110 204zM581 85l41 192h-144zM172 311h228l-35 65h-179zM459 311h170l14 65h-219z
" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="457" 
d="M65 383h88q-10 34 -20 66t-10 67q0 40 17.5 76.5t46.5 63.5t66 43t76 16q51 0 89 -28.5t55 -76.5l-37 -31q-14 43 -45.5 68t-71.5 25q-28 0 -54.5 -12t-47.5 -33t-33.5 -49t-12.5 -59q0 -34 10.5 -67t20.5 -69h173l-7 -34h-158q2 -13 3.5 -27.5t1.5 -35.5h150l-7 -34
h-146q-3 -22 -11.5 -43.5t-26 -46.5t-45 -54.5t-68.5 -66.5q38 8 71 8q21 0 37.5 -3t31.5 -6.5t30.5 -6.5t33.5 -3q35 0 92 19l4 -40q-56 -24 -108 -24q-21 0 -39 3.5t-35.5 7.5t-35.5 7.5t-40 3.5t-49.5 -4t-51.5 -10l-7 37q46 40 77 71t50.5 56.5t29.5 48t13 46.5h-105
l7 34h101q0 19 -2 34t-5 29h-103z" />
    <glyph glyph-name="uni20B9" unicode="&#x20b9;" horiz-adv-x="431" 
d="M448 572h-92q2 -11 3.5 -22t1.5 -22q0 -44 -16.5 -86.5t-45 -77t-66.5 -58.5t-81 -30l109 -276h-58l-125 315h48q38 0 71 18.5t58 48t39 66.5t14 74q0 13 -2 25.5t-5 24.5h-204l7 34h183q-15 26 -39 42t-54 16h-78l10 46h351l-7 -34h-171q16 -14 28 -32t20 -38h108z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="666" 
d="M632 710h50l-60 -283h-37l48 226l-123 -184h-31l-45 184l-47 -226h-38l60 283h49l46 -195zM310 650q-19 31 -65 31q-39 0 -56.5 -14.5t-17.5 -37.5q0 -17 18 -26t48 -17q32 -9 54.5 -23.5t22.5 -45.5q0 -47 -34 -70t-88 -23q-66 0 -103 43l24 29q27 -38 88 -38
q33 0 53 12t20 37q0 18 -16 27.5t-45 17.5q-34 10 -57.5 23.5t-23.5 43.5q0 45 34.5 70.5t85.5 25.5q55 0 83 -34z" />
    <glyph glyph-name="Schwa" unicode="&#x18f;" horiz-adv-x="707" 
d="M281 -8q-57 0 -103 21t-79 58.5t-51 88.5t-18 111q0 32 5 58.5t11 45.5h574q3 27 3 52q0 52 -14.5 96.5t-41.5 76t-64.5 49.5t-83.5 18q-35 0 -71 -10.5t-69 -30t-61 -46t-46 -59.5l-44 23q23 37 57.5 68.5t75 54t84.5 35.5t87 13q56 0 102 -22.5t78.5 -62.5t50 -93.5
t17.5 -114.5q0 -87 -34 -165.5t-90 -137t-127.5 -93t-147.5 -34.5zM290 40q54 0 106 22t94.5 61t74 91.5t45.5 114.5h-522q-3 -16 -5 -32t-2 -32q0 -44 13 -84.5t39 -72t65 -50t92 -18.5z" />
    <glyph glyph-name="uni01F1" unicode="&#x1f1;" horiz-adv-x="1269" 
d="M648 42l608 622h-470l10 46h531l-10 -42l-605 -622h471l-9 -46h-535zM178 710h225q66 0 114.5 -21t80 -57.5t47 -86t15.5 -106.5q0 -95 -30.5 -175t-85 -139t-129.5 -92t-163 -33h-225zM262 46q80 0 144 31.5t108.5 84.5t68.5 122.5t24 145.5q0 51 -13.5 94t-40.5 74
t-67 48.5t-93 17.5h-174l-131 -618h174z" />
    <glyph glyph-name="uni01F2" unicode="&#x1f2;" horiz-adv-x="1150" 
d="M660 36l440 445h-339l9 39h396l-8 -36l-438 -445h343l-9 -39h-402zM178 710h225q66 0 114.5 -21t80 -57.5t47 -86t15.5 -106.5q0 -95 -30.5 -175t-85 -139t-129.5 -92t-163 -33h-225zM262 46q80 0 144 31.5t108.5 84.5t68.5 122.5t24 145.5q0 51 -13.5 94t-40.5 74
t-67 48.5t-93 17.5h-174l-131 -618h174z" />
    <glyph glyph-name="uni01F3" unicode="&#x1f3;" horiz-adv-x="1054" 
d="M563 36l440 445h-339l9 39h396l-8 -36l-438 -445h343l-9 -39h-402zM214 -10q-44 0 -79 18t-59 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t93 72t109.5 27q32 0 58.5 -10t47.5 -26.5t36 -38.5t24 -47l68 323h51l-140 -658q-2 -10 -2 -12q0 -15 17 -15l-10 -45
q-7 -1 -12 -1h-10q-34 5 -34 36q0 3 0.5 6.5t1.5 11t3.5 19.5t6.5 32q-41 -52 -98 -83t-112 -31zM233 35q25 0 56.5 11t60.5 30.5t52.5 44.5t32.5 54l36 171q-4 28 -18 53.5t-34.5 44t-46 29.5t-52.5 11q-49 0 -93 -24t-77 -62.5t-52 -86t-19 -94.5q0 -37 11 -70t31 -58
t48.5 -39.5t63.5 -14.5z" />
    <glyph glyph-name="uni01C7" unicode="&#x1c7;" horiz-adv-x="1019" 
d="M657 -11q-85 0 -137 44l25 41q19 -17 50 -27.5t72 -10.5q54 0 91 21t62.5 61t42.5 98.5t33 132.5l77 360h51l-77 -360q-17 -81 -38 -147.5t-53 -113.5t-80 -73t-119 -26zM178 710h51l-141 -664h401l-10 -46h-452z" />
    <glyph glyph-name="uni01C8" unicode="&#x1c8;" horiz-adv-x="764" 
d="M446 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36.5 7.5t34.5 21t28 32t17 40.5l117 550h50l-116 -548q-7 -33 -25 -60t-42 -47t-51.5 -31t-53.5 -11zM729 730h50l-20 -92h-50zM178 710h51l-141 -664h401l-10 -46h-452z" />
    <glyph glyph-name="uni01C9" unicode="&#x1c9;" horiz-adv-x="457" 
d="M140 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36.5 7.5t34.5 21t28 32t17 40.5l117 550h50l-116 -548q-7 -33 -25 -60t-42 -47t-51.5 -31t-53.5 -11zM423 730h50l-20 -92h-50zM103 -6q-34 0 -53 18.5t-19 50.5q0 11 3 25l136 642h50l-133 -627
q-1 -5 -1.5 -9t-0.5 -9q0 -21 12.5 -33t36.5 -12q11 0 26 2.5t28 7.5v-40q-17 -7 -42.5 -11.5t-42.5 -4.5z" />
    <glyph glyph-name="uni01CA" unicode="&#x1ca;" horiz-adv-x="1205" 
d="M843 -11q-85 0 -137 44l25 41q19 -17 50 -27.5t72 -10.5q54 0 91 21t62.5 61t42.5 98.5t33 132.5l77 360h51l-77 -360q-17 -81 -38 -147.5t-53 -113.5t-80 -73t-119 -26zM178 710h40l336 -625l132 624h51l-151 -709h-45l-332 614l-131 -614h-51z" />
    <glyph glyph-name="uni01CB" unicode="&#x1cb;" horiz-adv-x="950" 
d="M632 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36.5 7.5t34.5 21t28 32t17 40.5l117 550h50l-116 -548q-7 -33 -25 -60t-42 -47t-51.5 -31t-53.5 -11zM915 730h50l-20 -92h-50zM178 710h40l336 -625l132 624h51l-151 -709h-45l-332 614l-131 -614
h-51z" />
    <glyph glyph-name="uni01CC" unicode="&#x1cc;" horiz-adv-x="760" 
d="M443 -177q-30 0 -54.5 8.5t-41.5 28.5l34 33q12 -14 30 -19t37 -5q18 0 36.5 7.5t34.5 21t28 32t17 40.5l117 550h50l-116 -548q-7 -33 -25 -60t-42 -47t-51.5 -31t-53.5 -11zM726 730h50l-20 -92h-50zM122 520h47l-27 -124q21 30 49 54.5t59.5 42t65 27t66.5 9.5
q60 0 86 -32t26 -93q0 -23 -3.5 -49t-9.5 -56l-63 -299h-50l61 290q6 28 8.5 51t2.5 42q0 100 -83 100q-31 0 -64 -12t-63.5 -34t-56.5 -51.5t-43 -63.5l-68 -322h-50z" />
    <glyph glyph-name="uni01C4" unicode="&#x1c4;" horiz-adv-x="1280" 
d="M993 900l33 17l68 -63l94 63l26 -17l-109 -81h-37zM660 42l608 622h-470l10 46h531l-10 -42l-605 -622h471l-9 -46h-535zM178 710h225q66 0 114.5 -21t80 -57.5t47 -86t15.5 -106.5q0 -95 -30.5 -175t-85 -139t-129.5 -92t-163 -33h-225zM262 46q80 0 144 31.5
t108.5 84.5t68.5 122.5t24 145.5q0 51 -13.5 94t-40.5 74t-67 48.5t-93 17.5h-174l-131 -618h174z" />
    <glyph glyph-name="uni01C5" unicode="&#x1c5;" horiz-adv-x="1150" 
d="M887 713l33 17l68 -63l94 63l26 -17l-109 -81h-37zM660 36l440 445h-339l9 39h396l-8 -36l-438 -445h343l-9 -39h-402zM178 710h225q66 0 114.5 -21t80 -57.5t47 -86t15.5 -106.5q0 -95 -30.5 -175t-85 -139t-129.5 -92t-163 -33h-225zM262 46q80 0 144 31.5t108.5 84.5
t68.5 122.5t24 145.5q0 51 -13.5 94t-40.5 74t-67 48.5t-93 17.5h-174l-131 -618h174z" />
    <glyph glyph-name="uni01C6" unicode="&#x1c6;" horiz-adv-x="1054" 
d="M790 713l33 17l68 -63l94 63l26 -17l-109 -81h-37zM563 36l440 445h-339l9 39h396l-8 -36l-438 -445h343l-9 -39h-402zM214 -10q-44 0 -79 18t-59 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t93 72t109.5 27q32 0 58.5 -10t47.5 -26.5t36 -38.5t24 -47l68 323
h51l-140 -658q-2 -10 -2 -12q0 -15 17 -15l-10 -45q-7 -1 -12 -1h-10q-34 5 -34 36q0 3 0.5 6.5t1.5 11t3.5 19.5t6.5 32q-41 -52 -98 -83t-112 -31zM233 35q25 0 56.5 11t60.5 30.5t52.5 44.5t32.5 54l36 171q-4 28 -18 53.5t-34.5 44t-46 29.5t-52.5 11q-49 0 -93 -24
t-77 -62.5t-52 -86t-19 -94.5q0 -37 11 -70t31 -58t48.5 -39.5t63.5 -14.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="468" 
d="M78 344h170l65 111l31 -10l-59 -101h92v-38h-115l-35 -60h150v-38h-173l-65 -111l-31 9l60 102h-90v38h112l35 60h-147v38z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="443" 
d="M363 314q-13 -12 -30.5 -20.5t-43.5 -8.5q-25 0 -45 6.5t-38 14.5t-34.5 14.5t-33.5 6.5t-28 -6t-18 -14l-25 28q11 13 28 22t42 9q22 0 41 -6.5t36.5 -14t35 -14t38.5 -6.5q36 0 51 15zM363 215q-13 -11 -30.5 -19t-43.5 -8q-25 0 -45 6.5t-38 14.5t-34.5 14.5
t-33.5 6.5t-28 -6t-18 -14l-25 28q11 13 28 22.5t42 9.5q22 0 41 -6.5t36.5 -14.5t35 -14.5t38.5 -6.5q36 0 51 16z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="458" 
d="M58 70v43h314v-43h-314zM58 376l307 155v-49l-234 -120l234 -120v-51l-307 155v30z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="475" 
d="M72 113h314v-43h-314v43zM79 242l235 120l-235 120l-6 49l313 -155v-30l-307 -155v51z" />
    <glyph glyph-name="Adblgrave" unicode="&#x200;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM440 918h50l39 -85l-32 -16zM328 918h52l37 -85l-32 -16z" />
    <glyph glyph-name="Ainvertedbreve" unicode="&#x202;" horiz-adv-x="649" 
d="M394 710h44l133 -710h-54l-44 234h-319l-142 -234h-55zM469 276l-66 372l-228 -372h294zM356 825q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32z" />
    <glyph glyph-name="Ddotbelow" unicode="&#x1e0c;" horiz-adv-x="688" 
d="M207 -151l19 90h46l-19 -90h-46zM178 710h225q66 0 114.5 -21t80 -57.5t47 -86t15.5 -106.5q0 -95 -30.5 -175t-85 -139t-129.5 -92t-163 -33h-225zM262 46q80 0 144 31.5t108.5 84.5t68.5 122.5t24 145.5q0 51 -13.5 94t-40.5 74t-67 48.5t-93 17.5h-174l-131 -618h174z
" />
    <glyph glyph-name="Edotbelow" unicode="&#x1eb8;" horiz-adv-x="584" 
d="M178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450zM195 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="Edblgrave" unicode="&#x204;" horiz-adv-x="584" 
d="M421 918h50l39 -85l-32 -16zM309 918h52l37 -85l-32 -16zM178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450z" />
    <glyph glyph-name="Einvertedbreve" unicode="&#x206;" horiz-adv-x="584" 
d="M342 825q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32zM178 710h441l-9 -46h-391l-59 -279h341l-10 -44h-340l-63 -295h399l-10 -46h-450z" />
    <glyph glyph-name="Gacute" unicode="&#x1f4;" horiz-adv-x="687" 
d="M455 816l-31 16l76 85h57zM546 110q-121 -115 -248 -115q-59 0 -105 25t-78 66.5t-49 95t-17 110.5q0 79 32.5 155t87.5 135.5t126.5 96t148.5 36.5q51 0 89 -11.5t65 -32t44.5 -49t26.5 -62.5l-46 -25q-20 70 -69 101.5t-121 31.5q-69 0 -129.5 -33.5t-105.5 -87
t-71.5 -120t-26.5 -132.5q0 -51 15 -97t42.5 -80.5t66 -55t86.5 -20.5q61 0 122 29.5t126 94.5l30 138h-152l9 41h196l-73 -345h-45z" />
    <glyph glyph-name="Hdotbelow" unicode="&#x1e24;" horiz-adv-x="706" 
d="M178 710h51l-69 -324h422l69 324h51l-151 -710h-51l72 340h-421l-73 -340h-51zM235 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="Idotbelow" unicode="&#x1eca;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM-3 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="Idblgrave" unicode="&#x208;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM229 918h50l39 -85l-32 -16zM117 918h52l37 -85l-32 -16z" />
    <glyph glyph-name="Iinvertedbreve" unicode="&#x20a;" horiz-adv-x="233" 
d="M178 709h51l-151 -709h-51zM146 825q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32z" />
    <glyph glyph-name="Odotbelow" unicode="&#x1ecc;" 
d="M246 -151l19 90h46l-19 -90h-46zM297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87t73 120.5t27 133
q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Odblgrave" unicode="&#x20c;" 
d="M489 918h50l39 -85l-32 -16zM377 918h52l37 -85l-32 -16zM297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5
t108 87t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Oinvertedbreve" unicode="&#x20e;" 
d="M396 825q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32zM297 -5q-60 0 -106.5 24t-78 64.5t-48 93.5t-16.5 110q0 82 34 159t90 137t129 96t150 36q60 0 106 -25t77.5 -66t47.5 -94t16 -109
q0 -81 -34 -158t-90 -136.5t-128 -95.5t-149 -36zM307 42q69 0 131 33.5t108 87t73 120.5t27 133q0 51 -14 96.5t-40.5 80t-65 55t-86.5 20.5q-69 0 -131 -33.5t-108.5 -87t-73.5 -120.5t-27 -134q0 -51 14 -96.5t40.5 -80t65 -54.5t87.5 -20z" />
    <glyph glyph-name="Rdotbelow" unicode="&#x1e5a;" horiz-adv-x="624" 
d="M27 0l151 710h282q36 0 65 -15.5t49 -41t30.5 -58t10.5 -67.5q0 -44 -16.5 -86.5t-45 -77t-66 -58.5t-80.5 -30l108 -276h-58l-106 268h-216l-57 -268h-51zM380 315q37 0 70.5 18.5t58 48t39 66.5t14.5 74q0 28 -8.5 54t-23.5 45.5t-36 31t-47 11.5h-228l-74 -349h235z
M197 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="Rdblgrave" unicode="&#x210;" horiz-adv-x="624" 
d="M421 918h50l39 -85l-32 -16zM309 918h52l37 -85l-32 -16zM27 0l151 710h282q36 0 65 -15.5t49 -41t30.5 -58t10.5 -67.5q0 -44 -16.5 -86.5t-45 -77t-66 -58.5t-80.5 -30l108 -276h-58l-106 268h-216l-57 -268h-51zM380 315q37 0 70.5 18.5t58 48t39 66.5t14.5 74
q0 28 -8.5 54t-23.5 45.5t-36 31t-47 11.5h-228l-74 -349h235z" />
    <glyph glyph-name="Rinvertedbreve" unicode="&#x212;" horiz-adv-x="624" 
d="M338 825q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32zM27 0l151 710h282q36 0 65 -15.5t49 -41t30.5 -58t10.5 -67.5q0 -44 -16.5 -86.5t-45 -77t-66 -58.5t-80.5 -30l108 -276h-58l-106 268h-216
l-57 -268h-51zM380 315q37 0 70.5 18.5t58 48t39 66.5t14.5 74q0 28 -8.5 54t-23.5 45.5t-36 31t-47 11.5h-228l-74 -349h235z" />
    <glyph glyph-name="Sdotbelow" unicode="&#x1e62;" horiz-adv-x="583" 
d="M240 -6q-158 0 -246 106l36 41q32 -47 87.5 -74.5t133.5 -27.5q101 0 160 42t59 121q0 55 -46.5 83.5t-134.5 48.5q-45 10 -79.5 22t-58.5 28.5t-36.5 40t-12.5 56.5q0 55 22 98.5t60 73.5t90.5 46t113.5 16q66 0 113.5 -20.5t79.5 -62.5l-35 -40q-48 77 -168 77
q-59 0 -100.5 -13.5t-68.5 -36.5t-39.5 -54.5t-12.5 -68.5q0 -26 10.5 -44t31.5 -31t52.5 -23t73.5 -20q45 -11 82.5 -23.5t63.5 -31t40.5 -44t14.5 -62.5q0 -54 -21.5 -96t-60 -70t-91 -42.5t-113.5 -14.5zM184 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="Tdotbelow" unicode="&#x1e6c;" horiz-adv-x="579" 
d="M168 -151l19 90h46l-19 -90h-46zM339 664h-246l9 46h544l-10 -46h-246l-142 -664h-50z" />
    <glyph glyph-name="Udotbelow" unicode="&#x1ee4;" 
d="M298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29
zM241 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="Udblgrave" unicode="&#x214;" 
d="M476 918h50l39 -85l-32 -16zM364 918h52l37 -85l-32 -16zM298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357
q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29z" />
    <glyph glyph-name="Uinvertedbreve" unicode="&#x216;" 
d="M389 825q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32zM298 -5q-62 0 -104 18.5t-67.5 50t-37 74t-11.5 91.5q0 59 14 124l76 357h51l-76 -357q-6 -30 -10 -59t-4 -57q0 -41 9 -76.5t30 -62t55 -41.5
t84 -15q68 0 118.5 27t86 71t58 100t34.5 113l75 357h51l-75 -357q-15 -72 -43.5 -136.5t-72.5 -114t-103.5 -78.5t-137.5 -29z" />
    <glyph glyph-name="Zdotbelow" unicode="&#x1e92;" horiz-adv-x="593" 
d="M-28 42l608 622h-470l10 46h531l-10 -42l-605 -622h471l-9 -46h-535zM181 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="zero.denominator" horiz-adv-x="434" 
d="M153 -5q-37 0 -63.5 14.5t-44 38t-25.5 52.5t-8 58q0 40 17.5 82t48.5 76t71.5 55.5t87.5 21.5q37 0 63 -15t43 -38t25 -52.5t8 -57.5q0 -41 -17.5 -82.5t-47.5 -75.5t-71 -55.5t-87 -21.5zM160 30q40 0 73.5 19.5t57 48.5t36.5 63t13 64q0 25 -7 48.5t-20.5 42.5
t-34.5 30.5t-49 11.5q-41 0 -74 -19.5t-56.5 -48.5t-36.5 -63.5t-13 -64.5q0 -24 6.5 -48t20.5 -42.5t34.5 -30t49.5 -11.5z" />
    <glyph glyph-name="one.denominator" horiz-adv-x="262" 
d="M-9 34h83l68 323q-6 -7 -19 -15t-28 -15.5t-31 -12.5t-28 -5l7 35q16 0 34.5 8t34.5 18t27 19t12 10h36l-77 -365h77l-7 -34h-196z" />
    <glyph glyph-name="five.denominator" horiz-adv-x="358" 
d="M108 -5q-47 0 -79.5 20.5t-43.5 54.5l29 19q11 -28 38 -44.5t65 -16.5q27 0 51 8.5t42.5 23.5t29.5 36t11 45q0 38 -28 58.5t-64 20.5q-30 0 -60 -13t-50 -35h-34q7 16 21.5 49t30 68.5t28.5 65t16 38.5h209l-8 -35h-181l-63 -142q21 17 47 25t54 8q53 0 85.5 -28.5
t32.5 -74.5q0 -33 -15 -60.5t-40 -48t-57.5 -31.5t-66.5 -11z" />
    <glyph glyph-name="six.denominator" horiz-adv-x="388" 
d="M135 -5q-57 0 -90.5 34t-33.5 94q0 57 15.5 106.5t44 86.5t68 58.5t86.5 21.5q42 0 72.5 -19t39.5 -54l-27 -22q-7 28 -32 45t-60 17q-57 0 -100.5 -44.5t-61.5 -127.5q21 32 60 52t80 20q56 0 89.5 -32t33.5 -80q0 -32 -15.5 -60.5t-41 -49.5t-58.5 -33.5t-69 -12.5z
M144 27q27 0 52 9.5t45 25.5t32 37.5t12 44.5q0 36 -27.5 62t-71.5 26q-27 0 -52.5 -9.5t-45.5 -25.5t-32 -37.5t-12 -44.5q0 -36 28 -62t72 -26z" />
    <glyph glyph-name="seven.denominator" horiz-adv-x="330" 
d="M276 358h-240l8 35h295l-304 -393h-40z" />
    <glyph glyph-name="nine.denominator" horiz-adv-x="388" 
d="M119 -5q-44 0 -74 19t-39 54l28 22q6 -29 31 -45.5t61 -16.5q56 0 100 45t62 126q-22 -32 -60.5 -51.5t-80.5 -19.5q-56 0 -90 32t-34 80q0 32 15.5 60.5t41 49.5t59 33.5t69.5 12.5q57 0 91 -34t34 -94q0 -57 -15.5 -106.5t-44 -86.5t-67.5 -58.5t-87 -21.5zM157 159
q27 0 52.5 9.5t45.5 25.5t32 37.5t12 44.5q0 36 -28 62t-71 26q-27 0 -52.5 -9.5t-45 -25.5t-31.5 -37.5t-12 -44.5q0 -37 27.5 -62.5t70.5 -25.5z" />
    <glyph glyph-name="zero.numerator" horiz-adv-x="433" 
d="M224 332q-37 0 -63.5 14.5t-44 37.5t-25.5 52.5t-8 58.5q0 40 17.5 82t48.5 76t71.5 55.5t87.5 21.5q37 0 63 -15t43 -38t25 -52.5t8 -57.5q0 -41 -17.5 -83t-47.5 -75.5t-70.5 -55t-87.5 -21.5zM120 499q0 -24 6.5 -48t20.5 -42.5t34.5 -30t49.5 -11.5q40 0 73.5 19.5
t57 48.5t36.5 63t13 64q0 25 -7 48.5t-20.5 42.5t-34 30.5t-48.5 11.5q-41 0 -74.5 -19.5t-57 -48.5t-36.5 -63.5t-13 -64.5z" />
    <glyph glyph-name="four.numerator" horiz-adv-x="364" 
d="M252 337l22 105h-218l7 33l283 255h24l-54 -253h55l-8 -35h-54l-22 -105h-35zM103 477h179l43 202z" />
    <glyph glyph-name="six.numerator" horiz-adv-x="388" 
d="M391 485q0 -32 -15.5 -60.5t-41 -50t-59 -34t-69.5 -12.5q-57 0 -91 34t-34 94q0 57 16 106.5t44.5 87t68 59t86.5 21.5q42 0 72.5 -19t39.5 -54l-28 -22q-6 28 -31 45t-61 17q-56 0 -99.5 -45t-61.5 -127q20 32 59.5 51.5t80.5 19.5q56 0 90 -31.5t34 -79.5zM214 361
q27 0 52.5 9.5t45.5 25.5t32 37.5t12 44.5q0 36 -27.5 62t-71.5 26q-27 0 -53 -9.5t-45.5 -25.5t-31.5 -37.5t-12 -44.5q0 -37 28 -62.5t71 -25.5z" />
    <glyph glyph-name="eight.numerator" horiz-adv-x="377" 
d="M370 461q0 -29 -15 -52.5t-39.5 -40.5t-56.5 -26.5t-65 -9.5q-59 0 -92.5 29t-33.5 70q0 23 10 41.5t26.5 33.5t37 25.5t40.5 16.5q-24 9 -41.5 27t-17.5 44t14.5 46.5t37.5 35t50.5 22t54.5 7.5q22 0 42.5 -5.5t36.5 -16.5t25.5 -26.5t9.5 -35.5q0 -37 -28 -61.5
t-66 -36.5q31 -11 50.5 -33t19.5 -54zM335 460q0 17 -8.5 30.5t-22 22.5t-31 14t-35.5 5q-23 0 -47 -7t-43 -19.5t-31 -29.5t-12 -38q0 -17 8.5 -30t22.5 -22t32 -14t36 -5q22 0 45.5 6.5t42.5 18.5t31 29.5t12 38.5zM159 620q0 -14 7.5 -24.5t19.5 -18t27 -11.5t31 -4
q18 0 38.5 5t37.5 15t28 24t11 32q0 28 -26 43.5t-61 15.5q-21 0 -41.5 -5.5t-36 -15.5t-25.5 -24t-10 -32z" />
    <glyph glyph-name="nine.numerator" horiz-adv-x="388" 
d="M94 574q0 32 15.5 60.5t41 49.5t59 33.5t69.5 12.5q57 0 91 -34t34 -94q0 -57 -15.5 -106.5t-44 -87t-68 -59t-87.5 -21.5q-42 0 -72.5 19.5t-39.5 54.5l28 21q6 -28 31 -45t61 -17q56 0 99.5 45t61.5 127q-20 -32 -59.5 -52t-80.5 -20q-27 0 -50 9t-39.5 24t-25.5 35.5
t-9 44.5zM271 697q-27 0 -52.5 -9.5t-45.5 -25.5t-32 -37t-12 -44q0 -37 28 -62.5t71 -25.5q27 0 52.5 9.5t45.5 25.5t32 37t12 44q0 36 -28 62t-71 26z" />
    <glyph glyph-name="adblgrave" unicode="&#x201;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM351 730h50l39 -85l-32 -16z
M239 730h52l37 -85l-32 -16z" />
    <glyph glyph-name="ainvertedbreve" unicode="&#x203;" horiz-adv-x="591" 
d="M212 -10q-43 0 -77.5 18t-58.5 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t92.5 72t109 27q33 0 59.5 -10t47.5 -26.5t36 -38.5t24 -47l24 113h51l-96 -448q-2 -8 -2 -12q0 -15 17 -15l-10 -45q-5 -1 -8.5 -1.5t-6.5 -0.5q-20 0 -30.5 10t-10.5 26q0 3 0.5 7.5
t2 12t3.5 19.5t6 31q-41 -52 -98 -83t-114 -31zM230 35q27 0 59 11.5t61.5 30.5t52.5 44.5t32 53.5l36 171q-4 28 -17.5 53t-34 44t-46.5 30t-56 11q-48 0 -91 -24t-75.5 -62.5t-52 -86.5t-19.5 -96q0 -36 11 -69t31 -57.5t48 -39t61 -14.5zM269 638q8 38 37 65t67 27
q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32z" />
    <glyph glyph-name="ddotbelow" unicode="&#x1e0d;" horiz-adv-x="591" 
d="M173 -151l19 90h46l-19 -90h-46zM214 -10q-44 0 -79 18t-59 48t-36.5 69.5t-12.5 83.5q0 61 24 118.5t64.5 102.5t93 72t109.5 27q32 0 58.5 -10t47.5 -26.5t36 -38.5t24 -47l68 323h51l-140 -658q-2 -10 -2 -12q0 -15 17 -15l-10 -45q-7 -1 -12 -1h-10q-34 5 -34 36
q0 3 0.5 6.5t1.5 11t3.5 19.5t6.5 32q-41 -52 -98 -83t-112 -31zM233 35q25 0 56.5 11t60.5 30.5t52.5 44.5t32.5 54l36 171q-4 28 -18 53.5t-34.5 44t-46 29.5t-52.5 11q-49 0 -93 -24t-77 -62.5t-52 -86t-19 -94.5q0 -37 11 -70t31 -58t48.5 -39.5t63.5 -14.5z" />
    <glyph glyph-name="edotbelow" unicode="&#x1eb9;" horiz-adv-x="529" 
d="M224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5
q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5
t-44 5.5zM169 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="edblgrave" unicode="&#x205;" horiz-adv-x="529" 
d="M348 730h50l39 -85l-32 -16zM236 730h52l37 -85l-32 -16zM224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17
q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5
t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5t-44 5.5z" />
    <glyph glyph-name="einvertedbreve" unicode="&#x207;" horiz-adv-x="529" 
d="M276 638q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32zM224 -10q-47 0 -83.5 17.5t-62 47.5t-38.5 70t-13 85q0 61 23.5 118.5t65.5 102t98.5 71.5t122.5 27q31 0 60 -9t51.5 -26.5t36 -43.5t13.5 -60
q0 -150 -272 -150q-32 0 -67.5 1t-78.5 4q-1 -8 -1.5 -17t-0.5 -17q0 -37 9.5 -70t29 -57.5t49 -39t68.5 -14.5q25 0 51.5 7.5t50.5 21t44.5 33t34.5 43.5l42 -13q-18 -30 -43.5 -54t-56 -41.5t-64.5 -27t-69 -9.5zM331 488q-47 0 -86.5 -17.5t-70.5 -46t-53 -65.5t-33 -76
q42 -3 75.5 -4t65.5 -1q62 0 104.5 8.5t68.5 23.5t37.5 35.5t11.5 45.5t-10.5 43.5t-27 30.5t-38.5 17.5t-44 5.5z" />
    <glyph glyph-name="gacute" unicode="&#x1f5;" horiz-adv-x="586" 
d="M322 629l-31 16l76 85h57zM165 -223q-86 0 -130.5 32.5t-63.5 87.5l38 23q18 -53 63 -77.5t102 -24.5q37 0 73 11t65.5 32.5t51 53.5t30.5 75l24 114q-20 -25 -45 -45.5t-52 -35.5t-56 -23t-56 -8q-43 0 -77 18t-57.5 48t-36 69t-12.5 82q0 60 24 117.5t64.5 102.5
t93.5 72.5t112 27.5q64 0 104.5 -35.5t63.5 -86.5l24 113h45l-113 -530q-11 -53 -38.5 -93t-65.5 -66.5t-83 -40t-92 -13.5zM231 35q31 0 63.5 12.5t60.5 32.5t49 45t30 50l36 171q-5 30 -18.5 55.5t-33.5 43.5t-46 28.5t-54 10.5q-51 0 -94.5 -25t-76 -63.5t-51 -86
t-18.5 -92.5q0 -37 11 -70t31 -58t48 -39.5t63 -14.5z" />
    <glyph glyph-name="hdotbelow" unicode="&#x1e25;" horiz-adv-x="552" 
d="M167 730h50l-71 -334q43 61 104 97t123 36q63 0 91 -35.5t28 -99.5q0 -21 -2.5 -45t-8.5 -50l-63 -299h-50l61 290q10 47 10 83q0 110 -91 110q-30 0 -61.5 -12t-61 -34t-54.5 -51.5t-41 -63.5l-68 -322h-50zM164 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="idotbelow" unicode="&#x1ecb;" horiz-adv-x="201" 
d="M122 520h50l-110 -520h-50zM167 730h50l-19 -92h-50zM-19 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="idblgrave" unicode="&#x209;" horiz-adv-x="201" 
d="M164 730h50l39 -85l-32 -16zM52 730h52l37 -85l-32 -16zM122 520h50l-110 -520h-50z" />
    <glyph glyph-name="iinvertedbreve" unicode="&#x20b;" horiz-adv-x="201" 
d="M88 638q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32zM122 520h50l-110 -520h-50z" />
    <glyph glyph-name="odotbelow" unicode="&#x1ecd;" horiz-adv-x="569" 
d="M166 -151l19 90h46l-19 -90h-46zM218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35q45 0 89.5 22t79 59t56 85.5
t21.5 101.5q0 38 -11 71t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="odblgrave" unicode="&#x20d;" horiz-adv-x="569" 
d="M362 730h50l39 -85l-32 -16zM250 730h52l37 -85l-32 -16zM218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35
q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="oinvertedbreve" unicode="&#x20f;" horiz-adv-x="569" 
d="M274 638q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32zM218 -10q-45 0 -80.5 17t-60 46.5t-38 69t-13.5 84.5q0 63 25.5 121.5t68.5 103t98.5 71t114.5 26.5q44 0 79.5 -17.5t60 -47t38 -69.5t13.5 -85
q0 -62 -25.5 -120t-68 -102.5t-97.5 -71t-115 -26.5zM227 35q45 0 89.5 22t79 59t56 85.5t21.5 101.5q0 38 -11 71t-30.5 57.5t-47.5 38.5t-61 14q-45 0 -88.5 -22t-78.5 -59.5t-56.5 -86.5t-21.5 -103q0 -38 11 -70.5t30.5 -56.5t47 -37.5t60.5 -13.5z" />
    <glyph glyph-name="rdotbelow" unicode="&#x1e5b;" horiz-adv-x="333" 
d="M122 520h48l-28 -128q42 60 96.5 94.5t105.5 34.5q10 0 14 -1l-10 -45q-67 -2 -124.5 -41.5t-92.5 -108.5l-69 -325h-50zM-18 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="rdblgrave" unicode="&#x211;" horiz-adv-x="333" 
d="M255 730h50l39 -85l-32 -16zM143 730h52l37 -85l-32 -16zM122 520h48l-28 -128q42 60 96.5 94.5t105.5 34.5q10 0 14 -1l-10 -45q-67 -2 -124.5 -41.5t-92.5 -108.5l-69 -325h-50z" />
    <glyph glyph-name="rinvertedbreve" unicode="&#x213;" horiz-adv-x="333" 
d="M167 640q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32zM122 520h48l-28 -128q42 60 96.5 94.5t105.5 34.5q10 0 14 -1l-10 -45q-67 -2 -124.5 -41.5t-92.5 -108.5l-69 -325h-50z" />
    <glyph glyph-name="sdotbelow" unicode="&#x1e63;" horiz-adv-x="461" 
d="M167 -10q-57 0 -105.5 20t-78.5 60l31 34q33 -38 71.5 -56t89.5 -18q36 0 66.5 9t53 25t35 39t12.5 52q0 38 -35 56t-100 35q-35 9 -61 18t-43.5 20.5t-26.5 27.5t-9 39q0 43 18 76.5t48 56t69.5 34t82.5 11.5q58 0 98.5 -20.5t57.5 -52.5l-33 -27q-17 30 -53.5 45
t-81.5 15q-31 0 -59 -7.5t-49.5 -23t-34.5 -39t-13 -55.5q0 -17 7 -28.5t20.5 -20t34.5 -15t49 -13.5q39 -10 69.5 -20t52 -23.5t32.5 -32t11 -44.5q0 -39 -17 -71.5t-47 -56t-71.5 -36.5t-90.5 -13zM116 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="tdotbelow" unicode="&#x1e6d;" horiz-adv-x="306" 
d="M69 -151l19 90h46l-19 -90h-46zM129 -6q-35 0 -58 18t-23 53q0 5 0.5 10.5t1.5 11.5l84 392h-70l9 41h70l37 178h50l-37 -178h115l-9 -41h-115l-75 -352q-7 -32 -7 -40q0 -23 14.5 -34.5t35.5 -11.5q15 0 28.5 3t24.5 7.5t18.5 8t10.5 5.5l6 -40q-4 -3 -14.5 -8
t-25.5 -10.5t-33 -9t-38 -3.5z" />
    <glyph glyph-name="udotbelow" unicode="&#x1ee5;" horiz-adv-x="562" 
d="M162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7
t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35zM151 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="udblgrave" unicode="&#x215;" horiz-adv-x="562" 
d="M346 730h50l39 -85l-32 -16zM234 730h52l37 -85l-32 -16zM162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45
q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35z" />
    <glyph glyph-name="uinvertedbreve" unicode="&#x217;" horiz-adv-x="562" 
d="M258 638q8 38 37 65t67 27q33 0 51 -22t18 -51v-9.5t-2 -9.5h-31q0 5 0.5 9t0.5 9q0 46 -40 46q-29 0 -46 -19t-23 -45h-32zM162 -10q-121 0 -121 136q0 20 2.5 43.5t8.5 50.5l65 300h49l-62 -293q-11 -50 -11 -85q0 -107 93 -107q31 0 63 11.5t61.5 32t55.5 49.5t44 64
l69 328h50l-92 -435q-2 -8 -3.5 -15t-1.5 -9q0 -16 17 -16l-10 -45q-7 -1 -12 -1h-10q-16 0 -24.5 10.5t-8.5 24.5q0 3 0.5 7t2 13t5 25.5t9.5 43.5q-46 -63 -109.5 -98t-129.5 -35z" />
    <glyph glyph-name="zdotbelow" unicode="&#x1e93;" horiz-adv-x="463" 
d="M-28 36l440 445h-339l9 39h396l-8 -36l-438 -445h343l-9 -39h-402zM114 -151l19 90h46l-19 -90h-46z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="811" 
d="M58 47h201q-91 45 -142.5 130t-51.5 188q0 69 25 132.5t69 111.5t104 77t131 29q70 0 130.5 -29t104 -77t68.5 -111.5t25 -132.5q0 -103 -51.5 -188t-142.5 -130h202v-47h-289v47q55 13 98 45.5t72 76.5t44.5 95t15.5 101q0 56 -19 110.5t-55 97t-87.5 68.5t-115.5 26
q-65 0 -116 -26t-87 -68.5t-55 -97t-19 -110.5q0 -50 15.5 -101t44.5 -95t72 -76.5t98 -45.5v-47h-289v47z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="811" 
d="M58 47h201q-91 45 -142.5 130t-51.5 188q0 69 25 132.5t69 111.5t104 77t131 29q70 0 130.5 -29t104 -77t68.5 -111.5t25 -132.5q0 -103 -51.5 -188t-142.5 -130h202v-47h-289v47q55 13 98 45.5t72 76.5t44.5 95t15.5 101q0 56 -19 110.5t-55 97t-87.5 68.5t-115.5 26
q-65 0 -116 -26t-87 -68.5t-55 -97t-19 -110.5q0 -50 15.5 -101t44.5 -95t72 -76.5t98 -45.5v-47h-289v47z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="566" 
d="M253 -10q-48 0 -88 16t-69.5 43.5t-45.5 65t-16 80.5q0 48 19.5 91t53.5 76t78.5 52.5t94.5 19.5q67 0 118 -36.5t67 -103.5q4 24 6 59.5t2 62.5q0 71 -12.5 119.5t-35.5 78.5t-55.5 43t-72.5 13q-51 0 -95 -17.5t-67 -45.5l-29 30q33 39 85 58.5t110 19.5
q109 0 164.5 -74t55.5 -242q0 -101 -19.5 -177.5t-55 -128t-84.5 -77.5t-109 -26zM253 35q45 0 81.5 18t62 46.5t39 63.5t13.5 68t-13 62t-36 50.5t-53.5 34.5t-66.5 13q-39 0 -75 -15.5t-63 -42t-43.5 -61.5t-16.5 -74q0 -34 12.5 -63.5t35.5 -51.5t54 -35t69 -13z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="646" 
d="M593 41v-41h-600v41l279 669h43zM537 46l-243 602l-245 -602h488z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="620" 
d="M-21 41l279 669h43l278 -669v-41h-600v41zM523 46l-243 602l-245 -602h488z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="715" 
d="M545 664h-336v-754h-49v754h-86v46h600v-46h-80v-754h-49v754z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="585" 
d="M507 667h-361v-4l214 -294l-223 -316v-4h385v-49h-447v41l229 323l-224 309v37h427v-43z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="395" 
d="M21 365h123l80 -316l123 741h49l-143 -790h-59l-92 326h-81v39z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="696" 
d="M211 517q29 0 51.5 -9t39.5 -25t30.5 -36.5t24.5 -43.5q11 23 24 43.5t30.5 36.5t39.5 25t51 9q35 0 63.5 -13t48.5 -35.5t31 -53t11 -64.5t-12 -64t-32.5 -52.5t-49 -35.5t-61.5 -13q-28 0 -49.5 9t-38.5 24.5t-30 35.5t-24 42q-11 -22 -24 -42t-30.5 -35.5t-40.5 -24.5
t-53 -9q-31 0 -59 13t-49 35.5t-33.5 53t-12.5 64.5t12 64t32.5 52.5t48.5 35.5t61 13zM211 228q25 0 44 10t34 26.5t27.5 38.5t23.5 47q-11 25 -23.5 48t-27.5 39.5t-34 26.5t-44 10q-20 0 -40 -7.5t-36 -22.5t-26 -38t-10 -54t10 -54.5t26 -39t36 -23t40 -7.5zM502 228
q20 0 40 7.5t36 23t26 39t10 54.5t-10 54t-26 38t-36 22.5t-40 7.5q-26 0 -44.5 -10.5t-33.5 -28t-26.5 -40.5t-21.5 -49q11 -24 23 -45.5t27 -37.5t33.5 -25.5t42.5 -9.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="283" 
d="M106 677q-2 28 -2.5 37t-0.5 14q0 40 23.5 66t65.5 26q20 0 45 -6t45 -15l-13 -36q-14 6 -33 11t-30 5q-26 0 -41.5 -16.5t-15.5 -48.5v-12.5t2 -32.5l38 -694q2 -27 2 -33.5v-9.5q0 -48 -23.5 -73.5t-65.5 -25.5q-19 0 -43.5 6t-44.5 15l13 36q15 -6 32 -11t30 -5
q26 0 41.5 18.5t15.5 53.5v7.5t-2 27.5z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="491" 
d="M286 730l186 -364l-186 -366h-55l-186 366l186 364h55zM422 354l-166 335l-157 -314l166 -332z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="653" 
d="M493 -6q-42 0 -67 25.5t-25 68.5v386h-217v-474h-49v474h-80v46h505v-46h-110v-371q0 -29 17 -46t46 -17q11 0 26 2.5t28 7.5l11 -40q-17 -7 -42.5 -11.5t-42.5 -4.5z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="613" 
d="M303 18q69 0 118.5 22t78.5 54l18 -21q-35 -35 -87.5 -59t-127.5 -24q-59 0 -107.5 22t-83 59t-53.5 86t-19 103q0 55 19.5 104t54 86t83 59t106.5 22t107 -22t83.5 -59.5t54 -87.5t19.5 -107h-439v-150q31 -41 76 -64t99 -23zM303 503q-54 0 -99 -23.5t-76 -65.5v-130
h353v129q-32 42 -78.5 66t-99.5 24z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" horiz-adv-x="324" 
d="M169 -6q-45 0 -69.5 25.5t-24.5 74.5v516q0 28 8 51.5t22 41t32.5 27.5t40.5 10q44 0 71 -36t27 -94q0 -39 -13 -84.5t-34.5 -91t-49.5 -87t-58 -71.5v-174q0 -29 18 -46t49 -17q12 0 27.5 4.5t29.5 9.5l10 -40q-19 -8 -42.5 -13.5t-43.5 -5.5zM233 604q0 45 -14 69.5
t-40 24.5t-42 -24.5t-16 -63.5v-278q22 27 42.5 62.5t36 73t24.5 73t9 63.5z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1077" 
d="M208 614l-130 -614h-51l151 710h39l337 -625l132 624h51l-151 -709h-46zM894 351q-32 0 -57 12t-42.5 32t-27 46.5t-9.5 56.5q0 42 17.5 81t46.5 68.5t68 47.5t81 18q31 0 56.5 -11.5t43 -31.5t27 -47t9.5 -57q0 -42 -17.5 -80.5t-46.5 -68.5t-68 -48t-81 -18zM802 503
q0 -48 27.5 -81t72.5 -33q30 0 58.5 14t51 37.5t36.5 54.5t14 65q0 49 -27 82t-72 33q-30 0 -59 -14t-51.5 -37.5t-36.5 -54.5t-14 -66z" />
    <hkern u1="A" u2="&#x142;" k="7" />
    <hkern u1="A" u2="&#x141;" k="11" />
    <hkern u1="A" u2="&#xd0;" k="11" />
    <hkern u1="A" u2="&#x110;" k="11" />
    <hkern g1="A.sc" u2="&#x2120;" k="28" />
    <hkern g1="A.sc" u2="&#x2122;" k="33" />
    <hkern g1="A.sc" u2="&#x20;" k="32" />
    <hkern g1="A.sc" u2="&#xae;" k="42" />
    <hkern g1="A.sc" u2="&#x3f;" k="35" />
    <hkern g1="A.sc" u2="&#xba;" k="27" />
    <hkern g1="A.sc" u2="&#xaa;" k="24" />
    <hkern g1="A.sc" u2="&#xa9;" k="42" />
    <hkern g1="A.sc" u2="&#xa6;" k="36" />
    <hkern g1="A.sc" u2="&#x7c;" k="39" />
    <hkern g1="A.sc" u2="\" k="81" />
    <hkern g1="A.sc" u2="&#x2a;" k="35" />
    <hkern g1="A.sc" u2="&#x2bc;" k="58" />
    <hkern g1="A.sc" g2="Y.sc" k="63" />
    <hkern g1="A.sc" g2="W.sc" k="50" />
    <hkern g1="A.sc" g2="V.sc" k="50" />
    <hkern g1="A.sc" u2="V" k="91" />
    <hkern g1="A.sc" g2="U.sc" k="28" />
    <hkern g1="A.sc" g2="T.sc" k="62" />
    <hkern g1="A.sc" g2="S.sc" k="11" />
    <hkern g1="A.sc" u2="&#xd0;" k="11" />
    <hkern u1="&#xc6;" u2="&#xdf;" k="8" />
    <hkern u1="&#x1fc;" u2="&#xdf;" k="8" />
    <hkern u1="&#xc1;" u2="&#x141;" k="11" />
    <hkern u1="&#xc1;" u2="&#xd0;" k="11" />
    <hkern u1="&#xc1;" u2="&#x110;" k="11" />
    <hkern u1="&#x102;" u2="&#x141;" k="11" />
    <hkern u1="&#x102;" u2="&#xd0;" k="11" />
    <hkern u1="&#x102;" u2="&#x110;" k="11" />
    <hkern u1="&#xc2;" u2="&#x141;" k="11" />
    <hkern u1="&#xc2;" u2="&#xd0;" k="11" />
    <hkern u1="&#xc2;" u2="&#x110;" k="11" />
    <hkern u1="&#xc4;" u2="&#x141;" k="11" />
    <hkern u1="&#xc4;" u2="&#xd0;" k="11" />
    <hkern u1="&#xc4;" u2="&#x110;" k="11" />
    <hkern u1="&#xc0;" u2="&#x141;" k="11" />
    <hkern u1="&#xc0;" u2="&#xd0;" k="11" />
    <hkern u1="&#xc0;" u2="&#x110;" k="11" />
    <hkern u1="&#x100;" u2="&#x141;" k="11" />
    <hkern u1="&#x100;" u2="&#xd0;" k="11" />
    <hkern u1="&#x100;" u2="&#x110;" k="11" />
    <hkern u1="&#x104;" u2="j" k="-80" />
    <hkern u1="&#x104;" u2="&#x141;" k="11" />
    <hkern u1="&#x104;" u2="&#xd0;" k="11" />
    <hkern u1="&#x104;" u2="&#x110;" k="11" />
    <hkern u1="&#xc5;" u2="&#x141;" k="11" />
    <hkern u1="&#xc5;" u2="&#xd0;" k="11" />
    <hkern u1="&#xc5;" u2="&#x110;" k="11" />
    <hkern u1="&#x1fa;" u2="&#x141;" k="11" />
    <hkern u1="&#x1fa;" u2="&#xd0;" k="11" />
    <hkern u1="&#x1fa;" u2="&#x110;" k="11" />
    <hkern u1="&#xc3;" u2="&#x141;" k="11" />
    <hkern u1="&#xc3;" u2="&#xd0;" k="11" />
    <hkern u1="&#xc3;" u2="&#x110;" k="11" />
    <hkern u1="B" u2="&#x2bc;" k="61" />
    <hkern u1="B" u2="X" k="10" />
    <hkern u1="B" g2="W.alt" k="7" />
    <hkern u1="B" u2="V" k="21" />
    <hkern u1="B" u2="&#x166;" k="18" />
    <hkern u1="B" u2="&#x141;" k="6" />
    <hkern u1="B" g2="J.sc" k="6" />
    <hkern u1="B" u2="&#xd0;" k="6" />
    <hkern u1="B" u2="&#x110;" k="6" />
    <hkern g1="B.sc" u2="&#x2122;" k="10" />
    <hkern g1="B.sc" u2="&#x3f;" k="22" />
    <hkern g1="B.sc" u2="&#xa6;" k="16" />
    <hkern g1="B.sc" u2="&#x7c;" k="18" />
    <hkern g1="B.sc" u2="\" k="34" />
    <hkern g1="B.sc" u2="&#x2a;" k="10" />
    <hkern g1="B.sc" u2="&#x2bc;" k="34" />
    <hkern g1="B.sc" g2="Y.sc" k="24" />
    <hkern g1="B.sc" g2="X.sc" k="9" />
    <hkern g1="B.sc" g2="W.sc" k="18" />
    <hkern g1="B.sc" g2="V.sc" k="18" />
    <hkern g1="B.sc" u2="V" k="45" />
    <hkern g1="B.sc" g2="T.sc" k="20" />
    <hkern g1="B.sc" u2="&#xd0;" k="6" />
    <hkern g1="B.sc" g2="A.sc" k="17" />
    <hkern g1="C.sc" u2="&#x2122;" k="9" />
    <hkern g1="C.sc" u2="&#x3f;" k="19" />
    <hkern g1="C.sc" u2="&#xa6;" k="11" />
    <hkern g1="C.sc" u2="&#x7c;" k="13" />
    <hkern g1="C.sc" u2="\" k="27" />
    <hkern g1="C.sc" u2="&#x2a;" k="12" />
    <hkern g1="C.sc" u2="&#x2bc;" k="34" />
    <hkern g1="C.sc" g2="Y.sc" k="13" />
    <hkern g1="C.sc" g2="W.sc" k="10" />
    <hkern g1="C.sc" g2="V.sc" k="9" />
    <hkern g1="C.sc" u2="V" k="35" />
    <hkern g1="C.sc" g2="T.sc" k="9" />
    <hkern g1="C.sc" g2="A.sc" k="12" />
    <hkern u1="D" u2="&#x166;" k="3" />
    <hkern u1="D" u2="&#x141;" k="7" />
    <hkern u1="D" u2="&#xd0;" k="7" />
    <hkern u1="D" u2="&#x110;" k="7" />
    <hkern g1="D.sc" u2="&#x2120;" k="8" />
    <hkern g1="D.sc" u2="&#x2122;" k="13" />
    <hkern g1="D.sc" u2="&#x3f;" k="27" />
    <hkern g1="D.sc" u2="&#x29;" k="10" />
    <hkern g1="D.sc" u2="&#xa6;" k="17" />
    <hkern g1="D.sc" u2="&#x7c;" k="19" />
    <hkern g1="D.sc" u2="\" k="44" />
    <hkern g1="D.sc" u2="&#x2a;" k="18" />
    <hkern g1="D.sc" u2="&#x2bc;" k="41" />
    <hkern g1="D.sc" g2="Z.sc" k="10" />
    <hkern g1="D.sc" g2="Y.sc" k="38" />
    <hkern g1="D.sc" g2="X.sc" k="22" />
    <hkern g1="D.sc" u2="X" k="14" />
    <hkern g1="D.sc" g2="W.sc" k="24" />
    <hkern g1="D.sc" g2="V.sc" k="23" />
    <hkern g1="D.sc" u2="V" k="48" />
    <hkern g1="D.sc" g2="T.sc" k="41" />
    <hkern g1="D.sc" g2="J.sc" k="16" />
    <hkern g1="D.sc" u2="&#xd0;" k="9" />
    <hkern g1="D.sc" g2="A.sc" k="21" />
    <hkern u1="&#x10e;" u2="&#x166;" k="3" />
    <hkern u1="&#x10e;" u2="&#x141;" k="7" />
    <hkern u1="&#x10e;" u2="&#xd0;" k="7" />
    <hkern u1="&#x10e;" u2="&#x110;" k="7" />
    <hkern u1="&#x110;" u2="&#x166;" k="3" />
    <hkern u1="&#x110;" u2="&#x141;" k="7" />
    <hkern u1="&#x110;" u2="&#xd0;" k="7" />
    <hkern u1="&#x110;" u2="&#x110;" k="7" />
    <hkern u1="E" u2="&#x129;" k="-14" />
    <hkern u1="E" u2="&#x12b;" k="-22" />
    <hkern u1="E" u2="&#xdf;" k="8" />
    <hkern u1="E" u2="&#x131;" k="9" />
    <hkern g1="E.sc" u2="&#x2bc;" k="22" />
    <hkern g1="E.sc" g2="S.sc" k="8" />
    <hkern u1="&#xc9;" u2="&#xdf;" k="8" />
    <hkern u1="&#x114;" u2="&#xdf;" k="8" />
    <hkern u1="&#x11a;" u2="&#xdf;" k="8" />
    <hkern u1="&#xca;" u2="&#xdf;" k="8" />
    <hkern u1="&#xcb;" u2="&#xdf;" k="8" />
    <hkern u1="&#x116;" u2="&#xdf;" k="8" />
    <hkern u1="&#xc8;" u2="&#xdf;" k="8" />
    <hkern u1="&#x112;" u2="&#xdf;" k="8" />
    <hkern u1="&#x14a;" u2="j" k="-8" />
    <hkern u1="&#x118;" u2="&#xdf;" k="8" />
    <hkern u1="&#xd0;" u2="&#x166;" k="3" />
    <hkern u1="&#xd0;" u2="&#x141;" k="7" />
    <hkern u1="&#xd0;" u2="&#xd0;" k="7" />
    <hkern u1="&#xd0;" u2="&#x110;" k="7" />
    <hkern u1="&#x1ebc;" u2="&#xdf;" k="8" />
    <hkern u1="&#x20ac;" g2="seven.lnum" k="10" />
    <hkern u1="&#x20ac;" g2="four.lnum" k="11" />
    <hkern u1="F" u2="&#x18f;" k="23" />
    <hkern u1="F" u2="&#x30;" k="10" />
    <hkern u1="F" g2="y.alt" k="14" />
    <hkern u1="F" u2="x" k="29" />
    <hkern u1="F" g2="w.alt" k="23" />
    <hkern u1="F" u2="v" k="20" />
    <hkern u1="F" u2="&#x32;" k="41" />
    <hkern u1="F" u2="&#x33;" k="30" />
    <hkern u1="F" g2="t.alt" k="15" />
    <hkern u1="F" u2="&#x20;" k="20" />
    <hkern u1="F" u2="&#x37;" k="32" />
    <hkern u1="F" u2="&#x259;" k="52" />
    <hkern u1="F" u2="&#x31;" k="18" />
    <hkern u1="F" u2="&#x39;" k="11" />
    <hkern u1="F" u2="&#x142;" k="7" />
    <hkern u1="F" u2="&#x129;" k="-14" />
    <hkern u1="F" u2="&#x12b;" k="-24" />
    <hkern u1="F" u2="&#xdf;" k="24" />
    <hkern u1="F" u2="&#x34;" k="74" />
    <hkern u1="F" u2="&#x35;" k="30" />
    <hkern u1="F" u2="&#xf0;" k="53" />
    <hkern u1="F" u2="&#x131;" k="52" />
    <hkern u1="F" u2="&#x40;" k="16" />
    <hkern u1="F" u2="&#x2bc;" k="37" />
    <hkern u1="F" g2="Z.sc" k="17" />
    <hkern u1="F" g2="Y.sc" k="13" />
    <hkern u1="F" g2="X.sc" k="15" />
    <hkern u1="F" g2="W.sc" k="13" />
    <hkern u1="F" g2="V.sc" k="13" />
    <hkern u1="F" g2="U.sc" k="27" />
    <hkern u1="F" g2="T.sc" k="9" />
    <hkern u1="F" g2="S.sc" k="42" />
    <hkern u1="F" u2="&#x2f;" k="80" />
    <hkern u1="F" g2="J.sc" k="118" />
    <hkern u1="F" g2="A.sc" k="93" />
    <hkern g1="F.sc" u2="&#x20;" k="17" />
    <hkern g1="F.sc" u2="&#x2bc;" k="21" />
    <hkern g1="F.sc" u2="X" k="16" />
    <hkern g1="F.sc" g2="S.sc" k="18" />
    <hkern g1="F.sc" u2="&#x2f;" k="31" />
    <hkern g1="F.sc" g2="J.sc" k="123" />
    <hkern g1="F.sc" g2="A.sc" k="60" />
    <hkern u1="G" u2="&#x166;" k="20" />
    <hkern g1="G.alt" g2="y.alt" k="5" />
    <hkern g1="G.alt" u2="v" k="6" />
    <hkern g1="G.alt" u2="&#x2122;" k="11" />
    <hkern g1="G.alt" u2="&#x37;" k="10" />
    <hkern g1="G.alt" u2="&#x2a;" k="9" />
    <hkern g1="G.alt" u2="&#x2bc;" k="79" />
    <hkern g1="G.alt" u2="X" k="21" />
    <hkern g1="G.alt" g2="W.alt" k="6" />
    <hkern g1="G.alt" u2="V" k="23" />
    <hkern g1="G.alt" u2="&#x166;" k="18" />
    <hkern g1="G.alt" u2="&#x2f;" k="15" />
    <hkern g1="G.alt" g2="J.sc" k="12" />
    <hkern g1="G.alt" g2="A.sc" k="9" />
    <hkern g1="G.sc" u2="&#x2122;" k="11" />
    <hkern g1="G.sc" u2="&#x3f;" k="21" />
    <hkern g1="G.sc" u2="&#xa6;" k="15" />
    <hkern g1="G.sc" u2="&#x7c;" k="16" />
    <hkern g1="G.sc" u2="\" k="35" />
    <hkern g1="G.sc" u2="&#x2a;" k="14" />
    <hkern g1="G.sc" u2="&#x2bc;" k="36" />
    <hkern g1="G.sc" g2="Y.sc" k="25" />
    <hkern g1="G.sc" g2="W.sc" k="18" />
    <hkern g1="G.sc" g2="V.sc" k="17" />
    <hkern g1="G.sc" u2="V" k="46" />
    <hkern g1="G.sc" g2="T.sc" k="21" />
    <hkern g1="G.sc" u2="&#xd0;" k="6" />
    <hkern u1="&#x11e;" u2="&#x166;" k="20" />
    <hkern u1="&#x11c;" u2="&#x166;" k="20" />
    <hkern u1="&#x122;" u2="&#x166;" k="20" />
    <hkern u1="&#x120;" u2="&#x166;" k="20" />
    <hkern u1="&#x126;" g2="q.alt" k="13" />
    <hkern u1="&#x126;" u2="q" k="13" />
    <hkern u1="&#x126;" u2="&#x121;" k="13" />
    <hkern u1="&#x126;" u2="g" k="13" />
    <hkern u1="&#x126;" g2="d.alt" k="13" />
    <hkern u1="&#x126;" u2="d" k="13" />
    <hkern u1="&#x126;" g2="a.alt" k="13" />
    <hkern u1="&#x126;" u2="a" k="13" />
    <hkern u1="&#x126;" u2="&#x141;" k="7" />
    <hkern u1="&#x126;" u2="&#xd0;" k="7" />
    <hkern u1="&#x126;" u2="&#x110;" k="7" />
    <hkern g1="J.sc" u2="\" k="12" />
    <hkern g1="J.sc" u2="&#x2bc;" k="23" />
    <hkern g1="J.sc" u2="V" k="26" />
    <hkern g1="J.sc" g2="J.sc" k="6" />
    <hkern g1="J.sc" g2="A.sc" k="5" />
    <hkern u1="K" u2="&#x129;" k="-44" />
    <hkern u1="K" u2="&#x12b;" k="-57" />
    <hkern g1="K.sc" u2="&#x20;" k="10" />
    <hkern g1="K.sc" u2="&#x2bc;" k="18" />
    <hkern u1="&#x136;" u2="&#x12b;" k="-57" />
    <hkern u1="L" u2="&#x141;" k="7" />
    <hkern u1="L" u2="&#xd0;" k="7" />
    <hkern u1="L" u2="&#x110;" k="7" />
    <hkern g1="L.sc" u2="&#x2120;" k="46" />
    <hkern g1="L.sc" u2="&#x2122;" k="51" />
    <hkern g1="L.sc" u2="&#x20;" k="25" />
    <hkern g1="L.sc" u2="&#xae;" k="75" />
    <hkern g1="L.sc" u2="&#x3f;" k="31" />
    <hkern g1="L.sc" u2="&#xba;" k="45" />
    <hkern g1="L.sc" u2="&#xaa;" k="45" />
    <hkern g1="L.sc" u2="&#xa9;" k="75" />
    <hkern g1="L.sc" u2="&#xa6;" k="26" />
    <hkern g1="L.sc" u2="&#x7c;" k="29" />
    <hkern g1="L.sc" u2="\" k="98" />
    <hkern g1="L.sc" u2="&#x2a;" k="49" />
    <hkern g1="L.sc" u2="&#x2bc;" k="69" />
    <hkern g1="L.sc" g2="Y.sc" k="97" />
    <hkern g1="L.sc" g2="W.sc" k="90" />
    <hkern g1="L.sc" g2="V.sc" k="90" />
    <hkern g1="L.sc" u2="V" k="86" />
    <hkern g1="L.sc" g2="U.sc" k="38" />
    <hkern g1="L.sc" g2="T.sc" k="108" />
    <hkern g1="L.sc" g2="S.sc" k="6" />
    <hkern g1="L.sc" u2="&#xb7;" k="77" />
    <hkern u1="&#x139;" u2="&#x141;" k="7" />
    <hkern u1="&#x139;" u2="&#xd0;" k="7" />
    <hkern u1="&#x139;" u2="&#x110;" k="7" />
    <hkern u1="&#x13d;" u2="&#x141;" k="7" />
    <hkern u1="&#x13d;" u2="&#xd0;" k="7" />
    <hkern u1="&#x13d;" u2="&#x110;" k="7" />
    <hkern u1="&#x13b;" u2="&#x141;" k="7" />
    <hkern u1="&#x13b;" u2="&#xd0;" k="7" />
    <hkern u1="&#x13b;" u2="&#x110;" k="7" />
    <hkern u1="&#xb7;" u2="&#x33;" k="35" />
    <hkern u1="&#xb7;" g2="seven.lnum" k="28" />
    <hkern u1="&#xb7;" u2="&#x37;" k="35" />
    <hkern u1="&#xb7;" u2="&#x31;" k="15" />
    <hkern u1="&#xb7;" u2="&#x34;" k="14" />
    <hkern u1="&#x141;" u2="&#x141;" k="7" />
    <hkern u1="&#x141;" u2="&#xd0;" k="7" />
    <hkern u1="&#x141;" u2="&#x110;" k="7" />
    <hkern u1="O" u2="&#x166;" k="3" />
    <hkern u1="O" u2="&#x141;" k="8" />
    <hkern u1="O" u2="&#xd0;" k="8" />
    <hkern u1="O" u2="&#x110;" k="8" />
    <hkern g1="O.sc" u2="&#xd0;" k="9" />
    <hkern u1="&#x152;" u2="&#xdf;" k="8" />
    <hkern u1="&#xd3;" u2="&#x166;" k="3" />
    <hkern u1="&#xd3;" u2="&#x141;" k="8" />
    <hkern u1="&#xd3;" u2="&#xd0;" k="8" />
    <hkern u1="&#xd3;" u2="&#x110;" k="8" />
    <hkern u1="&#x14e;" u2="&#x166;" k="3" />
    <hkern u1="&#x14e;" u2="&#x141;" k="8" />
    <hkern u1="&#x14e;" u2="&#xd0;" k="8" />
    <hkern u1="&#x14e;" u2="&#x110;" k="8" />
    <hkern u1="&#xd4;" u2="&#x166;" k="3" />
    <hkern u1="&#xd4;" u2="&#x141;" k="8" />
    <hkern u1="&#xd4;" u2="&#xd0;" k="8" />
    <hkern u1="&#xd4;" u2="&#x110;" k="8" />
    <hkern u1="&#xd6;" u2="&#x166;" k="3" />
    <hkern u1="&#xd6;" u2="&#x141;" k="8" />
    <hkern u1="&#xd6;" u2="&#xd0;" k="8" />
    <hkern u1="&#xd6;" u2="&#x110;" k="8" />
    <hkern u1="&#xd2;" u2="&#x166;" k="3" />
    <hkern u1="&#xd2;" u2="&#x141;" k="8" />
    <hkern u1="&#xd2;" u2="&#xd0;" k="8" />
    <hkern u1="&#xd2;" u2="&#x110;" k="8" />
    <hkern u1="&#x150;" u2="&#x166;" k="3" />
    <hkern u1="&#x150;" u2="&#x141;" k="8" />
    <hkern u1="&#x150;" u2="&#xd0;" k="8" />
    <hkern u1="&#x150;" u2="&#x110;" k="8" />
    <hkern u1="&#x14c;" u2="&#x166;" k="3" />
    <hkern u1="&#x14c;" u2="&#x141;" k="8" />
    <hkern u1="&#x14c;" u2="&#xd0;" k="8" />
    <hkern u1="&#x14c;" u2="&#x110;" k="8" />
    <hkern u1="&#xd8;" u2="&#x166;" k="2" />
    <hkern u1="&#xd8;" u2="&#x141;" k="8" />
    <hkern u1="&#xd8;" u2="&#xd0;" k="8" />
    <hkern u1="&#xd8;" u2="&#x110;" k="8" />
    <hkern u1="&#x2f;" u2="&#x18f;" k="17" />
    <hkern u1="&#x2f;" g2="zero.lnum" k="18" />
    <hkern u1="&#x2f;" u2="&#x30;" k="38" />
    <hkern u1="&#x2f;" u2="&#x32;" k="27" />
    <hkern u1="&#x2f;" u2="&#x33;" k="11" />
    <hkern u1="&#x2f;" g2="six.lnum" k="16" />
    <hkern u1="&#x2f;" u2="&#x36;" k="16" />
    <hkern u1="&#x2f;" u2="&#x129;" k="-24" />
    <hkern u1="&#x2f;" u2="&#x12b;" k="-44" />
    <hkern u1="&#x2f;" g2="four.lnum" k="69" />
    <hkern u1="&#x2f;" u2="&#x34;" k="81" />
    <hkern u1="&#x2f;" g2="five.lnum" k="13" />
    <hkern u1="&#x2f;" u2="&#x35;" k="17" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="15" />
    <hkern u1="&#x2f;" g2="S.sc" k="11" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="256" />
    <hkern u1="&#x2f;" g2="J.sc" k="90" />
    <hkern u1="&#x2f;" g2="A.sc" k="68" />
    <hkern u1="&#x1fe;" u2="&#x166;" k="2" />
    <hkern u1="&#x1fe;" u2="&#x141;" k="8" />
    <hkern u1="&#x1fe;" u2="&#xd0;" k="8" />
    <hkern u1="&#x1fe;" u2="&#x110;" k="8" />
    <hkern u1="&#xd5;" u2="&#x166;" k="3" />
    <hkern u1="&#xd5;" u2="&#x141;" k="8" />
    <hkern u1="&#xd5;" u2="&#xd0;" k="8" />
    <hkern u1="&#xd5;" u2="&#x110;" k="8" />
    <hkern u1="P" u2="&#x20;" k="25" />
    <hkern u1="P" u2="&#x34;" k="45" />
    <hkern u1="P" u2="&#xf0;" k="7" />
    <hkern u1="P" u2="&#x2bc;" k="57" />
    <hkern u1="P" u2="X" k="20" />
    <hkern u1="P" u2="V" k="14" />
    <hkern u1="P" u2="&#x166;" k="-1" />
    <hkern u1="P" u2="&#x2f;" k="63" />
    <hkern u1="P" g2="J.sc" k="87" />
    <hkern u1="P" g2="A.sc" k="60" />
    <hkern g1="P.sc" u2="&#x20;" k="21" />
    <hkern g1="P.sc" u2="&#x3f;" k="19" />
    <hkern g1="P.sc" u2="&#x29;" k="20" />
    <hkern g1="P.sc" u2="\" k="22" />
    <hkern g1="P.sc" u2="&#x2a;" k="9" />
    <hkern g1="P.sc" u2="&#x2bc;" k="31" />
    <hkern g1="P.sc" g2="Z.sc" k="7" />
    <hkern g1="P.sc" g2="Y.sc" k="14" />
    <hkern g1="P.sc" g2="X.sc" k="15" />
    <hkern g1="P.sc" u2="X" k="10" />
    <hkern g1="P.sc" g2="W.sc" k="12" />
    <hkern g1="P.sc" g2="V.sc" k="12" />
    <hkern g1="P.sc" u2="V" k="31" />
    <hkern g1="P.sc" g2="T.sc" k="5" />
    <hkern g1="P.sc" u2="&#x2f;" k="10" />
    <hkern g1="P.sc" g2="J.sc" k="84" />
    <hkern g1="P.sc" g2="A.sc" k="42" />
    <hkern u1="Q" u2="&#x166;" k="3" />
    <hkern u1="Q" u2="&#x141;" k="8" />
    <hkern u1="Q" u2="&#xd0;" k="8" />
    <hkern u1="Q" u2="&#x110;" k="8" />
    <hkern g1="Q.sc" u2="&#xd0;" k="9" />
    <hkern u1="R" u2="&#x166;" k="5" />
    <hkern u1="R" u2="&#x141;" k="7" />
    <hkern u1="R" u2="&#xd0;" k="7" />
    <hkern u1="R" u2="&#x110;" k="7" />
    <hkern g1="R.sc" u2="&#x2122;" k="8" />
    <hkern g1="R.sc" u2="&#x3f;" k="21" />
    <hkern g1="R.sc" u2="&#xa6;" k="13" />
    <hkern g1="R.sc" u2="&#x7c;" k="14" />
    <hkern g1="R.sc" u2="\" k="30" />
    <hkern g1="R.sc" u2="&#x2a;" k="10" />
    <hkern g1="R.sc" u2="&#x2bc;" k="34" />
    <hkern g1="R.sc" g2="Y.sc" k="20" />
    <hkern g1="R.sc" g2="W.sc" k="15" />
    <hkern g1="R.sc" g2="V.sc" k="14" />
    <hkern g1="R.sc" u2="V" k="45" />
    <hkern g1="R.sc" g2="T.sc" k="14" />
    <hkern g1="R.sc" u2="&#xd0;" k="7" />
    <hkern g1="R.sc" g2="A.sc" k="10" />
    <hkern u1="&#x154;" u2="&#x166;" k="5" />
    <hkern u1="&#x154;" u2="&#x141;" k="7" />
    <hkern u1="&#x154;" u2="&#xd0;" k="7" />
    <hkern u1="&#x154;" u2="&#x110;" k="7" />
    <hkern u1="&#x158;" u2="&#x166;" k="5" />
    <hkern u1="&#x158;" u2="&#x141;" k="7" />
    <hkern u1="&#x158;" u2="&#xd0;" k="7" />
    <hkern u1="&#x158;" u2="&#x110;" k="7" />
    <hkern u1="&#x156;" u2="&#x166;" k="5" />
    <hkern u1="&#x156;" u2="&#x141;" k="7" />
    <hkern u1="&#x156;" u2="&#xd0;" k="7" />
    <hkern u1="&#x156;" u2="&#x110;" k="7" />
    <hkern u1="S" u2="&#x166;" k="24" />
    <hkern g1="S.sc" u2="&#x2122;" k="8" />
    <hkern g1="S.sc" u2="&#x3f;" k="15" />
    <hkern g1="S.sc" u2="&#xa6;" k="11" />
    <hkern g1="S.sc" u2="&#x7c;" k="14" />
    <hkern g1="S.sc" u2="\" k="25" />
    <hkern g1="S.sc" u2="&#x2a;" k="10" />
    <hkern g1="S.sc" u2="&#x2bc;" k="31" />
    <hkern g1="S.sc" g2="Y.sc" k="14" />
    <hkern g1="S.sc" g2="W.sc" k="14" />
    <hkern g1="S.sc" g2="V.sc" k="14" />
    <hkern g1="S.sc" u2="V" k="38" />
    <hkern g1="S.sc" g2="T.sc" k="9" />
    <hkern g1="S.sc" g2="A.sc" k="10" />
    <hkern u1="&#x15a;" u2="&#x166;" k="24" />
    <hkern u1="&#x160;" u2="&#x166;" k="24" />
    <hkern u1="&#x15c;" u2="&#x166;" k="24" />
    <hkern u1="T" u2="&#x159;" k="77" />
    <hkern u1="T" u2="&#x129;" k="-48" />
    <hkern u1="T" u2="&#x12b;" k="-57" />
    <hkern u1="T" u2="&#xdf;" k="38" />
    <hkern u1="T" g2="f_i" k="27" />
    <hkern u1="T" g2="f_f_l" k="49" />
    <hkern u1="T" g2="f_f_i" k="49" />
    <hkern u1="T" g2="f_f" k="49" />
    <hkern u1="T" u2="&#x131;" k="88" />
    <hkern g1="T.sc" u2="&#x20;" k="19" />
    <hkern g1="T.sc" u2="&#x40;" k="11" />
    <hkern g1="T.sc" u2="&#x2bc;" k="16" />
    <hkern g1="T.sc" u2="&#x2f;" k="50" />
    <hkern g1="T.sc" g2="J.sc" k="91" />
    <hkern g1="T.sc" g2="A.sc" k="63" />
    <hkern u1="&#x166;" u2="&#x1ecc;" k="1" />
    <hkern u1="&#x166;" u2="&#x1f4;" k="1" />
    <hkern u1="&#x166;" u2="&#x18f;" k="3" />
    <hkern u1="&#x166;" u2="z" k="31" />
    <hkern u1="&#x166;" g2="y.alt" k="7" />
    <hkern u1="&#x166;" u2="y" k="8" />
    <hkern u1="&#x166;" u2="x" k="24" />
    <hkern u1="&#x166;" g2="w.alt" k="6" />
    <hkern u1="&#x166;" u2="w" k="8" />
    <hkern u1="&#x166;" u2="v" k="8" />
    <hkern u1="&#x166;" u2="&#xad;" k="55" />
    <hkern u1="&#x166;" g2="u.alt" k="14" />
    <hkern u1="&#x166;" u2="u" k="14" />
    <hkern u1="&#x166;" u2="&#x3b;" k="13" />
    <hkern u1="&#x166;" u2="&#xfb06;" k="11" />
    <hkern u1="&#x166;" u2="s" k="11" />
    <hkern u1="&#x166;" u2="&#xae;" k="-1" />
    <hkern u1="&#x166;" u2="r" k="17" />
    <hkern u1="&#x166;" g2="q.alt" k="17" />
    <hkern u1="&#x166;" u2="q" k="17" />
    <hkern u1="&#x166;" u2="p" k="17" />
    <hkern u1="&#x166;" u2="&#x153;" k="18" />
    <hkern u1="&#x166;" u2="o" k="18" />
    <hkern u1="&#x166;" u2="n" k="17" />
    <hkern u1="&#x166;" u2="m" k="17" />
    <hkern u1="&#x166;" u2="&#x2d;" k="55" />
    <hkern u1="&#x166;" u2="&#x203a;" k="14" />
    <hkern u1="&#x166;" u2="&#x2039;" k="13" />
    <hkern u1="&#x166;" u2="&#xbb;" k="14" />
    <hkern u1="&#x166;" u2="&#xab;" k="13" />
    <hkern u1="&#x166;" u2="&#xdf;" k="8" />
    <hkern u1="&#x166;" u2="g" k="17" />
    <hkern u1="&#x166;" g2="f_l" k="11" />
    <hkern u1="&#x166;" g2="f_i" k="11" />
    <hkern u1="&#x166;" u2="f" k="11" />
    <hkern u1="&#x166;" u2="&#xf0;" k="33" />
    <hkern u1="&#x166;" u2="&#x2013;" k="55" />
    <hkern u1="&#x166;" u2="&#x2014;" k="55" />
    <hkern u1="&#x166;" u2="e" k="18" />
    <hkern u1="&#x166;" g2="d.alt" k="17" />
    <hkern u1="&#x166;" u2="d" k="17" />
    <hkern u1="&#x166;" u2="&#xa9;" k="-1" />
    <hkern u1="&#x166;" u2="&#x3a;" k="13" />
    <hkern u1="&#x166;" g2="c_t" k="18" />
    <hkern u1="&#x166;" u2="c" k="18" />
    <hkern u1="&#x166;" u2="&#xe6;" k="36" />
    <hkern u1="&#x166;" g2="a.alt2" k="36" />
    <hkern u1="&#x166;" g2="a.alt" k="17" />
    <hkern u1="&#x166;" u2="a" k="17" />
    <hkern u1="&#x166;" g2="Z.sc" k="26" />
    <hkern u1="&#x166;" g2="Y.sc" k="18" />
    <hkern u1="&#x166;" g2="X.sc" k="21" />
    <hkern u1="&#x166;" g2="W.sc" k="9" />
    <hkern u1="&#x166;" g2="V.sc" k="9" />
    <hkern u1="&#x166;" g2="U.sc" k="5" />
    <hkern u1="&#x166;" g2="S.sc" k="5" />
    <hkern u1="&#x166;" g2="R.sc" k="9" />
    <hkern u1="&#x166;" g2="Q.sc" k="8" />
    <hkern u1="&#x166;" u2="Q" k="1" />
    <hkern u1="&#x166;" g2="P.sc" k="9" />
    <hkern u1="&#x166;" u2="&#xd5;" k="1" />
    <hkern u1="&#x166;" u2="&#x1fe;" k="1" />
    <hkern u1="&#x166;" u2="&#x2f;" k="68" />
    <hkern u1="&#x166;" u2="&#xd8;" k="1" />
    <hkern u1="&#x166;" u2="&#x14c;" k="1" />
    <hkern u1="&#x166;" u2="&#x150;" k="1" />
    <hkern u1="&#x166;" u2="&#xd2;" k="1" />
    <hkern u1="&#x166;" u2="&#xd6;" k="1" />
    <hkern u1="&#x166;" u2="&#xd4;" k="1" />
    <hkern u1="&#x166;" u2="&#x14e;" k="1" />
    <hkern u1="&#x166;" u2="&#xd3;" k="1" />
    <hkern u1="&#x166;" u2="&#x152;" k="1" />
    <hkern u1="&#x166;" g2="O.sc" k="8" />
    <hkern u1="&#x166;" u2="O" k="1" />
    <hkern u1="&#x166;" g2="N.sc" k="9" />
    <hkern u1="&#x166;" g2="M.sc" k="9" />
    <hkern u1="&#x166;" g2="L.sc" k="9" />
    <hkern u1="&#x166;" g2="K.sc" k="9" />
    <hkern u1="&#x166;" g2="I.sc" k="9" />
    <hkern u1="&#x166;" g2="H.sc" k="9" />
    <hkern u1="&#x166;" u2="&#x120;" k="1" />
    <hkern u1="&#x166;" u2="&#x122;" k="1" />
    <hkern u1="&#x166;" u2="&#x11c;" k="1" />
    <hkern u1="&#x166;" u2="&#x11e;" k="1" />
    <hkern u1="&#x166;" g2="G.sc" k="8" />
    <hkern u1="&#x166;" g2="G.alt" k="1" />
    <hkern u1="&#x166;" u2="G" k="1" />
    <hkern u1="&#x166;" g2="F.sc" k="9" />
    <hkern u1="&#x166;" g2="E.sc" k="9" />
    <hkern u1="&#x166;" g2="D.sc" k="9" />
    <hkern u1="&#x166;" u2="&#x10a;" k="1" />
    <hkern u1="&#x166;" u2="&#x108;" k="1" />
    <hkern u1="&#x166;" u2="&#xc7;" k="2" />
    <hkern u1="&#x166;" u2="&#x10c;" k="1" />
    <hkern u1="&#x166;" u2="&#x106;" k="1" />
    <hkern u1="&#x166;" g2="C.sc" k="8" />
    <hkern u1="&#x166;" u2="C" k="1" />
    <hkern u1="&#x166;" g2="B.sc" k="9" />
    <hkern u1="&#x166;" g2="A.sc" k="77" />
    <hkern u1="&#x164;" u2="&#xdf;" k="38" />
    <hkern u1="&#x164;" g2="f_i" k="27" />
    <hkern u1="&#x164;" g2="f_f_l" k="49" />
    <hkern u1="&#x164;" g2="f_f_i" k="49" />
    <hkern u1="&#x164;" g2="f_f" k="49" />
    <hkern u1="&#xde;" u2="&#x2122;" k="14" />
    <hkern u1="&#xde;" u2="&#x37;" k="12" />
    <hkern u1="&#xde;" u2="&#x3f;" k="18" />
    <hkern u1="&#xde;" u2="&#x29;" k="15" />
    <hkern u1="&#xde;" u2="\" k="22" />
    <hkern u1="&#xde;" u2="&#x2a;" k="27" />
    <hkern u1="&#xde;" u2="X" k="27" />
    <hkern u1="&#xde;" g2="W.alt" k="12" />
    <hkern u1="&#xde;" u2="V" k="34" />
    <hkern u1="&#xde;" u2="&#x166;" k="-1" />
    <hkern u1="&#xde;" u2="&#x2f;" k="18" />
    <hkern u1="&#xde;" u2="&#x141;" k="6" />
    <hkern u1="&#xde;" u2="&#xd0;" k="6" />
    <hkern u1="&#xde;" u2="&#x110;" k="6" />
    <hkern u1="&#xde;" g2="A.sc" k="8" />
    <hkern u1="U" u2="&#x141;" k="6" />
    <hkern u1="U" u2="&#xd0;" k="6" />
    <hkern u1="U" u2="&#x110;" k="6" />
    <hkern g1="U.sc" u2="\" k="10" />
    <hkern g1="U.sc" u2="&#x2bc;" k="23" />
    <hkern g1="U.sc" u2="X" k="6" />
    <hkern g1="U.sc" u2="V" k="23" />
    <hkern g1="U.sc" u2="&#x2f;" k="10" />
    <hkern g1="U.sc" g2="J.sc" k="22" />
    <hkern g1="U.sc" g2="A.sc" k="16" />
    <hkern u1="&#xda;" u2="&#x141;" k="6" />
    <hkern u1="&#xda;" u2="&#xd0;" k="6" />
    <hkern u1="&#xda;" u2="&#x110;" k="6" />
    <hkern u1="&#x16c;" u2="&#x141;" k="6" />
    <hkern u1="&#x16c;" u2="&#xd0;" k="6" />
    <hkern u1="&#x16c;" u2="&#x110;" k="6" />
    <hkern u1="&#xdb;" u2="&#x141;" k="6" />
    <hkern u1="&#xdb;" u2="&#xd0;" k="6" />
    <hkern u1="&#xdb;" u2="&#x110;" k="6" />
    <hkern u1="&#xdc;" u2="&#x141;" k="6" />
    <hkern u1="&#xdc;" u2="&#xd0;" k="6" />
    <hkern u1="&#xdc;" u2="&#x110;" k="6" />
    <hkern u1="&#xd9;" u2="&#x141;" k="6" />
    <hkern u1="&#xd9;" u2="&#xd0;" k="6" />
    <hkern u1="&#xd9;" u2="&#x110;" k="6" />
    <hkern u1="&#x170;" u2="&#x141;" k="6" />
    <hkern u1="&#x170;" u2="&#xd0;" k="6" />
    <hkern u1="&#x170;" u2="&#x110;" k="6" />
    <hkern u1="&#x16a;" u2="&#x141;" k="6" />
    <hkern u1="&#x16a;" u2="&#xd0;" k="6" />
    <hkern u1="&#x16a;" u2="&#x110;" k="6" />
    <hkern u1="&#x172;" u2="&#x141;" k="6" />
    <hkern u1="&#x172;" u2="&#xd0;" k="6" />
    <hkern u1="&#x172;" u2="&#x110;" k="6" />
    <hkern u1="&#x16e;" u2="&#x141;" k="6" />
    <hkern u1="&#x16e;" u2="&#xd0;" k="6" />
    <hkern u1="&#x16e;" u2="&#x110;" k="6" />
    <hkern u1="&#x168;" u2="&#x141;" k="6" />
    <hkern u1="&#x168;" u2="&#xd0;" k="6" />
    <hkern u1="&#x168;" u2="&#x110;" k="6" />
    <hkern u1="V" u2="&#x18f;" k="24" />
    <hkern u1="V" u2="&#x30;" k="34" />
    <hkern u1="V" u2="x" k="11" />
    <hkern u1="V" g2="w.alt" k="9" />
    <hkern u1="V" u2="v" k="8" />
    <hkern u1="V" u2="&#x32;" k="27" />
    <hkern u1="V" u2="&#x33;" k="27" />
    <hkern u1="V" u2="&#x20;" k="32" />
    <hkern u1="V" u2="&#x36;" k="24" />
    <hkern u1="V" u2="&#x259;" k="50" />
    <hkern u1="V" u2="&#xae;" k="22" />
    <hkern u1="V" u2="&#x159;" k="22" />
    <hkern u1="V" u2="&#x31;" k="23" />
    <hkern u1="V" u2="&#x39;" k="28" />
    <hkern u1="V" u2="&#x129;" k="-41" />
    <hkern u1="V" u2="&#x12b;" k="-55" />
    <hkern u1="V" u2="&#xdf;" k="15" />
    <hkern u1="V" u2="&#x34;" k="72" />
    <hkern u1="V" u2="&#x35;" k="39" />
    <hkern u1="V" u2="&#xf0;" k="66" />
    <hkern u1="V" u2="&#x38;" k="22" />
    <hkern u1="V" u2="&#x131;" k="38" />
    <hkern u1="V" u2="&#xa9;" k="22" />
    <hkern u1="V" u2="&#x40;" k="45" />
    <hkern u1="V" u2="&#x2bc;" k="48" />
    <hkern u1="V" u2="&#x26;" k="26" />
    <hkern u1="V" g2="U.sc" k="17" />
    <hkern u1="V" g2="S.sc" k="36" />
    <hkern u1="V" u2="&#x2f;" k="78" />
    <hkern u1="V" g2="J.sc" k="71" />
    <hkern u1="V" g2="A.sc" k="75" />
    <hkern g1="V.sc" u2="&#x20;" k="29" />
    <hkern g1="V.sc" u2="&#x29;" k="13" />
    <hkern g1="V.sc" u2="&#x40;" k="11" />
    <hkern g1="V.sc" u2="&#x2bc;" k="23" />
    <hkern g1="V.sc" u2="X" k="12" />
    <hkern g1="V.sc" g2="S.sc" k="18" />
    <hkern g1="V.sc" u2="&#x2f;" k="46" />
    <hkern g1="V.sc" g2="J.sc" k="73" />
    <hkern g1="V.sc" g2="A.sc" k="49" />
    <hkern u1="W" u2="&#x159;" k="22" />
    <hkern u1="W" u2="&#x129;" k="-41" />
    <hkern u1="W" u2="&#x12b;" k="-55" />
    <hkern u1="W" u2="&#xdf;" k="15" />
    <hkern u1="W" u2="&#x131;" k="38" />
    <hkern g1="W.alt" u2="&#x18f;" k="14" />
    <hkern g1="W.alt" u2="&#x30;" k="19" />
    <hkern g1="W.alt" u2="&#x32;" k="14" />
    <hkern g1="W.alt" u2="&#x33;" k="13" />
    <hkern g1="W.alt" u2="&#x20;" k="25" />
    <hkern g1="W.alt" u2="&#x36;" k="13" />
    <hkern g1="W.alt" u2="&#x259;" k="35" />
    <hkern g1="W.alt" u2="&#xae;" k="13" />
    <hkern g1="W.alt" u2="&#x159;" k="19" />
    <hkern g1="W.alt" u2="&#x39;" k="14" />
    <hkern g1="W.alt" u2="&#x129;" k="-37" />
    <hkern g1="W.alt" u2="&#x12b;" k="-48" />
    <hkern g1="W.alt" u2="&#xdf;" k="9" />
    <hkern g1="W.alt" u2="&#x34;" k="45" />
    <hkern g1="W.alt" u2="&#x35;" k="23" />
    <hkern g1="W.alt" u2="&#xf0;" k="39" />
    <hkern g1="W.alt" u2="&#x38;" k="13" />
    <hkern g1="W.alt" u2="&#x131;" k="28" />
    <hkern g1="W.alt" u2="&#xa9;" k="13" />
    <hkern g1="W.alt" u2="&#x40;" k="25" />
    <hkern g1="W.alt" u2="&#x2bc;" k="40" />
    <hkern g1="W.alt" u2="&#x26;" k="15" />
    <hkern g1="W.alt" g2="S.sc" k="18" />
    <hkern g1="W.alt" u2="&#x2f;" k="51" />
    <hkern g1="W.alt" g2="J.sc" k="56" />
    <hkern g1="W.alt" g2="A.sc" k="38" />
    <hkern g1="W.sc" u2="&#x20;" k="29" />
    <hkern g1="W.sc" u2="&#x29;" k="13" />
    <hkern g1="W.sc" u2="&#x40;" k="11" />
    <hkern g1="W.sc" u2="&#x2bc;" k="23" />
    <hkern g1="W.sc" u2="X" k="12" />
    <hkern g1="W.sc" g2="S.sc" k="18" />
    <hkern g1="W.sc" u2="&#x2f;" k="46" />
    <hkern g1="W.sc" g2="J.sc" k="73" />
    <hkern g1="W.sc" g2="A.sc" k="49" />
    <hkern u1="&#x1e82;" u2="&#xdf;" k="15" />
    <hkern u1="&#x174;" u2="&#xdf;" k="15" />
    <hkern u1="&#x1e84;" u2="&#xdf;" k="15" />
    <hkern u1="&#x1e80;" u2="&#xdf;" k="15" />
    <hkern u1="X" u2="&#x18f;" k="21" />
    <hkern u1="X" u2="&#x30;" k="19" />
    <hkern u1="X" g2="y.alt" k="8" />
    <hkern u1="X" g2="w.alt" k="8" />
    <hkern u1="X" u2="v" k="9" />
    <hkern u1="X" u2="&#x2122;" k="-9" />
    <hkern u1="X" u2="&#x20;" k="11" />
    <hkern u1="X" u2="&#x259;" k="6" />
    <hkern u1="X" u2="&#x129;" k="-49" />
    <hkern u1="X" u2="&#x12b;" k="-66" />
    <hkern u1="X" u2="&#xf0;" k="29" />
    <hkern u1="X" u2="\" k="-7" />
    <hkern u1="X" u2="&#x2bc;" k="37" />
    <hkern u1="X" g2="U.sc" k="10" />
    <hkern g1="X.sc" u2="&#x2bc;" k="17" />
    <hkern u1="Y" u2="&#x1ef9;" k="8" />
    <hkern u1="Y" u2="&#x177;" k="21" />
    <hkern u1="Y" u2="&#x159;" k="49" />
    <hkern u1="Y" u2="&#x129;" k="-42" />
    <hkern u1="Y" u2="&#x12b;" k="-58" />
    <hkern u1="Y" u2="&#xdf;" k="23" />
    <hkern u1="Y" u2="&#x131;" k="67" />
    <hkern g1="Y.sc" u2="&#x20;" k="30" />
    <hkern g1="Y.sc" u2="&#x29;" k="12" />
    <hkern g1="Y.sc" u2="&#x40;" k="16" />
    <hkern g1="Y.sc" u2="&#x2bc;" k="23" />
    <hkern g1="Y.sc" u2="&#x26;" k="11" />
    <hkern g1="Y.sc" u2="X" k="12" />
    <hkern g1="Y.sc" g2="S.sc" k="11" />
    <hkern g1="Y.sc" u2="&#x2f;" k="52" />
    <hkern g1="Y.sc" g2="J.sc" k="79" />
    <hkern g1="Y.sc" g2="A.sc" k="60" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="23" />
    <hkern u1="&#x176;" u2="&#xdf;" k="23" />
    <hkern u1="&#x178;" u2="&#xdf;" k="23" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="23" />
    <hkern u1="&#x1ef8;" u2="&#xdf;" k="23" />
    <hkern u1="Z" u2="&#x129;" k="-47" />
    <hkern u1="Z" u2="&#x12b;" k="-57" />
    <hkern g1="Z.sc" u2="&#x2bc;" k="17" />
    <hkern u1="&#x17d;" u2="&#x12b;" k="-57" />
    <hkern u1="a" u2="&#xd0;" k="8" />
    <hkern g1="a.alt2" g2="y.alt" k="14" />
    <hkern g1="a.alt2" g2="w.alt" k="12" />
    <hkern g1="a.alt2" u2="v" k="13" />
    <hkern g1="a.alt2" u2="&#x2122;" k="12" />
    <hkern g1="a.alt2" g2="t.alt" k="5" />
    <hkern g1="a.alt2" u2="&#x3f;" k="25" />
    <hkern g1="a.alt2" u2="&#xa6;" k="16" />
    <hkern g1="a.alt2" u2="&#x7c;" k="18" />
    <hkern g1="a.alt2" u2="\" k="41" />
    <hkern g1="a.alt2" u2="&#x2a;" k="15" />
    <hkern g1="a.alt2" u2="&#x2bc;" k="36" />
    <hkern g1="a.alt2" u2="V" k="53" />
    <hkern g1="a.alt2" u2="&#xd0;" k="9" />
    <hkern u1="&#xe6;" u2="&#x20;" k="28" />
    <hkern u1="&#xe6;" u2="&#xd0;" k="14" />
    <hkern u1="&#x1fd;" u2="&#x20;" k="28" />
    <hkern u1="&#x26;" u2="V" k="27" />
    <hkern u1="&#x26;" u2="&#x166;" k="7" />
    <hkern u1="&#x26;" g2="T.sc" k="20" />
    <hkern u1="&#x105;" u2="j" k="-28" />
    <hkern u1="&#x2bc;" g2="y.alt" k="17" />
    <hkern u1="&#x2bc;" u2="x" k="18" />
    <hkern u1="&#x2bc;" g2="w.alt" k="17" />
    <hkern u1="&#x2bc;" u2="v" k="19" />
    <hkern u1="&#x2bc;" g2="t.alt" k="31" />
    <hkern u1="&#x2bc;" g2="l.alt" k="25" />
    <hkern u1="&#x2bc;" g2="Z.sc" k="18" />
    <hkern u1="&#x2bc;" g2="Y.sc" k="18" />
    <hkern u1="&#x2bc;" g2="X.sc" k="16" />
    <hkern u1="&#x2bc;" g2="W.sc" k="19" />
    <hkern u1="&#x2bc;" g2="V.sc" k="19" />
    <hkern u1="&#x2bc;" g2="U.sc" k="23" />
    <hkern u1="&#x2bc;" g2="T.sc" k="16" />
    <hkern u1="&#x2bc;" g2="S.sc" k="35" />
    <hkern u1="&#x2bc;" g2="J.sc" k="59" />
    <hkern u1="&#x2bc;" g2="A.sc" k="61" />
    <hkern u1="&#x2a;" u2="&#x259;" k="8" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-39" />
    <hkern u1="&#x2a;" u2="&#x12b;" k="-10" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="15" />
    <hkern u1="&#x2a;" g2="J.sc" k="36" />
    <hkern u1="&#x2a;" g2="A.sc" k="34" />
    <hkern u1="&#x40;" g2="y.alt" k="12" />
    <hkern u1="&#x40;" u2="v" k="12" />
    <hkern u1="&#x40;" g2="Y.sc" k="29" />
    <hkern u1="&#x40;" g2="W.sc" k="19" />
    <hkern u1="&#x40;" g2="W.alt" k="29" />
    <hkern u1="&#x40;" g2="V.sc" k="19" />
    <hkern u1="&#x40;" u2="V" k="51" />
    <hkern u1="&#x40;" g2="T.sc" k="27" />
    <hkern u1="b" u2="&#xd0;" k="16" />
    <hkern u1="\" u2="&#x18f;" k="12" />
    <hkern u1="\" g2="zero.lnum" k="19" />
    <hkern u1="\" g2="y.alt" k="40" />
    <hkern u1="\" g2="w.alt" k="27" />
    <hkern u1="\" u2="v" k="32" />
    <hkern u1="\" g2="six.lnum" k="11" />
    <hkern u1="\" u2="&#x36;" k="11" />
    <hkern u1="\" u2="&#x37;" k="13" />
    <hkern u1="\" g2="Y.sc" k="48" />
    <hkern u1="\" g2="W.sc" k="46" />
    <hkern u1="\" g2="W.alt" k="40" />
    <hkern u1="\" g2="V.sc" k="45" />
    <hkern u1="\" u2="V" k="72" />
    <hkern u1="\" g2="U.sc" k="10" />
    <hkern u1="\" u2="&#x166;" k="62" />
    <hkern u1="\" g2="T.sc" k="49" />
    <hkern u1="&#x7c;" g2="y.alt" k="19" />
    <hkern u1="&#x7c;" g2="w.alt" k="20" />
    <hkern u1="&#x7c;" u2="v" k="22" />
    <hkern u1="&#x7c;" u2="&#x142;" k="10" />
    <hkern u1="&#x7c;" u2="&#x135;" k="-120" />
    <hkern u1="&#x7c;" u2="j" k="-112" />
    <hkern u1="&#x7c;" g2="Y.sc" k="28" />
    <hkern u1="&#x7c;" g2="W.sc" k="26" />
    <hkern u1="&#x7c;" g2="W.alt" k="29" />
    <hkern u1="&#x7c;" g2="V.sc" k="26" />
    <hkern u1="&#x7c;" u2="V" k="44" />
    <hkern u1="&#x7c;" g2="U.sc" k="11" />
    <hkern u1="&#x7c;" u2="&#x166;" k="25" />
    <hkern u1="&#x7c;" g2="T.sc" k="20" />
    <hkern u1="&#x7c;" u2="&#x141;" k="12" />
    <hkern u1="&#x7c;" u2="&#xd0;" k="12" />
    <hkern u1="&#x7c;" u2="&#x110;" k="12" />
    <hkern u1="&#x7b;" u2="&#x12b;" k="-20" />
    <hkern u1="[" u2="&#x12b;" k="-17" />
    <hkern u1="&#xa6;" g2="y.alt" k="17" />
    <hkern u1="&#xa6;" g2="w.alt" k="18" />
    <hkern u1="&#xa6;" u2="v" k="20" />
    <hkern u1="&#xa6;" u2="&#x135;" k="-123" />
    <hkern u1="&#xa6;" u2="j" k="-115" />
    <hkern u1="&#xa6;" g2="Y.sc" k="25" />
    <hkern u1="&#xa6;" g2="W.sc" k="24" />
    <hkern u1="&#xa6;" g2="W.alt" k="27" />
    <hkern u1="&#xa6;" g2="V.sc" k="24" />
    <hkern u1="&#xa6;" u2="V" k="42" />
    <hkern u1="&#xa6;" g2="T.sc" k="17" />
    <hkern u1="&#xa6;" u2="&#x141;" k="10" />
    <hkern u1="&#xa6;" u2="&#xd0;" k="10" />
    <hkern u1="&#xa6;" u2="&#x110;" k="10" />
    <hkern u1="c" u2="&#xd0;" k="9" />
    <hkern u1="&#x3a;" u2="&#x166;" k="5" />
    <hkern u1="&#x2c;" u2="&#x166;" k="72" />
    <hkern u1="d" u2="&#xd0;" k="8" />
    <hkern g1="d.alt" u2="&#x2bc;" k="25" />
    <hkern g1="d.alt" u2="&#xd0;" k="8" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-49" />
    <hkern u1="&#x10f;" u2="&#xfe;" k="-1" />
    <hkern u1="&#x10f;" u2="&#x2019;" k="-15" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="-15" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-37" />
    <hkern u1="&#x10f;" u2="k" k="-10" />
    <hkern u1="&#x10f;" u2="&#x133;" k="-10" />
    <hkern u1="&#x10f;" u2="i" k="-10" />
    <hkern u1="&#x10f;" u2="h" k="-10" />
    <hkern u1="&#x10f;" u2="&#xdf;" k="-1" />
    <hkern u1="&#x10f;" u2="&#xa6;" k="-54" />
    <hkern u1="&#x10f;" u2="]" k="-28" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-30" />
    <hkern u1="&#x10f;" u2="&#x7c;" k="-51" />
    <hkern u1="&#x10f;" u2="\" k="-50" />
    <hkern u1="&#x10f;" u2="b" k="-10" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-10" />
    <hkern u1="&#xb0;" u2="&#x30;" k="12" />
    <hkern u1="&#xb0;" u2="&#x33;" k="19" />
    <hkern u1="&#xb0;" u2="&#x39;" k="27" />
    <hkern u1="&#xb0;" g2="four.lnum" k="67" />
    <hkern u1="&#xb0;" u2="&#x34;" k="93" />
    <hkern u1="&#xb0;" u2="&#x35;" k="19" />
    <hkern u1="&#xf7;" u2="&#x33;" k="38" />
    <hkern u1="&#xf7;" g2="seven.lnum" k="30" />
    <hkern u1="&#xf7;" u2="&#x37;" k="45" />
    <hkern u1="&#xf7;" u2="&#x31;" k="20" />
    <hkern u1="&#xf7;" u2="&#x34;" k="17" />
    <hkern u1="&#x24;" u2="&#x37;" k="10" />
    <hkern u1="e" u2="&#xd0;" k="14" />
    <hkern u1="&#x38;" u2="V" k="21" />
    <hkern u1="&#x14b;" u2="j" k="-18" />
    <hkern u1="&#x3d;" u2="&#x33;" k="37" />
    <hkern u1="&#x3d;" g2="seven.lnum" k="21" />
    <hkern u1="&#x3d;" u2="&#x37;" k="31" />
    <hkern u1="&#xf0;" g2="y.alt" k="10" />
    <hkern u1="&#xf0;" u2="x" k="14" />
    <hkern u1="&#xf0;" g2="w.alt" k="10" />
    <hkern u1="&#xf0;" u2="v" k="12" />
    <hkern u1="&#xf0;" g2="t.alt" k="5" />
    <hkern u1="&#xf0;" u2="&#x3f;" k="15" />
    <hkern u1="&#xf0;" u2="&#x7c;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="8" />
    <hkern u1="&#xf0;" u2="X" k="21" />
    <hkern u1="&#xf0;" g2="W.alt" k="18" />
    <hkern u1="&#xf0;" u2="V" k="30" />
    <hkern u1="&#xf0;" u2="&#xd0;" k="13" />
    <hkern u1="&#xa1;" u2="V" k="12" />
    <hkern u1="f" u2="&#x129;" k="-77" />
    <hkern u1="f" u2="&#x12b;" k="-101" />
    <hkern u1="f" u2="&#xec;" k="-29" />
    <hkern u1="f" u2="&#xef;" k="-26" />
    <hkern u1="f" u2="&#x12d;" k="-36" />
    <hkern g1="f_f" u2="&#x149;" k="-125" />
    <hkern g1="f_f" u2="&#x129;" k="-65" />
    <hkern g1="f_f" u2="&#x12b;" k="-89" />
    <hkern g1="f_f" u2="&#xec;" k="-18" />
    <hkern g1="f_f" u2="&#xef;" k="-14" />
    <hkern g1="f_f" u2="&#x12d;" k="-24" />
    <hkern g1="f_f_i" u2="&#x149;" k="-46" />
    <hkern g1="f_f_i" u2="&#x12b;" k="-11" />
    <hkern g1="f_f_l" u2="&#x149;" k="-26" />
    <hkern g1="f_i" u2="&#x149;" k="-46" />
    <hkern g1="f_i" u2="&#x12b;" k="-11" />
    <hkern g1="f_l" u2="&#x149;" k="-26" />
    <hkern u1="&#x35;" u2="&#x37;" k="10" />
    <hkern u1="&#x35;" u2="&#x2b;" k="37" />
    <hkern u1="&#x35;" u2="&#xd7;" k="17" />
    <hkern u1="&#x35;" u2="&#x2212;" k="21" />
    <hkern u1="&#x35;" u2="&#x3d;" k="12" />
    <hkern u1="&#x35;" u2="&#xf7;" k="18" />
    <hkern u1="&#x35;" u2="V" k="13" />
    <hkern u1="&#x35;" u2="&#xb7;" k="12" />
    <hkern u1="&#x34;" u2="&#x37;" k="18" />
    <hkern u1="&#x34;" u2="&#x2b;" k="14" />
    <hkern u1="&#x34;" u2="&#x2212;" k="10" />
    <hkern u1="&#x34;" u2="&#xb0;" k="22" />
    <hkern u1="&#x34;" u2="\" k="25" />
    <hkern u1="&#x34;" g2="W.alt" k="21" />
    <hkern u1="&#x34;" u2="V" k="37" />
    <hkern u1="g" u2="j" k="-27" />
    <hkern u1="g" u2="W" k="40" />
    <hkern u1="&#x11f;" u2="j" k="-27" />
    <hkern u1="&#x11d;" u2="j" k="-27" />
    <hkern u1="&#x123;" u2="j" k="-27" />
    <hkern u1="&#x121;" u2="j" k="-27" />
    <hkern u1="&#xdf;" g2="y.alt" k="14" />
    <hkern u1="&#xdf;" u2="x" k="12" />
    <hkern u1="&#xdf;" g2="w.alt" k="14" />
    <hkern u1="&#xdf;" u2="v" k="15" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="8" />
    <hkern u1="&#xdf;" g2="t.alt" k="6" />
    <hkern u1="&#xdf;" u2="&#xa6;" k="10" />
    <hkern u1="&#xdf;" u2="&#x7c;" k="12" />
    <hkern u1="&#xdf;" u2="\" k="10" />
    <hkern u1="&#xdf;" u2="X" k="7" />
    <hkern u1="&#xdf;" g2="W.alt" k="22" />
    <hkern u1="&#xdf;" u2="V" k="36" />
    <hkern u1="&#xdf;" u2="&#xd0;" k="17" />
    <hkern u1="&#xab;" u2="&#x166;" k="14" />
    <hkern u1="&#xbb;" u2="&#x166;" k="40" />
    <hkern u1="&#xbb;" u2="&#x141;" k="10" />
    <hkern u1="&#xbb;" u2="&#xd0;" k="10" />
    <hkern u1="&#xbb;" u2="&#x110;" k="10" />
    <hkern u1="&#x2039;" u2="&#x166;" k="14" />
    <hkern u1="&#x203a;" u2="&#x166;" k="40" />
    <hkern u1="&#x203a;" u2="&#x141;" k="10" />
    <hkern u1="&#x203a;" u2="&#xd0;" k="10" />
    <hkern u1="&#x203a;" u2="&#x110;" k="10" />
    <hkern u1="h" u2="&#xd0;" k="9" />
    <hkern u1="i" u2="&#xd0;" k="8" />
    <hkern u1="&#x133;" u2="j" k="-13" />
    <hkern u1="&#x133;" u2="&#xd0;" k="8" />
    <hkern u1="j" u2="&#x135;" k="-21" />
    <hkern u1="j" u2="j" k="-13" />
    <hkern u1="j" u2="&#xd0;" k="8" />
    <hkern u1="&#x12b;" u2="&#x2122;" k="-42" />
    <hkern u1="&#x12b;" u2="&#x29;" k="-21" />
    <hkern u1="&#x12b;" u2="&#xa6;" k="-29" />
    <hkern u1="&#x12b;" u2="&#x7c;" k="-26" />
    <hkern u1="&#x12b;" u2="\" k="-44" />
    <hkern u1="&#x12b;" u2="&#x2a;" k="-8" />
    <hkern u1="&#x12f;" u2="j" k="-15" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-36" />
    <hkern u1="&#x129;" u2="&#x29;" k="-20" />
    <hkern u1="&#x129;" u2="&#xa6;" k="-32" />
    <hkern u1="&#x129;" u2="]" k="-7" />
    <hkern u1="&#x129;" u2="&#x7d;" k="-9" />
    <hkern u1="&#x129;" u2="&#x7c;" k="-29" />
    <hkern u1="&#x129;" u2="\" k="-39" />
    <hkern g1="j.alt" u2="j" k="-59" />
    <hkern g1="j.alt" u2="&#xd0;" k="8" />
    <hkern u1="&#x135;" u2="j" k="-13" />
    <hkern u1="k" u2="&#xd0;" k="11" />
    <hkern g1="l.alt" u2="&#x2bc;" k="25" />
    <hkern g1="l.alt" u2="&#xd0;" k="8" />
    <hkern u1="&#x13e;" g2="y.alt" k="10" />
    <hkern u1="&#x13e;" u2="y" k="5" />
    <hkern u1="&#x13e;" g2="w.alt" k="8" />
    <hkern u1="&#x13e;" u2="w" k="7" />
    <hkern u1="&#x13e;" u2="v" k="8" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x13e;" g2="q.alt" k="-1" />
    <hkern u1="&#x13e;" u2="q" k="-1" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-23" />
    <hkern u1="&#x13e;" u2="g" k="-1" />
    <hkern u1="&#x13e;" g2="d.alt" k="-1" />
    <hkern u1="&#x13e;" u2="d" k="-1" />
    <hkern u1="&#x13e;" u2="&#xa6;" k="-35" />
    <hkern u1="&#x13e;" u2="]" k="-9" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-12" />
    <hkern u1="&#x13e;" u2="&#x7c;" k="-32" />
    <hkern u1="&#x13e;" u2="\" k="-32" />
    <hkern u1="&#x13e;" u2="&#xe1;" k="-1" />
    <hkern u1="&#x13e;" g2="a.alt" k="-1" />
    <hkern u1="&#x13e;" u2="a" k="-1" />
    <hkern u1="&#x13e;" u2="&#x2f;" k="-6" />
    <hkern u1="&#x13c;" u2="j" k="-77" />
    <hkern u1="&#x142;" g2="y.alt" k="2" />
    <hkern u1="&#x142;" u2="y" k="-1" />
    <hkern u1="&#x142;" u2="x" k="-9" />
    <hkern u1="m" u2="&#xd0;" k="8" />
    <hkern u1="&#x2212;" u2="&#x33;" k="53" />
    <hkern u1="&#x2212;" g2="seven.lnum" k="33" />
    <hkern u1="&#x2212;" u2="&#x37;" k="47" />
    <hkern u1="&#x2212;" g2="one.lnum" k="10" />
    <hkern u1="&#x2212;" u2="&#x31;" k="21" />
    <hkern u1="&#x2212;" u2="&#x34;" k="23" />
    <hkern u1="&#xd7;" u2="&#x33;" k="29" />
    <hkern u1="&#xd7;" g2="seven.lnum" k="25" />
    <hkern u1="&#xd7;" u2="&#x37;" k="37" />
    <hkern u1="&#xd7;" u2="&#x31;" k="10" />
    <hkern u1="n" u2="&#xd0;" k="8" />
    <hkern u1="&#x39;" u2="&#x37;" k="29" />
    <hkern u1="&#x39;" u2="&#xb0;" k="53" />
    <hkern u1="&#x39;" u2="\" k="41" />
    <hkern u1="&#x39;" g2="W.alt" k="24" />
    <hkern u1="&#x39;" u2="V" k="45" />
    <hkern g1="nine.lnum" g2="seven.lnum" k="15" />
    <hkern g1="nine.lnum" u2="&#x29;" k="10" />
    <hkern g1="nine.lnum" u2="&#x2f;" k="16" />
    <hkern u1="o" u2="&#xd0;" k="16" />
    <hkern u1="&#x153;" u2="&#x20;" k="24" />
    <hkern u1="&#x153;" u2="&#xd0;" k="14" />
    <hkern u1="&#x31;" u2="&#x37;" k="13" />
    <hkern u1="&#x31;" u2="&#x2b;" k="18" />
    <hkern u1="&#x31;" u2="&#xd7;" k="14" />
    <hkern u1="&#x31;" u2="&#xb0;" k="23" />
    <hkern u1="&#x31;" u2="\" k="31" />
    <hkern u1="&#x31;" u2="X" k="-5" />
    <hkern u1="&#x31;" g2="W.alt" k="27" />
    <hkern u1="&#x31;" u2="V" k="44" />
    <hkern g1="one.lnum" g2="zero.lnum" k="11" />
    <hkern g1="one.lnum" u2="&#x2b;" k="17" />
    <hkern g1="one.lnum" u2="&#xd7;" k="10" />
    <hkern g1="one.lnum" u2="&#x2212;" k="10" />
    <hkern g1="one.lnum" g2="four.lnum" k="15" />
    <hkern u1="p" u2="&#xd0;" k="16" />
    <hkern u1="&#x28;" u2="&#x18f;" k="10" />
    <hkern u1="&#x28;" g2="zero.lnum" k="13" />
    <hkern u1="&#x28;" u2="&#x30;" k="20" />
    <hkern u1="&#x28;" g2="six.lnum" k="10" />
    <hkern u1="&#x28;" u2="&#x36;" k="10" />
    <hkern u1="&#x28;" u2="&#x28;" k="10" />
    <hkern u1="&#x28;" u2="&#x129;" k="-21" />
    <hkern u1="&#x28;" u2="&#x12b;" k="-41" />
    <hkern u1="&#x28;" g2="four.lnum" k="20" />
    <hkern u1="&#x28;" u2="&#xf0;" k="12" />
    <hkern u1="&#x28;" g2="U.sc" k="10" />
    <hkern u1="&#x29;" u2="&#x29;" k="10" />
    <hkern u1="&#x2e;" u2="&#x166;" k="72" />
    <hkern u1="&#x2b;" g2="two.lnum" k="12" />
    <hkern u1="&#x2b;" u2="&#x33;" k="29" />
    <hkern u1="&#x2b;" g2="seven.lnum" k="41" />
    <hkern u1="&#x2b;" u2="&#x37;" k="40" />
    <hkern u1="&#x2b;" g2="one.lnum" k="21" />
    <hkern u1="&#x2b;" u2="&#x31;" k="17" />
    <hkern u1="&#x2b;" u2="&#x34;" k="37" />
    <hkern u1="q" u2="j" k="-59" />
    <hkern u1="q" u2="W" k="40" />
    <hkern g1="q.alt" u2="j" k="-59" />
    <hkern u1="&#xbf;" u2="&#x18f;" k="29" />
    <hkern u1="&#xbf;" g2="y.alt" k="31" />
    <hkern u1="&#xbf;" u2="x" k="26" />
    <hkern u1="&#xbf;" g2="w.alt" k="40" />
    <hkern u1="&#xbf;" u2="v" k="41" />
    <hkern u1="&#xbf;" g2="t.alt" k="33" />
    <hkern u1="&#xbf;" u2="&#x259;" k="35" />
    <hkern u1="&#xbf;" u2="&#x142;" k="40" />
    <hkern u1="&#xbf;" g2="l.alt" k="26" />
    <hkern u1="&#xbf;" u2="&#x135;" k="-90" />
    <hkern u1="&#xbf;" u2="&#x12f;" k="6" />
    <hkern u1="&#xbf;" u2="j" k="-82" />
    <hkern u1="&#xbf;" u2="&#xf0;" k="39" />
    <hkern u1="&#xbf;" g2="Z.sc" k="29" />
    <hkern u1="&#xbf;" g2="Y.sc" k="49" />
    <hkern u1="&#xbf;" g2="X.sc" k="24" />
    <hkern u1="&#xbf;" u2="X" k="23" />
    <hkern u1="&#xbf;" g2="W.sc" k="45" />
    <hkern u1="&#xbf;" g2="W.alt" k="47" />
    <hkern u1="&#xbf;" g2="V.sc" k="45" />
    <hkern u1="&#xbf;" u2="V" k="67" />
    <hkern u1="&#xbf;" g2="U.sc" k="33" />
    <hkern u1="&#xbf;" u2="&#x166;" k="55" />
    <hkern u1="&#xbf;" g2="T.sc" k="41" />
    <hkern u1="&#xbf;" g2="S.sc" k="36" />
    <hkern u1="&#xbf;" u2="&#x141;" k="37" />
    <hkern u1="&#xbf;" g2="J.sc" k="49" />
    <hkern u1="&#xbf;" u2="&#x126;" k="31" />
    <hkern u1="&#xbf;" u2="&#xd0;" k="37" />
    <hkern u1="&#xbf;" u2="&#x110;" k="37" />
    <hkern u1="&#xbf;" g2="A.sc" k="45" />
    <hkern u1="&#x22;" u2="&#x129;" k="-8" />
    <hkern u1="&#x22;" u2="&#x12b;" k="-9" />
    <hkern u1="&#x201e;" u2="&#x166;" k="72" />
    <hkern u1="&#x201a;" u2="&#x166;" k="72" />
    <hkern u1="&#x201c;" u2="&#x12b;" k="-8" />
    <hkern u1="&#x2018;" u2="&#x12b;" k="-8" />
    <hkern u1="&#x201d;" u2="&#x129;" k="-13" />
    <hkern u1="&#x201d;" u2="&#x12b;" k="-15" />
    <hkern u1="&#x2019;" u2="&#x129;" k="-13" />
    <hkern u1="&#x2019;" u2="&#x12b;" k="-15" />
    <hkern u1="&#x27;" u2="&#x129;" k="-8" />
    <hkern u1="&#x27;" u2="&#x12b;" k="-9" />
    <hkern u1="r" u2="&#xd0;" k="9" />
    <hkern u1="s" u2="&#xd0;" k="12" />
    <hkern u1="&#x3b;" u2="&#x166;" k="6" />
    <hkern u1="&#x37;" u2="&#x30;" k="14" />
    <hkern u1="&#x37;" u2="&#x2b;" k="11" />
    <hkern u1="&#x37;" u2="&#x29;" k="10" />
    <hkern u1="&#x37;" u2="&#x2212;" k="41" />
    <hkern u1="&#x37;" u2="&#x34;" k="57" />
    <hkern u1="&#x37;" u2="&#x35;" k="19" />
    <hkern u1="&#x37;" u2="&#x3d;" k="17" />
    <hkern u1="&#x37;" u2="&#xf7;" k="19" />
    <hkern u1="&#x37;" u2="&#xa2;" k="20" />
    <hkern u1="&#x37;" u2="&#x2f;" k="63" />
    <hkern u1="&#x37;" u2="&#xb7;" k="14" />
    <hkern g1="seven.lnum" g2="zero.lnum" k="15" />
    <hkern g1="seven.lnum" g2="six.lnum" k="13" />
    <hkern g1="seven.lnum" g2="seven.lnum" k="-11" />
    <hkern g1="seven.lnum" u2="&#x2b;" k="14" />
    <hkern g1="seven.lnum" u2="&#x23;" k="22" />
    <hkern g1="seven.lnum" u2="&#xd7;" k="10" />
    <hkern g1="seven.lnum" u2="&#x2212;" k="27" />
    <hkern g1="seven.lnum" g2="four.lnum" k="54" />
    <hkern g1="seven.lnum" g2="five.lnum" k="11" />
    <hkern g1="seven.lnum" u2="&#xf7;" k="24" />
    <hkern g1="seven.lnum" u2="&#xa2;" k="39" />
    <hkern g1="seven.lnum" u2="\" k="-11" />
    <hkern g1="seven.lnum" u2="&#x2f;" k="87" />
    <hkern g1="seven.lnum" u2="&#xb7;" k="10" />
    <hkern g1="seven.numerator" u2="&#x2044;" k="54" />
    <hkern u1="&#x36;" u2="V" k="20" />
    <hkern u1="&#x20;" g2="y.alt" k="30" />
    <hkern u1="&#x20;" g2="w.alt" k="26" />
    <hkern u1="&#x20;" u2="v" k="28" />
    <hkern u1="&#x20;" u2="&#x142;" k="13" />
    <hkern u1="&#x20;" g2="Y.sc" k="34" />
    <hkern u1="&#x20;" u2="X" k="11" />
    <hkern u1="&#x20;" g2="W.sc" k="33" />
    <hkern u1="&#x20;" g2="W.alt" k="28" />
    <hkern u1="&#x20;" g2="V.sc" k="33" />
    <hkern u1="&#x20;" u2="V" k="37" />
    <hkern u1="&#x20;" u2="&#x166;" k="20" />
    <hkern u1="&#x20;" g2="T.sc" k="26" />
    <hkern u1="&#x20;" g2="J.sc" k="19" />
    <hkern u1="&#x20;" g2="A.sc" k="29" />
    <hkern u1="&#xa3;" g2="four.lnum" k="26" />
    <hkern u1="t" u2="&#xd0;" k="7" />
    <hkern g1="t.alt" u2="&#x20;" k="17" />
    <hkern g1="t.alt" u2="&#x259;" k="9" />
    <hkern g1="t.alt" u2="&#xf0;" k="31" />
    <hkern g1="t.alt" u2="&#x2bc;" k="37" />
    <hkern g1="t.alt" u2="X" k="12" />
    <hkern g1="t.alt" u2="V" k="5" />
    <hkern g1="t.alt" u2="&#x2f;" k="17" />
    <hkern g1="t.alt" u2="&#xd0;" k="7" />
    <hkern u1="&#xfe;" u2="&#xd0;" k="16" />
    <hkern u1="&#x33;" u2="&#x37;" k="17" />
    <hkern u1="&#x33;" u2="&#xb0;" k="37" />
    <hkern u1="&#x33;" u2="\" k="29" />
    <hkern u1="&#x33;" g2="W.alt" k="17" />
    <hkern u1="&#x33;" u2="V" k="37" />
    <hkern u1="&#x32;" u2="&#xb0;" k="18" />
    <hkern u1="&#x32;" u2="\" k="18" />
    <hkern u1="&#x32;" g2="W.alt" k="11" />
    <hkern u1="&#x32;" u2="V" k="31" />
    <hkern g1="two.lnum" g2="four.lnum" k="24" />
    <hkern u1="u" u2="&#xd0;" k="8" />
    <hkern u1="&#x15e;" u2="&#x166;" k="24" />
    <hkern u1="&#x162;" u2="&#xdf;" k="38" />
    <hkern u1="&#x162;" g2="f_i" k="27" />
    <hkern u1="&#x162;" g2="f_f_l" k="49" />
    <hkern u1="&#x162;" g2="f_f_i" k="49" />
    <hkern u1="&#x162;" g2="f_f" k="49" />
    <hkern u1="&#x163;" u2="j" k="-121" />
    <hkern u1="&#x218;" u2="&#x166;" k="24" />
    <hkern u1="&#x21a;" u2="&#xdf;" k="38" />
    <hkern u1="&#x21a;" g2="f_i" k="27" />
    <hkern u1="&#x21a;" g2="f_f_l" k="49" />
    <hkern u1="&#x21a;" g2="f_f_i" k="49" />
    <hkern u1="&#x21a;" g2="f_f" k="49" />
    <hkern u1="&#x21b;" u2="j" k="-48" />
    <hkern u1="&#x173;" u2="j" k="-28" />
    <hkern u1="v" u2="&#x20;" k="19" />
    <hkern u1="v" u2="&#x259;" k="7" />
    <hkern u1="v" u2="&#x29;" k="14" />
    <hkern u1="v" u2="&#xf0;" k="10" />
    <hkern u1="v" u2="&#x2bc;" k="21" />
    <hkern u1="v" u2="X" k="25" />
    <hkern u1="v" g2="W.alt" k="6" />
    <hkern u1="v" u2="V" k="22" />
    <hkern u1="v" u2="&#x2f;" k="26" />
    <hkern u1="v" u2="&#xd0;" k="15" />
    <hkern u1="w" u2="&#xd0;" k="15" />
    <hkern g1="w.alt" u2="&#x20;" k="24" />
    <hkern g1="w.alt" u2="&#x259;" k="16" />
    <hkern g1="w.alt" u2="&#x29;" k="13" />
    <hkern g1="w.alt" u2="&#xf0;" k="19" />
    <hkern g1="w.alt" u2="\" k="10" />
    <hkern g1="w.alt" u2="&#x2bc;" k="21" />
    <hkern g1="w.alt" u2="X" k="21" />
    <hkern g1="w.alt" u2="V" k="14" />
    <hkern g1="w.alt" u2="&#x2f;" k="30" />
    <hkern g1="w.alt" u2="&#xd0;" k="11" />
    <hkern u1="x" u2="&#x259;" k="13" />
    <hkern u1="x" u2="&#xf0;" k="26" />
    <hkern u1="x" u2="&#x2bc;" k="18" />
    <hkern u1="x" u2="V" k="10" />
    <hkern u1="x" u2="&#xd0;" k="6" />
    <hkern u1="y" u2="&#xd0;" k="15" />
    <hkern g1="y.alt" u2="&#x20;" k="24" />
    <hkern g1="y.alt" u2="&#x259;" k="9" />
    <hkern g1="y.alt" u2="&#x29;" k="10" />
    <hkern g1="y.alt" u2="&#xf0;" k="15" />
    <hkern g1="y.alt" u2="&#x2bc;" k="18" />
    <hkern g1="y.alt" u2="X" k="20" />
    <hkern g1="y.alt" u2="V" k="7" />
    <hkern g1="y.alt" u2="&#x2f;" k="35" />
    <hkern g1="y.alt" u2="&#xd0;" k="9" />
    <hkern u1="z" u2="&#xd0;" k="9" />
    <hkern u1="&#x30;" u2="&#x37;" k="28" />
    <hkern u1="&#x30;" u2="&#x29;" k="13" />
    <hkern u1="&#x30;" u2="&#xb0;" k="32" />
    <hkern u1="&#x30;" u2="\" k="33" />
    <hkern u1="&#x30;" g2="W.alt" k="22" />
    <hkern u1="&#x30;" u2="V" k="42" />
    <hkern u1="&#x30;" u2="&#x2f;" k="10" />
    <hkern g1="zero.lnum" g2="seven.lnum" k="25" />
    <hkern g1="zero.lnum" u2="&#x29;" k="13" />
    <hkern g1="zero.lnum" g2="one.lnum" k="12" />
    <hkern g1="zero.lnum" u2="&#xb0;" k="16" />
    <hkern g1="zero.lnum" u2="\" k="15" />
    <hkern g1="zero.lnum" u2="&#x2f;" k="17" />
    <hkern u1="&#x2120;" u2="&#x129;" k="-7" />
    <hkern u1="&#x2120;" u2="&#x12b;" k="-9" />
    <hkern u1="&#x2120;" g2="J.sc" k="32" />
    <hkern u1="&#x2120;" g2="A.sc" k="27" />
    <hkern u1="&#x18f;" u2="&#x166;" k="3" />
    <hkern u1="&#x18f;" u2="&#x141;" k="8" />
    <hkern u1="&#x18f;" u2="&#xd0;" k="8" />
    <hkern u1="&#x18f;" u2="&#x110;" k="8" />
    <hkern u1="&#x1e0c;" u2="&#x166;" k="3" />
    <hkern u1="&#x1e0c;" u2="&#x141;" k="7" />
    <hkern u1="&#x1e0c;" u2="&#xd0;" k="7" />
    <hkern u1="&#x1e0c;" u2="&#x110;" k="7" />
    <hkern u1="&#x1eb8;" u2="&#xdf;" k="8" />
    <hkern u1="&#x1f4;" u2="&#x166;" k="20" />
    <hkern u1="&#x1eca;" u2="j" k="-29" />
    <hkern u1="&#x1ecc;" u2="&#x166;" k="3" />
    <hkern u1="&#x1ecc;" u2="&#x141;" k="8" />
    <hkern u1="&#x1ecc;" u2="&#xd0;" k="8" />
    <hkern u1="&#x1ecc;" u2="&#x110;" k="8" />
    <hkern u1="&#x1e5a;" u2="&#x166;" k="5" />
    <hkern u1="&#x1e5a;" u2="&#x141;" k="7" />
    <hkern u1="&#x1e5a;" u2="&#xd0;" k="7" />
    <hkern u1="&#x1e5a;" u2="&#x110;" k="7" />
    <hkern u1="&#x1e62;" u2="&#x166;" k="24" />
    <hkern u1="&#x1e6c;" u2="&#xdf;" k="38" />
    <hkern u1="&#x1ee4;" u2="&#x141;" k="6" />
    <hkern u1="&#x1ee4;" u2="&#xd0;" k="6" />
    <hkern u1="&#x1ee4;" u2="&#x110;" k="6" />
    <hkern u1="&#x1f5;" u2="j" k="-27" />
    <hkern u1="&#x1ecb;" u2="j" k="-44" />
    <hkern u1="&#x1e6d;" u2="j" k="-32" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="14" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="18" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="a.alt2,ae,aeacute"
	k="6" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="11" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="17" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="6" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="19" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="16" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="28" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="31" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="31" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="13" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="77" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="T.sc"
	k="45" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="35" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="U.sc"
	k="15" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="V"
	k="66" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="V.sc"
	k="26" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="66" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="W.alt"
	k="36" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="W.sc"
	k="26" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="77" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="Y.sc"
	k="39" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="ampersand"
	k="16" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="apostrophe"
	k="121" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="asterisk"
	k="67" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="at"
	k="15" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="backslash"
	k="68" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="bar"
	k="38" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="brokenbar"
	k="36" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="copyright"
	k="42" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="eight"
	k="17" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="eth"
	k="15" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="guillemotleft,guilsinglleft"
	k="19" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="emdash,endash,hyphen,uni00AD"
	k="15" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="ordfeminine"
	k="47" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="ordmasculine"
	k="53" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="question"
	k="39" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="quotedblleft,quoteleft"
	k="69" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="quotedblright,quoteright"
	k="69" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="quotedbl,quotesingle"
	k="68" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="registered"
	k="42" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="schwa"
	k="11" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="seven"
	k="31" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="six"
	k="22" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="space"
	k="33" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="t.alt"
	k="11" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="trademark"
	k="64" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="v"
	k="25" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="w.alt"
	k="23" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="y.alt"
	k="32" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="zero"
	k="22" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="servicemark"
	k="54" />
    <hkern g1="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	g2="Schwa"
	k="21" />
    <hkern g1="B"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="5" />
    <hkern g1="B"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="5" />
    <hkern g1="B"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="32" />
    <hkern g1="B"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="21" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="28" />
    <hkern g1="B"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="19" />
    <hkern g1="B"
	g2="J,Jcircumflex"
	k="5" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="6" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="16" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="V"
	k="11" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="11" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="16" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="apostrophe"
	k="71" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="16" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="5" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="5" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="61" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="V"
	k="29" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="29" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="W.alt"
	k="11" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="48" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="apostrophe"
	k="79" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="asterisk"
	k="9" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="backslash"
	k="15" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="quotedblleft,quoteleft"
	k="9" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="quotedblright,quoteright"
	k="9" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="quotedbl,quotesingle"
	k="9" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="trademark"
	k="9" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="26" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="J,Jcircumflex"
	k="22" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="A.sc"
	k="13" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="AE,AEacute"
	k="8" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="J.sc"
	k="26" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="slash"
	k="18" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="X"
	k="28" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="12" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="parenright"
	k="13" />
    <hkern g1="D,Dcaron,Dcroat,Eth,Ddotbelow"
	g2="x"
	k="8" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="13" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="22" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="a.alt2,ae,aeacute"
	k="11" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="10" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="9" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="22" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="7" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="10" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="21" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="12" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="10" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="13" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="13" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="T.sc"
	k="6" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="U.sc"
	k="12" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="V.sc"
	k="6" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="W.sc"
	k="6" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="Y.sc"
	k="6" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="apostrophe"
	k="47" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="eth"
	k="23" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="guillemotleft,guilsinglleft"
	k="13" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="emdash,endash,hyphen,uni00AD"
	k="16" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="schwa"
	k="15" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="seven"
	k="17" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="t.alt"
	k="8" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="v"
	k="13" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="w.alt"
	k="12" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="y.alt"
	k="9" />
    <hkern g1="AE,AEacute,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eogonek,Etilde,OE,Edotbelow"
	g2="Schwa"
	k="14" />
    <hkern g1="F"
	g2="B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,K.sc,L.sc,M.sc,N.sc,P.sc,R.sc"
	k="35" />
    <hkern g1="F"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="22" />
    <hkern g1="F"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="37" />
    <hkern g1="F"
	g2="a.alt2,ae,aeacute"
	k="66" />
    <hkern g1="F"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="18" />
    <hkern g1="F"
	g2="dotlessi,dotlessj,i,iacute,ibreve,icircumflex,idieresis,igrave,ij,j,imacron,iogonek,itilde,j.alt,jcircumflex,idotbelow"
	k="9" />
    <hkern g1="F"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="52" />
    <hkern g1="F"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="37" />
    <hkern g1="F"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="55" />
    <hkern g1="F"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="15" />
    <hkern g1="F"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="45" />
    <hkern g1="F"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="18" />
    <hkern g1="F"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="13" />
    <hkern g1="F"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="31" />
    <hkern g1="F"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="18" />
    <hkern g1="F"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="24" />
    <hkern g1="F"
	g2="guillemotleft,guilsinglleft"
	k="26" />
    <hkern g1="F"
	g2="emdash,endash,hyphen,uni00AD"
	k="39" />
    <hkern g1="F"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="79" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="156" />
    <hkern g1="F"
	g2="AE,AEacute"
	k="128" />
    <hkern g1="F"
	g2="colon,semicolon"
	k="13" />
    <hkern g1="F"
	g2="guillemotright,guilsinglright"
	k="32" />
    <hkern g1="F"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="79" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="5" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="7" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="6" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="31" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="V"
	k="20" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="20" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="W.alt"
	k="5" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="29" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="apostrophe"
	k="75" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="v"
	k="6" />
    <hkern g1="G,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,Gacute"
	g2="y.alt"
	k="5" />
    <hkern g1="G.alt"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="6" />
    <hkern g1="G.alt"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="6" />
    <hkern g1="G.alt"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="45" />
    <hkern g1="G.alt"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="23" />
    <hkern g1="G.alt"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="36" />
    <hkern g1="G.alt"
	g2="quotedblleft,quoteleft"
	k="9" />
    <hkern g1="G.alt"
	g2="quotedblright,quoteright"
	k="10" />
    <hkern g1="G.alt"
	g2="quotedbl,quotesingle"
	k="9" />
    <hkern g1="G.alt"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="24" />
    <hkern g1="G.alt"
	g2="J,Jcircumflex"
	k="17" />
    <hkern g1="G.alt"
	g2="AE,AEacute"
	k="5" />
    <hkern g1="G.alt"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="11" />
    <hkern g1="Eng,H,Hbar,Hcircumflex,I,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,Hdotbelow,Idotbelow"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="7" />
    <hkern g1="Eng,H,Hbar,Hcircumflex,I,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,Hdotbelow,Idotbelow"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="6" />
    <hkern g1="Eng,H,Hbar,Hcircumflex,I,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,Hdotbelow,Idotbelow"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="8" />
    <hkern g1="Eng,H,Hbar,Hcircumflex,I,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,Hdotbelow,Idotbelow"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="7" />
    <hkern g1="Eng,H,Hbar,Hcircumflex,I,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,Hdotbelow,Idotbelow"
	g2="apostrophe"
	k="53" />
    <hkern g1="Eng,H,Hbar,Hcircumflex,I,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,Hdotbelow,Idotbelow"
	g2="eth"
	k="8" />
    <hkern g1="Eng,H,Hbar,Hcircumflex,I,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,Hdotbelow,Idotbelow"
	g2="t.alt"
	k="6" />
    <hkern g1="IJ,J,Jcircumflex"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="7" />
    <hkern g1="IJ,J,Jcircumflex"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="7" />
    <hkern g1="IJ,J,Jcircumflex"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="5" />
    <hkern g1="IJ,J,Jcircumflex"
	g2="apostrophe"
	k="51" />
    <hkern g1="IJ,J,Jcircumflex"
	g2="eth"
	k="8" />
    <hkern g1="IJ,J,Jcircumflex"
	g2="J,Jcircumflex"
	k="7" />
    <hkern g1="IJ,J,Jcircumflex"
	g2="A.sc"
	k="11" />
    <hkern g1="IJ,J,Jcircumflex"
	g2="J.sc"
	k="15" />
    <hkern g1="IJ,J,Jcircumflex"
	g2="slash"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="24" />
    <hkern g1="K,Kcommaaccent"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="29" />
    <hkern g1="K,Kcommaaccent"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="28" />
    <hkern g1="K,Kcommaaccent"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="16" />
    <hkern g1="K,Kcommaaccent"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="18" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="17" />
    <hkern g1="K,Kcommaaccent"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="35" />
    <hkern g1="K,Kcommaaccent"
	g2="U.sc"
	k="12" />
    <hkern g1="K,Kcommaaccent"
	g2="V.sc"
	k="7" />
    <hkern g1="K,Kcommaaccent"
	g2="W.sc"
	k="7" />
    <hkern g1="K,Kcommaaccent"
	g2="Y.sc"
	k="6" />
    <hkern g1="K,Kcommaaccent"
	g2="apostrophe"
	k="40" />
    <hkern g1="K,Kcommaaccent"
	g2="copyright"
	k="12" />
    <hkern g1="K,Kcommaaccent"
	g2="eth"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="emdash,endash,hyphen,uni00AD"
	k="24" />
    <hkern g1="K,Kcommaaccent"
	g2="registered"
	k="12" />
    <hkern g1="K,Kcommaaccent"
	g2="schwa"
	k="6" />
    <hkern g1="K,Kcommaaccent"
	g2="space"
	k="13" />
    <hkern g1="K,Kcommaaccent"
	g2="v"
	k="18" />
    <hkern g1="K,Kcommaaccent"
	g2="w.alt"
	k="17" />
    <hkern g1="K,Kcommaaccent"
	g2="y.alt"
	k="18" />
    <hkern g1="K,Kcommaaccent"
	g2="zero"
	k="16" />
    <hkern g1="K,Kcommaaccent"
	g2="Schwa"
	k="24" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="34" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="39" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="a.alt2,ae,aeacute"
	k="5" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="8" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="l,lacute,lcaron,lcommaaccent,ldot,lslash"
	k="5" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="37" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="6" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="15" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="18" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="70" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="82" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="61" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="10" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="136" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="T.sc"
	k="100" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="49" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="U.sc"
	k="32" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="V"
	k="112" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="V.sc"
	k="82" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="112" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="W.alt"
	k="68" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="W.sc"
	k="83" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="120" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="Y.sc"
	k="80" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="apostrophe"
	k="149" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="asterisk"
	k="108" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="backslash"
	k="96" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="bar"
	k="23" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="brokenbar"
	k="20" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="copyright"
	k="71" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="eth"
	k="30" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="guillemotleft,guilsinglleft"
	k="78" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="emdash,endash,hyphen,uni00AD"
	k="96" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="ordfeminine"
	k="107" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="ordmasculine"
	k="107" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="question"
	k="28" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="quotedblleft,quoteleft"
	k="81" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="quotedblright,quoteright"
	k="79" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="quotedbl,quotesingle"
	k="81" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="registered"
	k="71" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="schwa"
	k="24" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="seven"
	k="11" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="six"
	k="21" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="space"
	k="29" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="t.alt"
	k="8" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="trademark"
	k="108" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="v"
	k="66" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="w.alt"
	k="64" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="y.alt"
	k="83" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="zero"
	k="17" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="servicemark"
	k="107" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="Schwa"
	k="38" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="AE,AEacute"
	k="-9" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="guillemotright,guilsinglright"
	k="11" />
    <hkern g1="L,Lacute,Lcaron,Lcommaaccent,Ldot,Lslash"
	g2="periodcentered"
	k="156" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="5" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="6" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="62" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="V"
	k="30" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="30" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="W.alt"
	k="11" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="50" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="apostrophe"
	k="81" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="asterisk"
	k="10" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="backslash"
	k="16" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="quotedblleft,quoteleft"
	k="11" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="quotedblright,quoteright"
	k="11" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="quotedbl,quotesingle"
	k="11" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="trademark"
	k="10" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="27" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="J,Jcircumflex"
	k="26" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="A.sc"
	k="15" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="AE,AEacute"
	k="9" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="J.sc"
	k="29" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="slash"
	k="19" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="X"
	k="31" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="14" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="parenright"
	k="15" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="x"
	k="8" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Schwa,Odotbelow"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="9" />
    <hkern g1="P"
	g2="a.alt2,ae,aeacute"
	k="6" />
    <hkern g1="P"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="9" />
    <hkern g1="P"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="14" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="15" />
    <hkern g1="P"
	g2="emdash,endash,hyphen,uni00AD"
	k="13" />
    <hkern g1="P"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="51" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="105" />
    <hkern g1="P"
	g2="AE,AEacute"
	k="64" />
    <hkern g1="P"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="10" />
    <hkern g1="P"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="79" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="11" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="a.alt2,ae,aeacute"
	k="7" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="b,germandbls,h,hbar,hcircumflex,k,kcommaaccent,thorn,hdotbelow"
	k="5" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="dotlessi,dotlessj,i,iacute,ibreve,icircumflex,idieresis,igrave,ij,j,imacron,iogonek,itilde,j.alt,jcircumflex,idotbelow"
	k="5" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="l,lacute,lcaron,lcommaaccent,ldot,lslash"
	k="5" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="5" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="11" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="6" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="24" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="V"
	k="18" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="18" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="24" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="apostrophe"
	k="63" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="eth"
	k="17" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="11" />
    <hkern g1="R,Racute,Rcaron,Rcommaaccent,Rdotbelow"
	g2="l.alt"
	k="5" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="5" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="11" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="15" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="T.sc"
	k="9" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="V"
	k="16" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="V.sc"
	k="8" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="16" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="W.sc"
	k="8" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="16" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="Y.sc"
	k="9" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="apostrophe"
	k="67" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="ordfeminine"
	k="9" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="seven"
	k="10" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="v"
	k="12" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="w.alt"
	k="10" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="y.alt"
	k="10" />
    <hkern g1="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="9" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,K.sc,L.sc,M.sc,N.sc,P.sc,R.sc"
	k="67" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="90" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="110" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="a.alt2,ae,aeacute"
	k="103" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="20" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="88" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="110" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="102" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="8" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="85" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="81" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="77" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="91" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="34" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="T.sc"
	k="78" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="U.sc"
	k="60" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="V.sc"
	k="69" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="W.sc"
	k="69" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="Y.sc"
	k="68" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="apostrophe"
	k="26" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="at"
	k="76" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="copyright"
	k="11" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="eth"
	k="86" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="guillemotleft,guilsinglleft"
	k="81" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="emdash,endash,hyphen,uni00AD"
	k="67" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="registered"
	k="11" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="schwa"
	k="102" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="seven"
	k="42" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="six"
	k="14" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="space"
	k="18" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="t.alt"
	k="8" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="v"
	k="81" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="w.alt"
	k="80" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="y.alt"
	k="78" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="zero"
	k="73" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="Schwa"
	k="24" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="79" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="J,Jcircumflex"
	k="115" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="A.sc"
	k="84" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="AE,AEacute"
	k="86" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="J.sc"
	k="87" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="slash"
	k="80" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="x"
	k="74" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="colon,semicolon"
	k="53" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="guillemotright,guilsinglright"
	k="79" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="67" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="S.sc"
	k="83" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="X.sc"
	k="63" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="Z.sc"
	k="72" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="five"
	k="75" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="four"
	k="82" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="nine"
	k="74" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="one"
	k="71" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="three"
	k="74" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	g2="two"
	k="69" />
    <hkern g1="Thorn"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="5" />
    <hkern g1="Thorn"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="5" />
    <hkern g1="Thorn"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="90" />
    <hkern g1="Thorn"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="34" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="67" />
    <hkern g1="Thorn"
	g2="quotedblleft,quoteleft"
	k="29" />
    <hkern g1="Thorn"
	g2="quotedblright,quoteright"
	k="30" />
    <hkern g1="Thorn"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="Thorn"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="26" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="24" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="13" />
    <hkern g1="Thorn"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="20" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="7" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="a.alt2,ae,aeacute"
	k="6" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="b,germandbls,h,hbar,hcircumflex,k,kcommaaccent,thorn,hdotbelow"
	k="5" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="dotlessi,dotlessj,i,iacute,ibreve,icircumflex,idieresis,igrave,ij,j,imacron,iogonek,itilde,j.alt,jcircumflex,idotbelow"
	k="5" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="l,lacute,lcaron,lcommaaccent,ldot,lslash"
	k="6" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="5" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="7" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="7" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="7" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="apostrophe"
	k="50" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="eth"
	k="9" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="19" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="J,Jcircumflex"
	k="27" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="A.sc"
	k="22" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="AE,AEacute"
	k="14" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="J.sc"
	k="26" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="slash"
	k="23" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="13" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="l.alt"
	k="5" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	g2="four"
	k="12" />
    <hkern g1="V"
	g2="B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,K.sc,L.sc,M.sc,N.sc,P.sc,R.sc"
	k="26" />
    <hkern g1="V"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="44" />
    <hkern g1="V"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="56" />
    <hkern g1="V"
	g2="a.alt2,ae,aeacute"
	k="49" />
    <hkern g1="V"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="38" />
    <hkern g1="V"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="57" />
    <hkern g1="V"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="49" />
    <hkern g1="V"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="38" />
    <hkern g1="V"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="8" />
    <hkern g1="V"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="7" />
    <hkern g1="V"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="26" />
    <hkern g1="V"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="20" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="42" />
    <hkern g1="V"
	g2="emdash,endash,hyphen,uni00AD"
	k="45" />
    <hkern g1="V"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="59" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="89" />
    <hkern g1="V"
	g2="AE,AEacute"
	k="75" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="8" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="37" />
    <hkern g1="V"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="75" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,K.sc,L.sc,M.sc,N.sc,P.sc,R.sc"
	k="26" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="44" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="56" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="a.alt2,ae,aeacute"
	k="49" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="38" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="57" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="49" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="38" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="8" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="7" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="26" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="20" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="U.sc"
	k="17" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="ampersand"
	k="26" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="apostrophe"
	k="48" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="at"
	k="45" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="copyright"
	k="22" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="eight"
	k="22" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="eth"
	k="66" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="guillemotleft,guilsinglleft"
	k="42" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="emdash,endash,hyphen,uni00AD"
	k="45" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="registered"
	k="22" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="schwa"
	k="50" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="six"
	k="24" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="space"
	k="32" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="v"
	k="8" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="w.alt"
	k="9" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="zero"
	k="34" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="Schwa"
	k="24" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="59" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="J,Jcircumflex"
	k="89" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="A.sc"
	k="75" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="AE,AEacute"
	k="75" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="J.sc"
	k="71" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="slash"
	k="78" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="x"
	k="11" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="colon,semicolon"
	k="8" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="guillemotright,guilsinglright"
	k="37" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="75" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="S.sc"
	k="36" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="five"
	k="39" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="four"
	k="72" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="nine"
	k="28" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="one"
	k="23" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="three"
	k="27" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="two"
	k="27" />
    <hkern g1="W.alt"
	g2="B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,K.sc,L.sc,M.sc,N.sc,P.sc,R.sc"
	k="10" />
    <hkern g1="W.alt"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="28" />
    <hkern g1="W.alt"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="38" />
    <hkern g1="W.alt"
	g2="a.alt2,ae,aeacute"
	k="36" />
    <hkern g1="W.alt"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="28" />
    <hkern g1="W.alt"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="38" />
    <hkern g1="W.alt"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="33" />
    <hkern g1="W.alt"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="25" />
    <hkern g1="W.alt"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="15" />
    <hkern g1="W.alt"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="14" />
    <hkern g1="W.alt"
	g2="guillemotleft,guilsinglleft"
	k="25" />
    <hkern g1="W.alt"
	g2="emdash,endash,hyphen,uni00AD"
	k="26" />
    <hkern g1="W.alt"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="44" />
    <hkern g1="W.alt"
	g2="J,Jcircumflex"
	k="57" />
    <hkern g1="W.alt"
	g2="AE,AEacute"
	k="56" />
    <hkern g1="W.alt"
	g2="guillemotright,guilsinglright"
	k="18" />
    <hkern g1="W.alt"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="49" />
    <hkern g1="X"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="25" />
    <hkern g1="X"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="31" />
    <hkern g1="X"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="30" />
    <hkern g1="X"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="17" />
    <hkern g1="X"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="9" />
    <hkern g1="X"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="8" />
    <hkern g1="X"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="31" />
    <hkern g1="X"
	g2="emdash,endash,hyphen,uni00AD"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,K.sc,L.sc,M.sc,N.sc,P.sc,R.sc"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="69" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="a.alt2,ae,aeacute"
	k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="13" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="67" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="81" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="79" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="28" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="29" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="11" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="U.sc"
	k="37" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="V.sc"
	k="12" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="W.sc"
	k="11" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="Y.sc"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="ampersand"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="apostrophe"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="at"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="copyright"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="eight"
	k="22" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="eth"
	k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="guillemotleft,guilsinglleft"
	k="69" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="emdash,endash,hyphen,uni00AD"
	k="71" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="registered"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="schwa"
	k="91" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="six"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="space"
	k="33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="t.alt"
	k="5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="v"
	k="37" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="w.alt"
	k="38" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="y.alt"
	k="29" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="zero"
	k="53" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="Schwa"
	k="31" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="J,Jcircumflex"
	k="98" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="A.sc"
	k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="AE,AEacute"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="J.sc"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="slash"
	k="87" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="x"
	k="34" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="colon,semicolon"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="guillemotright,guilsinglright"
	k="58" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="79" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="S.sc"
	k="52" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="X.sc"
	k="9" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="Z.sc"
	k="9" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="five"
	k="51" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="four"
	k="88" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="nine"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="one"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="three"
	k="43" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	g2="two"
	k="41" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="7" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="10" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="10" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="9" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="14" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="apostrophe"
	k="39" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="eth"
	k="11" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="emdash,endash,hyphen,uni00AD"
	k="10" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="schwa"
	k="6" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="zero"
	k="16" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent,uni01F1,uni01C4,Zdotbelow"
	g2="Schwa"
	k="9" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="5" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="108" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde"
	g2="V"
	k="41" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="41" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="69" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde"
	g2="apostrophe"
	k="25" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde"
	g2="backslash"
	k="19" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde"
	g2="bar"
	k="11" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde"
	g2="question"
	k="14" />
    <hkern g1="a.alt,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,u.alt,gacute"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="108" />
    <hkern g1="a.alt,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,u.alt,gacute"
	g2="V"
	k="40" />
    <hkern g1="a.alt,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,u.alt,gacute"
	g2="W.alt"
	k="26" />
    <hkern g1="a.alt,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,u.alt,gacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="68" />
    <hkern g1="a.alt,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,u.alt,gacute"
	g2="apostrophe"
	k="24" />
    <hkern g1="a.alt,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,u.alt,gacute"
	g2="backslash"
	k="19" />
    <hkern g1="a.alt,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,u.alt,gacute"
	g2="bar"
	k="11" />
    <hkern g1="a.alt,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,u.alt,gacute"
	g2="question"
	k="14" />
    <hkern g1="a.alt2"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="5" />
    <hkern g1="a.alt2"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="6" />
    <hkern g1="a.alt2"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="13" />
    <hkern g1="a.alt2"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="12" />
    <hkern g1="a.alt2"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="6" />
    <hkern g1="a.alt2"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="110" />
    <hkern g1="a.alt2"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="5" />
    <hkern g1="a.alt2"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="53" />
    <hkern g1="a.alt2"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="86" />
    <hkern g1="a.alt2"
	g2="quotedblleft,quoteleft"
	k="15" />
    <hkern g1="a.alt2"
	g2="quotedblright,quoteright"
	k="15" />
    <hkern g1="a.alt2"
	g2="quotedbl,quotesingle"
	k="14" />
    <hkern g1="b,p,thorn"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="7" />
    <hkern g1="b,p,thorn"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="8" />
    <hkern g1="b,p,thorn"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="16" />
    <hkern g1="b,p,thorn"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="15" />
    <hkern g1="b,p,thorn"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="9" />
    <hkern g1="b,p,thorn"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="104" />
    <hkern g1="b,p,thorn"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="13" />
    <hkern g1="b,p,thorn"
	g2="V"
	k="61" />
    <hkern g1="b,p,thorn"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="61" />
    <hkern g1="b,p,thorn"
	g2="W.alt"
	k="36" />
    <hkern g1="b,p,thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="89" />
    <hkern g1="b,p,thorn"
	g2="apostrophe"
	k="74" />
    <hkern g1="b,p,thorn"
	g2="asterisk"
	k="25" />
    <hkern g1="b,p,thorn"
	g2="backslash"
	k="49" />
    <hkern g1="b,p,thorn"
	g2="bar"
	k="20" />
    <hkern g1="b,p,thorn"
	g2="brokenbar"
	k="19" />
    <hkern g1="b,p,thorn"
	g2="ordmasculine"
	k="9" />
    <hkern g1="b,p,thorn"
	g2="question"
	k="31" />
    <hkern g1="b,p,thorn"
	g2="quotedblleft,quoteleft"
	k="28" />
    <hkern g1="b,p,thorn"
	g2="quotedblright,quoteright"
	k="28" />
    <hkern g1="b,p,thorn"
	g2="quotedbl,quotesingle"
	k="27" />
    <hkern g1="b,p,thorn"
	g2="t.alt"
	k="7" />
    <hkern g1="b,p,thorn"
	g2="trademark"
	k="16" />
    <hkern g1="b,p,thorn"
	g2="v"
	k="16" />
    <hkern g1="b,p,thorn"
	g2="w.alt"
	k="15" />
    <hkern g1="b,p,thorn"
	g2="y.alt"
	k="16" />
    <hkern g1="b,p,thorn"
	g2="servicemark"
	k="11" />
    <hkern g1="b,p,thorn"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="12" />
    <hkern g1="b,p,thorn"
	g2="J,Jcircumflex"
	k="11" />
    <hkern g1="b,p,thorn"
	g2="X"
	k="18" />
    <hkern g1="b,p,thorn"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="8" />
    <hkern g1="b,p,thorn"
	g2="parenright"
	k="12" />
    <hkern g1="b,p,thorn"
	g2="x"
	k="20" />
    <hkern g1="b,p,thorn"
	g2="B,D,Dcaron,Dcroat,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eng,Eogonek,Eth,Etilde,F,H,Hbar,Hcircumflex,I,IJ,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,K,Kcommaaccent,L,Lacute,Lcaron,Lcommaaccent,Lslash,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,P,R,Racute,Rcaron,Rcommaaccent,Thorn,Ddotbelow,Edotbelow,Hdotbelow,Idotbelow,Rdotbelow"
	k="7" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="5" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="5" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="6" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="6" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="9" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="102" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="8" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="V"
	k="56" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="56" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="W.alt"
	k="25" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="79" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="apostrophe"
	k="34" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="asterisk"
	k="14" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="backslash"
	k="30" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="bar"
	k="14" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="brokenbar"
	k="13" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="eth"
	k="6" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="emdash,endash,hyphen,uni00AD"
	k="8" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="question"
	k="20" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="quotedblleft,quoteleft"
	k="14" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="quotedblright,quoteright"
	k="14" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="quotedbl,quotesingle"
	k="14" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="trademark"
	k="10" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="v"
	k="6" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="w.alt"
	k="4" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="y.alt"
	k="4" />
    <hkern g1="d,dcaron,dcroat,ddotbelow"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="5" />
    <hkern g1="d,dcaron,dcroat,ddotbelow"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="5" />
    <hkern g1="d,dcaron,dcroat,ddotbelow"
	g2="apostrophe"
	k="25" />
    <hkern g1="d.alt"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="5" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="4" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="5" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="11" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="11" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="98" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="12" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="V"
	k="53" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="53" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="W.alt"
	k="37" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="93" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="apostrophe"
	k="35" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="asterisk"
	k="15" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="backslash"
	k="37" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="bar"
	k="16" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="brokenbar"
	k="14" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="question"
	k="22" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="quotedblleft,quoteleft"
	k="14" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="quotedblright,quoteright"
	k="14" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="quotedbl,quotesingle"
	k="14" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="t.alt"
	k="4" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="trademark"
	k="11" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="v"
	k="11" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="w.alt"
	k="9" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="y.alt"
	k="9" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="7" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="x"
	k="4" />
    <hkern g1="ae,aeacute,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,oe,edotbelow"
	g2="B,D,Dcaron,Dcroat,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eng,Eogonek,Eth,Etilde,F,H,Hbar,Hcircumflex,I,IJ,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,K,Kcommaaccent,L,Lacute,Lcaron,Lcommaaccent,Lslash,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,P,R,Racute,Rcaron,Rcommaaccent,Thorn,Ddotbelow,Edotbelow,Hdotbelow,Idotbelow,Rdotbelow"
	k="6" />
    <hkern g1="eth"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="5" />
    <hkern g1="eth"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="5" />
    <hkern g1="eth"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="11" />
    <hkern g1="eth"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="11" />
    <hkern g1="eth"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="6" />
    <hkern g1="eth"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="36" />
    <hkern g1="eth"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="10" />
    <hkern g1="eth"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="30" />
    <hkern g1="eth"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="34" />
    <hkern g1="eth"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="13" />
    <hkern g1="eth"
	g2="J,Jcircumflex"
	k="13" />
    <hkern g1="eth"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="9" />
    <hkern g1="f,f_f"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="16" />
    <hkern g1="f,f_f"
	g2="a.alt2,ae,aeacute"
	k="7" />
    <hkern g1="f,f_f"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="18" />
    <hkern g1="f,f_f"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="-37" />
    <hkern g1="f,f_f"
	g2="V"
	k="-37" />
    <hkern g1="f,f_f"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="-37" />
    <hkern g1="f,f_f"
	g2="W.alt"
	k="-32" />
    <hkern g1="f,f_f"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="-42" />
    <hkern g1="f,f_f"
	g2="apostrophe"
	k="9" />
    <hkern g1="f,f_f"
	g2="backslash"
	k="-37" />
    <hkern g1="f,f_f"
	g2="bar"
	k="-19" />
    <hkern g1="f,f_f"
	g2="brokenbar"
	k="-22" />
    <hkern g1="f,f_f"
	g2="eth"
	k="33" />
    <hkern g1="f,f_f"
	g2="guillemotleft,guilsinglleft"
	k="38" />
    <hkern g1="f,f_f"
	g2="emdash,endash,hyphen,uni00AD"
	k="27" />
    <hkern g1="f,f_f"
	g2="schwa"
	k="10" />
    <hkern g1="f,f_f"
	g2="space"
	k="16" />
    <hkern g1="f,f_f"
	g2="trademark"
	k="-33" />
    <hkern g1="f,f_f"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="32" />
    <hkern g1="f,f_f"
	g2="J,Jcircumflex"
	k="56" />
    <hkern g1="f,f_f"
	g2="AE,AEacute"
	k="34" />
    <hkern g1="f,f_f"
	g2="slash"
	k="27" />
    <hkern g1="f,f_f"
	g2="X"
	k="-42" />
    <hkern g1="f,f_f"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="-24" />
    <hkern g1="f,f_f"
	g2="parenright"
	k="-18" />
    <hkern g1="f,f_f"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="f,f_f"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="27" />
    <hkern g1="germandbls"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="6" />
    <hkern g1="germandbls"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="7" />
    <hkern g1="germandbls"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="14" />
    <hkern g1="germandbls"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="14" />
    <hkern g1="germandbls"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="4" />
    <hkern g1="germandbls"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="6" />
    <hkern g1="germandbls"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="48" />
    <hkern g1="germandbls"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="16" />
    <hkern g1="germandbls"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="36" />
    <hkern g1="germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="46" />
    <hkern g1="germandbls"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="8" />
    <hkern g1="germandbls"
	g2="B,D,Dcaron,Dcroat,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eng,Eogonek,Eth,Etilde,F,H,Hbar,Hcircumflex,I,IJ,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,K,Kcommaaccent,L,Lacute,Lcaron,Lcommaaccent,Lslash,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,P,R,Racute,Rcaron,Rcommaaccent,Thorn,Ddotbelow,Edotbelow,Hdotbelow,Idotbelow,Rdotbelow"
	k="9" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="5" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="5" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="13" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="12" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="7" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="110" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="5" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="V"
	k="52" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="52" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="W.alt"
	k="27" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="84" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="apostrophe"
	k="69" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="asterisk"
	k="18" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="backslash"
	k="40" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="bar"
	k="18" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="brokenbar"
	k="17" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="question"
	k="27" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="quotedblleft,quoteleft"
	k="21" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="quotedblright,quoteright"
	k="21" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="t.alt"
	k="5" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="trademark"
	k="13" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="v"
	k="13" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="w.alt"
	k="12" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="y.alt"
	k="13" />
    <hkern g1="h,hbar,hcircumflex,hdotbelow"
	g2="servicemark"
	k="8" />
    <hkern g1="dotlessi,dotlessj,f_f_i,f_i,i,iacute,ibreve,icircumflex,idieresis,igrave,ij,j,imacron,iogonek,itilde,j.alt,jcircumflex,idotbelow"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="5" />
    <hkern g1="dotlessi,dotlessj,f_f_i,f_i,i,iacute,ibreve,icircumflex,idieresis,igrave,ij,j,imacron,iogonek,itilde,j.alt,jcircumflex,idotbelow"
	g2="apostrophe"
	k="25" />
    <hkern g1="k,kcommaaccent"
	g2="a.alt2,ae,aeacute"
	k="4" />
    <hkern g1="k,kcommaaccent"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="4" />
    <hkern g1="k,kcommaaccent"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="95" />
    <hkern g1="k,kcommaaccent"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="V"
	k="41" />
    <hkern g1="k,kcommaaccent"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="41" />
    <hkern g1="k,kcommaaccent"
	g2="W.alt"
	k="17" />
    <hkern g1="k,kcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="66" />
    <hkern g1="k,kcommaaccent"
	g2="apostrophe"
	k="64" />
    <hkern g1="k,kcommaaccent"
	g2="asterisk"
	k="11" />
    <hkern g1="k,kcommaaccent"
	g2="backslash"
	k="21" />
    <hkern g1="k,kcommaaccent"
	g2="question"
	k="14" />
    <hkern g1="k,kcommaaccent"
	g2="quotedblleft,quoteleft"
	k="13" />
    <hkern g1="k,kcommaaccent"
	g2="quotedblright,quoteright"
	k="13" />
    <hkern g1="k,kcommaaccent"
	g2="quotedbl,quotesingle"
	k="13" />
    <hkern g1="k,kcommaaccent"
	g2="v"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="w.alt"
	k="4" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="6" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="5" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="13" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="12" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="13" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="20" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="11" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="V"
	k="18" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="18" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="19" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="apostrophe"
	k="36" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="asterisk"
	k="9" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="bar"
	k="15" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="brokenbar"
	k="13" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="copyright"
	k="22" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="eth"
	k="5" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="guillemotleft,guilsinglleft"
	k="26" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="emdash,endash,hyphen,uni00AD"
	k="15" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="ordfeminine"
	k="8" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="ordmasculine"
	k="8" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="question"
	k="13" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="quotedblleft,quoteleft"
	k="8" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="quotedblright,quoteright"
	k="8" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="quotedbl,quotesingle"
	k="8" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="registered"
	k="22" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="space"
	k="26" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="trademark"
	k="8" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="v"
	k="14" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="w.alt"
	k="15" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="y.alt"
	k="16" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="servicemark"
	k="8" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="AE,AEacute"
	k="-6" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="guillemotright,guilsinglright"
	k="11" />
    <hkern g1="f_f_l,f_l,l,lacute,lcaron,lcommaaccent,lslash"
	g2="periodcentered"
	k="66" />
    <hkern g1="l.alt"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="5" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="4" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="5" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="11" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="10" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="6" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="110" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="V"
	k="52" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="52" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="W.alt"
	k="26" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="80" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="apostrophe"
	k="35" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="asterisk"
	k="13" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="backslash"
	k="38" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="bar"
	k="18" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="brokenbar"
	k="15" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="question"
	k="23" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="quotedblleft,quoteleft"
	k="12" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="quotedblright,quoteright"
	k="12" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="quotedbl,quotesingle"
	k="12" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="t.alt"
	k="4" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="trademark"
	k="11" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="v"
	k="11" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="w.alt"
	k="10" />
    <hkern g1="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde"
	g2="y.alt"
	k="11" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="8" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="9" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="16" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="16" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="9" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="104" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="14" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="V"
	k="62" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="62" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="W.alt"
	k="37" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="92" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="apostrophe"
	k="42" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="asterisk"
	k="20" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="backslash"
	k="49" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="bar"
	k="20" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="brokenbar"
	k="19" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="ordmasculine"
	k="9" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="question"
	k="29" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="quotedblleft,quoteleft"
	k="20" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="quotedblright,quoteright"
	k="21" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="t.alt"
	k="8" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="trademark"
	k="15" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="v"
	k="17" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="w.alt"
	k="15" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="y.alt"
	k="17" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="servicemark"
	k="10" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="12" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="J,Jcircumflex"
	k="11" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="X"
	k="17" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="8" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="x"
	k="21" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,schwa,odotbelow"
	g2="B,D,Dcaron,Dcroat,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eng,Eogonek,Eth,Etilde,F,H,Hbar,Hcircumflex,I,IJ,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,K,Kcommaaccent,L,Lacute,Lcaron,Lcommaaccent,Lslash,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,P,R,Racute,Rcaron,Rcommaaccent,Thorn,Ddotbelow,Edotbelow,Hdotbelow,Idotbelow,Rdotbelow"
	k="8" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="24" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="a.alt2,ae,aeacute"
	k="11" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="26" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="81" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="V"
	k="6" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="6" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="28" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="apostrophe"
	k="18" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="eth"
	k="48" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="guillemotleft,guilsinglleft"
	k="52" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="emdash,endash,hyphen,uni00AD"
	k="43" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="schwa"
	k="17" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="space"
	k="18" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="46" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="J,Jcircumflex"
	k="79" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="AE,AEacute"
	k="55" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="slash"
	k="41" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="X"
	k="30" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="29" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="guillemotright,guilsinglright"
	k="18" />
    <hkern g1="r,racute,rcaron,rcommaaccent,rdotbelow"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="45" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="9" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="10" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="98" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="12" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="V"
	k="48" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="48" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="W.alt"
	k="25" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="74" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="apostrophe"
	k="30" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="asterisk"
	k="11" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="backslash"
	k="25" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="bar"
	k="14" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="brokenbar"
	k="11" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="question"
	k="16" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="quotedblleft,quoteleft"
	k="11" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="quotedblright,quoteright"
	k="12" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="quotedbl,quotesingle"
	k="12" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="trademark"
	k="8" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="v"
	k="10" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="w.alt"
	k="7" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="y.alt"
	k="7" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="6" />
    <hkern g1="s,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	g2="B,D,Dcaron,Dcroat,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eng,Eogonek,Eth,Etilde,F,H,Hbar,Hcircumflex,I,IJ,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,K,Kcommaaccent,L,Lacute,Lcaron,Lcommaaccent,Lslash,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,P,R,Racute,Rcaron,Rcommaaccent,Thorn,Ddotbelow,Edotbelow,Hdotbelow,Idotbelow,Rdotbelow"
	k="5" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="13" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="13" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="6" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="59" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="10" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="V"
	k="18" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="18" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="39" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="apostrophe"
	k="38" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="eth"
	k="15" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="guillemotleft,guilsinglleft"
	k="29" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="emdash,endash,hyphen,uni00AD"
	k="9" />
    <hkern g1="c_t,s_t,t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	g2="schwa"
	k="6" />
    <hkern g1="t.alt"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="14" />
    <hkern g1="t.alt"
	g2="a.alt2,ae,aeacute"
	k="6" />
    <hkern g1="t.alt"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="15" />
    <hkern g1="t.alt"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="47" />
    <hkern g1="t.alt"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="23" />
    <hkern g1="t.alt"
	g2="guillemotleft,guilsinglleft"
	k="29" />
    <hkern g1="t.alt"
	g2="emdash,endash,hyphen,uni00AD"
	k="20" />
    <hkern g1="t.alt"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="29" />
    <hkern g1="t.alt"
	g2="J,Jcircumflex"
	k="32" />
    <hkern g1="t.alt"
	g2="AE,AEacute"
	k="21" />
    <hkern g1="t.alt"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="13" />
    <hkern g1="t.alt"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="t.alt"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="20" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="5" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="108" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	g2="V"
	k="41" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="41" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="69" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	g2="apostrophe"
	k="25" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	g2="backslash"
	k="19" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	g2="bar"
	k="11" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	g2="question"
	k="14" />
    <hkern g1="v"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="7" />
    <hkern g1="v"
	g2="a.alt2,ae,aeacute"
	k="8" />
    <hkern g1="v"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="7" />
    <hkern g1="v"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="6" />
    <hkern g1="v"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="103" />
    <hkern g1="v"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="22" />
    <hkern g1="v"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="48" />
    <hkern g1="v"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="v"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="23" />
    <hkern g1="v"
	g2="J,Jcircumflex"
	k="49" />
    <hkern g1="v"
	g2="AE,AEacute"
	k="34" />
    <hkern g1="v"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="14" />
    <hkern g1="v"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="27" />
    <hkern g1="v"
	g2="B,D,Dcaron,Dcroat,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eng,Eogonek,Eth,Etilde,F,H,Hbar,Hcircumflex,I,IJ,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,K,Kcommaaccent,L,Lacute,Lcaron,Lcommaaccent,Lslash,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,P,R,Racute,Rcaron,Rcommaaccent,Thorn,Ddotbelow,Edotbelow,Hdotbelow,Idotbelow,Rdotbelow"
	k="5" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="6" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="a.alt2,ae,aeacute"
	k="6" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="6" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="5" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="103" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="V"
	k="23" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="23" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="49" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="apostrophe"
	k="21" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="eth"
	k="9" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="schwa"
	k="5" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="space"
	k="19" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="24" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="J,Jcircumflex"
	k="52" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="AE,AEacute"
	k="35" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="slash"
	k="27" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="X"
	k="25" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="14" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="parenright"
	k="13" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="27" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="B,D,Dcaron,Dcroat,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eng,Eogonek,Eth,Etilde,F,H,Hbar,Hcircumflex,I,IJ,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,K,Kcommaaccent,L,Lacute,Lcaron,Lcommaaccent,Lslash,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,P,R,Racute,Rcaron,Rcommaaccent,Thorn,Ddotbelow,Edotbelow,Hdotbelow,Idotbelow,Rdotbelow"
	k="6" />
    <hkern g1="w.alt"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="16" />
    <hkern g1="w.alt"
	g2="a.alt2,ae,aeacute"
	k="16" />
    <hkern g1="w.alt"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="16" />
    <hkern g1="w.alt"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="15" />
    <hkern g1="w.alt"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="100" />
    <hkern g1="w.alt"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="43" />
    <hkern g1="w.alt"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="w.alt"
	g2="emdash,endash,hyphen,uni00AD"
	k="12" />
    <hkern g1="w.alt"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="23" />
    <hkern g1="w.alt"
	g2="J,Jcircumflex"
	k="50" />
    <hkern g1="w.alt"
	g2="AE,AEacute"
	k="35" />
    <hkern g1="w.alt"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="14" />
    <hkern g1="w.alt"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="36" />
    <hkern g1="x"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="19" />
    <hkern g1="x"
	g2="a.alt2,ae,aeacute"
	k="8" />
    <hkern g1="x"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="20" />
    <hkern g1="x"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="85" />
    <hkern g1="x"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="10" />
    <hkern g1="x"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="32" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="26" />
    <hkern g1="x"
	g2="emdash,endash,hyphen,uni00AD"
	k="21" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="a.alt2,ae,aeacute"
	k="4" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="4" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="102" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="V"
	k="23" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="23" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="51" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="apostrophe"
	k="21" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="eth"
	k="6" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="space"
	k="18" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="24" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="J,Jcircumflex"
	k="44" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="AE,AEacute"
	k="34" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="slash"
	k="25" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="X"
	k="25" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="13" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="parenright"
	k="12" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="24" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	g2="B,D,Dcaron,Dcroat,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eng,Eogonek,Eth,Etilde,F,H,Hbar,Hcircumflex,I,IJ,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,K,Kcommaaccent,L,Lacute,Lcaron,Lcommaaccent,Lslash,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,P,R,Racute,Rcaron,Rcommaaccent,Thorn,Ddotbelow,Edotbelow,Hdotbelow,Idotbelow,Rdotbelow"
	k="6" />
    <hkern g1="y.alt"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="10" />
    <hkern g1="y.alt"
	g2="a.alt2,ae,aeacute"
	k="9" />
    <hkern g1="y.alt"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="10" />
    <hkern g1="y.alt"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="8" />
    <hkern g1="y.alt"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="90" />
    <hkern g1="y.alt"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="33" />
    <hkern g1="y.alt"
	g2="guillemotleft,guilsinglleft"
	k="15" />
    <hkern g1="y.alt"
	g2="emdash,endash,hyphen,uni00AD"
	k="11" />
    <hkern g1="y.alt"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="26" />
    <hkern g1="y.alt"
	g2="J,Jcircumflex"
	k="64" />
    <hkern g1="y.alt"
	g2="AE,AEacute"
	k="38" />
    <hkern g1="y.alt"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="12" />
    <hkern g1="y.alt"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="37" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="10" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="10" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="91" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="V"
	k="14" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="14" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="38" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="apostrophe"
	k="19" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="eth"
	k="15" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="guillemotleft,guilsinglleft"
	k="25" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="emdash,endash,hyphen,uni00AD"
	k="22" />
    <hkern g1="z,zacute,zcaron,zdotaccent,uni01F2,uni01F3,uni01C5,uni01C6,zdotbelow"
	g2="schwa"
	k="6" />
    <hkern g1="A.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="25" />
    <hkern g1="A.sc"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="23" />
    <hkern g1="A.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="83" />
    <hkern g1="A.sc"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="22" />
    <hkern g1="A.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="91" />
    <hkern g1="A.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="101" />
    <hkern g1="A.sc"
	g2="guillemotleft,guilsinglleft"
	k="33" />
    <hkern g1="A.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="22" />
    <hkern g1="A.sc"
	g2="quotedblleft,quoteleft"
	k="35" />
    <hkern g1="A.sc"
	g2="quotedblright,quoteright"
	k="35" />
    <hkern g1="A.sc"
	g2="quotedbl,quotesingle"
	k="35" />
    <hkern g1="B.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="80" />
    <hkern g1="B.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="45" />
    <hkern g1="B.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="64" />
    <hkern g1="B.sc"
	g2="quotedblleft,quoteleft"
	k="10" />
    <hkern g1="B.sc"
	g2="quotedblright,quoteright"
	k="11" />
    <hkern g1="B.sc"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="C.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="6" />
    <hkern g1="C.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="100" />
    <hkern g1="C.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="35" />
    <hkern g1="C.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="72" />
    <hkern g1="C.sc"
	g2="quotedblleft,quoteleft"
	k="13" />
    <hkern g1="C.sc"
	g2="quotedblright,quoteright"
	k="14" />
    <hkern g1="C.sc"
	g2="quotedbl,quotesingle"
	k="13" />
    <hkern g1="D.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="83" />
    <hkern g1="D.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="48" />
    <hkern g1="D.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="69" />
    <hkern g1="D.sc"
	g2="quotedblleft,quoteleft"
	k="19" />
    <hkern g1="D.sc"
	g2="quotedblright,quoteright"
	k="19" />
    <hkern g1="D.sc"
	g2="quotedbl,quotesingle"
	k="19" />
    <hkern g1="D.sc"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="11" />
    <hkern g1="D.sc"
	g2="J,Jcircumflex"
	k="7" />
    <hkern g1="E.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="10" />
    <hkern g1="E.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="98" />
    <hkern g1="E.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="14" />
    <hkern g1="E.sc"
	g2="guillemotleft,guilsinglleft"
	k="14" />
    <hkern g1="E.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="9" />
    <hkern g1="F.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="12" />
    <hkern g1="F.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="102" />
    <hkern g1="F.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="12" />
    <hkern g1="F.sc"
	g2="guillemotleft,guilsinglleft"
	k="15" />
    <hkern g1="F.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="18" />
    <hkern g1="F.sc"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="31" />
    <hkern g1="F.sc"
	g2="J,Jcircumflex"
	k="109" />
    <hkern g1="F.sc"
	g2="AE,AEacute"
	k="59" />
    <hkern g1="F.sc"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="20" />
    <hkern g1="F.sc"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="F.sc"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="75" />
    <hkern g1="G.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="84" />
    <hkern g1="G.sc"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="5" />
    <hkern g1="G.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="46" />
    <hkern g1="G.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="74" />
    <hkern g1="G.sc"
	g2="quotedblleft,quoteleft"
	k="15" />
    <hkern g1="G.sc"
	g2="quotedblright,quoteright"
	k="16" />
    <hkern g1="G.sc"
	g2="quotedbl,quotesingle"
	k="15" />
    <hkern g1="H.sc,I.sc,M.sc,N.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="88" />
    <hkern g1="H.sc,I.sc,M.sc,N.sc"
	g2="V"
	k="32" />
    <hkern g1="H.sc,I.sc,M.sc,N.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="32" />
    <hkern g1="H.sc,I.sc,M.sc,N.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="40" />
    <hkern g1="H.sc,I.sc,M.sc,N.sc"
	g2="apostrophe"
	k="24" />
    <hkern g1="H.sc,I.sc,M.sc,N.sc"
	g2="backslash"
	k="13" />
    <hkern g1="H.sc,I.sc,M.sc,N.sc"
	g2="question"
	k="12" />
    <hkern g1="J.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="85" />
    <hkern g1="J.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="26" />
    <hkern g1="J.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="40" />
    <hkern g1="K.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="29" />
    <hkern g1="K.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="68" />
    <hkern g1="K.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="11" />
    <hkern g1="K.sc"
	g2="guillemotleft,guilsinglleft"
	k="28" />
    <hkern g1="K.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="26" />
    <hkern g1="L.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="45" />
    <hkern g1="L.sc"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="58" />
    <hkern g1="L.sc"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="9" />
    <hkern g1="L.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="100" />
    <hkern g1="L.sc"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="52" />
    <hkern g1="L.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="86" />
    <hkern g1="L.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="95" />
    <hkern g1="L.sc"
	g2="guillemotleft,guilsinglleft"
	k="83" />
    <hkern g1="L.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="81" />
    <hkern g1="L.sc"
	g2="quotedblleft,quoteleft"
	k="49" />
    <hkern g1="L.sc"
	g2="quotedblright,quoteright"
	k="49" />
    <hkern g1="L.sc"
	g2="quotedbl,quotesingle"
	k="49" />
    <hkern g1="L.sc"
	g2="guillemotright,guilsinglright"
	k="24" />
    <hkern g1="O.sc,Q.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="84" />
    <hkern g1="O.sc,Q.sc"
	g2="T.sc"
	k="46" />
    <hkern g1="O.sc,Q.sc"
	g2="V"
	k="49" />
    <hkern g1="O.sc,Q.sc"
	g2="V.sc"
	k="24" />
    <hkern g1="O.sc,Q.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="49" />
    <hkern g1="O.sc,Q.sc"
	g2="W.sc"
	k="24" />
    <hkern g1="O.sc,Q.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="70" />
    <hkern g1="O.sc,Q.sc"
	g2="Y.sc"
	k="40" />
    <hkern g1="O.sc,Q.sc"
	g2="apostrophe"
	k="42" />
    <hkern g1="O.sc,Q.sc"
	g2="asterisk"
	k="19" />
    <hkern g1="O.sc,Q.sc"
	g2="backslash"
	k="45" />
    <hkern g1="O.sc,Q.sc"
	g2="bar"
	k="19" />
    <hkern g1="O.sc,Q.sc"
	g2="brokenbar"
	k="17" />
    <hkern g1="O.sc,Q.sc"
	g2="question"
	k="28" />
    <hkern g1="O.sc,Q.sc"
	g2="quotedblleft,quoteleft"
	k="20" />
    <hkern g1="O.sc,Q.sc"
	g2="quotedblright,quoteright"
	k="20" />
    <hkern g1="O.sc,Q.sc"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="O.sc,Q.sc"
	g2="trademark"
	k="13" />
    <hkern g1="O.sc,Q.sc"
	g2="servicemark"
	k="9" />
    <hkern g1="O.sc,Q.sc"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="12" />
    <hkern g1="O.sc,Q.sc"
	g2="J,Jcircumflex"
	k="9" />
    <hkern g1="O.sc,Q.sc"
	g2="A.sc"
	k="21" />
    <hkern g1="O.sc,Q.sc"
	g2="J.sc"
	k="19" />
    <hkern g1="O.sc,Q.sc"
	g2="X"
	k="17" />
    <hkern g1="O.sc,Q.sc"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="5" />
    <hkern g1="O.sc,Q.sc"
	g2="parenright"
	k="10" />
    <hkern g1="O.sc,Q.sc"
	g2="X.sc"
	k="24" />
    <hkern g1="O.sc,Q.sc"
	g2="Z.sc"
	k="12" />
    <hkern g1="P.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="72" />
    <hkern g1="P.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="31" />
    <hkern g1="P.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="53" />
    <hkern g1="P.sc"
	g2="quotedblleft,quoteleft"
	k="8" />
    <hkern g1="P.sc"
	g2="quotedblright,quoteright"
	k="8" />
    <hkern g1="P.sc"
	g2="quotedbl,quotesingle"
	k="8" />
    <hkern g1="P.sc"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="25" />
    <hkern g1="P.sc"
	g2="J,Jcircumflex"
	k="79" />
    <hkern g1="P.sc"
	g2="AE,AEacute"
	k="31" />
    <hkern g1="P.sc"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="8" />
    <hkern g1="P.sc"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="62" />
    <hkern g1="R.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="82" />
    <hkern g1="R.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="45" />
    <hkern g1="R.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="68" />
    <hkern g1="R.sc"
	g2="quotedblleft,quoteleft"
	k="10" />
    <hkern g1="R.sc"
	g2="quotedblright,quoteright"
	k="11" />
    <hkern g1="R.sc"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="S.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="81" />
    <hkern g1="S.sc"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="5" />
    <hkern g1="S.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="38" />
    <hkern g1="S.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="53" />
    <hkern g1="S.sc"
	g2="quotedblleft,quoteleft"
	k="11" />
    <hkern g1="S.sc"
	g2="quotedblright,quoteright"
	k="12" />
    <hkern g1="S.sc"
	g2="quotedbl,quotesingle"
	k="11" />
    <hkern g1="T.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="24" />
    <hkern g1="T.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="77" />
    <hkern g1="T.sc"
	g2="guillemotleft,guilsinglleft"
	k="63" />
    <hkern g1="T.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="49" />
    <hkern g1="T.sc"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="45" />
    <hkern g1="T.sc"
	g2="J,Jcircumflex"
	k="85" />
    <hkern g1="T.sc"
	g2="AE,AEacute"
	k="52" />
    <hkern g1="T.sc"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="13" />
    <hkern g1="T.sc"
	g2="guillemotright,guilsinglright"
	k="36" />
    <hkern g1="T.sc"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="49" />
    <hkern g1="U.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="85" />
    <hkern g1="U.sc"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="23" />
    <hkern g1="U.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="40" />
    <hkern g1="U.sc"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="5" />
    <hkern g1="U.sc"
	g2="J,Jcircumflex"
	k="12" />
    <hkern g1="V.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="22" />
    <hkern g1="V.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="80" />
    <hkern g1="V.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="16" />
    <hkern g1="V.sc"
	g2="guillemotleft,guilsinglleft"
	k="28" />
    <hkern g1="V.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="22" />
    <hkern g1="V.sc"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="26" />
    <hkern g1="V.sc"
	g2="J,Jcircumflex"
	k="70" />
    <hkern g1="V.sc"
	g2="AE,AEacute"
	k="37" />
    <hkern g1="V.sc"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="13" />
    <hkern g1="V.sc"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="53" />
    <hkern g1="W.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="21" />
    <hkern g1="W.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="80" />
    <hkern g1="W.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="16" />
    <hkern g1="W.sc"
	g2="guillemotleft,guilsinglleft"
	k="29" />
    <hkern g1="W.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="22" />
    <hkern g1="W.sc"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="26" />
    <hkern g1="W.sc"
	g2="J,Jcircumflex"
	k="70" />
    <hkern g1="W.sc"
	g2="AE,AEacute"
	k="37" />
    <hkern g1="W.sc"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="13" />
    <hkern g1="W.sc"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="53" />
    <hkern g1="X.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="24" />
    <hkern g1="X.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="67" />
    <hkern g1="X.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="8" />
    <hkern g1="X.sc"
	g2="guillemotleft,guilsinglleft"
	k="26" />
    <hkern g1="X.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="23" />
    <hkern g1="Y.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="29" />
    <hkern g1="Y.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="87" />
    <hkern g1="Y.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="11" />
    <hkern g1="Y.sc"
	g2="guillemotleft,guilsinglleft"
	k="44" />
    <hkern g1="Y.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="38" />
    <hkern g1="Y.sc"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="34" />
    <hkern g1="Y.sc"
	g2="J,Jcircumflex"
	k="80" />
    <hkern g1="Y.sc"
	g2="AE,AEacute"
	k="47" />
    <hkern g1="Y.sc"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="17" />
    <hkern g1="Y.sc"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="Y.sc"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="65" />
    <hkern g1="Z.sc"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="12" />
    <hkern g1="Z.sc"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="88" />
    <hkern g1="Z.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="15" />
    <hkern g1="Z.sc"
	g2="guillemotleft,guilsinglleft"
	k="23" />
    <hkern g1="Z.sc"
	g2="emdash,endash,hyphen,uni00AD"
	k="21" />
    <hkern g1="periodcentered"
	g2="l,lacute,lcaron,lcommaaccent,ldot,lslash"
	k="39" />
    <hkern g1="slash"
	g2="B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,K.sc,L.sc,M.sc,N.sc,P.sc,R.sc"
	k="11" />
    <hkern g1="slash"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="14" />
    <hkern g1="slash"
	g2="a.alt2,ae,aeacute"
	k="12" />
    <hkern g1="slash"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="19" />
    <hkern g1="slash"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="79" />
    <hkern g1="slash"
	g2="J,Jcircumflex"
	k="97" />
    <hkern g1="slash"
	g2="AE,AEacute"
	k="88" />
    <hkern g1="ampersand"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="62" />
    <hkern g1="ampersand"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="27" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="46" />
    <hkern g1="ampersand"
	g2="quotedblright,quoteright"
	k="41" />
    <hkern g1="ampersand"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="asterisk"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="11" />
    <hkern g1="asterisk"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="11" />
    <hkern g1="asterisk"
	g2="a.alt2,ae,aeacute"
	k="9" />
    <hkern g1="asterisk"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="12" />
    <hkern g1="asterisk"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="68" />
    <hkern g1="asterisk"
	g2="J,Jcircumflex"
	k="86" />
    <hkern g1="asterisk"
	g2="AE,AEacute"
	k="67" />
    <hkern g1="at"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="12" />
    <hkern g1="at"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="12" />
    <hkern g1="at"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="78" />
    <hkern g1="at"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="51" />
    <hkern g1="at"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="73" />
    <hkern g1="at"
	g2="quotedblright,quoteright"
	k="40" />
    <hkern g1="at"
	g2="quotedbl,quotesingle"
	k="39" />
    <hkern g1="at"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="19" />
    <hkern g1="backslash"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="34" />
    <hkern g1="backslash"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="39" />
    <hkern g1="backslash"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="22" />
    <hkern g1="backslash"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="71" />
    <hkern g1="backslash"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="19" />
    <hkern g1="backslash"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="72" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="75" />
    <hkern g1="backslash"
	g2="quotedblright,quoteright"
	k="77" />
    <hkern g1="backslash"
	g2="quotedbl,quotesingle"
	k="76" />
    <hkern g1="backslash"
	g2="AE,AEacute"
	k="-33" />
    <hkern g1="bar"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="10" />
    <hkern g1="bar"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="10" />
    <hkern g1="bar"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="10" />
    <hkern g1="bar"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="13" />
    <hkern g1="bar"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="22" />
    <hkern g1="bar"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="19" />
    <hkern g1="bar"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="14" />
    <hkern g1="bar"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="32" />
    <hkern g1="bar"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="15" />
    <hkern g1="bar"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="44" />
    <hkern g1="bar"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="47" />
    <hkern g1="bar"
	g2="quotedblright,quoteright"
	k="33" />
    <hkern g1="bar"
	g2="quotedbl,quotesingle"
	k="33" />
    <hkern g1="bar"
	g2="AE,AEacute"
	k="-7" />
    <hkern g1="brokenbar"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="10" />
    <hkern g1="brokenbar"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="20" />
    <hkern g1="brokenbar"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="17" />
    <hkern g1="brokenbar"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="13" />
    <hkern g1="brokenbar"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="29" />
    <hkern g1="brokenbar"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="13" />
    <hkern g1="brokenbar"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="42" />
    <hkern g1="brokenbar"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="45" />
    <hkern g1="brokenbar"
	g2="quotedblright,quoteright"
	k="30" />
    <hkern g1="brokenbar"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="brokenbar"
	g2="AE,AEacute"
	k="-10" />
    <hkern g1="colon,semicolon"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="58" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="14" />
    <hkern g1="colon,semicolon"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="14" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="25" />
    <hkern g1="eight"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="19" />
    <hkern g1="eight"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="21" />
    <hkern g1="eight"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="28" />
    <hkern g1="eight"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="21" />
    <hkern g1="exclamdown"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="12" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="13" />
    <hkern g1="five"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="70" />
    <hkern g1="five"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="13" />
    <hkern g1="five"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="20" />
    <hkern g1="five"
	g2="emdash,endash,hyphen,uni00AD"
	k="21" />
    <hkern g1="four"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="76" />
    <hkern g1="four"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="11" />
    <hkern g1="four"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="37" />
    <hkern g1="four"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="48" />
    <hkern g1="four"
	g2="emdash,endash,hyphen,uni00AD"
	k="11" />
    <hkern g1="four"
	g2="quotedbl,quotesingle"
	k="17" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="78" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T.sc"
	k="49" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V"
	k="36" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V.sc"
	k="12" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="36" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W.alt"
	k="14" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W.sc"
	k="12" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="57" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y.sc"
	k="19" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="quotedblright,quoteright"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="quotedbl,quotesingle"
	k="37" />
    <hkern g1="guillemotright,guilsinglright"
	g2="f,f_f,f_f_i,f_f_l,f_i,f_l"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="24" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="25" />
    <hkern g1="guillemotright,guilsinglright"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="25" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="80" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T.sc"
	k="65" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="59" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V.sc"
	k="36" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="59" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W.alt"
	k="33" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W.sc"
	k="36" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="80" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y.sc"
	k="57" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quotedblright,quoteright"
	k="79" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quotedbl,quotesingle"
	k="81" />
    <hkern g1="guillemotright,guilsinglright"
	g2="t.alt"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="v"
	k="25" />
    <hkern g1="guillemotright,guilsinglright"
	g2="w.alt"
	k="21" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y.alt"
	k="26" />
    <hkern g1="guillemotright,guilsinglright"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="J,Jcircumflex"
	k="11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="A.sc"
	k="25" />
    <hkern g1="guillemotright,guilsinglright"
	g2="J.sc"
	k="43" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="26" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X.sc"
	k="25" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Z.sc"
	k="21" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="12" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="12" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="25" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="66" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="T.sc"
	k="48" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="V"
	k="44" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="V.sc"
	k="20" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="44" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="W.alt"
	k="21" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="W.sc"
	k="20" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="68" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="Y.sc"
	k="37" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="quotedblright,quoteright"
	k="79" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="quotedbl,quotesingle"
	k="76" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="seven"
	k="52" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="v"
	k="13" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="w.alt"
	k="11" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="y.alt"
	k="13" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="17" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="J,Jcircumflex"
	k="66" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="A.sc"
	k="23" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="J.sc"
	k="65" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="X"
	k="18" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="x"
	k="23" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="X.sc"
	k="24" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="Z.sc"
	k="21" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="four"
	k="24" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="one"
	k="25" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="three"
	k="55" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="one.lnum"
	k="13" />
    <hkern g1="emdash,endash,hyphen,uni00AD"
	g2="seven.lnum"
	k="36" />
    <hkern g1="nine"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="76" />
    <hkern g1="nine"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="45" />
    <hkern g1="nine"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="65" />
    <hkern g1="nine"
	g2="quotedbl,quotesingle"
	k="41" />
    <hkern g1="one"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="12" />
    <hkern g1="one"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="75" />
    <hkern g1="one"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="16" />
    <hkern g1="one"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="44" />
    <hkern g1="one"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="54" />
    <hkern g1="one"
	g2="emdash,endash,hyphen,uni00AD"
	k="10" />
    <hkern g1="one"
	g2="quotedbl,quotesingle"
	k="19" />
    <hkern g1="one"
	g2="AE,AEacute"
	k="-41" />
    <hkern g1="one.lnum"
	g2="emdash,endash,hyphen,uni00AD"
	k="12" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="11" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="10" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="9" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="41" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="45" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="27" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="66" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="T.sc"
	k="49" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="22" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="U.sc"
	k="11" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="V"
	k="75" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="V.sc"
	k="55" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="75" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="W.alt"
	k="46" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="W.sc"
	k="55" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="76" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="Y.sc"
	k="59" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="quotedblleft,quoteleft"
	k="79" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="quotedblright,quoteright"
	k="79" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="quotedbl,quotesingle"
	k="79" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="seven"
	k="22" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="six"
	k="14" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="v"
	k="40" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="w.alt"
	k="36" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="y.alt"
	k="46" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="Schwa"
	k="21" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="four.lnum"
	k="18" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="nine.lnum"
	k="11" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="six.lnum"
	k="14" />
    <hkern g1="comma,period,quotedblbase,quotesinglbase"
	g2="zero.lnum"
	k="21" />
    <hkern g1="questiondown"
	g2="B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,K.sc,L.sc,M.sc,N.sc,P.sc,R.sc"
	k="25" />
    <hkern g1="questiondown"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="36" />
    <hkern g1="questiondown"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="37" />
    <hkern g1="questiondown"
	g2="a.alt2,ae,aeacute"
	k="37" />
    <hkern g1="questiondown"
	g2="b,germandbls,h,hbar,hcircumflex,k,kcommaaccent,thorn,hdotbelow"
	k="26" />
    <hkern g1="questiondown"
	g2="dotlessi,dotlessj,i,iacute,ibreve,icircumflex,idieresis,igrave,ij,j,imacron,iogonek,itilde,j.alt,jcircumflex,idotbelow"
	k="26" />
    <hkern g1="questiondown"
	g2="l,lacute,lcaron,lcommaaccent,ldot,lslash"
	k="31" />
    <hkern g1="questiondown"
	g2="eng,m,n,nacute,napostrophe,ncaron,ncommaaccent,ndotaccent,ntilde,p,r,racute,rcaron,rcommaaccent,rdotbelow"
	k="26" />
    <hkern g1="questiondown"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="37" />
    <hkern g1="questiondown"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="37" />
    <hkern g1="questiondown"
	g2="t,tbar,tcaron,uni0163,uni021B,tdotbelow"
	k="36" />
    <hkern g1="questiondown"
	g2="u,u.alt,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde,udotbelow"
	k="33" />
    <hkern g1="questiondown"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="40" />
    <hkern g1="questiondown"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="18" />
    <hkern g1="questiondown"
	g2="z,zacute,zcaron,zdotaccent,zdotbelow"
	k="30" />
    <hkern g1="questiondown"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="35" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scaron,Scircumflex,uni015E,uni0218,Sdotbelow"
	k="29" />
    <hkern g1="questiondown"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="99" />
    <hkern g1="questiondown"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde,Udotbelow"
	k="35" />
    <hkern g1="questiondown"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="67" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="80" />
    <hkern g1="questiondown"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="29" />
    <hkern g1="questiondown"
	g2="J,Jcircumflex"
	k="39" />
    <hkern g1="questiondown"
	g2="AE,AEacute"
	k="14" />
    <hkern g1="questiondown"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="24" />
    <hkern g1="questiondown"
	g2="B,D,Dcaron,Dcroat,E,Eacute,Ebreve,Ecaron,Ecircumflex,Edieresis,Edotaccent,Egrave,Emacron,Eng,Eogonek,Eth,Etilde,F,H,Hbar,Hcircumflex,I,IJ,Iacute,Ibreve,Icircumflex,Idieresis,Idotaccent,Igrave,Imacron,Iogonek,Itilde,K,Kcommaaccent,L,Lacute,Lcaron,Lcommaaccent,Lslash,M,N,Nacute,Ncaron,Ncommaaccent,Ndotaccent,Ntilde,P,R,Racute,Rcaron,Rcommaaccent,Thorn,Ddotbelow,Edotbelow,Hdotbelow,Idotbelow,Rdotbelow"
	k="24" />
    <hkern g1="quotedblleft,quoteleft"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="11" />
    <hkern g1="quotedblleft,quoteleft"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="11" />
    <hkern g1="quotedblleft,quoteleft"
	g2="a.alt2,ae,aeacute"
	k="9" />
    <hkern g1="quotedblleft,quoteleft"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="12" />
    <hkern g1="quotedblleft,quoteleft"
	g2="eth"
	k="15" />
    <hkern g1="quotedblleft,quoteleft"
	g2="schwa"
	k="9" />
    <hkern g1="quotedblleft,quoteleft"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="69" />
    <hkern g1="quotedblleft,quoteleft"
	g2="J,Jcircumflex"
	k="81" />
    <hkern g1="quotedblleft,quoteleft"
	g2="A.sc"
	k="34" />
    <hkern g1="quotedblleft,quoteleft"
	g2="AE,AEacute"
	k="70" />
    <hkern g1="quotedblleft,quoteleft"
	g2="J.sc"
	k="35" />
    <hkern g1="quotedblleft,quoteleft"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="79" />
    <hkern g1="quotedblright,quoteright"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="11" />
    <hkern g1="quotedblright,quoteright"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="12" />
    <hkern g1="quotedblright,quoteright"
	g2="a.alt2,ae,aeacute"
	k="9" />
    <hkern g1="quotedblright,quoteright"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="12" />
    <hkern g1="quotedblright,quoteright"
	g2="s,s_t,sacute,scaron,scircumflex,uni015F,uni0219,sdotbelow"
	k="8" />
    <hkern g1="quotedblright,quoteright"
	g2="at"
	k="31" />
    <hkern g1="quotedblright,quoteright"
	g2="eth"
	k="16" />
    <hkern g1="quotedblright,quoteright"
	g2="guillemotleft,guilsinglleft"
	k="56" />
    <hkern g1="quotedblright,quoteright"
	g2="emdash,endash,hyphen,uni00AD"
	k="54" />
    <hkern g1="quotedblright,quoteright"
	g2="schwa"
	k="9" />
    <hkern g1="quotedblright,quoteright"
	g2="space"
	k="28" />
    <hkern g1="quotedblright,quoteright"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="71" />
    <hkern g1="quotedblright,quoteright"
	g2="J,Jcircumflex"
	k="79" />
    <hkern g1="quotedblright,quoteright"
	g2="A.sc"
	k="34" />
    <hkern g1="quotedblright,quoteright"
	g2="AE,AEacute"
	k="71" />
    <hkern g1="quotedblright,quoteright"
	g2="J.sc"
	k="35" />
    <hkern g1="quotedblright,quoteright"
	g2="slash"
	k="79" />
    <hkern g1="quotedblright,quoteright"
	g2="guillemotright,guilsinglright"
	k="31" />
    <hkern g1="quotedblright,quoteright"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="79" />
    <hkern g1="quotedbl,quotesingle"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="11" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="11" />
    <hkern g1="quotedbl,quotesingle"
	g2="a.alt2,ae,aeacute"
	k="9" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="12" />
    <hkern g1="quotedbl,quotesingle"
	g2="at"
	k="28" />
    <hkern g1="quotedbl,quotesingle"
	g2="eth"
	k="15" />
    <hkern g1="quotedbl,quotesingle"
	g2="guillemotleft,guilsinglleft"
	k="51" />
    <hkern g1="quotedbl,quotesingle"
	g2="emdash,endash,hyphen,uni00AD"
	k="32" />
    <hkern g1="quotedbl,quotesingle"
	g2="schwa"
	k="9" />
    <hkern g1="quotedbl,quotesingle"
	g2="space"
	k="31" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="68" />
    <hkern g1="quotedbl,quotesingle"
	g2="J,Jcircumflex"
	k="81" />
    <hkern g1="quotedbl,quotesingle"
	g2="A.sc"
	k="34" />
    <hkern g1="quotedbl,quotesingle"
	g2="AE,AEacute"
	k="68" />
    <hkern g1="quotedbl,quotesingle"
	g2="J.sc"
	k="35" />
    <hkern g1="quotedbl,quotesingle"
	g2="slash"
	k="81" />
    <hkern g1="quotedbl,quotesingle"
	g2="guillemotright,guilsinglright"
	k="16" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="79" />
    <hkern g1="quotedbl,quotesingle"
	g2="five"
	k="17" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="81" />
    <hkern g1="quotedbl,quotesingle"
	g2="nine"
	k="14" />
    <hkern g1="quotedbl,quotesingle"
	g2="three"
	k="11" />
    <hkern g1="quotedbl,quotesingle"
	g2="four.lnum"
	k="60" />
    <hkern g1="seven"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="60" />
    <hkern g1="seven"
	g2="emdash,endash,hyphen,uni00AD"
	k="43" />
    <hkern g1="seven"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="55" />
    <hkern g1="seven"
	g2="J,Jcircumflex"
	k="59" />
    <hkern g1="seven"
	g2="AE,AEacute"
	k="46" />
    <hkern g1="seven"
	g2="Z,Zacute,Zcaron,Zdotaccent,Zdotbelow"
	k="27" />
    <hkern g1="seven"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="71" />
    <hkern g1="seven.lnum"
	g2="emdash,endash,hyphen,uni00AD"
	k="30" />
    <hkern g1="seven.lnum"
	g2="comma,ellipsis,period,quotedblbase,quotesinglbase"
	k="77" />
    <hkern g1="six"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="20" />
    <hkern g1="six"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="20" />
    <hkern g1="six"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="28" />
    <hkern g1="six"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="22" />
    <hkern g1="space"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="28" />
    <hkern g1="space"
	g2="y,yacute,ycircumflex,ydieresis,ygrave,ytilde"
	k="29" />
    <hkern g1="space"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="30" />
    <hkern g1="space"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="37" />
    <hkern g1="space"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="39" />
    <hkern g1="space"
	g2="quotedblright,quoteright"
	k="29" />
    <hkern g1="space"
	g2="quotedbl,quotesingle"
	k="33" />
    <hkern g1="space"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="26" />
    <hkern g1="space"
	g2="J,Jcircumflex"
	k="22" />
    <hkern g1="space"
	g2="AE,AEacute"
	k="35" />
    <hkern g1="three"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="75" />
    <hkern g1="three"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="37" />
    <hkern g1="three"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="54" />
    <hkern g1="three"
	g2="quotedbl,quotesingle"
	k="26" />
    <hkern g1="three"
	g2="AE,AEacute"
	k="-5" />
    <hkern g1="two"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="75" />
    <hkern g1="two"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="31" />
    <hkern g1="two"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="45" />
    <hkern g1="two"
	g2="AE,AEacute"
	k="-9" />
    <hkern g1="zero"
	g2="T,Tbar,Tcaron,uni0162,uni021A,Tdotbelow"
	k="76" />
    <hkern g1="zero"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="42" />
    <hkern g1="zero"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave,Ytilde"
	k="60" />
    <hkern g1="zero"
	g2="quotedbl,quotesingle"
	k="22" />
    <hkern g1="zero"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="24" />
    <hkern g1="servicemark"
	g2="A,Aacute,Abreve,Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Aringacute,Atilde"
	k="55" />
    <hkern g1="servicemark"
	g2="J,Jcircumflex"
	k="84" />
    <hkern g1="servicemark"
	g2="AE,AEacute"
	k="53" />
    <hkern g1="parenleft"
	g2="C.sc,G.sc,O.sc,Q.sc"
	k="14" />
    <hkern g1="parenleft"
	g2="a,a.alt,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,aringacute,atilde,d,d.alt,dcaron,dcroat,g,gbreve,gcircumflex,gcommaaccent,gdotaccent,q,q.alt,uni01F3,uni01C6,ddotbelow,gacute"
	k="11" />
    <hkern g1="parenleft"
	g2="c,c_t,cacute,ccaron,ccedilla,ccircumflex,cdotaccent,e,eacute,ebreve,ecaron,ecircumflex,edieresis,edotaccent,egrave,emacron,eogonek,etilde,o,oacute,obreve,ocircumflex,odieresis,oe,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde,edotbelow,odotbelow"
	k="10" />
    <hkern g1="parenleft"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent,G,G.alt,Gbreve,Gcircumflex,Gcommaaccent,Gdotaccent,O,OE,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde,Q,Gacute,Odotbelow"
	k="17" />
    <hkern g1="parenleft"
	g2="AE,AEacute"
	k="-12" />
  </font>
</defs></svg>
