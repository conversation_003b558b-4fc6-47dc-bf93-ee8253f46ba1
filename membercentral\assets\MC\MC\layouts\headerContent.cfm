<cfoutput>
<header>
	<div class="container">
		<div class="row">	
			<div class="logo span3">
				#application.objCMS.renderZone(zone='U',event=event)#
			</div>
			<div class="span9">
				<nav class="nav-menu-cntnr">
					<div class="menu-icn"><div class="menu-icn-inner"><span></span></div></div>
					<div class="nav-menu">
						<div class="close-icn"><div class="close-icn-inr"><span></span></div></div>
						<cfif structKeyExists(local.strMenus,"primaryNav")>
		 					#local.strMenus.primaryNav.menuHTML.rawcontent#
		     			</cfif>
		     			<div class="free-demo"><a href="/?pg=RequestDemo">Free Demo</a></div>
					</div>
				</nav>
			</div>
		</div>
	</div>
	<div class="overlay-bg"></div>
	</header>
</cfoutput>