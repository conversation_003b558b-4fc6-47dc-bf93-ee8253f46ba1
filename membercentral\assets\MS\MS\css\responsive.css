@charset "utf-8";
@media only screen and (min-width:980px) {
}
.dropdown-submenu:hover>.dropdown-menu { padding: 0; display: block !important; left: 100%; visibility: visible; -moz-transition: all 0.3s ease 0s; -ms-transition: all 0.3s ease 0s; -o-transition: all 0.3s ease 0s; -webkit-transition: all 0.3s ease 0s; transition: all 0.3s ease 0s; opacity: 1; }
*/
}
 @media only screen and (max-width:1600px) {
.dropdown-menu { width: 200px; }
}
 @media only screen and (max-width:1280px) {
.span3.footCol { }
.footer .footCol { padding: 0 15px; float: left; }
.footer .row-fluid { margin: 0 0px; }
}
 @media only screen and (max-width:1199px) {
.header .navbar .container, .container { width: 940px; }
#cssmenu > ul > li > a { padding: 0px 8px; }
.footer { padding: 7px 15px; }
}
 @media only screen and (max-width:979px) {
header { padding: 0px 15px; }
.newheader header { padding: 0; }
.header .navbar .container, .container { width: 100%; }
#cssmenu > ul > li > a { padding: 0px 5px; font-size: 16px; }
}
 @media screen and (max-width:767px) {
.logo { text-align: center; }
.logo a img { max-width: 300px; height: auto; width: 100%; }
.loginSec { display: flex; justify-content: space-between; align-items: center; flex-direction: row-reverse; }
.headertop .loginSec ul { margin-top: 15px; margin-left: 0px; }
#cssmenu > ul > li > a:hover { border-bottom: 0px solid #E8D772; color: #fff; }
.logo2 { display: none }
nav { width: 100%; }
#cssmenu { width: 100% }
#cssmenu > ul { width: 100%; display: none; padding-top: 0px; background: #746d71 }
#cssmenu > ul > li > a { font-size: 14px; }
#cssmenu ul li { width: 100%; border-top: 1px solid #fff }
#cssmenu ul li:hover { background: #363636; }
#cssmenu ul ul li, #cssmenu li:hover > ul > li { height: auto; text-align: left; color: #fff; }
#cssmenu ul li a, #cssmenu ul ul li a { width: 100%; border-bottom: 0; color: #fff; text-align: left; }
#cssmenu > ul > li { float: none }
#cssmenu ul ul li a { padding-left: 25px }
#cssmenu ul ul li { background: transparent; }
#cssmenu ul ul li:hover { background: #363636!important }
#cssmenu ul ul ul li a { padding-left: 35px }
#cssmenu ul ul li a { color: #ddd; background: none }
#cssmenu ul ul li:hover > a, #cssmenu ul ul li.active > a { color: #fff }
#cssmenu ul ul, #cssmenu ul ul ul { position: relative; left: 0; width: 100%; margin: 0; text-align: left }
#cssmenu > ul > li.has-sub > a:after, #cssmenu > ul > li.has-sub > a:before, #cssmenu ul ul > li.has-sub > a:after, #cssmenu ul ul > li.has-sub > a:before { display: none }
#cssmenu #head-mobile { display: block; padding: 23px; color: #ddd; font-size: 12px; font-weight: 700 }
.button { width: 55px; height: 46px; position: absolute; right: 0; top: 0px; cursor: pointer; z-index: 12399994; }
.button:after { position: absolute; top: 22px; right: 20px; display: block; height: 4px; width: 20px; border-top: 2px solid #fff; border-bottom: 2px solid #fff; content: '' }
.button:before { -webkit-transition: all .3s ease; -ms-transition: all .3s ease; transition: all .3s ease; position: absolute; top: 16px; right: 20px; display: block; height: 2px; width: 20px; background: #fff; content: '' }
.button.menu-opened:after { -webkit-transition: all .3s ease; -ms-transition: all .3s ease; transition: all .3s ease; top: 23px; border: 0; height: 2px; width: 19px; background: #fff; -webkit-transform: rotate(45deg); -moz-transform: rotate(45deg); -ms-transform: rotate(45deg); -o-transform: rotate(45deg); transform: rotate(45deg) }
.button.menu-opened:before { top: 23px; background: #fff; width: 19px; -webkit-transform: rotate(-45deg); -moz-transform: rotate(-45deg); -ms-transform: rotate(-45deg); -o-transform: rotate(-45deg); transform: rotate(-45deg) }
#cssmenu .submenu-button { position: absolute; z-index: 99; right: 0; top: 0; display: block; border-left: 0px solid #444; height: 30px; width: 46px; cursor: pointer }
#cssmenu .submenu-button.submenu-opened { background: #transparent }
#cssmenu ul ul .submenu-button { height: 34px; width: 34px }
#cssmenu .submenu-button:after { position: absolute; top: 16px; right: 19px; width: 8px; height: 2px; display: block; background: #ddd; content: '' }
#cssmenu ul ul .submenu-button:after { top: 15px; right: 13px }
#cssmenu .submenu-button.submenu-opened:after { background: #fff }
#cssmenu .submenu-button:before { position: absolute; top: 13px; right: 22px; display: block; width: 2px; height: 8px; background: #ddd; content: '' }
#cssmenu ul ul .submenu-button:before { top: 12px; right: 16px }
#cssmenu .submenu-button.submenu-opened:before { display: none }
#cssmenu ul ul ul li.active a { border-left: none }
#cssmenu > ul > li.has-sub > ul > li.active > a, #cssmenu > ul ul > li.has-sub > ul > li.active > a { border-top: none }
#cssmenu > ul > li > a { border-right: 0px solid #c6c6c6; padding-right: 40px; padding-top: 7px; padding-left: 15px; padding-bottom: 7px; }
#cssmenu > ul > li.active:hover > a { color: #fff; }
tr.navybg span { font-size: 12px !important; }
}
 @media only screen and (max-width:767px) {
.footer .footCol, .footer .footCol:nth-child(2), .footer .footCol:last-child { float: none; width: 100%; text-align: center; }
.footerIn { padding: 40px 0 30px; }
.mainLogo img { max-width: 200px; }
#cssmenu { padding: 0px; }
#cssmenu li:hover > ul { left: auto; top: 0; padding-top: 0; }
#cssmenu > ul > li { border-left: 0px solid #fff; }
.headertop { display: block; }
.headertop .loginSec ul li a { margin: 0px 6px; }
.span12.footCol.mainLogo { padding-left: 15px; }
.footer a { margin-left: 7px; }
}
 @media only screen and (max-width:599px) {
.logo a img { width: 100%; min-height: 45px; }
.button { width: 55px; height: 46px; position: absolute; right: -7px; top: 0px; cursor: pointer; z-index: 12399994; }
.content { padding: 30px 0; padding-top: 0px; padding-bottom: 0px; }
#cssmenu > ul { padding-top: 0px; }
.headertop ul li a { font-size: 11px; }
}
 @media only screen and (max-width:450px) {
}
