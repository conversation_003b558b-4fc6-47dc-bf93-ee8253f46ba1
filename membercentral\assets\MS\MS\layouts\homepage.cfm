<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct"><!--- DISPLAY THE FOLLOWING IN DIRECT MODE --->
	<cfinclude template="directMode.cfm" />
<cfelse>
	<cfoutput>
		<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
		<html xmlns="http://www.w3.org/1999/xhtml">
			<head>
				<cfinclude template="head.cfm">
			</head>
			<body>
				<div id="main">
					<cfinclude template="header.cfm">
					<cfinclude template="mainNav.cfm">
					<div class="clear" />
					<div id="container">
						<div id="mastHead">
							<div id="mastHeadImage">#application.objCMS.renderZone(zone='A',event=event, mode='div')#</div>
							<div id="mastHeadContent">
								<div id="mastHeadContentArea">#application.objCMS.renderZone(zone='B',event=event, mode='div')#</div>
							</div>
						</div>
						
						<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "normal">
							<div id="cols">
								<div class="column" id="columnContent1">#application.objCMS.renderZone(zone='C',event=event, mode='div')#</div>
								<div class="column" id="columnContent2">#application.objCMS.renderZone(zone='D',event=event, mode='div')#</div>
								<div class="column" id="columnContent3">#application.objCMS.renderZone(zone='E',event=event, mode='div')#</div>
								<div class="column" id="columnContent4">#application.objCMS.renderZone(zone='F',event=event, mode='div')#</div>
							</div>
							<div id="content">
								<div id="normalMode">
									<div id="ncLeft">#application.objCMS.renderZone(zone='Main',event=event, mode='div')#</div>
									<div id="ncRight">#application.objCMS.renderZone(zone='G',event=event, mode='div')#</div>
								</div>
							</div>
						<cfelse>
							<div id="contentFull">#application.objCMS.renderZone(zone='Main',event=event, mode='div')#</div>
						</cfif>	
						
					</div>
					<!--- END: container *********************************************************************************************** --->
					<!--- <div id="clear" /> --->
					<div class="clear" />
					<div id="containerBottom"></div>
					<cfinclude template="footer.cfm">
				</div>
				<cfinclude template="toolBar.cfm" />
				<!--- END: main ****************************************************************************************************** --->
			</body>
		</html>
	</cfoutput>
</cfif>