<!--- include headerPanel to include all start scripts --->
<cfinclude template="headerPanel.cfm">
<body leftmargin="0" topmargin="0">
	<cfinclude template="jssalesscript.cfm">
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct">
		<div class="bodyText" style="margin:6px;">
		<cfoutput>#application.objCMS.renderZone(zone='Main',event=event)#</cfoutput>
		</div>
	<cfelse>
		<cfinclude template="headerContent.cfm">
		<div id="blue_content_js">			
			<div class="transHeader">
				<ul id="nav_solutions">
					<li<cfif event.getValue('pg','') EQ 'webApplications'> class="selected"</cfif>><a id="webApps" href="/?pg=webApplications">Web Applications</a></li>
					<li<cfif event.getValue('pg','') EQ 'socialNetworking'> class="selected"</cfif>><a id="socNet" href="/?pg=socialNetworking">Social Networking</a></li>
					<li<cfif event.getValue('pg','') EQ 'distanceLearning'> class="selected"</cfif>><a id="distLearn" href="/?pg=distanceLearning">Distance Learning</a></li>
				</ul>
			</div>			
			<div id="blue_main_area" class="pageSize"><cfoutput>#application.objCMS.renderZone(zone='C',event=event)#</cfoutput></div>
		</div>
		<div id="white_content">
			<cfinclude template="subNavigation.cfm">
			<div class="pageSize white_content_area"><cfoutput>#application.objCMS.renderZone(zone='Main',event=event)#</cfoutput></div>
		</div>
		<cfinclude template="footerContent.cfm">
	</cfif>
	<!--- toolbar --->
	<cfif application.objCMS.getZoneItemCount(zone='ToolBar',event=event)>
		<div class="bodyText" style="margin:6px;"><cfoutput>#application.objCMS.renderZone(zone='ToolBar',event=event)#</cfoutput></div>
	</cfif>
	<cfinclude template="adRoll.cfm" />
</body>
</html>