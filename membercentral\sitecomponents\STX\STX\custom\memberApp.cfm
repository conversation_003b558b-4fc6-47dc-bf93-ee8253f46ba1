<cfscript>
	variables.applicationReservedURLParams = "issubmitted";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	local.currentDate = dateTimeFormat(now());

	/* ************************* */
	/* Custom Page Custom Fields */
	/* ************************* */
	local.arrCustomFields = [];

	local.tmpField = { name="ConfirmationEmailStaffFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ConfirmationEmailStaffTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);

	variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID,	arrCustomFields=local.arrCustomFields);

	// set page defaults
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=event, 
		formName='frmJoin',
		formNameDisplay='Membership Form',
		orgEmailTo=variables.strPageFields.ConfirmationEmailStaffTo,
		memberEmailFrom=variables.strPageFields.ConfirmationEmailStaffFrom
	));

	local.profile_1._profileCode = 'ACCCIM';
	local.profile_1._profileID = application.objCustomPageUtils.acct_getProfileID(siteid=event.getValue('mc_siteinfo.siteid'),profileCode=local.profile_1._profileCode);
	local.profile_1._description = '#local.organization# - #local.formNameDisplay#';

	local.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname=local.formName);

	if( application.objUser.isLoggedIn(cfcuser=session.cfcuser) ){
		local.customFields = arrayNew(1);
		arrayAppend(local.customFields,'Bar Number');
		local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=local.orgID, memberNumber=session.cfcUser.memberData.memberNumber, customFields=local.customFields);
		if (NOT StructKeyExists(local.memberData,"website")) local.memberData.website = '';
	} else{
		local.memberdata.website = '';
		local.memberData['Bar Number'] = '';
	}
</cfscript>

<cfset local.strPhones = { phone="", fax="", cell="" }>
<cfloop query="local.data.phone">
	<cfset structInsert(local.strPhones, local.data.phone.phoneType, local.data.phone.phone, true)>
</cfloop>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.frmContent{ padding:10px; background:##dddddd; }
			.frmRow1{ background:##ffffff; }
			.frmRow2{ background:##dedede; }
			.frmRow3{ background:##aeaeae; }
			.frmTotals{ background:##666666; color:##ffffff; font-weight:bold; }
			.frmText{ font-size:9pt; color:##505050; }
			.frmButtons{ padding:5px 0; border-top:1px solid ##666666; border-bottom:1px solid ##666666; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			<!--- background:url(/assets/common/images/interior_titleBG_Tan.jpg) repeat-x; --->
			.TitleText { font-family:Tahoma; font-size:16pt; color:##03608b; font-weight:bold; }
			.CPSection{ border:1px solid ##666666; margin-bottom:15px; }
			.CPSectionTitle { font-size:14pt; font-weight:bold; color:##ffffff; padding:10px; background:##336699; }
			.CPSectionContent{ padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##cde4f3; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
			.subCPSectionTitle { font-size:10pt; font-weight:bold; }
			.subCPSectionText { font-size:9pt; color:##36617d; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.info{ font-style:italic; font-size:7pt; color:##777777; }
			.small{ font-size:7pt;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b{ font-weight:bold; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.BB { border-bottom:1px solid ##666666; }
			.BL { border-left:1px solid ##666666; }
			.BT { border-top:1px solid ##666666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.tsAppBodyText { color:##03608b;}
			select.tsAppBodyText{color:##666666;}
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			<!--- ----------------------------------------------------------------------------------------------------------- --->
			@media screen and (min-width: 768px){
				.formTableHolder table td:nth-child(2) span:nth-child(1) {
					display: none;
				}
			}
			@media screen and (max-width: 767px){
				.formTableHolder table td:nth-child(1) {
					display: inline;
					margin: 0;
					padding: 0;
				}
				.formTableHolder table td:nth-child(1) span {
					display: none;
				}
				.formTableHolder table td:nth-child(2), .formTableHolder table td:nth-child(2) span {
					margin-bottom: 12px;
					margin-left: 0;
					padding-left: 0;
					margin-bottom: 0px;
				}
				.formTableHolder table td, .formTableHolder table td span {
					display: block;
					margin-bottom: 0px;
				}
				.formTableHolder select {
					width: 220px!important;
				}
				.formTableHolder input[type="text"], textarea {
					width: 206px!important;
				}
			}
			
			
		</style>
	</cfsavecontent>

	#local.pageJS#
	#local.pageCSS#

	<div id="customPage">
		<div class="TitleText" style="padding-bottom:15px;">#local.Organization# - #local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<!--- FORM: ========================================================================================================================================= --->
			<cfcase value="0">
				
				<script type="text/javascript">
					function _FB_validateForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						// -----------------------------------------------------------------------------------------------------------------
							if (!_FB_hasValue(thisForm['joinRenew'], 'RADIO')) arrReq[arrReq.length] 				= 'Please select Join or Renew';
							if (!_FB_hasValue(thisForm['firstName'], 'TEXT')) arrReq[arrReq.length] 				= 'First Name';
							if (!_FB_hasValue(thisForm['lastName'], 'TEXT')) arrReq[arrReq.length] 					= 'Last Name';
						// -----------------------------------------------------------------------------------------------------------------
							if (!_FB_hasValue(thisForm['firmName'], 'TEXT')) arrReq[arrReq.length] 					= 'Firm Name';
							if (!_FB_hasValue(thisForm['address'], 'TEXT')) arrReq[arrReq.length] 					= 'Office Address';
							if (!_FB_hasValue(thisForm['city'], 'TEXT')) arrReq[arrReq.length] 						= 'City';
							if (!_FB_hasValue(thisForm['state'], 'TEXT')) arrReq[arrReq.length] 					= 'State';
							if (!_FB_hasValue(thisForm['zip'], 'TEXT')) arrReq[arrReq.length] 						= 'Zip';
							if (!_FB_hasValue(thisForm['county'], 'TEXT')) arrReq[arrReq.length] 					= 'County';
							if (!_FB_hasValue(thisForm['phone'], 'TEXT')) arrReq[arrReq.length] 					= 'Office Phone';
							if (!_FB_hasValue(thisForm['email'], 'TEXT')) arrReq[arrReq.length] 					= 'Email';
							if (!_FB_hasValue(thisForm['barState'], 'TEXT')) arrReq[arrReq.length] 					= 'States Licensed';
							if (!_FB_hasValue(thisForm['barNum'], 'TEXT')) arrReq[arrReq.length] 					= 'Bar Number';
						// -----------------------------------------------------------------------------------------------------------------
						// -----------------------------------------------------------------------------------------------------------------
							if (!_FB_hasValue(thisForm['Membership'], 'RADIO')) arrReq[arrReq.length] 			= 'Membership level';
						// -----------------------------------------------------------------------------------------------------------------
						
						if($("[name=pacContrib]:checked").length){
							
							if($("[name=frequency]:checked").length == 0){
								arrReq[arrReq.length] 					= 'Please select the PAC Contribution Schedule';
							}else if($("[name=frequency]:checked").val() != 'One-time' && $("[name=duration]:checked").length == 0){
								arrReq[arrReq.length] 					= 'Please select the PAC Contribution expiration date';
							}else if($("[name=frequency]:checked").val() != 'One-time' && $("[name=duration]:checked").val() == 'GiveUntil' && $("[name=dateUntil]").val().length == 0){
								arrReq[arrReq.length] 					= 'Please select a PAC Contribution donation deadline date';
							}
							if(parseInt($("[name=pacContrib]:checked").val()) == 7 && $.trim($("[name=pacContribOther]").val()).length == 0){
								arrReq[arrReq.length] 					= 'Please enter PAC Contribution amount';
							}
						}
						
						if (!_FB_hasValue(thisForm['consent'], 'SINGLE_VALUE_CHECKBOX')) arrReq[arrReq.length] 			= 'Please accept the Membership Statement';
						// -----------------------------------------------------------------------------------------------------------------
						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
					// -----------------------------------------------------------------------------------------------------------------
					function assignMemberData(memObj){
						var thisForm = document.forms["#local.formName#"];
						var er_change = function(r) {
							var results = r;
							if( results.success ){
								thisForm['memberNumber'].value 	= results.membernumber;
								thisForm['memberID'].value 			= results.memberid;

								thisForm['firstName'].value 		= results.firstname;
								thisForm['lastName'].value 			= results.lastname;
								thisForm['firmName'].value			= results.company;
								thisForm['address'].value 			= results.address1;
								thisForm['city'].value 					= results.city;
								thisForm['state'].value 				= results.statecode;
								thisForm['zip'].value 					= results.postalcode;
								thisForm['county'].value 				= results.county;
								thisForm['phone'].value 				= results.phone;
								thisForm['email'].value 				= results.email;
								thisForm['website'].value				= results.website;
								//---------------------------------------------------------------------------------------
								// un hide form   
								document.getElementById('formToFill').style.display 			= '';
							}
							else{ /*alert('not success');*/ }
						};
						/************************************************************************************************/
						var objParams = { memberNumber:memObj.memberNumber };
						TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
					}
					// -----------------------------------------------------------------------------------------------------------------
					
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}
					
					function showJoinFields(){
						var joinRenew = getSelectedRadio(document.getElementsByName('joinRenew'));
						var references = document.getElementById('references');
						
						if (joinRenew == 0){
							references.style.display = '';
						}
						
						if (joinRenew == 1){
							references.style.display = 'none';
						}
					}
					function frequencyChanges(){
						var frequency = getSelectedRadio(document.getElementsByName('frequency'));
						var validity = document.getElementById('validity');
						if (frequency == 0){
							validity.style.display = 'none';
							$("[name='duration']").prop("checked", false);
							$("[name=dateUntil]").val('');							
						}else{
							validity.style.display = '';
						}					
					}
					function pacChange(){
						if($.trim($("[name=pacContribOther]").val()).length){
							$("[name='pacContrib'][value=7]").prop("checked", true);
						}
					}
					function durationChange(){
						if($.trim($("[name=dateUntil]").val()).length){
							$("[name='duration'][value='GiveUntil']").prop("checked", true);
						}
					}
					
					function checkCaptchaAndValidate(){
							var thisForm = document.forms["#local.formName#"];
							var status = false;
							var captcha_callback = function(captcha_response){
								if (captcha_response.response && captcha_response.response != 'success') {
									status = false;
								} else {
									status = true;
								}
							}
							if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
								alert('Please enter the correct code shown in the graphic.');
								return false;
							} else {
								#local.captchaDetails.jsvalidationcode#
							}
							if(status){
								return _FB_validateForm();
							} else {
								alert('Please enter the correct code shown in the graphic.');
								return false;
							}
						}
						$(document).ready(function(){
							
							<cfif NOT structKeyExists(session, "captchaEntered")>
								showCaptcha();
							</cfif>
							mca_setupDatePickerField('dateUntil');
							
						});
				</script>
				<div class="r i frmText">*Denotes required field</div>
				<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return checkCaptchaAndValidate();">
					<input type="hidden" name="isSubmitted" value="1" />
					<input type="hidden" name="memberID" value="#session.cfcUser.memberData.memberID#" />
					<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />
					<input type="checkbox" name="iAgree" id="iAgree" value="1" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
					<!--- =============================================================================================================================================== --->
					<!--- ACCOUNT LOCATOR: ============================================================================================================================== --->
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection">
							<div class="CPSectionTitle BB">Account Lookup / Create New Account</div>
							<div class="frmRow1" style="padding:10px;">
								<table cellspacing="0" cellpadding="2" border="0" width="100%"  class="accountHolder">
									<tr>
										<td width="175" class="c">
											<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
												<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
											</div>
										</td>
										<td>
											<span class="frmText">
												<span class="block" style="padding-bottom:5px;">Click the <span class="b">Account Lookup</span> button to the left.</span>
												<span class="block" style="padding-bottom:5px;">Enter the search criteria and click <span class="b">Continue</span>.</span>
												<span class="block" style="padding-bottom:5px;">If you see your name, please press the <span class="b">Choose</span> button next to your name.</span>
												<span class="block" style="padding-bottom:5px;">If you do not see your name, click the <span class="b">Create an Account</span> link.</span>
											</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</cfif>
					<!--- =============================================================================================================================================== --->
					
					<div id="formToFill" style="display:none;">
						<!--- JOIN/RENEW: ========================================================================================================================= --->
							<div class="CPSection">
							<div class="CPSectionTitle BB">* Type of Application</div>
								<div class=" frmRow1 frmText" style="padding:10px;">
									<table cellspacing="0" cellpadding="4" width="65%" border="0" align="center" style="text-align:center;">
										<tr>
											<td><input name="joinRenew" type="radio" value="join" class="tsAppBodyText" onChange="showJoinFields()" />&nbsp;Join</td>
											<td><input name="joinRenew" type="radio" value="renew" class="tsAppBodyText" onChange="showJoinFields()" />&nbsp;Renew</td>
										</tr>
									</table>
								</div>
							</div>
						<!--- =============================================================================================================================================== --->
						<!--- PERSONAL INFORMATION: ========================================================================================================================= --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">Contact Information</div>
							<div class=" frmRow1 frmText formTableHolder" style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="r" width="200">*First Name:</td>
										<td><input size="40" name="firstName" type="text" value="#session.cfcUser.memberData.firstname#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Last Name:</td>
										<td><input size="40" name="lastName" type="text" value="#session.cfcUser.memberData.lastname#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Firm Name:</td>
										<td><input size="60" name="firmName" type="text" value="#session.cfcUser.memberData.Company#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Address:</td>
										<td><input size="60" name="address" type="text" value="#local.data.address.address1#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">
											<span>
												*City:
											</span>
										</td>
										<td class=" frmText">
											<span>
												*City:
											</span>
											<span>
												<input size="25" name="city" type="text" value="#local.data.address.city#" class="tsAppBodyText" />											
											</span>
											<span>
												&nbsp;*State:
											</span>
											<span>
												<input size="2" maxlength="2" name="state" type="text" value="#local.data.address.stateCode#" class="tsAppBodyText" />
											</span>
											<span>
												&nbsp;*Zip:
											</span>
											<span>
												<input size="10" maxlength="15" name="zip" type="text" value="#local.data.address.postalCode#" class="tsAppBodyText" />
											</span>
										</td>
									</tr>
									<tr>
										<td class="r">*County:</td>
										<td><input size="60" name="county" type="text" value="#local.data.address.county#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">
											<span>
												*Office Phone:
											</span>
										</td>
										<td class=" frmText">
											<span>
												*Office Phone:
											</span>
											<span>
												<input size="13" maxlength="13" name="phone" type="text" value="#local.strPhones.phone#" class="tsAppBodyText" />
											</span>
											<span>
												&nbsp;Cell Phone:
											</span>
											<span>
												<input size="13" maxlength="13" name="cellPhone" type="text" value="#local.strPhones.cell#" class="tsAppBodyText" />
											</span>
											<span>
												&nbsp;Fax:
											</span>
											<span>											
												<input size="10" maxlength="13" name="fax" type="text" value="#local.strPhones.fax#" class="tsAppBodyText" />
											</span>
										</td>
									</tr>
									<tr>
										<td class="r">*Email:</td>
										<td><input size="60" name="email" type="text" value="#session.cfcUser.memberData.email#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">Web Site:</td>
										<td><input size="60" name="website" type="text" value="#local.memberData.website#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*States Licensed:</td>
										<td><input size="60" name="barState" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Bar Number:</td>
										<td><input size="60" name="barNum" type="text" value="#local.memberData['Bar Number']#" class="tsAppBodyText" /></td>
									</tr>
								</table>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->
						<div class="CPSection" id="references" style="display: none;">
							<div class="CPSectionTitle BB">References</div>
							<div class="subCPSectionArea1 BB">
								<span class="subCPSectionText">List two current SATLA or Listserve members who will vouch for you:<br />(You can still apply if you do not know any SATLA members, but approval time will take longer.)
							</div>
							<div class="frmText frmRow1 BB formTableHolder"  style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="r" width="200">Reference 1 - Name:</td>
										<td><input size="55" name="ref1_name" type="text" value="" class="tsAppBodyText" /></td>
									</tr>	
									<tr>
										<td class="r"><span>E-mail:</span></td>
										<td class=" frmText">
											<span>
												&nbsp;E-mail:
											</span>
											<span>
												<input size="20" name="ref1_email" type="text" value="" class="tsAppBodyText" />
											</span>
											<span>
												Phone:&nbsp;
											</span>
											<span>
												<input size="20" maxlength="13" name="ref1_phone" type="text" value="" class="tsAppBodyText" />
											</span>
										</td>
									</tr>
								</table>
							</div>
							
							<div class="frmText frmRow2 formTableHolder"  style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="r" width="200">Reference 2 - Name:</td>
										<td><input size="55" name="ref2_name" type="text" value="" class="tsAppBodyText" /></td>
									</tr>	
									<tr>
										<td class="r"><span>E-mail:</span></td>
										<td class=" frmText">
											<span>
												&nbsp;E-mail:
											</span>
											<span>
												<input size="20" name="ref2_email" type="text" value="" class="tsAppBodyText" />
											</span>
											<span>
												&nbsp;Phone:
											</span>
											<span>
												<input size="20" maxlength="13" name="ref2_phone" type="text" value="" class="tsAppBodyText" />
											</span>
										</td>
									</tr>
								</table>
							</div>
							
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">*Membership Level</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="P c" width="25"><input type="radio" value="1" name="Membership" /></td>
										<td>Licensed less than five (5) years</td>
										<td width="150" class="P r">$150.00</td>
									</tr>
									<tr class="frmRow1"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P c"><input type="radio" value="2" name="Membership" /></td>
										<td>Licensed more than five (5) years</td>
										<td class="P r">$200.00</td>
									</tr>
								</table>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">Voluntary PAC Contribution</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td colspan="2">
											<table cellspacing="0" cellpadding="4" border="0" align="center" style="text-align:center;">
												<tr>
													<td><input name="frequency" id="frequency" type="radio" value="One-time" class="tsAppBodyText" onChange="frequencyChanges()"/>&nbsp;One-Time</td>
													<td><input name="frequency" id="frequency" type="radio" value="Monthly" class="tsAppBodyText" onChange="frequencyChanges()"/>&nbsp;Monthly</td>
													<td><input name="frequency" id="frequency" type="radio" value="Annual" class="tsAppBodyText" onChange="frequencyChanges()"/>&nbsp;Annually</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr class="frmRow1" id="validity" style="display: none;">
										<td colspan="2">
											<table cellspacing="0" cellpadding="4" border="0" align="center" style="text-align:center;">
												<tr>
													<td><input name="duration" id="duration" type="radio" value="GiveUntil" class="tsAppBodyText"/>&nbsp;Give Until: <input size="10" maxlength="10" readonly="" onChange="durationChange();" name="dateUntil" id="dateUntil" type="text" value="" class="tsAppBodyText" style="background-image:url('/assets/common/images/calendar/monthView.gif'); background-position:right center; background-repeat:no-repeat; cursor:pointer !important;border: 1px solid;"></td>
													<td><input name="duration" id="duration" type="radio" value="Ongoing" class="tsAppBodyText" />&nbsp;Ongoing</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr class="frmRow1"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>									
									<tr class="frmRow2">
										<td class="P l" width="25"><input type="radio" value="1" name="pacContrib" id="pacContrib" /></td>
										<td class=" l">$0.00</td>
									</tr>
									<tr class="frmRow2"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="P l"><input type="radio" value="2" name="pacContrib" id="pacContrib" /></td>
										<td class=" l">$50.00</td>
									</tr>
									<tr class="frmRow1"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P l"><input type="radio" value="3" name="pacContrib" id="pacContrib" /></td>
										<td class=" l">$100.00</td>
									</tr>
									<tr class="frmRow2"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="P l"><input type="radio" value="4" name="pacContrib" id="pacContrib" /></td>
										<td class=" l">$250.00</td>
									</tr>
									<tr class="frmRow1"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P l"><input type="radio" value="5" name="pacContrib" id="pacContrib" /></td>
										<td class=" l">$500.00</td>
									</tr>
									<tr class="frmRow2"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="P l"><input type="radio" value="6" name="pacContrib" id="pacContrib" /></td>
										<td class=" l">$1000.00</td>
									</tr>
									<tr class="frmRow1"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P l"><input type="radio" value="7" name="pacContrib" id="pacContrib" /></td>
										<td class=" l">Other: <input size="10" name="pacContribOther" type="text" value="" class="tsAppBodyText" onChange="pacChange();" onBlur="this.value=formatCurrency(this.value);"/></td>
									</tr>
								</table>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">*Membership Statement</div>
							<div class=" subCPSectionArea1 BB">
								<div class=" subCPSectionText"> 
									Yes, I want to be a part of SATLA and the List Server.  In support of my membership, I submit the following: I am currently licensed to practice law in at least one state of the United States of America. In addition, neither I nor anyone with whom I practice regularly represents insurance companies or corporations in personal injury claims or claims brought by individuals. I affirm that I and my firm are aware of, and are in full compliance with, the Barratry Statute (38.12 Texas Penal Code).  And, neither I nor my firm knowingly obtains cases directly or indirectly from companies or individuals that solicit cases in contravention of the statue and/or in any manner that is not allowed for attorneys. <br />
									<br />
									I am hereby applying for membership in SATLA.  If accepted as a member, I agree to abide by the By-Laws of the Association. <br />
									<br />
									I further agree, under penalty of expulsion, to keep confidential all communications between fellow members regarding case selection, trial strategy, judges or other judicial officers, clients,  opposing counsel, and the like.  Confidential information is shared by SATLA members with other SATLA members for the sole and express purpose of rendering legal assistance to clients.  SATLA members receiving confidential information about another member's case or client are consulting experts as that term is defined in the applicable rules of civil procedure or evidence.  Thus, the information shared or developed during brainstorming, the discussion of legal strategy or issues, or the rendition of legal advice is privileged from disclosure to anyone outside the consulting experts' group.  No SATLA member may waive this privilege for another member's case or client. Dues to professional associations are normally deductible as ordinary and necessary business expenses; however, the portion of dues related to lobbying is not deductible.  We recommend that 95% of the dues are deductible, as 5% of the dues go toward administrating the PAC. As always, we suggest you consult your tax attorney or tax advisor for further details. Contributions or gifts to SATLA and SATLA PAC are not deductible as charitable contributions for Federal Income Tax purposes. <br />
								</div>
							</div>
							<div class=" frmRow1 frmText" style="padding:10px;">
								<input type="checkbox" name="consent" value="I Agree">&nbsp;I agree to the above membership statement.
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<!--- =============================================================================================================================================== --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">* Listserver Agreement Form</div>
							<div class="frmRow1 frmText" style="padding:10px;">
								<strong>PLEASE NOTE THAT YOU ARE REQUIRED TO COMPLETE THE SATLA LISTSERVER AGREEMENT FORM ON THE NEXT PAGE AS PART OF YOUR MEMBERSHIP REQUEST.</strong><br /><br />
							</div>
						</div>
						<!--- =============================================================================================================================================== --->
						
						<div class="row-fluid">
 							<div class="span12">							
 								#local.captchaDetails.htmlContent#
 							</div>
 						</div> 
						<!---  style="display:none;" --->
						<!--- BUTTONS: ====================================================================================================================================== --->					
						<div id="formButtons">
							<div style="padding:10px;">
								<div align="center" class="frmButtons">
									<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
								</div>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->					
					</div>
				</cfform>

				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
				</cfif>
				
			</cfcase>
			
			<!--- PAYMENT INFO: ================================================================================================================================= --->
			<cfcase value="1">
				<cfif structKeyExists(event.getCollection(), 'iAgree') OR (NOT structKeyExists(session, "captchaEntered") AND (NOT Len(event.getValue('captcha',''))) OR application.objCustomPageUtils.validateCaptcha(code=event.getValue('captcha'),captcha=event.getValue('captcha_check')).response NEQ "success")>
					<cflocation url="#local.customPage.baseURL#&issubmitted=100" addtoken="no">
				</cfif>
				<cfscript>
					// GATEWAY INFORMATION: --------------------------------------------------------------------------------------------------
					local.profile_1.strPaymentForm = 	application.objPayments.showGatewayInputForm(
																			siteid=event.getValue('mc_siteinfo.siteid'),
																			profilecode=local.profile_1._profileCode,
																			pmid = local.useMID,
																			showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																			usePopupDIVName='paymentTable'
																		);
				</cfscript>
				<script type="text/javascript">
					function checkPaymentMethod() {
						var rdo = document.forms["#local.formName#"].payMeth;
						if (rdo[0].checked) {//credit card
							document.getElementById('CCInfo').style.display = '';
							document.getElementById('CheckInfo').style.display = 'none';
						}  
						else if (rdo[1].checked) {//check
							document.getElementById('CCInfo').style.display = 'none';
							document.getElementById('CheckInfo').style.display = '';
						}  
						
					}
					// -----------------------------------------------------------------------------------------------------------------
					function getMethodOfPayment() {
						var btnGrp = document.forms['#local.formName#'].payMeth;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}
					// -----------------------------------------------------------------------------------------------------------------
					function _validate() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						// -----------------------------------------------------------------------------------------------------------------
						if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();
						
						if( MethodOfPaymentValue == 'CC' )	{
							#local.profile_1.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement			= thisForm['confirmationStatement'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						}
						
						// -----------------------------------------------------------------------------------------------------------------
						if (arrReq.length > 0) {
							var msg = 'The following fields are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
				</script>
				
				<script>
					function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
				</script>
				<cfif len(local.profile_1.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
				</cfif>
				
				<div id="paymentTable">
					<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
					<div class="form">
						<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="#local.customPage.baseURL#" onSubmit="return _validate();">
							<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
							<cfloop collection="#event.getCollection()#" item="local.key">
								<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
									and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
									and left(local.key,4) neq "fld_">
									<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#event.getValue(local.key)#">
								</cfif>
							</cfloop>
							<div>
								<!--- ----------------------------------------------------------------------------------------------------- --->
								<div class="CPSection">
									<div class="CPSectionTitle">*Method of Payment</div>
									<div class="P">
										<table cellpadding="2" cellspacing="0" width="100%" border="0">
											<tr valign="top">
												<td colspan="2">Please select your preferred method of payment from the options below.</td>
											</tr>
											<tr>
												<td>
													<table cellpadding="2" cellspacing="0" width="100%" border="0">
														<tr>
															<td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
															<td>Credit Card</td>
														</tr>
														<tr>
															<td width="25"><input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
															<td>Check</td>
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</div>
								</div>
								
								<!--- CREDIT CARD INFO: ----------------------------------------------------------------------------------- --->
								<div id="CCInfo" style="display:none;" class="CPSection">
									<div class="CPSectionTitle">Credit Card Information</div>
									<div class="PL PR frmText paymentGateway BT BB">
										<cfif len(local.profile_1.strPaymentForm.inputForm)>
											<div>#local.profile_1.strPaymentForm.inputForm#</div>
										</cfif>
									</div>
									
									<div class="P">
										<div class="PB">* Please confirm the statement below:</div>
										<table width="100%">
											<tr>
												<td width="25"><input name="confirmationStatement" id="confirmationStatement"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
												<td>I confirm that I have full authority to make payment from the above credit card account for my contribution.</td>
											</tr>
										</table>
									</div>
									
									<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">AUTHORIZE</button></div>
								</div>
								
								<!--- CHECK INFORMATION: ---------------------------------------------------------------------------------- --->
								<div id="CheckInfo" style="display:none;" class="CPSection">
									<div class="CPSectionTitle">Check Information</div>
									<div class="P">
										
												Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
												<strong>San Antonio Trial Lawyers</strong><br />
												PO BOX 120212<br />
												San Antonio, TX 78212
												
												
									</div>
									<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">CONTINUE</button></div>
								</div>
								
							</div>
						</cfform>
					</div>
				</div>
			</cfcase>
		
			<!--- PROCESS: ====================================================================================================================================== --->
			<cfcase value="2">
				
				<cfscript>
					switch(event.getValue('Membership')){					
						case '1': local.memberShipDues = 150; 		local.memberShipName = 'Licensed less than five (5) years'; 	break;
						case '2': local.memberShipDues = 200; 		local.memberShipName = 'Licensed more than five (5) years'; 	break;
					}
					
					switch(event.getValue('pacContrib',1)){
						case '1': local.pacDonation = 0;	break;
						case '2': local.pacDonation = 50; 	break;
						case '3': local.pacDonation = 100; 	break;
						case '4': local.pacDonation = 250; 	break;
						case '5': local.pacDonation = 500; 	break;
						case '6': local.pacDonation = 1000; break;
						case '7': 
							local.pacContribOther = val(rereplace(event.getTrimValue('pacContribOther'),'[^0-9\.]','','ALL'));
							local.pacDonation = local.pacContribOther;
							break;
					}
					local.totalAmount = local.memberShipDues + local.pacDonation;
				</cfscript>
				
				<cfsavecontent variable="local.name">
					#event.getValue('firstName','')# <cfif len(trim(event.getValue('middleName','')))>#event.getValue('middleName','')# </cfif>#event.getValue('lastName','')#
				</cfsavecontent>
				<cfset local.ORGEmail.SUBJECT = local.ORGEmail.SUBJECT & " - From: " & local.name />

				<cfsavecontent variable="local.invoice">
					#local.pageCSS#
					<!-- @accResponseMessage@ -->
					<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
					
					<tr class="msgHeader"><td colspan="2" class="b">CONTACT INFORMATION</td></tr>
						<tr class="frmRow1"><td class="frmText b">First Name:</td><td class="frmText">#event.getValue('firstName')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Last Name:</td><td class="frmText">#event.getValue('lastName')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Firm Name:</td><td class="frmText">#event.getValue('firmName')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Address:</td><td class="frmText">#event.getValue('address')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">State:</td><td class="frmText">#event.getValue('state')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">County:</td><td class="frmText">#event.getValue('county')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Office Phone:</td><td class="frmText">#event.getValue('phone')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Cell Phone:</td><td class="frmText">#event.getValue('cellPhone')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Fax:</td><td class="frmText">#event.getValue('fax')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Email:</td><td class="frmText">#event.getValue('email')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Website:</td><td class="frmText">#event.getValue('website')#&nbsp;</td></tr>						
						<tr class="frmRow1"><td class="frmText b">States Licensed:</td><td class="frmText">#event.getValue('barState')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Bar Number:</td><td class="frmText">#event.getValue('barNum')#&nbsp;</td></tr>
						<cfif event.getValue('joinRenew') eq 'join'>
							<tr class="msgHeader"><td colspan="2" class="b">REFERENCES</td></tr>
							<tr class="frmRow1"><td class="frmText b">Name:</td><td class="frmText">#event.getValue('ref1_name')#&nbsp;</td></tr>
							<tr class="frmRow1"><td class="frmText b">E-mail:</td><td class="frmText">#event.getValue('ref1_email')#&nbsp;</td></tr>
							<tr class="frmRow1"><td class="frmText b">Phone:</td><td class="frmText">#event.getValue('ref1_phone')#&nbsp;</td></tr>
							
							<tr class="frmRow2"><td class="frmText b">Name:</td><td class="frmText">#event.getValue('ref2_name')#&nbsp;</td></tr>
							<tr class="frmRow2"><td class="frmText b">E-mail:</td><td class="frmText">#event.getValue('ref2_email')#&nbsp;</td></tr>
							<tr class="frmRow2"><td class="frmText b">Phone:</td><td class="frmText">#event.getValue('ref2_phone')#&nbsp;</td></tr>
						</cfif>
						<tr><td colspan="2">&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP STATEMENT</td></tr>
						<tr class="frmRow1"><td class="frmText b">I Agree to the membership statement:</td><td class="frmText l">#event.getValue('consent','I Agree')#&nbsp;</td></tr>
						
						<tr><td colspan="2">&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP LEVEL</td></tr>
						<tr class="frmRow1"><td class="frmText b">#local.memberShipName#:</td><td class="frmText">#dollarFormat(local.memberShipDues)#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">PAC DONATION</td></tr>
						<tr class="frmRow1"><td class="frmText b">Donation Amount:</td><td class="frmText">#dollarFormat(local.pacDonation)#&nbsp;</td></tr>
						<cfif local.pacDonation GT 0>
							<tr class="frmRow1"><td class="frmText b">Schedule:</td><td class="frmText">#event.getValue('frequency')#&nbsp;</td></tr>						
							<cfif event.getValue('frequency','') eq 'Monthly' or event.getValue('frequency','') eq 'Annual'>
								<tr class="frmRow1"><td class="frmText b">Until:</td><td class="frmText">
									<cfif event.getValue('duration','') EQ "GiveUntil">
									#dateFormat(event.getValue('dateUntil'),'mm/dd/yyyy')#
									<cfelse>
									Perpetual
									</cfif>
								&nbsp;</td></tr>
							</cfif>
						</cfif>
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2" class="b">TOTAL</td></tr>
						<tr class="frmRow1"><td class="frmText b">Total Amount:</td><td class="frmText">#dollarFormat(local.totalAmount)#&nbsp;</td></tr>
						
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2">PAYMENT INFORMATION</td></tr>
						<tr><td class="frmText b">Payment Type: </td><td class="frmText"><cfif event.getValue('payMeth','CC') EQ 'CC'>Credit Card<cfelse>Check</cfif></td></tr>
					
					</table>
				</cfsavecontent>
				<!--- ---------------------- --->
				<!--- Payment and accounting --->
				<!--- ---------------------- --->
				<cfset local.strAccTemp = { totalPaymentAmount=local.memberShipDues, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, rc=event.getCollection() } >

				<cfif local.strAccTemp.totalPaymentAmount gt 0 and event.getValue('payMeth','') eq "CC">
					<cfset local.strAccTemp.payment = { detail=local.profile_1._description, amount=local.strAccTemp.totalPaymentAmount, profileID=local.profile_1._profileID, profileCode=local.profile_1._profileCode }>
				</cfif>			
				<cfset local.strAccTemp.revenue = [ { revenueGLAccountCode='Dues', detail=local.profile_1._description, amount=local.strAccTemp.totalPaymentAmount } ]>

				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				

				<!--- 	Close invoice if payment method is check --->
				<cfif arguments.event.getValue('payMeth','Check') neq "CC" 
				and structKeyExists(local.strACCResponse, "revenueResponse") 
				and arrayLen(local.strACCResponse.revenueResponse)
				and structKeyExists(local.strACCResponse.revenueResponse[1], "invoiceID")>

					<cfquery name="local.qryChangeInvoiceStatus" datasource="#application.dsn.membercentral.dsn#">
						SET XACT_ABORT, NOCOUNT ON;  
						BEGIN TRY

							declare @assignedToMemberID int = <cfqueryparam value="#local.useMID#" cfsqltype="cf_sql_integer">,
									@invoiceID int = <cfqueryparam value="#local.strACCResponse.revenueResponse[1].invoiceID#" cfsqltype="cf_sql_integer">,
									@invoiceIDList varchar(10),
									@orgID int = <cfqueryparam value="#local.orgID#" cfsqltype="cf_sql_integer">;

							set @invoiceIDList = cast(@invoiceID as varchar(10));

							BEGIN TRAN;		
								-- close invoices
								EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@assignedToMemberID, @invoiceIDList=@invoiceIDList;
			
							COMMIT TRAN;
			
							select 1 as 'success';
						END TRY  
						BEGIN CATCH  
							select 0 as 'success';
							IF @@trancount > 0 ROLLBACK TRANSACTION;  
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;  
						END CATCH  
					</cfquery>				
				</cfif>
			
				<cfset local.pacContribContent = "">
				<cfdump var="#local.strACCResponse#">
				<cfif local.pacDonation GT 0 and event.getValue('frequency','') eq 'Monthly' or event.getValue('frequency','') eq 'Annual'>
					<cfset local.stFinalMemberNumber = application.objMember.getMemberNumberByMemberID(memberID=local.useMID, orgID=local.orgID)>
					<cfsavecontent variable="local.pacContribContent">
						<p>A member has 
						<cfif event.getValue('joinRenew') eq 'join'>
						Joined
						<cfelse>
						Renewed
						</cfif>
						and selected a #event.getValue('frequency')# giving option. Please review the information and create a new perpetual PAC contribution.</p>

						#event.getValue('firstName')# #event.getValue('lastName')#<br>
						#event.getValue('firmName')#<br>
						#local.stFinalMemberNumber#<br>
						<br>
						<p><b>Voluntary PAC Contribution</b></p>
						Amount: #dollarFormat(local.pacDonation)#<br>
						Schedule: #event.getValue('frequency')#<br>
						<cfif event.getValue('duration','') EQ "GiveUntil">
						Until: #dateFormat(event.getValue('dateUntil'),'mm/dd/yyyy')#
						<cfelse>
						Until: Perpetual
						</cfif>
					</cfsavecontent>
					
					<cfscript>
						local.arrEmailTo = [];
						local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
						local.toEmailArr = listToArray(local.ORGEmail.to,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
						local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject="SATLA Admin",
							emailtitle=event.getTrimValue('mc_siteinfo.sitename') & " - SATLA Admin",
							emailhtmlcontent=local.pacContribContent,
							siteID=event.getTrimValue('mc_siteinfo.siteID'),
							memberID=val(event.getTrimValue('mc_siteinfo.sysMemberID')),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						);
					</cfscript>
				<cfelseif local.pacDonation GT 0>
					<!--- ---------------------- --->
					<!--- Payment and accounting --->
					<!--- ---------------------- --->
					<cfset local.strAccTemp = { totalPaymentAmount=local.pacDonation, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, rc=event.getCollection() } >

					<cfif local.strAccTemp.totalPaymentAmount gt 0 and event.getValue('payMeth','') eq "CC">
						<cfset local.strAccTemp.payment = { detail=local.profile_1._description, amount=local.strAccTemp.totalPaymentAmount, profileID=local.profile_1._profileID, profileCode=local.profile_1._profileCode }>
					</cfif>			
					<cfset local.strAccTemp.revenue = [ { revenueGLAccountCode='PAC', detail=local.profile_1._description, amount=local.strAccTemp.totalPaymentAmount } ]>

					<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
					<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>

					<!--- 	Close invoice if payment method is check --->
					<cfif arguments.event.getValue('payMeth','Check') neq "CC" 
					and structKeyExists(local.strACCResponse, "revenueResponse") 
					and arrayLen(local.strACCResponse.revenueResponse)
					and structKeyExists(local.strACCResponse.revenueResponse[1], "invoiceID")>

						<cfquery name="local.qryChangeInvoiceStatus" datasource="#application.dsn.membercentral.dsn#">
							SET XACT_ABORT, NOCOUNT ON;  
							BEGIN TRY

								declare @assignedToMemberID int = <cfqueryparam value="#local.useMID#" cfsqltype="cf_sql_integer">,
										@invoiceID int = <cfqueryparam value="#local.strACCResponse.revenueResponse[1].invoiceID#" cfsqltype="cf_sql_integer">,
										@invoiceIDList varchar(10),
										@orgID int = <cfqueryparam value="#local.orgID#" cfsqltype="cf_sql_integer">;

								set @invoiceIDList = cast(@invoiceID as varchar(10));

								BEGIN TRAN;		
									-- close invoices
									EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@assignedToMemberID, @invoiceIDList=@invoiceIDList;

								COMMIT TRAN;

								select 1 as 'success';

							END TRY  
							BEGIN CATCH  
								select 0 as 'success';
								IF @@trancount > 0 ROLLBACK TRANSACTION;  
								EXEC dbo.up_MCErrorHandler @raise=1, @email=0;  
							END CATCH  
						</cfquery>				
					</cfif>
				</cfif>
				<cfdump var="#local.strACCResponse#" >
				<!--- email member ---------------------------------------------------------------------------------------------- --->
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>Thank you for submitting your application! Please print this page - it is your receipt.</p>	
						<hr />
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.memberEmail.from },
					emailto=[{ name="", email=local.memberEmail.to }],
					emailreplyto=local.ORGEmail.to,
					emailsubject=local.memberEmail.SUBJECT,
					emailtitle="#event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=local.siteID,
					memberID=val(local.useMID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>

				<cfset local.emailSentToUser = local.responseStruct.success>

				<!--- email association ------------------------------------------------------------- --->
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							<p style="color:red;">We were not able to send #local.name# an e-mail confirmation.</p>
						</cfif>
						#replaceNoCase(local.invoice,'<!-- @accResponseMessage@ -->',local.strACCResponse.accResponseMessage)#
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
					local.toEmailArr = listToArray(local.ORGEmail.to,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
					local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email=local.ORGEmail.from },
						emailto=local.arrEmailTo,
						emailreplyto=local.ORGEmail.from,
						emailsubject=local.ORGEmail.SUBJECT,
						emailtitle=event.getTrimValue('mc_siteinfo.sitename') & " - " & local.formNameDisplay,
						emailhtmlcontent=local.mailContent,
						siteID=event.getTrimValue('mc_siteinfo.siteID'),
						memberID=val(event.getTrimValue('mc_siteinfo.sysMemberID')),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					);
				</cfscript>
				
				 <!--- create pdf and put on member's record --->
				<cfset local.uid = createuuid()>
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
				<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
					<cfoutput>
						<html>
						<head>
						<style>

						</style>
						</head>
						<body>
							<p>Here are the details of your application:</p>
							#local.invoice#
						</body>
						</html>
					</cfoutput>
				</cfdocument>
				<cfset local.strPDF = structNew()>
				<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
				<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(local.currentDate,'m-d-yyyy')#.pdf">
				<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
				<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(local.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
				<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
				<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=local.useMID, strPDF=local.strPDF, siteID=local.siteID, docTitle='Membership Form - #DateFormat(now(),'m/d/yyyy')#', docDesc='Membership Form Confirmation')>
				
				<!--- relocate to message page --->
				<cfset session.invoice = local.invoice />
				<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
			</cfcase>
			
			<!--- MESSAGE AFTER THE POST: ======================================================================================================================= --->
			<cfcase value="99">
				<!--- output to screen --->
				<div class="HeaderText">Thank you for submitting your application!</div>
				<br/>
				<cfif isDefined("session.invoice")>
					<div>This page has been emailed to the email address on file. If you would like you could also print the page out as a receipt.</div>
					<br />
					<br />
					<div>Please wait while you are redirected to the required List Server Agreement page.</div>
					<br />
						<meta http-equiv="refresh" content="25; URL=/?pg=ListserverAgreement">
					<div>If you are not redirected automatically please <a href="/?pg=ListserverAgreement">click here</a> to continue.</div>
					<br />
					<div class="BodyText">
						#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
					</div>
				</cfif>
			</cfcase>
			
			<!--- SPAM MESSAGE: ================================================================================================================================= --->
			<cfcase value="100">
					<div>
						Error! you Can't Post Here.
					</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>