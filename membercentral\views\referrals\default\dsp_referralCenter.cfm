﻿﻿<cfset  variables.structData=attributes.data>
<cfset variables.objReferrals = CreateObject("component","model.referrals.referrals")>
<cfset local.redirectBaseUrl = '/?pg=referrals'>

<cfsavecontent variable="variables.gridJS">
	<cfoutput>	
	<link rel="stylesheet" href="/assets/common/javascript/intl-tel-input/18.1.1/css/intlTelInput.css">
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/dhtmlxgrid/dhtmlxgrid.css" />
 	<script type="text/javascript" src="/assets/common/javascript/dhtmlxgrid/memberCentral_grid.js"></script>
	<script type="text/javascript">dhtmlxError.catchError("LoadXML", mcg_ErrorHandler);</script>	
	<script src="/assets/common/javascript/intl-tel-input/18.1.1/js/intlTelInput.min.js"></script>	
	<style type="text/css">
	.boxName { width:795px; height:17px; margin-bottom:-10px; padding-top:10px; padding-left:3px; border:1px solid ##707070; background-color:##ddd; }
	.gridBox { width:800px; height:250px; margin-top:6px; margin-bottom:10px; }
	.gridNavigation { width:800px; background-color:##dddddd; margin-top:3px; margin-bottom:3px; border:1px solid ##707070; }
	.gridRNumMember { margin:14px 0 0 0; }
	##boxes { padding: 5px 0 15px 20px; }
	.saveButton { padding: 0 10px 0 700px; }
	fieldset.panelBorder {  border: 1px solid ##d6d6d6;  padding: 0 20px 10px; margin: 0 0 11px 0; width: 760px;}
	legend.panelBorder { width:auto; padding:0 10px;border-bottom:none; margin-bottom:0px; }
	.smsCntWrap{
		position: relative;
		border: 1px solid ##9E9E9E;
		padding-top: 10px;
		min-height: 50px;
	}
	.smsCntWrap .addNumberBtn{
		text-decoration:underline;
		cursor:pointer;
		color:##08c !important
	}
	.smsCntWrap img.addPhoneIcon{
		cursor:pointer;
	}
	
	.hidden{
		display:none;
	}
	.divWrap{
		padding: 11px;
		border: 1px solid ##cccccc;
		margin: 8px;
		border-radius: 8px;
		box-shadow: 0 4px 29px rgba(0, 0, 0, 2%), 0 1px 4px rgba(0, 0, 0, 0.1);
	}
	.phoneLabel{
		padding-top:3px;
		padding-bottom:3px;
	}
	##MFAPhNo{
		margin-top:3px;
	}
	.phoneNumberCol {
		float: left;
		width: 30.33%;
		padding: 10px;
		}

	.phoneNumberRow:after {
		content: "";
		display: table;
		clear: both;
	}
	.phoneNumberRow .strPhoneVerificationLabel{
		font-size:18px;
		font-weight:bold;
		padding-left: 5px;
	}
	.phoneNumberRow .strPhoneNumberLabel{
		font-size:18px;
	}
	.phoneNumberColAction{
		text-align:right;
	}
	.verifyIcon{
		margin-top: -5px;
	}
	.smsCntWrap{
		padding:10px !important;
	}
	</style>

	<script>
		var mcg_itemsingle = 'referral'; 
		var mcg_itemplural = 'referrals';
		
		var #ToScript(variables.structData.referralList ,'memberReferralsXML')#
		var #ToScript(variables.structData.referralList ,'mcg_gridQString')#
		var #ToScript(variables.structData.viewDirectory,'viewDirectory')#
		var mcg_gridBaseQString = memberReferralsXML;
		
		var mcg2_itemsingle = 'case'; 
		var mcg2_itemplural = 'cases';	
		
		var #ToScript(variables.structData.caseList,'memberCasesXML')#
		var #ToScript(variables.structData.caseList,'mcg2_gridQString')#	
		var mcg2_gridBaseQString = memberCasesXML;
		
		var mcg3_itemsingle = 'record'; 
		var mcg3_itemplural = 'records';	
		
		var #ToScript(variables.structData.historyList,'memberReferralHistoryXML')#
		var #ToScript(variables.structData.historyList,'mcg3_gridQString')#	
		var mcg3_gridBaseQString = memberReferralHistoryXML;
		
		var #ToScript(variables.structData.rfpList,'rfpListXML')#
		var #ToScript(variables.structData.rfpList,'mcg4_gridQString')#	
		var mcg4_gridBaseQString = rfpListXML;

		function mcg_change(queryString) {
			mcg_gridQString = mcg_gridBaseQString + queryString;
			mcg_reloadGrid();
		}
		
		function mcg2_change(queryString) {
			mcg2_gridQString = mcg2_gridBaseQString + queryString;
			mcg2_reloadGrid();
		}
		
		function mcg3_change(queryString) {
			mcg3_gridQString = mcg3_gridBaseQString + queryString;
			mcg3_reloadGrid();
		}	
		function mcg4_change(queryString) {
			mcg4_gridQString = mcg4_gridBaseQString + queryString;
			mcg4_reloadGrid();
		}		

		function refreshGrids() { 
			mcg_reloadGrid();
			mcg2_reloadGrid();
			mcg3_reloadGrid();
			mcg4_reloadGrid();
		}

		function editReferral(rID){
			parent.location.href = "#variables.structData.mainurl#&ra=editReferral&clientReferralID=" + rID;
		}
		
		function editCase(cID){
			parent.location.href = "#variables.structData.mainurl#&ra=editCase&clientReferralID=" + cID;
		}

		function applyPayment(rID){
			parent.location.href = "#variables.structData.mainurl#&ra=editCase&applyPayment=1&clientReferralID=" + rID + "##feesInfo";
		}
		
		function sendStatement(rID) {
			$.colorbox( {innerWidth:600, innerHeight:400, href:'#variables.structData.link.viewCaseStatement#&clientReferralID=' + rID, iframe:true, overlayClose:false} );
		}
		
		function closeBox() { $.colorbox.close(); }		
		
		function checkMaxPanel(){
			<cfif val(variables.structData.maxNumberOfPanels) gt 0>
				var memPanelCheckBox = $('form[name="memberPanelForm"] input[type="checkbox"].memberPanelCheckBox');
				var count = 0;
				$.each(memPanelCheckBox, function(index,value){
				  if($(value).is(':checked')){
				    count = count + 1; 
				  }
				});
				if(count > #val(variables.structData.maxNumberOfPanels)#){
					alert('You can select a maximum of #val(variables.structData.maxNumberOfPanels)# panel(s).');
					return false;
				}
			<cfelse>
				return true;
			</cfif>
		}
		
		function showHideSubPanels(pID) {
			$('##panelParent_'+pID).toggle();
		}
		
		function printReport(){
			$.ajax({
				type: "POST",
				url: '#variables.structData.mainurl#&ra=printMonthlyReport&mode=stream',
				success: successPrint,
				dataType: 'JSON'
			});
		}
		function successPrint(r){
			var content = r['HTMLCONTENT'];
			var pri = document.getElementById("contenttoprint").contentWindow;
			pri.document.open();
			pri.document.write(content);
			pri.document.close();
			pri.focus();
			pri.print();
		}

		$(function() {
			mcg_init();
			mcg2_init();
			mcg3_init();
			mcg4_init();

			$(".refFilterForm").submit(function(event){			
				event.preventDefault();

				var currentTab = $(this).find("[name='tab']").val();
				var filterReferralID = $.trim($(this).find("[name='filterReferralID']").val());
				var filterClientLastName = $.trim($(this).find("[name='filterClientLastName']").val());
				var filterClientFirstName = $.trim($(this).find("[name='filterClientFirstName']").val());
				var filterStatus = $.trim($(this).find("[name='filterStatus']").val());
				var refDateRange = $.trim($(this).find("[name='refDateRange']").val());	
				var gridBaseQString	= "";

				if(refDateRange != 0){
					gridBaseQString = gridBaseQString + '&refDateRange=' + refDateRange;
				}
				if(filterReferralID != ""){
					gridBaseQString = gridBaseQString + '&filterReferralID=' + filterReferralID;
				}
				if(filterClientLastName != ""){
					gridBaseQString = gridBaseQString + '&filterClientLastName=' + filterClientLastName;
				}
				if(filterClientFirstName != ""){
					gridBaseQString = gridBaseQString + '&filterClientFirstName=' + filterClientFirstName;
				}
				if(filterStatus != 0){
					gridBaseQString = gridBaseQString + '&filterStatus=' + filterStatus;
				}
				if(currentTab == "referral"){
					mcg_change(gridBaseQString);
				}
				if(currentTab == "mrc"){
					var filterfeesDue = $.trim($(this).find("[name='filterfeesDue']").val());
					if(filterfeesDue != ""){
						gridBaseQString = gridBaseQString + '&filterfeesDue=' + filterfeesDue;
					}
					mcg2_change(gridBaseQString);
				}
				if(currentTab == "mrch"){					
					var filterAmountDue = $.trim($(this).find("[name='filterAmountDue']").val());
					if(filterAmountDue != ""){
						gridBaseQString = gridBaseQString + '&filterAmountDue=' + filterAmountDue;
					}					
					mcg3_change(gridBaseQString);
				}
				if(currentTab == "rfp"){					
					mcg4_change(gridBaseQString);
				}
					
			});

			$('.filterClear').on('click',function(){
				$(this).parents('form')[0].reset();
				$(this).parents('form').trigger('submit');
			});

			$(".refFilterForm input").keypress(function(event) {
				if (event.which == 13) {
					event.preventDefault();
					$(this).parents('form').find('button').trigger('click');
				}
			});


			arrCheckedClientReferrals = [];
			totaldue = 0;
			$(document).on('change','.checkRefcheckRef',function(){				
				vCrid = $(this).data('crid');
				if($(this).is(':checked')){
					$('input[data-crid="'+vCrid+'"]').each(function(){
						$(this).attr('checked','checked');
					});
				}else{
					$('input[data-crid="'+vCrid+'"]').each(function(){
						$(this).removeAttr('checked');
					});
				}
				setRefTotal();
			});
			$(document).on('click','##paymentBtn',function(){				
				strCheckedReferral = arrCheckedClientReferrals.toString();

				if ($('##billingState').length) {
					let arrReq = [];
					let stateIDForTax = $('##billingState').val();
					let zipForTax = $('##billingZip').val();

					if (stateIDForTax == '') arrReq[arrReq.length] = 'Billing State/Province is required.';
					if (zipForTax == '') arrReq[arrReq.length] = 'Billing Postal Code is required.';
					
					if (stateIDForTax > 0 && zipForTax.length && !mc_isValidBillingZip(zipForTax,stateIDForTax,''))
						arrReq[arrReq.length] = 'Invalid Billing Postal Code.';

					if (arrReq.length) {
						alert(arrReq.join('\n'));
						return false;
					}

					$('##stateIDForTax').val(stateIDForTax);
					$('##zipForTax').val(zipForTax);
				}

				$('.step1').hide();
				$('.paymentWrap').html('Please Wait...');	
				$('##checkedReferral').val(strCheckedReferral);
				$('##frmInitPay').submit();
			});
		});
		function checkAllReferrals(chk){
			if(chk == 1){
				mcg4_g.forEachRow(function(id){	
					var cell = mcg4_g.cells(id,0);
					cell.setChecked(1)
				});
			}else{
				mcg4_g.forEachRow(function(id){	
					var cell = mcg4_g.cells(id,0);
					cell.setChecked(0)
				});
				$('span##currentTotal').html('$00.00');
			}
			setRefTotal();
		}
		function setRefTotal(){
			arrCheckedClientReferrals = [];
			totaldue = parseFloat('00.00');
			var checkedNumRows = 0;
			mcg4_g.forEachRow(function(id){				
				var cell = mcg4_g.cells(id,0);
				if (cell.isCheckbox() && cell.isChecked()) { 
					arrIDList = id.split('_');
					totaldue += parseFloat(arrIDList[2]);
					clientRefId = arrIDList[1];
					arrCheckedClientReferrals.push(clientRefId);
					checkedNumRows = checkedNumRows + 1;
				}
			});
			$('span##currentTotal').html('$'+parseFloat(totaldue).toFixed(2));
			
			if(checkedNumRows != mcg4_g.getRowsNum()){
				$('##masterCheckBox').val(0);
				$('##masterCheckBox').removeAttr('checked');
			}else{
				$('##masterCheckBox').val(1);
				$('##masterCheckBox').attr('checked','checked');
			}
		}
		function removeParam(key, sourceURL) {
			var rtn = sourceURL.split("?")[0],
				param,
				params_arr = [],
				queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";
			if (queryString !== "") {
				params_arr = queryString.split("&");
				for (var i = params_arr.length - 1; i >= 0; i -= 1) {
					param = params_arr[i].split("=")[0];
					if (param === key) {
						params_arr.splice(i, 1);
					}
				}
				rtn = rtn + "?" + params_arr.join("&");
			}
			return rtn;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(variables.gridJS)#" />

<cfquery name="local.qryGetReferralHistoryStatusSet3" dbtype="query">
	select clientReferralStatusID, statusName, isRetainedCase, isOpen from [variables.structData.qryGetReferralHistoryStatusSet1]
	union
	select clientReferralStatusID, statusName, isRetainedCase, isOpen from [variables.structData.qryGetReferralHistoryStatusSet2]						
</cfquery>
<cfquery name="local.qryGetReferralHistoryStatus" dbtype="query">
	select clientReferralStatusID, statusName, isRetainedCase, isOpen from [local.qryGetReferralHistoryStatusSet3]
	order by isRetainedCase, isOpen desc				
</cfquery>

<cfif variables.structData.qryReferralSettings.dspMonthReportLink >
<cfoutput>
	<div id="addArea" class="fr saveButton">
		Monthly Report: <a href="<cfoutput>#variables.structData.mainurl#&ra=downloadMonthlyReport</cfoutput>">Download</a> | <a href="javascript:printReport();" >Print</a>
		<iframe id="contenttoprint" style="height: 0px; width: 0px; position: absolute;" frameborder="0"></iframe>
	</div>
</cfoutput>
</cfif>

<div id="boxes">
	<h3 class="tsAppHeading">My Referrals</h3>
	<cfoutput>
	<form class="form-inline refFilterForm" action="/" method="get">
		<input type="hidden" name="tab" value="#variables.structData.tabToShow#">
		<label class="refDateRange"><br/>
			<select name="refDateRange" id="refDateRange" class="refDateRange refDateRangeEle">
				<option value="0">View All My Referrals</option>
				<option value="30">Last 30 Days</option>
				<option value="60">Last 60 Days</option>
				<option value="90">Last 90 Days</option>
			</select>
		</label>
		<label class="refferalID"><b>Referral ID</b><br/>
			<input type="text" name="filterReferralID" class="input-small refferalID">
		</label>
		<label class="clientLastName"><b>Client Last Name</b><br/>
			<input type="text" name="filterClientLastName" class="input-medium clientLastName" value="">
		</label>
		<label class="clientFirstName"><b>Client First Name</b><br/>
			<input type="text" name="filterClientFirstName" class="input-medium clientFirstName" value="">
		</label>
		<label class="Status"><b>Status</b><br/>
			<select class="Status input-medium" name="filterStatus">
				<option value="0"></option>
				<cfloop query="variables.structData.qryGetReferralStatus">
					<option value="#variables.structData.qryGetReferralStatus.clientReferralStatusID#">#variables.structData.qryGetReferralStatus.statusName#</option>
				</cfloop>
			</select>
		</label>	
		<button type="submit" class="btn filterRefferals">Search</button>
		<button type="reset" class="btn filterClear">Clear Filters</button>	
	</form>	
	</cfoutput>
	<div id="mcg_rnum" class="tsAppBodyText gridRNumMember"><b>loading...</b></div>
	<div id="mcg_gridbox" class="gridBox"></div>
	
	<h3 class="tsAppHeading">My Retained Cases</h3>
	<cfoutput>
	<form class="form-inline refFilterForm" action="/" method="get">
		<input type="hidden" name="tab" value="mrc">
		<label class="refDateRange"><br/>
			<select name="refDateRange" id="refDateRange" class="refDateRange refDateRangeEle">
				<option value="0">View All My Cases</option>
				<option value="30">Last 30 Days</option>
				<option value="60">Last 60 Days</option>
				<option value="90">Last 90 Days</option>
			</select>
		</label>
		<label class="refferalID"><b>Referral ID</b><br/>
			<input type="text" name="filterReferralID" class="input-small refferalID">
		</label>
		<label class="clientLastName"><b>Client Last Name</b><br/>
			<input type="text" name="filterClientLastName" class="input-medium clientLastName">
		</label>
		<label class="clientFirstName"><b>Client First Name</b><br/>
			<input type="text" name="filterClientFirstName" class="input-medium clientFirstName">
		</label>
		<label class="Status"><b>Status</b><br/>
			<select class="Status input-medium" name="filterStatus">
				<option value="0"></option>
				<cfloop query="variables.structData.qryGetReferralCaseStatus">
					<option value="#variables.structData.qryGetReferralCaseStatus.clientReferralStatusID#">#variables.structData.qryGetReferralCaseStatus.statusName#</option>
				</cfloop>
			</select>
		</label>				
		<label class="feesDue"><b>Fees Due</b><br/>
			<input type="text" name="filterfeesDue" class="input-small feesDue">
		</label>								
		<button type="submit" class="btn filterRefferals">Search</button>
		<button type="reset" class="btn filterClear">Clear Filters</button>	
	</form>
	</cfoutput>
	<div id="mcg2_rnum" class="tsAppBodyText gridRNumMember"><b>loading...</b></div>
	<div id="mcg2_gridbox" class="gridBox"></div>
	
	<h3 class="tsAppHeading">My Referral/Case History</h3>
	<cfoutput>
	<form class="form-inline refFilterForm" action="/" method="get">
		<input type="hidden" name="tab" value="mrch">
		<label class="refDateRange"><br/>
			<select name="refDateRange" id="refDateRange" class="refDateRange refDateRangeEle">
				<option value="0">View All My History</option>
				<option value="30">Last 30 Days</option>
				<option value="60">Last 60 Days</option>
				<option value="90">Last 90 Days</option>
			</select>
		</label>
		<label class="refferalID"><b>Referral ID</b><br/>
			<input type="text" name="filterReferralID" class="input-small refferalID">
		</label>
		<label class="clientLastName"><b>Client Last Name</b><br/>
			<input type="text" name="filterClientLastName" class="input-medium clientLastName">
		</label>
		<label class="clientFirstName"><b>Client First Name</b><br/>
			<input type="text" name="filterClientFirstName" class="input-medium clientFirstName">
		</label>
		<label class="Status"><b>Status</b><br/>			
			<select class="Status input-medium" name="filterStatus">
				<option value="0"></option>
				<cfloop query="local.qryGetReferralHistoryStatus">
					<option value="#local.qryGetReferralHistoryStatus.clientReferralStatusID#">#local.qryGetReferralHistoryStatus.statusName#</option>
				</cfloop>
			</select>
		</label>
		<label class="amountDue"><b>Amount Due</b><br/>
			<select class="amountDue input-small" name="filterAmountDue">
				<option value=""></option>
				<option value="Yes">Yes</option>
				<option value="No">No</option>
			</select>
		</label>								
		<button type="submit" class="btn filterRefferals">Search</button>
		<button type="reset" class="btn filterClear">Clear Filters</button>	
	</form>
	</cfoutput>
	<div id="mcg3_rnum" class="tsAppBodyText gridRNumMember"><b>loading...</b></div>
	<div id="mcg3_gridbox" class="gridBox"></div>
	
	<h3 class="tsAppHeading">My Outstanding Fees</h3>
	<cfoutput>
	<form class="form-inline refFilterForm" action="/" method="get">
		<input type="hidden" name="tab" value="mrch">
		<label class="refDateRange"><br/>
			<select name="refDateRange" id="refDateRange" class="refDateRange refDateRangeEle">
				<option value="0">My Outstanding Fees</option>
				<option value="30">Last 30 Days</option>
				<option value="60">Last 60 Days</option>
				<option value="90">Last 90 Days</option>
			</select>
		</label>
		<label class="refferalID"><b>Referral ID</b><br/>
			<input type="text" name="filterReferralID" class="input-small refferalID">
		</label>
		<label class="clientLastName"><b>Client Last Name</b><br/>
			<input type="text" name="filterClientLastName" class="input-medium clientLastName">
		</label>
		<label class="clientFirstName"><b>Client First Name</b><br/>
			<input type="text" name="filterClientFirstName" class="input-medium clientFirstName">
		</label>							
		<button type="submit" class="btn filterRefferals">Search</button>
		<button type="reset" class="btn filterClear">Clear Filters</button>	
	</form>
	</cfoutput>
	<div id="mcg4_rnum" class="tsAppBodyText gridRNumMember"><b>loading...</b></div>
	<div id="mcg4_gridbox" class="gridBox"></div>
	<div class="step1 makePaymentSection">
		<!--- prompt for missing tax information --->
		<cfif variables.structData.stateIDforTax EQ 0 OR NOT len(variables.structData.zipForTax)>
			<cfoutput>
			<fieldset id="billingInfoContainer">
				<legend>Billing Information</legend>
				<div style="display:flex;">
					<div>
						<div>State/Province *</div>
						<cfset local.qryStates = application.objCommon.getStates()>
						<select id="billingState" name="billingState">
							<option value=""></option>
							<cfset local.currentCountryID = 0>
							<cfloop query="local.qryStates">
								<cfif local.qryStates.countryID neq local.currentCountryID>
									<cfset local.currentCountryID = local.qryStates.countryID>
									<optgroup label="#local.qryStates.country#">
								</cfif>
								<option value="#local.qryStates.stateID#" <cfif variables.structData.stateIDforTax is local.qryStates.stateID>selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
								<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
									</optgroup>
								</cfif>
							</cfloop>
						</select>
					</div>
					<div style="padding-left:20px;">
						<div>Postal Code *</div>
						<input type="text" id="billingZip" name="billingZip" maxlength="25" value="#variables.structData.zipForTax#">
					</div>
				</div>
			</fieldset>
			</cfoutput>
		</cfif>

		<table class="table" width="100%">	
			<tbody>
				<tr class="odd_modern">
					<td class="text-right" style="">
						<b>Current Total: <span id="currentTotal">00.00</span>
					</td>
					<td class="text-right" colspan="2">
						<button type="button" id="paymentBtn" class="btn">Make a payment</button>
					</td>
				</tr>
			</tbody>
		</table>
		<cfoutput>
			<form action="#variables.structData.mainurl#&ra=editReferrals&initPay=1" method="post" id="frmInitPay" name="frmInitPay">
				<input type="hidden" name="checkedReferral" id="checkedReferral" value=''>
				<input type="hidden" id="stateIDForTax" name="stateIDForTax" value="#variables.structData.stateIDforTax#">
				<input type="hidden" id="zipForTax" name="zipForTax" value="#variables.structData.zipForTax#">
			</form>
		</cfoutput>
	</div>
	<div class="paymentWrap">
								
	</div>		

	<cfif variables.structData.dspPanelList>
		<div id="panelListDiv">
			<fieldset class="panelBorder">
				<legend class="panelBorder"><h3 class="tsAppHeading">Panels</h3></legend>				
				
				<form role="form" name="memberPanelForm" method="post" onsubmit="return checkMaxPanel();">
					<cfoutput>
						<div class="row-fluid">
							<cfset variables.secondSpan = "">
							<cfif len(trim(variables.structData.rcPanelInstructionsTxt)) GT 0>
								<div class="text-left span10 tsAppBodyText"><cfoutput>#variables.structData.rcPanelInstructionsTxt#</cfoutput></div>
								<cfset variables.secondSpan = "span2">
							</cfif>
							<div class="text-right <cfoutput>#variables.secondSpan#</cfoutput>">
								<cfif variables.structData.allowPanelMgmt>
									<input type="submit" class="btn btn-default" name="savePanelList" id="savePanelList" value="Save Changes">
								</cfif>
							</div>
						</div>
						
						<cfloop query="variables.structData.qryGetPanels">
							<div class="checkbox">
								<label style="width:50%;"><input type="checkbox" class="memberPanelCheckBox" name="panelID_#variables.structData.qryGetPanels.panelID#" <cfif ListFind(valueList(variables.structData.qryMemberPanels.panelID),variables.structData.qryGetPanels.panelID) or ListFind(valueList(variables.structData.qryIncativeMemberPanels.panelID),variables.structData.qryGetPanels.panelID)>checked="checked"</cfif> <cfif not variables.structData.allowPanelMgmt or ListFind(valueList(variables.structData.qryIncativeMemberPanels.panelID),variables.structData.qryGetPanels.panelID)>disabled="disabled"</cfif> onclick="javascript:showHideSubPanels(#variables.structData.qryGetPanels.panelID#);"> #variables.structData.qryGetPanels.name# 
								<cfif ListFind(valueList(variables.structData.qryIncativeMemberPanels.panelID),variables.structData.qryGetPanels.panelID)>*</cfif></label>
							</div>
							<cfset variables.qryGetSubPanels = variables.objReferrals.getSubPanels(panelID=variables.structData.qryGetPanels.panelID) />
							<cfif variables.qryGetSubPanels.RecordCount>
								<div id="panelParent_#variables.structData.qryGetPanels.panelID#" <cfif ListFind(valueList(variables.structData.qryMemberPanels.panelID),variables.structData.qryGetPanels.panelID)>style="display:block;"<cfelse>style="display:none;"</cfif>>
									<cfset variables.qryGetMemberSubPanels = variables.objReferrals.getMemberSubPanels(panelID=variables.structData.qryGetPanels.panelID, memberid=variables.structData.memberLoggedInID) />
									<cfloop query="variables.qryGetSubPanels">
										<div style="padding-left:40px;" id="subPanel_#variables.qryGetSubPanels.panelID#"> 
											<label style="width:60%;"><input type="checkbox" name="subPanelID_#variables.qryGetSubPanels.panelID#_panelID_#variables.structData.qryGetPanels.panelID#" <cfif ListFind(valueList(variables.qryGetMemberSubPanels.panelID),variables.qryGetSubPanels.panelID)>checked="checked"</cfif> <cfif not variables.structData.allowPanelMgmt>disabled="disabled"</cfif>> #variables.qryGetSubPanels.name#
											<cfif ListFind(valueList(variables.structData.qryIncativeMemberPanels.panelID),variables.qryGetSubPanels.panelID)>*</cfif></label>
										</div>
									</cfloop>
								</div>
							</cfif>
						</cfloop>
						<div class="text-right" style="font-style: italic; font-size:10pt; margin-top:30px;">
							<p>(*) Denotes that you are either "inactive" or "pending approval" in that panel.</p>
						</div>						
					</cfoutput>
				</form>
			</fieldset>
		</div>
	</cfif>
	<cfif variables.structData.referralsSMS eq 1 AND variables.structData.feEnableTextMessagingMember eq 1>
		<cfoutput>
			<h3 class="tsAppHeading">Text Messaging</h3>
			#variables.structData.feReferralCenterInstruction#	
			<div class="smsCntWrap">
				<table cellspacing="0" cellpadding="2" border="0" width="100%">
					<tr class="gridHead">
						<td>
							<img class="addPhoneIcon" src="/assets/common/images/details_open.png" width="23" height="19" align="absmiddle" border="0">
							&nbsp; <a href="javascript:void(0);" class="addNumberBtn"> Add Number</a>
						</td>
					</tr>
					<tr class="gridForm">
						<td>
							<div id="editMFAPhNo" class="login-card login-p-3 MFAPhNoTool hidden divWrap editDivWrap">
								<div class="login-mb-1 phoneLabel">Enter your phone number</div>
								<div class="login-d-flex">
									<div class="login-col-auto">
										<input type="tel" id="MFAPhNo" name="MFAPhNo" value="" style="height:auto;">
									</div>
									<div class="login-col-auto" style="margin-top:5px !important;">
										<button type="button" id="btnVerifyMFAPhNo" class="tsAppBodyButton" onclick="addNumber();">Add</button>
										<button type="button" id="btnCancelMFAPhNo" class="tsAppBodyButton" onclick="cancelAdd();">Cancel</button>
									</div>
								</div>
								<div class="login-mt-2 login-text-center" style="width:210px;margin-top:5px !important;">
									<div id="MFAPhNoValidMsg" class="login-text-success" style="display:none;">Valid</div>
									<div id="MFAPhNoErrMsg" class="alert" style="display:none;"></div>
								</div>
								<br/>
							</div>
						</td>
					</tr>
					<cfloop query="variables.structData.queryMSPhonenumbers">
						<tr class="gridForm">
							<td>
								<div class="login-card login-p-3 MFAPhNoTool  divWrap">

		
									<div class="phoneNumberRow">
										<div class="phoneNumberCol">
											<cfif variables.structData.queryMSPhonenumbers.isVerified eq 1>
												<img class="verifyIcon" src="/assets/common/images/sheild-login-good.png" width="21" height="17" align="absmiddle" border="0">
											<cfelse>
												<img class="verifyIcon" src="/assets/common/images/sheild-login-danger-x-mark.png" width="21" height="17" align="absmiddle" border="0">
										  	</cfif>
											<span class="strPhoneVerificationLabel">Phone Verification</span>
										</div>
										<div class="phoneNumberCol">
											
											<span class="strPhoneNumberLabel">
												#variables.structData.queryMSPhonenumbers.phoneNumber#
											</span>
										</div>
										<div class="phoneNumberCol phoneNumberColAction">
											<button type="button" id="btnEditMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="tsAppBodyButton" onclick="initializeEditNumber(#variables.structData.queryMSPhonenumbers.smsNotificationID#);">Edit</button>
											&nbsp;<button type="button" id="btnDeleteMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="tsAppBodyButton" onclick="deleteNumber(#variables.structData.queryMSPhonenumbers.smsNotificationID#,'#variables.structData.queryMSPhonenumbers.phoneNumber#');">Delete</button>
										</div>
									  </div>

									
								
									<div id="editMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="hidden editDivWrap">
										<div class="login-mb-1 phoneLabel">Enter your phone number</div>
										<div class="login-d-flex">
											<div class="login-col-auto">
												<input type="tel" id="MFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" name="MFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" value="#variables.structData.queryMSPhonenumbers.phoneNumber#" style="height:auto;">
											</div>
											<div class="login-col-auto" style="margin-top:5px !important;">
												<button type="button" id="btnVerifyMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="tsAppBodyButton" onclick="updateNumber(#variables.structData.queryMSPhonenumbers.smsNotificationID#);">Save</button>
												<button type="button" id="btnCancelMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="tsAppBodyButton" onclick="cancelEditList(#variables.structData.queryMSPhonenumbers.smsNotificationID#);">Cancel</button>
											</div>
										</div>
										<div class="login-mt-2 login-text-center" style="width:210px;margin-top:5px !important;">
											<div id="MFAPhNoValidMsg" class="login-text-success" style="display:none;">Valid</div>
											<div id="MFAPhNoErrMsg#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="alert" style="display:none;"></div>
										</div>
										<br/>
									</div>
								</div>
							</td>
						</tr>

					</cfloop>
				</table>
			</div>
			
		</cfoutput>
		<cfinclude template="../commonMFASMSJS.cfm">
	</cfif>
</div>