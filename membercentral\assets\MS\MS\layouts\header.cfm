<cfoutput>
	<div id="header">
		<div class="left">&nbsp;
			<a href="/"><div id="logo"></div></a>
		</div>
		<div class="right">
			<div id="authArea">
				<div id="authContent">
					<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<a href="/?logout"><div class="auth">logout</div></a><br />
						Welcome #session.cfcUser.memberData.firstName#
					<cfelse>
						<a href="/?pg=login"><div class="auth">login</div></a>
					</cfif>
				</div>
			</div>
			<div id="iconArea" class="iconBox">
				<div id="iconContent">
					<ul>
		        <li><a href="/" onMouseOver="Tip('Home');" onmouseout="UnTip();"><img src="/images/icon_home.png"></a></li>
		      	<li><a href="/?pg=events&evAction=viewMonth" onMouseOver="Tip('Calendar');" onmouseout="UnTip();"><img src="/images/icon_calendar.png"></a></li>
		      	<li><a href="/?pg=search" onMouseOver="Tip('Search');" onmouseout="UnTip();"><img src="/images/icon_search.png"></a></li>
		      	<li><a href="/?pg=RenewMembership" onMouseOver="Tip('Join/Renew');" onmouseout="UnTip();"><img src="/images/icon_joinrenew.png"></a></li>
				<li><a href="/?pg=uploadDocuments" onMouseOver="Tip('Upload Documents');" onmouseout="UnTip();"><img src="/images/icon_upload.png"></a></li>
		      </ul>
				</div>
			</div>
			
		</div>
	</div>
	<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
</cfoutput>