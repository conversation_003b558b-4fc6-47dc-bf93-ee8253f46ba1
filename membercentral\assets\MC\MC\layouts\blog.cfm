<cfoutput>
	<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
		<cfinclude template="head.cfm" />
		<body>
			<cfinclude template="jssalesscript.cfm">
			<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct">
				<div class="bodyText" style="margin:6px;">#application.objCMS.renderZone(zone='Main',event=event)#</div>
			<cfelse>
				<cfinclude template="headerContent.cfm">
				
				<section class="banner blog-page">
					#application.objCMS.renderZone(zone='F',event=event)#		
				</section>
				<section class="blog-inner">
					<div class="container">
						<div class="top-heading">
							#application.objCMS.renderZone(zone='G',event=event)#		
						</div>
						<div class="blog-three-block">
							<ul class="clearfix">
								<li>
									#application.objCMS.renderZone(zone='H',event=event)#	
								</li>
								<li>
									#application.objCMS.renderZone(zone='I',event=event)#		
								</li>
								<li>
									#application.objCMS.renderZone(zone='J',event=event)#		
								</li>
							</ul>
						</div>
						<cfsavecontent variable="local.items">
								#application.objCMS.renderZone(zone='K',event=event, mode='div')#
						</cfsavecontent>
							<cfset local.items = replaceNoCase(local.items, '<div id="zoneK" class="zonewrapper">', "", "All")>
							<cfset local.items = replaceNoCase(local.items, "zoneresource", "item", "All")>
							<cfset local.items = trim(replaceNoCase(local.items, "item", "active item", "One"))>
							<cfif len(local.items) gt len("</div>")>
								<cfset local.items = left(local.items, len(local.items) - len("</div>"))>
							</cfif>
						<div class="blogtesti-slider">
							<div class="owl-carousel">
									#local.items#
							</div>	
						</div>
					</div>
				</section>
				<section class="other-member">
					<div class="container">
							#application.objCMS.renderZone(zone='L',event=event)#	
						<div class="other-member-imgs">
							#application.objCMS.renderZone(zone='M',event=event)#	
						</div>
					</div>
				</section>



				<cfinclude template="footerContent.cfm" />
			</cfif>
			<cfinclude template="toolBar.cfm" />
			<cfinclude template="adRoll.cfm" />
		</body>
	</html>
</cfoutput>
