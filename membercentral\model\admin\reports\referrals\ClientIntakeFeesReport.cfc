<cfcomponent extends="model.admin.reports.report" output="false">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();
			
		// call common report controller
		reportController(event=arguments.event);
			
		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript> 
	</cffunction>

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		<cfset var local = structNew() />
		
		<cfset local.showForm = Val(arguments.event.getValue('qryReportInfo').reportID) />
		
		<cfif local.showForm>
			
			<cfscript>
				local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
				/* Get referralID */
				local.referralID = local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).referralID;
				/* Populate "Counselors" drop-down */ 
				local.qryGetCounselors = local.objAdminReferrals.getInterviewers(referralID=local.referralID,orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				
				local.reportInfoXML = arguments.event.getValue('qryReportInfo').otherXML;
				local.callDateFrom = XMLSearch(local.reportInfoXML,"string(/report/extra/calldatefrom/text())");
				local.callDateTo = XMLSearch(local.reportInfoXML,"string(/report/extra/calldateto/text())");
				local.intMemberID = XMLSearch(local.reportInfoXML,"string(/report/extra/intmemberid/text())");
				
				/* just for the referral dates, we want to default from one month prior */
				if (local.callDateFrom EQ "") {
					local.callDateFrom = dateFormat(dateAdd("m", -1, now()), "m/d/yyyy");
				}
				if (local.callDateTo EQ "") {
					local.callDateTo = dateFormat(now(), "m/d/yyyy");
				}
			</cfscript>
			
		</cfif>

		<cfsavecontent variable="local.dataHead">
			<cfoutput>
				<script language="javascript">
					$(document).ready(function() {
						var reportView = $('##reportView');
						var fileTypeButtons = $('button##btnReportBarscreen, button##btnReportBarpdf');
						var loadingMessage = $('##divReportShowScreenLoading');
						var loadingScreen = $('##divReportShowScreen');						
						
						setupRptFilterDateRange('callDateFrom','callDateTo');
						mca_setupCalendarIcons('frmReport');
						mca_setupSelect2();
					});
				</script> 
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#" />
		<cfsavecontent variable="local.data">
			<cfoutput>
				<div id="reportDefs">
					#showCommonTop(event=arguments.event)# 
					<cfif local.showForm>
						<cfform name="frmReport" id="frmReport" method="post">
							<input type="hidden" name="reportAction" id="reportAction" value="" />
							#showStepReferralPanelCriteria(event=arguments.event, title="Optionally Define Panel Filter", desc="Optionally filter the panels appearing on this report using the defined criteria below.")#							
							<div class="mb-5 stepDIV">
								<h5>Define Extra Options</h5>
								<div class="row mt-2">
									<div class="col-sm-12">
										<div class="form-group row">
											<label for="callDateFrom" class="col-md-4 col-sm-12 col-form-label">Call Date between</label>
											<div class="col-md-8 col-sm-12">
												<div class="row">
													<div class="col-md col-sm-12 pr-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="callDateFrom"  id="callDateFrom" value="#local.callDateFrom#" mcrdtxt="Call Date Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="callDateFrom"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
													<div class="col-md col-sm-12 pl-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="callDateTo"  id="callDateTo" value="#local.callDateTo#" mcrdtxt="Call Date End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="callDateTo"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="form-group row">
											<label for="intMemberID" class="col-md-4 col-sm-12 col-form-label">Counselor</label>
											<div class="col-md-8 col-sm-12">
												<select name="intMemberID" id="intMemberID" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="All">
													<cfloop query="local.qryGetCounselors">
														<option value="#local.qryGetCounselors.intMemberID#" <cfif listFind(local.intMemberID, local.qryGetCounselors.intMemberID) gt 0>selected</cfif>>#local.qryGetCounselors.interviewerName#</option>
													</cfloop>
												</select>
											</div>
										</div>
									</div>
								</div>
							</div>

							#showStepRollingDates(event=arguments.event)#
							#showButtonBar(event=arguments.event)# 
						</cfform>
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.otherXML = XMLParse(arguments.event.getValue('qryReportInfo').otherXML);
			
			local.strFields = structNew();
			local.strFields.callDateFrom = { label="Call Date Start Date", value=arguments.event.getValue('callDateFrom','') };
			local.strFields.callDateTo = { label="Call Date End Date", value=arguments.event.getValue('callDateTo','') };
			local.strFields.intMemberID = { label="Counselor", value=arguments.event.getValue('intMemberID','') };
			local.strFields.rpList = { label="Panel List", value=XMLSearch(local.otherXML,'string(/report/extra/rplist/text())') };

			reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
			return returnAppStruct('','echo');
		</cfscript> 
	</cffunction>
	
	<cffunction name="generateData" access="private" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">
		
		<cfscript>
		var local = structNew();
		local.tempTableName = "rpt#getTickCount()#";
		local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
		local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode);

		local.referralID = local.objAdminReferrals.getReferralSettings(siteID=local.mc_siteInfo.siteID).referralID;	

		local.callDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldatefrom/text())");
		local.callDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldateto/text())");
		local.intMemberID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/intmemberid/text())");
		local.includeDetails = true;
		local.includeFeeData = true;
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
				
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#Reorg') IS NOT NULL
					DROP TABLE ###local.tempTableName#Reorg;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2;
				IF OBJECT_ID('tempdb..###local.tempTableName#_3') IS NOT NULL
					DROP TABLE ###local.tempTableName#_3;
				IF OBJECT_ID('tempdb..###local.tempTableName#_3Final') IS NOT NULL
					DROP TABLE ###local.tempTableName#_3Final;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2_3') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2_3;
				IF OBJECT_ID('tempdb..##clientReferralTbl') IS NOT NULL
					DROP TABLE ##clientReferralTbl;
				IF OBJECT_ID('tempdb..##tmpClients') IS NOT NULL
					DROP TABLE ##tmpClients;
				IF OBJECT_ID('tempdb..##clientReferralFees') IS NOT NULL
					DROP TABLE ##clientReferralFees;
				CREATE TABLE ##clientReferralTbl (clientReferralID int, callDate datetime, callUID varchar(50), enteredByMemberID int, primaryClientID int, clientReferralClientID int);
				CREATE TABLE ##tmpClients (clientID int PRIMARY KEY);
				CREATE TABLE ##clientReferralFees (clientID int, ReferralDues decimal(18,2), amtToBePaid decimal(18,2), collectedFee decimal(18,2));
				
				declare @t_Tax int = dbo.fn_tr_getTypeID('Sales Tax');
				declare @tr_SalesTaxTrans int = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');
				declare @orgID int = <cfqueryparam value="#local.mc_siteInfo.orgID#" cfsqltype="cf_sql_integer">;
				declare @referralID int = <cfqueryparam value="#local.referralID#" cfsqltype="cf_sql_integer">;

				declare @clientReferralTypeID int, @clientTypeID int;

				select @clientReferralTypeID = clientReferralTypeID 
				from dbo.ref_clientReferralTypes 
				where isReferral = 1 
				and isAgency = 0	
				and referralID = @referralID;

				SELECT @clientTypeID = clientTypeID 
				FROM dbo.ref_clientTypes 
				WHERE clientType = 'Client';

				insert into ##clientReferralTbl (clientReferralID, callDate, callUID, enteredByMemberID, clientReferralClientID)
				select cr.clientReferralID, cr.dateCreated, cr.callUID, cr.enteredByMemberID, c.clientID
				from dbo.ref_clients c
				inner join dbo.ref_clientReferrals cr on cr.referralID = @referralID
					and c.typeID = @clientTypeID
					and cr.clientID = c.clientID
					and cr.dateCreated >= <cfqueryparam value="#local.callDateFrom#" cfsqltype="cf_sql_timestamp" />					 
					and cr.dateCreated < <cfqueryparam value="#dateAdd('d', 1, local.callDateTo)#" cfsqltype="cf_sql_timestamp" />
					and cr.typeID = @clientReferralTypeID
					and cr.callUID is not null
				where c.referralID = @referralID
				<cfif Len(local.intMemberID)>	
					/* COUNSELOR */
					and cr.enteredByMemberID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.intMemberID#" list="yes" />)
				</cfif>		
				group by cr.clientReferralID, cr.dateCreated, cr.callUID, cr.enteredByMemberID, c.clientID;

				WITH refClientIDs as (
					select c.clientID, c.clientParentID, tmp.clientReferralID
					from dbo.ref_clients as c
					inner join ##clientReferralTbl as tmp on tmp.clientReferralClientID = c.clientID
					where c.referralID =  @referralID
					and c.typeID = @clientTypeID
						union all
					select c.clientID, c.clientParentID, rc.clientReferralID
					from refClientIDs as rc
					inner join dbo.ref_clients as c on c.referralID =  @referralID and c.clientID = rc.clientParentID
					where c.typeID = @clientTypeID
				)
				UPDATE tmp
				SET tmp.primaryClientID = rc.clientID
				FROM ##clientReferralTbl AS tmp
				INNER JOIN refClientIDs AS rc ON rc.clientReferralID = tmp.clientReferralID
				WHERE rc.clientParentID IS NULL;

				INSERT INTO ##tmpClients (clientID)
				SELECT DISTINCT primaryClientID
				FROM ##clientReferralTbl;

				WITH ordTrans as (
					select ts.transactionID as saleTID, ts.transactionID, ts.cache_amountAfterAdjustment, 
						ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
					from dbo.tr_applications as tra
					inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
					inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID					
					inner join ##tmpClients as c on c.clientID = tra.itemID
					where tra.orgID = @orgID
					and tra.itemType = 'ClientReferralFee'
					and tra.status = 'A'
						union all
					select rt.transactionID as saleTID, t.transactionID, ts.cache_amountAfterAdjustment, 
						ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
					from dbo.tr_transactions as t
					inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = @tr_SalesTaxTrans and tr.transactionID = t.transactionID
					inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = t.transactionID
					inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID 
					where t.ownedByOrgID = @orgID
					and t.typeID = @t_Tax
				)
				INSERT INTO ##clientReferralFees (clientID, ReferralDues, amtToBePaid, collectedFee)
				select clientID, sum(ReferralDues) as ReferralDues, sum(amtToBePaid) as amtToBePaid, sum(collectedFee) as collectedFee
				from (
					select c2.clientID, sum(ot.ReferralDues) as ReferralDues, sum(ot.amtToBePaid) as amtToBePaid, sum(ot.ReferralDues - ot.amtToBePaid) as collectedFee
					from dbo.tr_applications tra
					inner join dbo.tr_transactionSales ts on ts.orgID = @orgID 
						and ts.transactionID = tra.transactionID 
					inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID					
					inner join dbo.ref_clients c2 on c2.referralID = @referralID and c2.clientID = tra.itemID
					inner join (
						select saleTID, sum(cache_amountAfterAdjustment) as ReferralDues, sum(amtToBePaid) as amtToBePaid
						from ordTrans
						group by saleTID) ot on ot.saleTID = tra.transactionID 
					where tra.orgID = @orgID
					and ts.cache_amountAfterAdjustment > 0.00											
					group by c2.clientID
				) tmp
				group by clientID;

				SELECT DISTINCT
					panelID = case when p.panelParentID is not null then p.panelParentID else p.panelID end, 
					panel = case when p2.name is not null then p2.name else p.name end, 				
					case when p2.shortDesc is not null then p2.shortDesc else p.shortDesc end as 'Panel Code',
					1 as ttlrow,
					1 as subCount,
					c.lastname + ', ' + c.firstName as 'Client Name',
					m2.lastname + ', ' + m2.firstName as 'Counselor Name',
					convert(varchar(10), cr.callDate, 101) + ' '  + convert(varchar(8), cr.callDate, 14) as 'Call Date',
					cr.callUID as 'Call ID',
					cast(isNull(fees.ReferralDues,0.00) as decimal(18,2)) as 'Total Referral Fees',
					cast(isNull(fees.amtToBePaid,0.00) as decimal(18,2)) as 'Total Amount To Be Paid',
					cast(isNull(fees.collectedFee,0.00) as decimal(18,2)) as 'Total Fees Paid by Client'
				into ###local.tempTableName#
				from dbo.ref_clients c
				inner join ##clientReferralTbl cr on cr.clientReferralClientID = c.clientID
				left outer join dbo.ams_members m
					inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
						and m2.status <> 'D'	
						and m2.orgID = @orgID
						and m2.isProtected = 0
					on m.orgID = @orgID
					and m.memberid = cr.enteredByMemberID
				inner join searchMC.dbo.tblSearchReferralHistory h on h.clientID = cr.primaryClientID
				left outer join dbo.ref_panels p on p.referralID = @referralID and p.panelID = h.panelID1
				outer apply (select p2.name,p2.shortDesc from ref_panels p2 where p2.panelID = p.panelParentID) as p2
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>
					#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#
				</cfif>
				left outer join ##clientReferralFees as fees on fees.clientID = cr.primaryClientID
				where c.referralID = @referralID
				
				/* ONLY PRIMARY PANEL and ITS SUBPANELS BEING REPORTED IN THIS CASE */
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA) and findNoCase("panelid", arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>
					and (
						(isnull(h.panelID1,0) > 0 and p.panelParentID is null)
						or 
						(isnull(h.subPanelID1,'') <> '' and p.panelParentID is not null)
					)
				</cfif>;

				<cfset local.finalTempTableName = local.tempTableName>			
					
				<cfset local.args.fieldsInSelectClause = "panelID, Panel, [Panel Code]">
				<cfset local.args.fieldsInOrderClause = "Panel, [Call Date] desc">			
				<cfset local.fieldsToDisplay = "Panel, [Panel Code], [Client Name], [Counselor Name], [Call Date], [Call ID]">
				
				<cfloop list="#local.fieldsToDisplay#" index="local.thisItem">
					ALTER TABLE ###local.tempTableName# ALTER COLUMN #local.thisItem# varchar (255) NULL;
				</cfloop>			

				<cfset local.args.tempTableName = local.finalTempTableName />
				<cfset local.args.includeFeeData = local.includeFeeData />
				<cfset local.args.hasDetails = local.includeDetails />
				#reorgDataLayout(ArgumentCollection=local.args)#

				<cfif arguments.reportAction eq "customcsv">
					<cfset local.finalTempTableName3 = local.finalTempTableName & "_3" />
					<cfset local.rowOrderField = ListFirst(local.args.fieldsInOrderClause) />
					<cfset local.rowOrderField_SQLSafe = REReplace(local.rowOrderField,"[[:punct:]]", "", "ALL") />
					<cfset local.rowOrderField_SQLSafe = ReplaceNoCase(local.rowOrderField_SQLSafe, "desc", "", "ALL") />
					<cfset local.rowOrderField_NoSort = ReplaceNoCase(local.rowOrderField, "desc", "", "ALL") />
					<cfset local.otherFieldsToDisplay = ListDeleteAt(local.fieldsToDisplay, 1) />
					
					SELECT ROW_NUMBER() OVER (	order by 
												case 
													/* change it to zzzzzzzzzzz to put it last in sort order */
													when #local.rowOrderField_NoSort# = 'Report Total' then 'zzzzzzzzzzz' 
													else #local.rowOrderField_NoSort# end,											
												ttlrow) AS row, 
						*
					into ###local.finalTempTableName3#
					from ###local.finalTempTableName#Reorg;
					
					SELECT ROW_NUMBER() OVER(ORDER by row) as MCROWID,
						case 
							when (ttlrow = 99999998 AND #local.rowOrderField_NoSort# != 'Report Total') then null 
							when ttlrow = 99999999 then null 
							else #local.rowOrderField_NoSort# 
						end	AS [#local.rowOrderField_SQLSafe#],	
						<cfif Len(local.otherFieldsToDisplay)>
							#local.otherFieldsToDisplay#,
						</cfif>
						case 
							when ttlrow = 99999998 then subCount 
							else null 
						end AS Count, 
						[Total Referral Fees],
						[Total Amount To Be Paid],
						[Total Fees Paid by Client]						
					INTO ###local.finalTempTableName3#Final
					FROM ###local.finalTempTableName3#;
					
					DELETE from ###local.finalTempTableName3#Final where [Call ID] is null;
					#generateFinalBCPTable(tblName="###local.finalTempTableName3#Final", dropFields="MCROWID,Count")#
				<cfelse>
					select *
					from ###local.finalTempTableName#Reorg
					order by reorgRowID;
				</cfif>

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#Reorg') IS NOT NULL
					DROP TABLE ###local.tempTableName#Reorg;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2;
				IF OBJECT_ID('tempdb..###local.tempTableName#_3') IS NOT NULL
					DROP TABLE ###local.tempTableName#_3;
				IF OBJECT_ID('tempdb..###local.tempTableName#_3Final') IS NOT NULL
					DROP TABLE ###local.tempTableName#_3Final;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2_3') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2_3;
				IF OBJECT_ID('tempdb..##clientReferralTbl') IS NOT NULL
					DROP TABLE ##clientReferralTbl;
				IF OBJECT_ID('tempdb..##clientReferralFees') IS NOT NULL
					DROP TABLE ##clientReferralFees;
				IF OBJECT_ID('tempdb..##tmpClients') IS NOT NULL
					DROP TABLE ##tmpClients;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local>
	</cffunction>
	
	<cffunction name="reorgDataLayout" access="private" output="false" returntype="string">
		<cfargument name="tempTableName" required="true" type="string" />
		<cfargument name="fieldsInSelectClause" required="true" type="string" />
		<cfargument name="fieldsInOrderClause" required="true" type="string" />
		<cfargument name="reportTotalLabel" required="false" type="string" default="Report Total" />
		<cfargument name="includeFeeData" required="false" type="boolean" default="false" />
		<cfargument name="feesFields" required="false" type="string" default="[Total Referral Fees], [Total Amount To Be Paid], [Total Fees Paid by Client]" />
		<cfargument name="hasDetails" required="false" type="boolean" default="false" />
		<cfargument name="hasFieldsets" required="false" type="boolean" default="false" />
		
		<cfset var local = structNew() />

		<cfset local.feesFieldsWithSUM = "" />
		
		<cfloop list="#arguments.feesFields#" index="local.feeField">
			<cfset local.feesFieldsWithSUM = ListAppend(local.feesFieldsWithSUM, "SUM(#local.feeField#)") />
		</cfloop>
		
		<cfset local.first2Fields = ListGetAt(arguments.fieldsInSelectClause, 1) & "," & ListGetAt(arguments.fieldsInSelectClause, 2) />
		<cfset local.rollupField = ListFirst(arguments.fieldsInSelectClause) />
		
		<cfsavecontent variable="local.returnSQL">
			<cfoutput>
			/* reorganize data rows to be in a format that can easily be formatted for screen, PDF, or CVS
			 *	99999998 = a 'Report Total' or subtotal row
			 *	99999999 = an empty row, for display purposes only
			 */
				
			<!--- subcount row --->
			insert into ###arguments.tempTableName# (ttlrow, subCount, #arguments.fieldsInSelectClause#,  #arguments.feesFields#)
			select '99999998', sum(subCount), #arguments.fieldsInSelectClause#, #local.feesFieldsWithSUM#
			from ###arguments.tempTableName#
			group by #arguments.fieldsInSelectClause#
			
			ALTER TABLE ###arguments.tempTableName#  ALTER COLUMN subCount int NULL;
			
			<!--- blank row --->
			insert into ###arguments.tempTableName# (ttlrow, subCount, #arguments.fieldsInSelectClause#, #arguments.feesFields#)
			select '99999999', null, #arguments.fieldsInSelectClause#, null, null, null
			from ###arguments.tempTableName#
			group by #arguments.fieldsInSelectClause#
			
			<!--- GRAND TOTAL --->
			insert into ###arguments.tempTableName# (ttlrow, subCount, #local.first2Fields#
				<cfif arguments.includeFeeData>, #arguments.feesFields#</cfif>
				)
			select 99999998, sum(subCount), '99999998', '#arguments.reportTotalLabel#'
				<cfif arguments.includeFeeData>, #local.feesFieldsWithSUM#</cfif>
			from ###arguments.tempTableName#
			where ttlrow = 1

			<!--- return the new data table layout --->
			select *, ROW_NUMBER() OVER(ORDER BY case when #local.rollupField# = 99999998 then 0 else 1 end desc, 
				<cfif arguments.hasFieldsets>
					#arguments.fieldsInSelectClause#
					, case when ttlrow >= 99999998 then 0 else 1 end desc, #arguments.fieldsInOrderClause#
				<cfelse>
					#arguments.fieldsInOrderClause#
				</cfif>
				, subCount desc) as reorgRowID
			into ###arguments.tempTableName#Reorg
			from ###arguments.tempTableName#
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn Trim(local.returnSQL)>
	</cffunction>
	
	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfset local.callDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldatefrom/text())")>
			<cfset local.callDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldateto/text())")>

			<cfset local.Report = generateData(strSQLPrep=local.strSQLPrep, reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode)>
			<cfset local.qry = local.Report.qryData>
		
			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
					<div>Call Date Range: #local.callDateFrom# - #local.callDateTo#</div>
					<br/>
				</cfoutput>

				<cfif local.qry.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfset local.outputFieldsList = "ttlrow,Panel,Panel Code,Client Name,Counselor Name,Call Date,Call ID" />
					<cfset local.groupedID = "PanelID" />
					<cfset local.groupedColumn = "Panel" />
					<cfset local.feesFieldsList = "Total Referral Fees,Total Amount To Be Paid,Total Fees Paid by Client" />		
					
					<cfoutput>
					<table class="table table-sm table-borderless">
					<thead>
						<tr>
							<!--- column headers --->
							<cfloop from="1" to="#ListLen(local.outputFieldsList)#" index="local.fieldNumber">
								<cfset local.fieldName = ListGetAt(local.outputFieldsList, local.fieldNumber)>
								<cfif local.fieldName NEQ "ttlrow">
									<th class="text-left">#local.fieldName#</th>
								</cfif> 
							</cfloop>

							<th class="text-right">Count</th>
							
							<cfif structKeyExists(local, "feesFieldsList")>
								<cfloop list="#local.feesFieldsList#" index="local.fieldName">
									<th class="text-right">#local.fieldName#</th>
								</cfloop>
							</cfif>							
						</tr>
					</thead>
					</cfoutput>
					
					<!--- table of data --->
					<cfset local.resetGroupedFieldValue = "" />
					<cfoutput query="local.qry">
						
						<cfset local.groupedFieldValueIsReset = false />
						
						<cfif local.qry[local.groupedID][local.qry.CurrentRow] EQ "99999998">
							<cfset local.ttlRow = "font-weight-bold" />
							<tr style="line-height:10px;"><td colspan="20">&nbsp;</td></tr>
						<cfelse>
							<cfset local.ttlRow = "">
						</cfif>
						
						<tr valign="top">
							
							<cfloop from="1" to="#ListLen(local.outputFieldsList)#" index="local.fieldNumber">
								<cfset local.fieldName = ListGetAt(local.outputFieldsList, local.fieldNumber) />
								<cfset local.fieldValue = local.qry[local.fieldName][local.qry.CurrentRow] />
								
								<cfif local.fieldName EQ local.groupedColumn>
									<cfset local.groupedFieldValue = local.fieldValue />
									
									<cfif local.resetGroupedFieldValue NEQ local.groupedFieldValue>
										<cfset local.groupedFieldValueIsReset = true />
									</cfif>
									
									<cfset local.resetGroupedFieldValue = local.groupedFieldValue />
								</cfif>
								
								<cfif local.groupedFieldValueIsReset IS false AND local.fieldName EQ local.groupedColumn>
									
									<cfif ListFindNoCase("99999998", local.qry.ttlrow[local.qry.CurrentRow])>
										<cfset local.ttlRow = "font-weight-bold" />
									</cfif>
									<td class="#local.ttlRow#">&nbsp;</td>
								
								<cfelseif local.fieldName NEQ "ttlrow">
								
									<td class="#local.ttlRow#">
										
										<cfif isDate(local.fieldValue)>
											#DateFormat(local.fieldValue, "m/d/yyyy")#
										<cfelse>
											<cfif local.fieldName eq "Member">
												<cfif arguments.reportAction eq "screen" and memberid neq 99999998>
													<a href="#local.memberLink#&memberid=#local.qry.memberid#" target="_blank">#local.qry.member#</a><br/>
												<cfelse>
													<b>#local.qry.member#</b><br/>
												</cfif>
												<cfif len(local.qry.company)>#local.qry.company#<br/></cfif>
											<cfelse>
												#local.qry[local.fieldName][local.qry.CurrentRow]#
											</cfif>
										</cfif>
										&nbsp;
									</td>
								
								</cfif>
								 
							</cfloop>
							
							<td class="text-right #local.ttlRow#">
								<cfif local.qry.ttlrow[local.qry.CurrentRow] EQ "99999998"
										OR local.qry[local.groupedID][local.qry.CurrentRow] EQ "99999998">
									#local.qry.subCount#
								<cfelse>
									&nbsp;
								</cfif>
							</td>
							
							<cfif structKeyExists(local, "feesFieldsList")>
								<cfloop list="#local.feesFieldsList#" index="local.fieldName">
									<cfset local.fieldValue = local.qry[local.fieldName][local.qry.CurrentRow] />
									
									<td class="text-right #local.ttlRow#">
										<cfif Len(local.fieldValue)>#dollarformat(local.fieldValue)#<cfelse>&nbsp;</cfif>
									</td>
								</cfloop>
							</cfif>
							
						</tr>
					</cfoutput>	
					<cfoutput></table></cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.Report.qryData", strQryResult=local.Report.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>

		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfscript>
			local.strReport = generateData(strSQLPrep=local.strSQLPrep, reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode);
			local.arrInitialReportSort = arrayNew(1);
			local.strTemp = { field='Panel', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='Call Date', dir='desc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strReportQry = { qryReportFields=local.strReport.qryData, strQryResult=local.strReport.qryDataResult };
			local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML);
			</cfscript>
		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="pdfReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfreturn SUPER.pdfReport(reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode, pdfOrientation="landscape")>
	</cffunction>
</cfcomponent>