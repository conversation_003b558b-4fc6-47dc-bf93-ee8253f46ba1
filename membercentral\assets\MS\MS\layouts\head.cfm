<cfoutput>
	<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
	<title><cfoutput>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</cfoutput></title>
	<link rel="stylesheet" href="/css/main.css" />
	<link rel="stylesheet" href="/css/styles.css" />
	<link rel="stylesheet" href="/css/menu.css" />
	<!--- custom JavaScript Files --->
	<script type="text/javascript" src="/javascript/dw_event.js"></script>
	<script type="text/javascript" src="/javascript/dw_rotator.js"></script>
	<script type="text/javascript" src="/javascript/dw_rotator_aux.js"></script>
	<script type="text/javascript" src="/javascript/menu.js"></script>


	
	<script type="text/javascript"> 
	 
	    jQuery(document).ready(function() { 
	        jQuery('ul.sf-menu').superfish({ 
	            delay:       0,                            // one second delay on mouseout 
	            animation:   {opacity:'show'},  // fade-in and slide-down animation 
	            speed:       'fast',                          // faster animation speed 
	            autoArrows:  false,                           // disable generation of arrow mark-up 
	            dropShadows: false                            // disable drop shadows 
	        }); 
	    }); 
	 
	</script>

	<style type="text/css">
	  <cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		  ##noRightsLoggedOut{display:none;}
			##noRightsLoggedIn{display:block;}
  	<cfelse>
			##noRightsLoggedOut{display:block;}
			##noRightsLoggedIn{display:none;}
	  </cfif>
	</style>
</cfoutput>


