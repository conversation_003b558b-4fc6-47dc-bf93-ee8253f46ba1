// JavaScript Document
$(document).ready(function(e) {
	$('.testimonials .owl-carousel, .blogtesti-slider .owl-carousel').owlCarousel({
		loop:true,
		margin:10,
		responsiveClass:true,
		autoplay:true,
		autoplayTimeout:20000,
		autoplayHoverPause:true,
		responsive:{
			0:{
				items:1,
				nav:false
			},
			1000:{
				items:1,
				nav:false,
				loop:false
			}
		}
	});
	
	$('.menu-icn-inner').click(function(e) {
		$('body').addClass('openMenu');
	});
	$('.close-icn-inr, .overlay-bg').click(function(e) {
		$('body').removeClass('openMenu');
	});
	
});

$(window).scroll(function() {    
	var scroll = $(window).scrollTop();
	if (scroll >= 1) {
		$("body").addClass("fixHeader");
	} else {
		$("body").removeClass("fixHeader");
	}
});