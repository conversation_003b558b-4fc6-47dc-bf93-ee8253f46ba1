<cfoutput>
	<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
		<cfinclude template="head.cfm" />
		<body>
			<cfinclude template="jssalesscript.cfm">
			<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct">
				<div class="bodyText" style="margin:6px;">#application.objCMS.renderZone(zone='Main',event=event)#</div>
			<cfelse>
				<cfinclude template="headerContent.cfm">
				
				<section class="banner">
					#application.objCMS.renderZone(zone='D',event=event)#
				</section>
				<section class="top-three-part">
					<div class="container">
						<h1>MemberCentral provides a better member experience, saves time and reduces your costs.</h1>
						<div class="row">
							<div class="span4 single-block">
								#application.objCMS.renderZone(zone='E',event=event)#
							</div>
							<div class="span4 single-block">
								#application.objCMS.renderZone(zone='F',event=event)#
							</div>
							<div class="span4 single-block">
								#application.objCMS.renderZone(zone='G',event=event)#
							</div>
						</div>
					</div>
				</section>
				<section class="meet-staff">
					<div class="container">
						<div class="row">
							<div class="span6">#application.objCMS.renderZone(zone='H',event=event)#</div>
							<div class="span6 meet-rght-side head-matter">
								#application.objCMS.renderZone(zone='I',event=event)#
							</div>
						</div>
					</div>
				</section> 
				<section class="simply-billing">
					<div class="container">
						<div class="row">
							<div class="span6 right-img">#application.objCMS.renderZone(zone='K',event=event)#</div>
							<div class="span6 blng-left-side head-matter">
								#application.objCMS.renderZone(zone='J',event=event)#
							</div>
						</div>
					</div>
				</section> 
				<section class="engage-member">
					<div class="container">
						<div class="row">
							<div class="span12">#application.objCMS.renderZone(zone='M',event=event)#</div>
							<div class="span6"></div>
							<div class="span6 meet-rght-side head-matter">
								#application.objCMS.renderZone(zone='L',event=event)#
							</div>
						</div>
					</div>
				</section> 

				
				
				<cfinclude template="innerFooterContent.cfm" />
				<!--- MAIN BODY AREA --->
				<!--- <div id="white_content" style="height:500px;">
					<div class="pageSize white_content_area" style="margin-top:15px;">
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="vertical-align:top;"><img src="/images/homepageMainLeft.png" /></td>
								<td id="homepageMainCenter">
									<table width="100%" cellpadding="0" cellspacing="0" border="0">
										<tr>
											<td colspan="5"><img src="/images/visionBanner.png" style="vertical-align:top; padding-top:27px;" /></td>
										</tr>
										<tr><td colspan="5"><img src="/images/spacer.gif" style="height:20px;" /></td></tr>
										<tr>
											<td class="leftCol">#application.objCMS.renderZone(zone='Main',event=event)#</td>
											<td style="vertical-align:top; text-align:center;"><img src="/images/blueDots.png" /></td>
											<td class="midCol">#application.objCMS.renderZone(zone='A',event=event)#</td>
											<td style="vertical-align:top; text-align:center;"><img src="/images/blueDots.png" /></td>
											<td class="rightCol">#application.objCMS.renderZone(zone='B',event=event)#</td>
										</tr>
									</table>
								</td>
								<td style="vertical-align:top; margin-right:0px;"><img src="/images/homepageMainRight.png" /></td>
							</tr>
						</table>
					</div>
					<div style="clear: both">&nbsp;</div>
				</div> --->
				<!--- END MAIN BODY AREA --->
				<cfinclude template="footerContent.cfm" />
			</cfif>
			<cfinclude template="toolBar.cfm" />
			<cfinclude template="adRoll.cfm" />
		</body>
	</html>
</cfoutput>
