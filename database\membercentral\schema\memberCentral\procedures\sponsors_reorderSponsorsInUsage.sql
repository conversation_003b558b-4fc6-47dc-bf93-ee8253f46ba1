ALTER PROC dbo.sponsors_reorderSponsorsInUsage
@referenceType varchar(20),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (neworder INT NOT NULL, sponsorUsageID INT NOT NULL, sponsorOrder INT NOT NULL);
	
	INSERT INTO @tmp (sponsorUsageID, sponsorOrder, newOrder)
	SELECT sponsorUsageID, sponsorOrder, ROW_NUMBER() OVER(ORDER BY sponsorOrder) AS newOrder
	FROM dbo.sponsorsUsage
	WHERE referenceType = @referenceType
	AND referenceID = @referenceID;
	
	UPDATE su
	SET su.sponsorOrder = t.neworder
	FROM dbo.sponsorsUsage su
	INNER JOIN @tmp t ON su.sponsorUsageID = t.sponsorUsageID;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
