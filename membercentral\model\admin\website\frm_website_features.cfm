<cfsavecontent variable="local.featuresFormJS">
	<cfoutput>		
	<script type="text/javascript">
		function areAllFeaturesDisabled() {
			var featuresList = document.frmFeatures.toolType;
			var allChecked = true;

			if (typeof featuresList != "undefined") {
				if (featuresList.length != undefined) {
					for (var i = 0; i < featuresList.length; i++) {
						if (!featuresList[i].disabled) {
							allChecked = false;
							break;
						}
					}
				}						
			}					
			return allChecked;
		}
		function disableContributionAdmin() {
			$('##feature_contrib').prop('checked',true);

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable Contributions',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable Contributions?<br/>Any contributions in current, pledged or delinquent status will be marked as cancelled if you disable this feature.</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				disableSiteFeature('ContributionAdmin','Contributions');
			});
		}
		function disableMemberDocs() {
			var getMemDocsCountResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					$('##MCModalBody').html('<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable Member Documents?<br/>'+(r.docscount ? ('There are '+r.docscount+' member documents in the system that will be immediately deleted if you disable this feature.</span></div>') : ''));
					
					$('##btnMCModalSave').on('click', function(){
						disableSiteFeature('MemberDocs','Member Documents');
					});
				} else {
					alert('We were unable to get the active Member Documents count.');
				}
			};

			$('##feature_docs').prop('checked',true);

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable Member Documents Feature',
				strmodalbody: {
					content: mca_getLoadingHTML(),
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});

			TS_AJX('SITE','getMemberDocumentsCount',{},getMemDocsCountResult,getMemDocsCountResult,300000,getMemDocsCountResult);
		}
		function disableMemberHistoryAdmin() {
			$('##feature_hist').prop('checked',true);

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable Member History',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable Member History? Any existing history entries will be immediately deleted if you disable this feature.</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				disableSiteFeature('MemberHistoryAdmin','Member History');
			});
		}
		function disableRelationshipAdmin() {
			$('##feature_rel').prop('checked',true);

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable Relationships',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable Relationships? Any existing relationships will be immediately deleted if you disable this feature.</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				disableSiteFeature('RelationshipAdmin','Relationships');
			});
		}
		function disableSubscriptionAdmin() {
			$('##feature_sub').prop('checked',true);

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable Subscriptions',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable Subscriptions? Any existing subscriptions will be immediately deleted if you disable this feature.</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				disableSiteFeature('SubscriptionAdmin','Subscriptions');
			});
		}
		function disableTaskAdmin() {
			$('##feature_task').prop('checked',true);

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable Tasks',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable Tasks? Any existing tasks will be immediately deleted if you disable this feature.</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				disableSiteFeature('TaskAdmin','Tasks');
			});
		}
		function disableRecurringEvents() {
			$('##feature_recurringEvents').prop('checked',true);

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable Recurring Events',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable Recurring Events? Any existing recurring events will be immediately deleted if you disable this feature.</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				disableSiteFeature('RecurringEvents','Recurring Events');
			});
		}
		function disableAPIAccess() {
			$('##feature_apiaccess').prop('checked',true);

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable API Access',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable API Access?</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				disableSiteFeature('APIAccess','API Access');
			});
		}
		function disablebadges() {
			$('##feature_badges').prop('checked',true);

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable Badge Printers',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable Badge Printers?</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				disableSiteFeature('BadgeDeviceAdmin','Badge Printers');
			});
		}
		
		function disableSiteFeature(sf, sfdetail) {
			var disableSiteFeatureResult = function(r) {
				MCModalUtils.hideModal();
				if (r.success && r.success.toLowerCase() == 'true'){
					self.location.href = '#this.link.edit#&tab=features&refreshAdminNavData=1';
				} else {
					alert('We were unable to disable '+sfdetail+'.');
				}
			};
			$('##btnMCModalSave').prop('disabled',true).html('Disabling...');
			var objParams = { toolType:sf, siteResourceId:siteResourceId };
			TS_AJX('SITE','disableSiteFeature',objParams,disableSiteFeatureResult,disableSiteFeatureResult,300000,disableSiteFeatureResult);
		}
		function disableReferralsSMS() {
			$('##featue_referrals_SMS').prop('checked',true);
			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Disable Referrals SMS',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want disable Referrals SMS?</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Disable',
				}
			});
			$('##btnMCModalSave').on('click', function(){
				disableSiteFeature('referralsSMS','Referrals SMS');
			});
		}

		$(function() {
			var featuresList = document.frmFeatures.toolType;

			if (areAllFeaturesDisabled()) 
				$("##addFeatureBtn").attr('disabled','disabled');
			else {
				$('##addFeatureBtn').click(function(){
					mca_hideAlert('err_features');
					var checkedFeature = false;
					var featuresList = document.frmFeatures.toolType;
					var assocOrgList = $('##associateOrgLists').is(':checked');
			
					if (typeof featuresList != "undefined") {
						if (featuresList.length != undefined) {
							for (var i = 0; i < featuresList.length; i++) {
								if (featuresList[i].checked && !featuresList[i].disabled) {
									checkedFeature = true;
									break;
								}
							}
						}						
					}

					if (!checkedFeature && !assocOrgList) {
						mca_showAlert('err_features', 'Select a feature.');
					} else {
						$('##frmFeatures').submit();
					}	
				});
			}
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.featuresFormJS)#">

<cfoutput>
<cfif arguments.event.valueExists("addedFunc")>
	<div id="saveArea">
		<span id="saveMsg">The Feature(s) selected have successfully been added.</span>
	</div>
</cfif>
<div class="row">
	<div class="col align-items-start">
		<div id="err_features" class="alert alert-danger mb-2 d-none"></div>
	</div>
	<div class="col-auto text-right">
		<button type="button" name="addFeatureBtn" id="addFeatureBtn" class="btn btn-sm btn-primary">Save Features</button>
	</div>
</div>
<form name="frmFeatures" id="frmFeatures" action="#this.link.saveFeature#" method="POST">
	<div class="font-weight-bold mb-3">The following features are currently available:</div>

	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"APIAccess")>
			<input type="checkbox" name="toolType_feature_apiaccess" id="feature_apiaccess" value="APIAccess" class="custom-control-input" onclick="disableAPIAccess();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="feature_apiaccess" value="APIAccess" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="feature_apiaccess">API Access</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"BadgeDeviceAdmin")>
			<input type="checkbox" name="toolType_feature_badges" id="feature_badges" value="BadgeDeviceAdmin" class="custom-control-input" onclick="disablebadges();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="feature_badges" value="BadgeDeviceAdmin" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="feature_badges">Badge Printers</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"ContributionAdmin")>
			<input type="checkbox" name="toolType_feature_contrib" id="feature_contrib" value="ContributionAdmin" class="custom-control-input" onclick="disableContributionAdmin();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="feature_contrib" value="ContributionAdmin" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="feature_contrib">Contributions</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<input type="checkbox" name="toolType" id="feature_email" class="custom-control-input" value="EmailBlast" <cfif listFind(local.featuresList,"EmailBlast")>checked="checked" disabled="true"</cfif>>
		<label class="custom-control-label text-body" for="feature_email">Email Blast</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"MemberDocs")>
			<input type="checkbox" name="toolType_feature_docs" id="feature_docs" value="MemberDocs" class="custom-control-input" onclick="disableMemberDocs();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="feature_docs" value="MemberDocs" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="feature_docs">Member Documents</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"MemberHistoryAdmin")>
			<input type="checkbox" name="toolType_feature_hist" id="feature_hist" value="MemberHistoryAdmin" class="custom-control-input" onclick="disableMemberHistoryAdmin();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="feature_hist" value="MemberHistoryAdmin" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="feature_hist">Member History</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"referralsSMS")>
			<input type="checkbox" name="toolType_email_optouts_required" id="featue_referrals_SMS" value="referralsSMS" class="custom-control-input" onclick="disableReferralsSMS();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="featue_referrals_SMS" value="referralsSMS" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="featue_referrals_SMS">Referrals SMS</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"RelationshipAdmin")>
			<input type="checkbox" name="toolType_feature_rel" id="feature_rel" value="RelationshipAdmin" class="custom-control-input" onclick="disableRelationshipAdmin();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="feature_rel" value="RelationshipAdmin" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="feature_rel">Relationships</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<input type="checkbox" name="toolType" id="feature_rep" class="custom-control-input" value="Reports" <cfif listFind(local.featuresList,"Reports")>checked="checked" disabled="true"</cfif>>
		<label class="custom-control-label text-body" for="feature_rep">Reports</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"SubscriptionAdmin")>
			<input type="checkbox" name="toolType_feature_sub" id="feature_sub" value="SubscriptionAdmin" class="custom-control-input" onclick="disableSubscriptionAdmin();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="feature_sub" value="SubscriptionAdmin" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="feature_sub">Subscriptions</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"TaskAdmin")>
			<input type="checkbox" name="toolType_feature_task" id="feature_task" value="TaskAdmin" class="custom-control-input" onclick="disableTaskAdmin();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="feature_task" value="TaskAdmin" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="feature_task">Tasks</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<input type="checkbox" name="toolType" id="feature_ads" class="custom-control-input" value="AdsAdmin" <cfif listFind(local.featuresList,"AdsAdmin")>checked="checked" disabled="true"</cfif>>
		<label class="custom-control-label text-body" for="feature_ads">Sponsor Ad Management</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<input type="checkbox" name="toolType" id="feature_sitePass" class="custom-control-input" value="EnableSitePasswords" <cfif listFind(local.featuresList,"EnableSitePasswords")>checked="checked" disabled="true"</cfif>>
		<label class="custom-control-label text-body" for="feature_sitePass">Site Specific Passwords</label>
	</div>
	<div class="custom-control custom-switch my-2">
		<cfif listFind(local.featuresList,"RecurringEvents")>
			<input type="checkbox" name="toolType_feature_recurringEvents" id="feature_recurringEvents" value="RecurringEvents" class="custom-control-input" onclick="disableRecurringEvents();" checked>
		<cfelse>
			<input type="checkbox" name="toolType" id="feature_recurringEvents" value="RecurringEvents" class="custom-control-input">
		</cfif>
		<label class="custom-control-label text-body" for="feature_recurringEvents">Recurring Events</label>
	</div>

	<div class="mt-4 font-weight-bold">Lists:</div>
	<div class="custom-control custom-switch my-2">
		<input type="checkbox" name="associateOrgLists" id="associateOrgLists" class="custom-control-input" value="1">
		<label class="custom-control-label text-body" for="associateOrgLists">Associate Organization's Lists</label>
	</div>
</form>
</cfoutput>