<cfsavecontent variable="local.pageHeadJS">
	<cfoutput>
		<script type="text/javascript">	
		
			
			var arrNumbersEdited = [];
			function editMFAPhNo() {
				$('.MFAPhNoTool').hide();
				$('##editMFAPhNo').show();
			}
			function verifyMFAPhNo(resendCode) {
				/* invalid */
				MFAPhNo = $("##MFAPhNo")[0];
				if (MFAPhNo.value.trim().length == 0 || !MFAPhNoInput.isValidNumber()){
					return false;
				}


				let isResend = typeof resendCode != "undefined" && resendCode ? true : false;

				let channel = $('##mfasms_channel').length ? $('##mfasms_channel').val() : 'sms';
				let strChannel = getMFASMSChannelInfo(channel);

				if (isResend) {
					$('##btnVerifyMFAPhNo').prop('disabled',true);
					$('##resendMFAPhNoOTPControl').html('<i class="icon-refresh icon-spin"></i> ' + strChannel.attempting);
				} else {
					$('##btnVerifyMFAPhNo').html('Please wait...').prop('disabled',true);
				}				
				
			}
			function showMFASMSOTPContainer(r) {				
				let strChannel = getMFASMSChannelInfo(r.channel);
				
				let MFAPhNoOTPResendOption = function() {
					if (timeLeft == -1) {
						clearTimeout(MFAPhNoOTPTimer);
						$('##resendMFAPhNoOTPControl').html('<a href="##" onclick="verifyMFAPhNo(1);return false;"><i class="icon-refresh"></i> '+strChannel.text+'</a>');
						$('.mfasms_channels').removeClass('login-disabled');
					} else {
						$('##resendMFAPhNoOTPRemainingTime').html(timeLeft);
						timeLeft--;
					}
				};
				
				let timeLeft = r.reattemptdelay;
				let MFASMSVerifyTemplate = Handlebars.compile($('##MFASMSVerifyTemplate').html());
				r.mfaphno = MFAPhNoInput.getNumber();
				r.resendcodetext = '<i class="icon-refresh"></i> '+strChannel.text+' in <span id="resendMFAPhNoOTPRemainingTime">'+timeLeft+'</span> seconds';
				r.disablesmschannels = r.codeattempts > 1;
				
				$('.MFAPhNoTool').hide();
				$('##verifyMFAPhNoOTP').html(MFASMSVerifyTemplate(r)).show();
				
				clearTimeout(MFAPhNoOTPTimer);
				MFAPhNoOTPTimer = setInterval(MFAPhNoOTPResendOption, 1000);
			}
			function verifySecurityCode() {
				/* invalid */
				if (MFAPhNo.value.trim().length == 0 || !MFAPhNoInput.isValidNumber() || $('##securityCode').val().trim().length == 0)
					return false;
				
				/* verify ph no */
				$('##btnVerifyCode').html('Please wait...').prop('disabled',true);
				$('##MFAPhNoOTPErrMsg').hide();
				clearResendOTP();
				
				
			}
			function clearResendOTP() {
				$('##resendMFAPhNoOTPControl').html('');
				$('.mfasms_channels').addClass('login-disabled');
			}
			function deleteMFAPhoneNo() {
				$('##delMFAPhNo').html('<i class="icon-spinner icon-spin"></i>');
				self.location.href = '/?pg=login&logact=manageSettings&la=mfasms&deleteMFAPhNo';
			}
			function chooseMFASMSChannel(channel) {
				$('##mfasms_channel').val(channel);
				verifyMFAPhNo();
			}
			function getMFASMSChannelInfo(channel) {
				let strChannel = {};
				switch (channel.toLowerCase()) {
					case 'sms':
						strChannel = { text:'Resend code', attempting:'Resending code' };
						break;
					case 'call':
						strChannel = { text:'Call again', attempting:'Calling' };
						break;
					case 'whatsapp':
						strChannel = { text:'Resend code', attempting:'Resending code' };
						break;
				}
				return strChannel;
			}
			function gotoManageSettings() {
				self.location.href = '/?pg=login&logact=manageSettings';
			}

			$(function() {
				setUpNumberSection('');
				gotoSMS = localStorage.getItem("gotoSMS");
				if(gotoSMS == 1){
					$('html, body').animate({
						scrollTop: $(".addPhoneIcon").offset().top
					}, 2000);
					localStorage.removeItem("gotoSMS");
				}

			});
			function setUpNumberSection(id){
				var MFAPhNo = $("##MFAPhNo"+id)[0];
				var MFAPhNoErrMsg = $("##MFAPhNoErrMsg"+id);
				var MFAPhNoValidMsg = $("##MFAPhNoValidMsg"+id);
				var MFAPhNoErrMap = ["Invalid number", "Invalid country code", "Too short", "Too long", "Invalid number"];
				var MFAPhNoOTPTimer = 0;
			
				/* initialise plugin */
				MFAPhNoInput = window.intlTelInput(MFAPhNo, {
					utilsScript: "/assets/common/javascript/intl-tel-input/18.1.1/js/utils.js",
					preferredCountries: [ 'us', 'ca' ]
				});
			
				var MFAPhNoReset = () => {
					MFAPhNoErrMsg.html('').hide();
					MFAPhNoValidMsg.hide();
				};

				/* on blur: validate */
				MFAPhNo.addEventListener('blur', () => {
					MFAPhNoReset();
					if (MFAPhNo.value.trim()) {
						if (MFAPhNoInput.isValidNumber()) {
							if(id == ''){
								$('##btnVerifyMFAPhNo'+id).html('Add').removeAttr('disabled');
							}else{
								$('##btnVerifyMFAPhNo'+id).html('Save').removeAttr('disabled');
							}
							

						} else {
							var MFAPhNoErrCode = MFAPhNoInput.getValidationError();
							if (MFAPhNoErrMap.length >= MFAPhNoErrCode + 1)
								MFAPhNoErrMsg.html(MFAPhNoErrMap[MFAPhNoErrCode]).show();
						}
					}
				});
			
				/* on keyup / change flag: reset */
				MFAPhNo.addEventListener('change', MFAPhNoReset);
				MFAPhNo.addEventListener('keyup', MFAPhNoReset);
				

				$('.addNumberBtn').on('click',function(){
					$('.editDivWrap').addClass('hidden');
					$('##editMFAPhNo').removeClass('hidden');
				});

				$('##MFAPhNo'+id).focus();
			}
			function addNumber(){
				/* invalid */
				MFAPhNo = $("##MFAPhNo")[0];
				if (MFAPhNo.value.trim().length == 0 || !MFAPhNoInput.isValidNumber()){
					return false;
				}
				var channel = $('##mfasms_channel').length ? $('##mfasms_channel').val() : 'sms';
				$.ajax({
					url: '#variables.structData.link.savePhoneNumber#&la=mfasms',
					type: 'POST',
					data: { "MFAPhNo":MFAPhNoInput.getNumber()},
					dataType: 'json',
					success: function(response) { 
						$('##btnVerifyMFAPhNo').html('Add').prop('disabled',false);

						if (response.SUCCESS) {
							MFAPhNo.value = '';
							$('##editMFAPhNo').addClass('hidden');
							localStorage.setItem("gotoSMS", 1);
							window.location.reload();
						} else {
							alert(response.MSG);
						}
						
					}, fail: function(response) { 
						$('##btnVerifyMFAPhNo').html('Add').prop('disabled',false);
						alert(response.MSG);
					}
				});
			}
			function cancelAdd(){
				MFAPhNo.value = '';
				$('##editMFAPhNo').addClass('hidden');
				$('##MFAPhNoErrMsg').html('').hide();
			}
			function cancelEditList(id){
				$('##editMFAPhNo'+id).addClass('hidden');
			}
			function initializeEditNumber(id){
				if($.inArray(id, arrNumbersEdited) == -1){
					setUpNumberSection(id)
					arrNumbersEdited.push(id);
				}
				$('.editDivWrap').addClass('hidden');
				$('##editMFAPhNo'+id).removeClass('hidden');
			}
			function deleteNumber(id,strNumber){
				var result = confirm("Do you really want to delete Phone Number "+strNumber+"?");
				if (result) {
					$.ajax({
						url: '#variables.structData.link.deletePhoneNumber#&la=mfasms',
						type: 'POST',
						data: { "smsNotificationID":id},
						dataType: 'json',
						success: function(response) { 
							$('##btnVerifyMFAPhNo').html('Save').prop('disabled',false);

							if (response.SUCCESS) {
								$('##editMFAPhNo'+id).closest('.divWrap').remove();
								localStorage.setItem("gotoSMS", 1);
								window.location.reload();
							} else {
								alert(response.MSG);
							}
							
						}, fail: function(response) { 
							$('##btnVerifyMFAPhNo').html('Save').prop('disabled',false);
							alert(response.MSG);
						}
					});
				}
			}
			function updateNumber(id){
				MFAPhNo = $("##MFAPhNo"+id)[0];
				if (MFAPhNo.value.trim().length == 0 || !MFAPhNoInput.isValidNumber()){
					return false;
				}
				var channel = $('##mfasms_channel').length ? $('##mfasms_channel').val() : 'sms';
				$.ajax({
					url: '#variables.structData.link.updatePhoneNumber#&la=mfasms',
					type: 'POST',
					data: { "MFAPhNo":MFAPhNoInput.getNumber(),"smsNotificationID":id},
					dataType: 'json',
					success: function(response) { 
						$('##btnVerifyMFAPhNo').html('Save').prop('disabled',false);

						if (response.SUCCESS) {
							$('##btnVerifyMFAPhNo').html('Saving...').prop('disabled',false);
							localStorage.setItem("gotoSMS", 1);
							window.location.reload();
							MFAPhNo.value = '';
							$('##editMFAPhNo'+id).addClass('hidden');
						} else {
							alert(response.MSG);
						}
						
					}, fail: function(response) { 
						$('##btnVerifyMFAPhNo').html('Save').prop('disabled',false);
						alert(response.MSG);
					}
				});
			}
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pageHeadJS#">