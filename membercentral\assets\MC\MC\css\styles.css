/* CSS RESET ELEMENTS --------------------------------------------------------------------------------------*/
body,img,h1,h2,h3,h4,h5,h6{ border:none; list-style:none; margin:0; padding:0; border:0; }
body{ 
  font-family:Arial,helvetica,sans-serif;
  margin:0 0 0 0; 
  padding:0 0 0 0; 
  background-color: #fff;
}
/*----------------------------------------------------------------------------------------------------------*/
.grey_box_header{ color:#eeeeee; display:block; font-family:Georgia, "Times New Roman", Times, serif; font-size:21px; padding-bottom:15px; padding-top:0px; text-align:left; }
.grey_box_content{ color:#eeeeee; font-family:Verdana, Geneva, sans-serif; font-size:12px; line-height:17px; padding-bottom:15px; text-align:left; }
.small_sq_header{ display:block; margin-left: 15px; padding-bottom:15px; padding-right:5px; padding-top:0px; text-align:left; }
.small_sq_content{ margin-left: 15px; text-align:left; }
.customer_stories_header{ display:block; margin-left: 5px; padding-bottom:15px; padding-top:0px; }
.customer_stories_content{ margin-left: 5px; padding-bottom:15px; }
.whats_new_header{ display:block; padding-bottom:15px; padding-top:0px; text-align:left; }
.whats_new_content{ padding-bottom:15px; text-align:left; }
/*----------------------------------------------------------------------------------------------------------*/
/* -- MemberCentral logo, main nav menu, and "Request Demo" button on white gradient -- */
/*----------------------------------------------------------------------------------------------------------*/
div#topnav{ background:url(../images/home_memberCentral_01.jpg) bottom repeat-x; border-bottom:#6c91b4 solid 3px; height:83px; text-align:center; width:100%; }
#topnav ul, #topnav li{ margin:0; padding:0; list-style:none; }
.pageSize{ margin:auto auto; position:relative; width:974px; }
/*----------------------------------------------------------------------------------------------------------*/
#navLinks{ height:30px; text-align:center; position:relative; padding-top:15px;}
#navLinks a{ color:#406791; display:inline-block; font-family:myriad pro, helvetica; font-size:13pt; height:20px; margin-right:30px; margin-left:30px; padding-right:0; text-decoration:none;}
#navLinks a:hover{ color:#709D40; }
#navLinks .divider {vertical-align:middle;}

/*----------------------------------------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------------------------------------*/
/* -- Area containing homepage swf, javascript menu menu (3 links), and related content on blue graphic BG -- */
/*----------------------------------------------------------------------------------------------------------*/
#mastHead{ width:1000px; margin:auto auto; position:relative; }
#mastHeadContentArea{  }
#mastHeadContent{ text-align:left; }
#mastHeadContent ul, #slider li{ margin:0; padding:0; list-style:none; }
#mastHeadContent li{ width:720px; height:300px; overflow:hidden; }
#mastHeadContent li p{ margin:0; padding:0; }
#mastHeadContent li p{ margin-bottom:15px; }

#mastHeadContentInterior { text-align:left; }
#mastHeadContentInterior ul, #slider li{ margin:0; padding:0; list-style:none; }
#mastHeadContentInterior li{ width:720px; height:225px; overflow:hidden; }
#mastHeadContentInterior li p{ margin:0; padding:0; }
#mastHeadContentInterior li p{ margin-bottom:15px; }
/*----------------------------------------------------------------------------------------------------------*/
#blue_main_area{ center no-repeat; height: 250px; margin:0 auto; }
#blue_main_area td{ vertical-align:top; }
#blue_main_area p{ padding:0; margin:0; }
#blue_main_area p{ margin-bottom:10px; }
/*----------------------------------------------------------------------------------------------------------*/
#blue_content{ background: url(../images/bluebgmax_02.jpg) center; height:300px; text-align: center; width:100%; border-bottom:#E1E1E1 3px solid; }
#blue_content_js{ background: url(../images/bluebgmax_02.jpg) center; height:322px; margin-top: 0px; text-align: center; width:100%; }
#blue_content_js_small{ background: url(../images/bluebgmax_02_small.jpg) center; height:52px; margin-top: 0px; width:100%; }

#blue_content_interior{ background: url(../images/bluebgmax_02.jpg) center; height:235px; text-align: center; width:100%; border-bottom:#E1E1E1 3px solid; }
/*----------------------------------------------------------------------------------------------------------*/
td{  }
.transHeader{ background: url(../images/transHeader_tile.png) repeat-x top center; height: 52px; }
/*----------------------------------------------------------------------------------------------------------*/
ul#nav_solutions{ list-style:none; margin: auto auto; position:relative; padding: 0; width: 800px;  }
ul#nav_solutions li{ float: left; height: 52px; position: relative; text-align: center;  padding:0 5px 0 5px;  width:165px; }
ul#nav_solutions li.selected{ float: left; background: url(../images/solutions_OnPage.png) no-repeat; color: #6d9e41; position: relative; width:165px; } 
ul#nav_solutions li.selected a{ color: #6d9e41; position: relative;  }
ul#nav_solutions li a{ color: #0c55a5; font-family: verdana; font-size: 13px; font-weight:normal; padding:10px 0 10px 0; position: relative; text-decoration: none; width:165px; display:inline-block; }
ul#nav_solutions li a:hover{ background:url(../images/jsNavHover150.png) center no-repeat; color:#f4fafb; position:relative; }
ul#nav_solutions li.selected a:hover{ background: url(../images/jsNavSelectedHover.png) no-repeat; color: #6d9e41; position: relative; }
/*----------------------------------------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------------------------------------*/

/* -- Area with main content on white flat background. Also contains sub-menu (6 links) on off-white strip -- */
#white_content{ 
margin-right:155px;
padding-bottom:5px; 
padding-top:5px; 
position:relative; 
}
#white_content p{ margin:0; padding:0; }
#white_content p{ margin-bottom:25px; }

#white_content_interior{ 
margin-right:155px;
padding-bottom:5px; 
padding-top:1px; 
position:relative; 
width:100%;
}


.white_content_area{ position:relative; text-align:left;}
.white_content_area_interior { position:relative; text-align:left;}
#zoneA_homepage{ 
  background:url(../images/shadow_content_05.jpg) top no-repeat; 
  height:445px; 
  left:-29px; 
  position:absolute; 
  top:19px; 
  width:460px; }
#zoneA_homepage p{ margin:0; padding:0;}
#zoneA_homepage p{ margin-bottom:15px;}
 
#main_homepage p{ margin:0; padding:0;}
#main_homepage p{ margin-bottom:15px;}
 
#zoneB_homepage p{ margin:0; padding:0;}
#zoneB_homepage p{ margin-bottom:25px;}
 
.small_sq{ height:174px; position:absolute; width:200px; }


#homepageMainCenter {margin:0; padding:0; padding-top:10px; background:url(/images/homepageMainCenter.png) repeat-x; width:870px; vertical-align:top; }
#homepageMainCenter img{ padding-bottom:7px; }
#homepageMainCenter .HeaderText { font-family:Georgia, Helvetica, Arial; font-size:16pt; color:#5e8ebd; margin-top:25px;}
#homepageMainCenter .BodyText { font-family:Arial, Georgia, Helvetica; font-size:10pt; color:#353c61; text-align:left;}
#homepageMainCenter .leftCol { text-align:left; padding-left:12px; vertical-align:top; width:270px; }
#homepageMainCenter .midCol { vertical-align:top; padding-left:3px; padding-right:0px; width:276px;}
#homepageMainCenter .rightCol { vertical-align:top; padding-left:7px; }




#interior_content { padding-top:10px;}

#interiorSideNav { background-color:#e8f1f3; padding:0px 10px 7px 20px; text-align:left;}
#interiorSideNav a { text-decoration:none; }
#interiorSideNav a:hover { text-decoration:underline; }
#interiorSideNav .MainSubContent a { color:#555; width:175px; }
#interiorSideNav .MainSubContent p { padding:0px 10px; }
#interiorSideNav p { width:80%; border-bottom:1px solid white; padding:6px 5px;}


#interiorSideNav img.selected { display:; padding-right:7px;}
#interiorSideNav img.hidden { display:none; }
/*----------------------------------------------------------------------------------------------------------*/
#ss1{ left:22px; top:24px; }
#ss2{ left:230px; top:24px; }
#ss3{ left:22px; top:198px; }
#ss4{ left:230px; top:198px; }
/*----------------------------------------------------------------------------------------------------------*/
#main_homepage{ left:446px; position:absolute; text-align:left; top:43px; width:254px; }
#main_homepage img{ border: #c8dcdf 3px solid; margin-bottom:15px; margin-left: 5px; }
#main_homepage a{ color:#6d9e41; display:block; font-family:Verdana, Geneva, sans-serif; font-size:12px; margin-left: 5px; text-decoration:underline; width:100%; }
/*----------------------------------------------------------------------------------------------------------*/
/* WHAT IS THIS CODE --------------- */
#main_homepage a:before{
--content:"<img src="">";
margin-left: 5px;
}
/* ================================= */
/*----------------------------------------------------------------------------------------------------------*/
#zoneB_homepage{ background:#e8f1f3; height:360px; left:720px; padding:24px 15px 0px; position:absolute; top:19px; width:260px; }
/*----------------------------------------------------------------------------------------------------------*/
/* table of options (2 rows, 3 cols) */
table#table_solutions{ margin-top: 20px; }
td#sol_col{ width: 330px; }
td.sol_col_topLeft{ border-bottom: 1px dotted #d2d2d2; border-right: 1px dotted #d2d2d2; padding: 15px; }
td.sol_col_topMiddle{ border-bottom: 1px dotted #d2d2d2; border-right: 1px dotted #d2d2d2; padding: 15px; }
td.sol_col_topRight{ border-bottom: 1px dotted #d2d2d2; padding: 15px; }
td.sol_col_bottomLeft{ border-right: 1px dotted #d2d2d2; padding: 15px; }
td.sol_col_bottomMiddle{ border-right: 1px dotted #d2d2d2; padding: 15px; }
td.sol_col_bottomRight{ padding: 15px; } 
td#sol_col p.sol_header{ color: #425c78; font-family: georgia; font-size: 21px; height: 55px; }
td#sol_col p.sol_content{ color: #555555; font-family: verdana; font-size: 13px; height: 75px; }
td#sol_col a.learnMore{ color: #6d9e41; font-family: verdana; font-size: 14px; height: 20px; }
/*----------------------------------------------------------------------------------------------------------*/
/* sub-menu on off-white strip */
#subMenu { background: url(../images/subMenu_tile.jpg) repeat-x top center; height: 46px; margin-top: -2px; width:100%} 
ul#subMenu{ list-style: none; margin: auto auto; padding: 0; padding-left:60px; position: relative; width: 975px; }
ul#subMenu li{ float: left; padding: 15px 30px 15px 30px; position: relative; }
ul#subMenu img.selected { display:; padding-right:10px;}
ul#subMenu img.hidden { display:none; }
ul#subMenu li a{ color: #6d9e41; font-family: verdana; font-size: 12px; position: relative; text-decoration: none; }
ul#subMenu li a:hover { text-decoration: underline; }
/*----------------------------------------------------------------------------------------------------------*/
#main_subInterior { font-family: verdana; height: 100%; left: 0px; position: absolute; text-align: left; top: 15px; width: 475px; }
#main_subInterior ul { color: #555555; font-size: 14px; margin-bottom: 10px; margin-left: 20px; }
#main_subInterior a.printPage { color: #6d9e41; font-family: verdana; font-size: 12px; position: relative; top: -2px; }

#zoneB_subInterior { font-family: verdana; height: 100%; left: 515px; position: absolute; text-align: left; top: 15px; width: 475px; }
#zoneB_subInterior p.zoneB_subInterior_header { color: #555555; font-family: verdana; font-size: 18px; margin-bottom: 10px; }
#zoneB_subInterior ul { font-size: 14px; color: #555555; margin-bottom: 10px; margin-left: 20px; }
#zoneB_subInterior li { margin-bottom: 8px; }

/*----------------------------------------------------------------------------------------------------------*/
.MainSubContent li{ padding-bottom:4px; padding-top:4px; }

/* Additional info (Demo, Q's & Comments, Mailing List) and legal footer on gray background -- */
/*----------------------------------------------------------------------------------------------------------*/
#grey_content{ background:url(../images/last_03.jpg) top repeat-x; background-color:#82817a; height:80px; margin:auto auto; position:relative; text-align:center; width:100%; border-top:#B8B8B8 3px solid;}
#grey_content p{ margin:0; padding:0; }
#grey_content p{ margin-bottom:10px; }

#grey_content .footerText { text-align:left; color:#fff; font-family:Arial, Georgia, Helvetica; font-size:10pt;}
#grey_content .footerText a {color:#fff;}

.grey_box{ position:relative; }
.grey_box img{  }
/*----------------------------------------------------------------------------------------------------------*/
#gb1{ left:50px; position:absolute; text-align:left; top:34px; width:235px; }
#gb2{ left:350px; position:absolute; text-align:left; top:34px; width:265px; }
#gb3{ left:713px; position:absolute; text-align:left; top:34px; width:270px; }
#gb3 form{ position:relative; }

#gb3 form .inputtext{ 
color:#aaaaaa; 
display:inline-block;
font-size:15px; 
height:20px; 
left:0px; 
padding-left:5px; 
padding-top:5px; 
position:relative;
top:0px; 
width:160px;
}
#gb3 form .subscribe_button{ 
background:url(../images/home_memberCentral_42.jpg) center no-repeat; 
display:inline-block; 
height:29px; 
position:relative; 
top:-2px; 
width:81px; 
}
.padTop{ padding-top:20px; }
/*----------------------------------------------------------------------------------------------------------*/
img#mlist_icon{ margin-left: -5px; }
.grey_box_content a{ color:#eeeeee; }
#zoneF{ color:#bbbbbb; font-family:Verdana, Geneva, sans-serif; font-size:11px; line-height:17px; margin:0 auto; padding-top:170px; text-align:center; width:100%; }
#zoneF p a{ color:#bbbbbb; text-decoration:none; }
/*----------------------------------------------------------------------------------------------------------*/


/* TOOLBAR STYLES ------------------------------------------------------------------------------------------*/
#toolBarArea{ margin:15px 0 15px 0; }
#toolBar{ background:#fff; border-top:1px solid #000000; border-bottom:1px solid #000000; color:#355b8c; padding:5px 0 5px 0; }
#toolBar a{ color:#b4191a; font-size:1em; }
/*----------------------------------------------------------------------------------------------------------*/
.clear{ clear:both; }


