@charset "utf-8";
body { font-size: 11px; margin: 0; padding: 0; background: #ffffff url(../images/background.png); color: #333; font-family: Arial, helvetica, sans-serif; line-height: 1.33em; margin: 0 0 0 0; padding: 0 0 0 0; }
*, input[type="search"] { -moz-box-sizing: border-box; -ms-box-sizing: border-box; -o-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
img { max-width: 100%; }
a { color: #08c; text-decoration: none; }
h1, h2, h3, h4, h5, h6 { color: #383a39; }
a:hover .BodyText, .BodyText a:hover, a:hover, a:focus { color: #005580; text-decoration: none }
.container { max-width: 1012px; width: 100%; margin: 0 auto; }
p { font-size: 12px; line-height: 25px; }
.clearfix::before, .clearfix::after { content: ""; display: table; width: 100%; clear: both; }
/***Header***/

.headertop { }
.headertop .loginSec a { color: #fff; font-size: 13px; font-weight: bold; margin: 6px 10px 0 0; display: inline-block; float: right; }
.headertop .loginSec ul { display: -webkit-box; display: -ms-flexbox; display: flex; background: #514f50; border-radius: 10px; padding: 3px 15px 3px 5px; margin-top: 126px; margin-bottom: 7px; }
.headertop .loginSec ul li { list-style: none; }
.headertop .loginSec ul li a { margin: 0px 3px; }
header { position: relative; width: 100%; padding-top: 5px; }
.headertop { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-pack: justify; -ms-flex-pack: justify; justify-content: space-between; }
.newheader header { padding: 0; }
.logo { position: relative; z-index: 123; padding: 0px; font: 18px verdana; float: none; }
#cssmenu { background: #362a2d; padding: 35px 0 25px 10px; border-top-left-radius: 12px; border-top-right-radius: 12px; }
#cssmenu, #cssmenu ul, #cssmenu ul li, #cssmenu ul li a, #cssmenu #head-mobile { margin: 0px; border: 0; list-style: none; line-height: 1; display: block; position: relative; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box }
#cssmenu:after, #cssmenu > ul:after { content: "."; display: block; clear: both; visibility: hidden; line-height: 0; height: 0 }
#cssmenu #head-mobile { display: none }
#cssmenu { background: #362a2d; }
#cssmenu > ul > li { float: left; position: relative; border-left: 2px solid #fff; }
#cssmenu > ul > li:first-child { border: none; }
#cssmenu > ul > li > a { padding: 0px 14px; font-size: 19px; text-decoration: none; color: #fff; font-weight: 700; text-align: center; line-height: 16px; }
#cssmenu > ul > li:hover > a { color: #fff; }
#cssmenu > ul > li:hover, #cssmenu ul li.active:hover, #cssmenu ul li.active, #cssmenu ul li.has-sub.active:hover { -webkit-transition: background .3s ease; -ms-transition: background .3s ease; transition: background .3s ease; }
#cssmenu > ul > li:last-child> a { border-right: none; }
#cssmenu > ul > li.has-sub:hover > a:before { top: 23px; height: 0 }
#cssmenu ul ul { position: absolute; left: -9999px }
#cssmenu ul ul li { height: 0; -webkit-transition: all .25s ease; -ms-transition: all .25s ease; background: #746d71; transition: all .25s ease }
#cssmenu ul ul li:hover { }
#cssmenu li:hover > ul { left: auto; top: 11px; box-shadow: 2px 2px 6px rgba(0,0,0,.2); padding-top: 15px; }
#cssmenu li:hover > ul > li { height: auto; background: #746d71; border-top: 1px solid #bebabb; }
#cssmenu ul ul ul { margin-left: 100%; top: 0 }
#cssmenu ul ul li a { border-bottom: 0px solid rgba(150,150,150,0.15); padding: 6px 8px; width: 175px; font-size: 14px; text-decoration: none; color: #fff; font-weight: 400; }
#cssmenu ul ul li:last-child > a, #cssmenu ul ul li.last-item > a { border-bottom: 0 }
#cssmenu ul ul li:hover > a, #cssmenu ul ul li a:hover { background: #bebabb; color: #fff; }
#cssmenu ul ul li.has-sub > a:after { position: absolute; top: 16px; right: 11px; width: 8px; height: 2px; display: block; background: #ddd; content: '' }
#cssmenu ul ul li.has-sub > a:before { position: absolute; top: 13px; right: 14px; display: block; width: 2px; height: 8px; background: #ddd; content: ''; -webkit-transition: all .25s ease; -ms-transition: all .25s ease; transition: all .25s ease }
#cssmenu ul ul > li.has-sub:hover > a:before { top: 17px; height: 0 }
#cssmenu ul ul li.has-sub:hover, #cssmenu ul li.has-sub ul li.has-sub ul li:hover { background: #363636; }
#cssmenu ul ul ul li.active a { border-left: 1px solid #333 }
#cssmenu > ul > li.has-sub > ul > li.active > a, #cssmenu > ul ul > li.has-sub > ul > li.active> a { border-top: 1px solid #333 }
/******Footer*****/
.footer { padding: 0px; }
.footer .container { background: transparent; border: 0px; }
.footer-wrapper { width: 100%; position: relative; }
.footCol h3 { font-size: 11pt; color: #aba48f; text-transform: uppercase; line-height: normal; margin-bottom: 10px; }
.affliates img { width: 80px; height: auto; float: none; margin-bottom: 10px; display: block; }
.mainLogo { text-align: center; }
.footCol address { margin: 0; line-height: 25px; color: #fff; }
.footCol p { color: #000; font-size: 12px; text-align: center; line-height: normal; margin: 0; display: inline-block; }
.footCol address span { display: block; }
.footer a { color: #fff; display: inline-block; text-decoration: underline; }
.mainLogo a img { width: 155px; height: 60px; }
.footer a:hover { color: #fff; }
.footCol.mainLogo { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: justify; -ms-flex-pack: justify; justify-content: space-between; }
.mainLogo { text-align: center; padding: 0px 1px 0px 5px; }
.footCol p { font-size: 11px; text-align: center; line-height: normal; margin: 0; display: inline-block; color: #5b5b5b; }
/******Content*****/
.zonewrapper form { margin: 0; }
.template input { height: auto; }
.template .form-horizontal .controls span.input-xlarge.uneditable-input { height: auto; }
/* .mcpanel-body ul { margin-left: 22px; } */
.nav-pills>li>a, .nav-list>li>a { text-decoration: none; }
.nav-pills>.active>a, .nav-pills>.active>a:hover, .nav-pills>.active>a:focus { background-color: #642c8a; }
.nav-list>.active>a, .nav-list>.active>a:hover, .nav-list>.active>a:focus { background: #642c8a; }
.quicklinks h3 { line-height: 1; border-bottom: 1px solid #ccc; padding: 0 0 10px; margin: 0 0 14px; }
.quicklinks-menu { margin: 0 0 20px; }
.quicklinks-menu li { list-style: none; text-align: center; }
.quicklinks-menu li a { background: #642C8A; display: block; color: #fff; text-decoration: none !important; padding: 11px; border-bottom: 1px solid #ffffff; text-transform: uppercase; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px; -ms-border-radius: 3px; }
.quicklinks-menu li a:hover { opacity: 0.8; }
