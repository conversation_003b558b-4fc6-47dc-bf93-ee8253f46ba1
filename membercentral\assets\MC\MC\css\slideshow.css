	
/* image replacement */
.graphic, #prevBtn, #nextBtn, #slider1prev, #slider1next{margin:0;padding:0;display:block;overflow:hidden;text-indent:-8000px;}
/* // image replacement */
			
#content{margin:0 auto;position:relative;text-align:left;width:735px;}			
#content.img{border:none;}

/* Easy Slider */
#slider ul, #slider li{margin:0;padding:0;list-style:none;}
#slider li{ width:220x;height:211px;overflow:hidden; }
#arrows{ position:relative; float:right; width:159px; margin-top:-32px; margin-right:14px; *margin-right:-138px; z-index:800; }
#prevBtn, #slider1prev{position:relative;float:left;width:20px;height:20px;}
#nextBtn, #slider1next{position:relative;float:right;width:20px;height:20px;}
#prevBtn a, #slider1prev a{display:block;position:relative;width:20px;height:20px;background:url(../images/prev.png) no-repeat;}
#nextBtn a, #slider1next a{display:block;position:relative;width:20px;height:20px;background:url(../images/next.png) no-repeat;}
/**********************************************************************************************/	

/**********************************************************************************************/	

.contentItem{  }
#mastHeadContent #0{  }
/**********************************************************************************************/	
.mhTitle{ color:#ffffff; font-family:Georgia, "Times New Roman", Times, serif; font-size:22pt; font-weight:500;}
.mhTitleAlt{ color:#eaffa7; }
.mhBodyText{ color:#ffffff; font-family:Verdana, Geneva, sans-serif; font-size:10pt; line-height:14pt; }

/**********************************************************************************************/	
#mhWebsiteHosting{ background:url(../images/mhWebsiteHosting.png) no-repeat; height:300px; width:720px; }
#mhWebsiteHosting .title{ width:420px; padding:30px 10px 2px 20px;}
#mhWebsiteHosting .mhTitle{ color:#ffffff; font-family:Georgia, "Times New Roman", Times, serif; font-size:20pt; font-weight:500; line-height:19pt;}
#mhWebsiteHosting .contentMessage{ width:405px; padding:2px 10px 0 20px; }
#mhWebsiteHosting .mhBodyText{ color:#ffffff; font-family:Verdana, Geneva, sans-serif; font-size:11pt; line-height:14pt; }
#mhWebsiteHosting .buttons{ width:325px; padding:2px 0 0 20px; }
/**********************************************************************************************/	
/**********************************************************************************************/	
#mhSocialNetworking{ background:url(../images/mhSocialNetworking.png) no-repeat; height:300px; width:720px; }
#mhSocialNetworking .title{ width:420px; padding:30px 10px 2px 20px;}
#mhSocialNetworking .mhTitle{ color:#ffffff; font-family:Georgia, "Times New Roman", Times, serif; font-size:20pt; font-weight:500; line-height:19pt;}
#mhSocialNetworking .contentMessage{ width:405px; padding:2px 10px 0 20px; }
#mhSocialNetworking .mhBodyText{ color:#ffffff; font-family:Verdana, Geneva, sans-serif; font-size:11pt; line-height:14pt; }
#mhSocialNetworking .buttons{ width:325px; padding:2px 0 0 20px; }
/**********************************************************************************************/	
/**********************************************************************************************/	
#mhDistanceLearning{ background:url(../images/mhDistanceLearning.png) no-repeat; height:300px; width:720px; }
#mhDistanceLearning .title{ width:420px; padding:30px 10px 2px 20px;}
#mhDistanceLearning .mhTitle{ color:#ffffff; font-family:Georgia, "Times New Roman", Times, serif; font-size:20pt; font-weight:500; line-height:19pt;}
#mhDistanceLearning .contentMessage{ width:405px; padding:2px 10px 0 20px; }
#mhDistanceLearning .mhBodyText{ color:#ffffff; font-family:Verdana, Geneva, sans-serif; font-size:11pt; line-height:14pt; }
#mhDistanceLearning .buttons{ width:325px; padding:2px 0 0 20px; }
/**********************************************************************************************/	
#mhMemberManagement{ background:url(../images/mhMemberManagement.png) no-repeat; height:300px; width:720px; }
#mhMemberManagement .title{ width:375px; padding:35px 10px 2px 20px; }
#mhMemberManagement .mhTitle{ color:#ffffff; font-family:Georgia, "Times New Roman", Times, serif; font-size:20pt; font-weight:500; line-height:19pt;}
#mhMemberManagement .contentMessage{ width:405px; padding:2px 10px 0 20px; }
#mhMemberManagement .mhBodyText{ color:#ffffff; font-family:Verdana, Geneva, sans-serif; font-size:11pt; line-height:14pt; }
#mhMemberManagement .buttons{ width:325px; padding:2px 0 0 20px; }
/**********************************************************************************************/	
#mhDefault{ background:url(../images/mhDefault.png) no-repeat; height:300px; width:720px; }
#mhDefault .title{ width:375px; padding:35px 10px 2px 20px; }
#mhDefault .mhTitle{ color:#ffffff; font-family:Georgia, "Times New Roman", Times, serif; font-size:20pt; font-weight:500; line-height:19pt;}
#mhDefault .contentMessage{ width:325px; padding:2px 0 0 20px; }
#mhDefault .mhBodyText{ color:#ffffff; font-family:Verdana, Geneva, sans-serif; font-size:11pt; line-height:14pt; }
#mhDefault .buttons{ width:325px; padding:2px 0 0 20px; }
/**********************************************************************************************/	

/* numeric controls
 * 
 *  
 *  background-color:#cadaf0;
 *  
 *  */	
ol#controls{ position:absolute; top:15px; right:2px;  list-style:none; margin:0; padding:0; }
ol#controls li{ width:270px; height:68px; }
ol#controls li a{ display: block;
                  
                  text-align:center;
}
ol#controls li a:hover{
                  text-decoration:none; 
                  color:#fff; 
                  background: url(../images/btnHover.png) no-repeat;
                  
}

ol#controls li a:focus, #prevBtn a:focus, #nextBtn a:focus{outline:none;}