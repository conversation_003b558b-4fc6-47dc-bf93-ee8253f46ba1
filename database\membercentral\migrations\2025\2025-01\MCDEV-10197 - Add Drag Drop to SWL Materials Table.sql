-- MCDEV-10197 - Add Drag/Drop to SWL Materials Table
USE seminarWeb;
GO

-- Add documentOrder column to tblSeminarsAndDocuments
ALTER TABLE dbo.tblSeminarsAndDocuments ADD documentOrder INT NULL;
GO

-- Populate existing records with sequential order based on seminarDocumentID
UPDATE sd 
SET documentOrder = t.newOrder
FROM dbo.tblSeminarsAndDocuments sd
INNER JOIN (
    SELECT seminarDocumentID, 
           ROW_NUMBER() OVER(PARTITION BY seminarID ORDER BY seminarDocumentID) as newOrder
    FROM dbo.tblSeminarsAndDocuments
) t ON sd.seminarDocumentID = t.seminarDocumentID;
GO

-- Make documentOrder column NOT NULL after populating data
ALTER TABLE dbo.tblSeminarsAndDocuments ALTER COLUMN documentOrder INT NOT NULL;
GO

-- Create stored procedure to reorder seminar documents
CREATE PROC dbo.sw_reorderSeminarDocuments
@seminarID int
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (seminarDocumentID int NOT NULL, documentOrder int NOT NULL, newOrder int NOT NULL);
	
	INSERT INTO @tmp (seminarDocumentID, documentOrder, newOrder)
	SELECT seminarDocumentID, documentOrder, ROW_NUMBER() OVER(ORDER BY documentOrder) as newOrder
	FROM dbo.tblSeminarsAndDocuments
	WHERE seminarID = @seminarID;
	
	UPDATE sd
	SET sd.documentOrder = t.newOrder
	FROM dbo.tblSeminarsAndDocuments as sd 
	INNER JOIN @tmp as t on sd.seminarDocumentID = t.seminarDocumentID;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Switch to membercentral database to add AJAX method
USE membercentral;
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @editRTFID int, @publishRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;
	CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);
	
	SELECT @editRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('SeminarWebAdmin'),dbo.fn_getResourceFunctionID('editSWLProgramAll',dbo.fn_getResourceTypeID('SeminarWebAdmin')));
	SELECT @publishRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('SeminarWebAdmin'),dbo.fn_getResourceFunctionID('editSWLProgramPublish',dbo.fn_getResourceTypeID('SeminarWebAdmin')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES ('reorderSWLMaterialDocuments', @editRTFID), 
	('reorderSWLMaterialDocuments', @publishRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='ADMINSWL',
		@requestCFC='model.admin.seminarWeb.seminarWebSWL',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO
