<cfcomponent output="false">

	<cffunction name="manageSMSTemplates" access="public" output="false" returntype="struct">
		<cfargument name="strETData" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { js="", html="", idExt="et#createUUID()#" }>
		<cfset local.baseURL = "/?mode=stream&pg=admin&mca_ajaxlib=smsTemplates&mca_ajaxfunc=">
		<cfset arguments.strETData.editURL = "/?pg=admin&mca_ajaxlib=smsTemplates&mca_ajaxfunc=">
		<cfsavecontent variable="local.strReturn.js">
			<cfoutput>
			<script language="javascript">
				var #toScript(local.baseURL,"mcet_baseURLSMS")#
				<cfif len(arguments.strETData.gridExt)>
					<cfset local.thisGridJS = getGridJS(strETData=arguments.strETData, baseURL=local.baseURL)>
					#local.thisGridJS# 
				</cfif>
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.html">
			<cfoutput>
			<div id="mcet_div_grids#local.strReturn.idExt#" data-idExt="#local.strReturn.idExt#">
				<cfif len(arguments.strETData.gridExt)>
					<cfset local.thisGridHTML = getGridBoxHTMLTemplatesSMS(strETData=arguments.strETData)>
					#local.thisGridHTML#
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getGridJS" access="public" output="false" returntype="string">
		<cfargument name="strETData" type="struct" required="true">
		<cfargument name="baseURL" type="string" required="true">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.js">			
			<cfif arguments.strETData.SMSApplicationType eq 'referrals'>	
				<cfset local.smsTemplatesJSONURL = "/?pg=admin&mca_jsonlib=mcdatatable&com=smsTemplateJSON&meth=getSMSTemplates&area=#arguments.strETData.section#&treeCode=#arguments.strETData.treeCode#&gridext=#arguments.strETData.gridext#&SMSApplicationType=#arguments.strETData.SMSApplicationType#&siteid=#arguments.strETData.siteID#&mode=stream">
				<cfset local.editCategoryURL = "#arguments.strETData.editURL#editCategory&_ettreecode=#arguments.strETData.treeCode#&_etg=#arguments.strETData.gridext#&mode=direct">
				<cfset local.editTemplateURL = "#arguments.strETData.editURL#editTemplate&_ettreecode=#arguments.strETData.treeCode#&_etg=#arguments.strETData.gridext#&mode=direct">
				<cfset local.editTextTemplateURL = "#arguments.strETData.editURL#editSMSTemplate&_ettreecode=#arguments.strETData.treeCode#&_etg=#arguments.strETData.gridext#&mode=direct">
				<cfset local.gridExt  = arguments.strETData.gridExt>				
				<cfset local.referralIDFromStrETData  = arguments.strETData.referralID>				
				<cfset local.initGridOnLoad  = arguments.strETData.initGridOnLoad>		
				<cfset local.strETData  = arguments.strETData>			
				<cfinclude template="/model/admin/referrals/dsp_refSMSJS.cfm">
			</cfif>			
		</cfsavecontent>

		<cfreturn local.js>
	</cffunction>
	<cffunction name="getGridBoxHTMLTemplatesSMS" access="public" output="false" returntype="string">
		<cfargument name="strETData" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.siteid = arguments.strETData.siteid>
		
		<cfif arguments.strETData.SMSApplicationType eq 'referrals'>	
			<cfif arguments.strETData.section eq 'client'>
				<cfset local.messagingServiceRef = getMessagingService(local.siteid,'REFCLIENTS')>
				<cfset local.messagingServiceRefText = 'REFCLIENTS'>
				<cfset local.feEnableTextMessagingClient = (arguments.strETData.messagingServiceRefUsage.recordcount gt 0)?1:0>
			<cfelseif arguments.strETData.section eq 'member'>
				<cfset local.messagingServiceRef = getMessagingService(local.siteid,'REFMEMBERS')>
				<cfset local.messagingServiceRefText = 'REFMEMBERS'>
				<cfset local.feEnableTextMessagingMember = (arguments.strETData.messagingServiceRefUsage.recordcount gt 0)?1:0>			
			</cfif>
			<cfset local.gridExt  = arguments.strETData.gridExt>			
			<cfset local.strETData  = arguments.strETData>			
			<cfsavecontent variable="local.html">
				<cfinclude template="/model/admin/referrals/dsp_refSMSTemplates.cfm">
			</cfsavecontent>
		</cfif>

		<cfreturn local.html>
	</cffunction>
	
	<cffunction name="editSMSTemplate" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="templateID" type="numeric" required="true">
		<cfargument name="treeCode" type="string" required="true">
		<cfargument name="gridExt" type="string" required="true">		
		<cfargument name="triggerID" type="numeric" required="true">
		<cfargument name="usageTypeID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryTemplate" datasource="#application.dsn.platformMail.dsn#">
			select et.*,smsTemplateContent.rawContent,sat.activeTemplateID,sat.isDefault
				from dbo.sms_usageTypeTemplates et
			left join dbo.sms_subuserMessagingServiceUsageActiveTemplates sat on sat.templateID = et.templateID
			cross apply membercentral.dbo.fn_getContent(et.contentID,1) as smsTemplateContent
			where et.templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
			and et.triggerID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.triggerID#">
			and et.usageTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageTypeID#">
			and et.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>

		<cfif val(local.qryTemplate.templateID)>
			<cfset local.actionWord = "Edit">
		<cfelse>
			<cfset local.actionWord = "Add">
		</cfif>

		<cfset local.qryGetLanguages = CreateObject("component","model.admin.website.website").getLanguages()>
		<cfset local.communicateLanguageID = local.qryTemplate.languageID>
		
		<cfscript>
			local.mergeInstLink = "/?pg=ContentEditor&ceAction=showMergeCodeInstructions&mode=stream";
			local.mergeCodeList = "";

			switch(arguments.treeCode) {
				case "ETREFCLIENTS":
					local.heading = "#local.actionWord# Referral Client Template";
					local.mergeCodeList = listAppend(local.mergeCodeList, "incReferral,exMem");
					local.parentResourceType = 'ReferralsAdmin';
					local.strArea = 'client';
					break;
				case "ETREFMEMBERS":
					local.heading = "#local.actionWord# Referral Member Template";
					local.mergeCodeList = listAppend(local.mergeCodeList, "incReferral");
					local.parentResourceType = 'ReferralsAdmin';
					local.strArea = 'member';
					break;
			}

			if (len(local.mergeCodeList)){
				local.mergeInstLink = "#local.mergeInstLink#&" & listChangeDelims(local.mergeCodeList, "=1&") & "=1";
			}
		</cfscript>

		<cfset local.qryDefaultTemplate = getDefaultTemplates(siteID = arguments.siteID, templateID = arguments.templateID,triggerID = arguments.triggerID,usageTypeID = arguments.usageTypeID)>
		<cfset local.defaultTemplateID = 0>
		<cfset local.isDefault = 0>
		
		<cfif local.qryDefaultTemplate.recordCount gt 0>
			<cfset local.defaultTemplateID = local.qryDefaultTemplate.templateID>
			<cfset local.isDefault = local.qryDefaultTemplate.isDefault>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SMSTemplate.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSMSTemplate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="templateID" type="numeric" required="true">
		<cfargument name="templateContentID" type="numeric" required="true">
		<cfargument name="treeCode" type="string" required="true">
		<cfargument name="templateName" type="string" required="true">
		<cfargument name="templateDesc" type="string" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="isDefault" type="numeric" required="false" default="0">
		<cfargument name="isActive" type="numeric" required="false" default="0">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="triggerID" type="numeric" required="true">
		<cfargument name="usageTypeID" type="numeric" required="true">
		<cfargument name="parentResourceType" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.languageObj = checkLanguageActive(
			mcproxy_siteID=arguments.mcproxy_siteID,
			templateID=arguments.templateID, 
			treeCode=arguments.treeCode,
			languageID=arguments.languageID, 
			isDefault=arguments.isDefault, 
			isActive=arguments.isActive, 
			triggerID=arguments.triggerID,
			usageTypeID=arguments.usageTypeID
		)>

		<cfset local.returnStruct.languageactive = 0>
		<cfif arguments.templateName neq '' AND NOT local.languageObj.LANGUAGEACTIVE>			

			<cfif arguments.templateID gt 0>
				<cfset updateSMSTemplate(siteID=arguments.mcproxy_siteID,templateID=arguments.templateID, 
					templateName=arguments.templateName,templateDesc=arguments.templateDesc, 
					templateContentID=arguments.templateContentID, 
					templateContent=arguments.templateContent, 
					languageID=arguments.languageID, isDefault=arguments.isDefault, isActive=arguments.isActive, 
					updatedByMemberID=session.cfcuser.memberdata.memberID,parentResourceType=arguments.parentResourceType,
					triggerID=arguments.triggerID,usageTypeID=arguments.usageTypeID)>
			<cfelse>
				<cfset insertSMSTemplate(siteID=arguments.mcproxy_siteID, templateID=arguments.templateID, 
				templateName=arguments.templateName,templateDesc=arguments.templateDesc, 
				templateContentID=arguments.templateContentID, templateContent=arguments.templateContent, 
				languageID=arguments.languageID, isDefault=arguments.isDefault, isActive=arguments.isActive, 
				updatedByMemberID=session.cfcuser.memberdata.memberID,parentResourceType=arguments.parentResourceType,
				triggerID=arguments.triggerID,usageTypeID=arguments.usageTypeID
				
				)>
			</cfif>

			<cfset local.returnStruct.success = true>
		<cfelse>
			<cfset local.returnStruct.success = false>
			<cfif local.languageObj.LANGUAGEACTIVE>
				<cfset local.returnStruct.languageactive = 1>
			</cfif>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="insertSMSTemplate" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="templateName" type="string" required="true">
		<cfargument name="templateDesc" type="string" required="true">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="isDefault" type="numeric" required="true">
		<cfargument name="isActive" type="numeric" required="true">
		<cfargument name="updatedByMemberID" type="numeric" required="true">
		<cfargument name="parentResourceType" type="string" required="true">
		<cfargument name="triggerID" type="numeric" required="true">
		<cfargument name="usageTypeID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.templateContent, siteid=arguments.siteid)>	

		<cfstoredproc datasource="#application.dsn.platformMail.dsn#" procedure="sms_createTemplate">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.templateName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.templateDesc#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.templateContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.isDefault#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.isActive#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.updatedByMemberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.parentResourceType#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.triggerID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.usageTypeID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.emailTemplateID">
		</cfstoredproc>
	</cffunction>

	<cffunction name="updateSMSTemplate" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="templateID" type="numeric" required="true">
		<cfargument name="templateName" type="string" required="true">
		<cfargument name="templateDesc" type="string" required="true">
		<cfargument name="templateContentID" type="numeric" required="true">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="isDefault" type="numeric" required="true">
		<cfargument name="isActive" type="numeric" required="true">
		<cfargument name="updatedByMemberID" type="numeric" required="true">
		<cfargument name="parentResourceType" type="string" required="true">
		<cfargument name="triggerID" type="numeric" required="true">
		<cfargument name="usageTypeID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryUpdate">
			DECLARE @usageID INT,@siteID INT;
			SELECT TOP 1 @usageID = usageID ,@siteID = su.siteID FROM dbo.sms_subuserMessagingServiceUsages su
			WHERE su.siteID  = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and su.usageTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageTypeID#">;

			UPDATE dbo.sms_usageTypeTemplates
			SET templateName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.templateName#">,
				templateDescription = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.templateDesc#">,
				languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">,
				status = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.isActive#">
				WHERE templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
				AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			<cfif arguments.isActive eq 0>
				DELETE FROM dbo.sms_subuserMessagingServiceUsageActiveTemplates 
				WHERE templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
				AND triggerID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.triggerID#">
				AND usageID = @usageID AND languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">
				AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfelse>

				IF EXISTS (SELECT * FROM dbo.sms_subuserMessagingServiceUsageActiveTemplates 
					WHERE templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
					AND triggerID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.triggerID#">
					AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
					AND usageID = @usageID AND languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">)
				BEGIN
					UPDATE dbo.sms_subuserMessagingServiceUsageActiveTemplates
					SET isDefault = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.isDefault#">
						WHERE templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
						AND triggerID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.triggerID#">
						AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
						AND usageID = @usageID AND languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">
				END
				ELSE 
				BEGIN
					INSERT INTO dbo.sms_subuserMessagingServiceUsageActiveTemplates
					(usageID,siteID,triggerID,templateID,languageID,isDefault) 
					VALUES (@usageID,@siteID,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.triggerID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">,
					<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.isDefault#">);
				END				
			</cfif>
		</cfquery>
			
		<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.templateContent, siteid=arguments.siteid)>					

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_updateContent">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.templateContentID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.templateName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.templateContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.updatedByMemberID#">
		</cfstoredproc>
	</cffunction>
	<cffunction name="checkLanguageActive" access="public" output=false returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="templateID" type="numeric" required="true">
		<cfargument name="treeCode" type="string" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="isDefault" type="numeric" required="false" default="0">
		<cfargument name="isActive" type="numeric" required="false" default="0">
		<cfargument name="triggerID" type="numeric" required="true">
		<cfargument name="usageTypeID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>


		<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryCheck">

			declare @usageID int;

			select top 1 @usageID = usageID from dbo.sms_subuserMessagingServiceUsages su
			where su.siteID  = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			and su.usageTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageTypeID#">


			select et.templateID
			from dbo.sms_subuserMessagingServiceUsageActiveTemplates as et
			inner join  sms_usageTypeTemplates  st on st.templateID = et.templateID and st.status <> 2
			where et.languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">
			and et.siteID  = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			and et.usageID = @usageID
			and et.triggerID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.triggerID#">
			<cfif arguments.templateID neq 0>
				and et.templateID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
			</cfif>
		</cfquery>
		
		<cfset local.data.languageactive = local.qryCheck.recordCount gt 0>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="removeSMSTemplate" access="public" output="false" returntype="struct">
		<cfargument name="templateID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformMail.dsn#" procedure="sms_deleteTemplate">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_BIT" variable="local.data.templateInUse">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getSMSPreview" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>

		<cfset var local = structNew()>
		
		<cfset local.template = arguments.event.getValue('template','')>
		<cfset local.SMSApplicationType = arguments.event.getValue('SMSApplicationType','')>
		<cfset local.templateID = arguments.event.getValue('templateID','')>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.orgID = arguments.event.getValue('mc_siteInfo.orgID')>
		<cfset local.triggerID = arguments.event.getValue('triggerID')>
		<cfset local.usageTypeID = arguments.event.getValue('usageTypeID')>
		<cfset local.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname')>
		<cfset local.defaultTimeZoneID = arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')>
		<cfset local.sitename = arguments.event.getValue('mc_siteinfo.sitename')>
		<cfset local.siteCode = arguments.event.getValue('mc_siteinfo.siteCode')>
		<cfset local.orgcode = arguments.event.getValue('mc_siteinfo.orgcode')>
		<cfset local.showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType')>
		<cfset local.defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')>
		
		
		<cfset local.previewTitle = ''>
		<cfif local.SMSApplicationType eq 'referrals'>
			<cfset local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals")>
			<cfset local.qryReferrals = local.objAdminReferrals.getClientReferralsIds(Event = arguments.event)>
			<cfset local.referralList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=referralsXML&meth=previewEmailReferralsXML&mode=stream">
			<cfset local.area = arguments.event.getValue('section','')>
			
			<cfif local.area eq 'client'>
				<cfset local.previewTitle = 'SMS Client Referral: Preview Message'>
			<cfelse>	
				<cfset local.previewTitle = 'SMS Member Referral: Preview Message'>
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_previewSMSTemplate.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSMSTemplate" access="public" output=false returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="IDToGetClientDetails" type="string" numeric="true">
		<cfargument name="sitename" type="string" required="true">
		<cfargument name="templateID" type="numeric" required="true">
		<cfargument name="triggerID" type="numeric" required="true">
		<cfargument name="usageTypeID" type="numeric" required="true">
		<cfargument name="mainhostname" type="string" required="true">
		<cfargument name="defaultTimeZoneID" type="string" required="true">
		<cfargument name="showCurrencyType" type="string" required="true">
		<cfargument name="defaultCurrencyType" type="string" required="true">
		<cfargument name="SMSApplicationType" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.retStruct = structNew()>
		
		<cfset local.objAppBaseLink = CreateObject('component', 'model.apploader')>
		<cfset local.objTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		
		
		<cfset local.siteID = arguments.mcproxy_siteID>		
		<cfset local.orgID = arguments.mcproxy_orgID>
		<cfset local.orgCode = arguments.mcproxy_orgCode>
		<cfset local.siteCode = arguments.mcproxy_siteCode>
		<cfset local.sitename = arguments.sitename>
		<cfset local.showCurrencyType = arguments.showCurrencyType>
		<cfset local.defaultCurrencyType = arguments.defaultCurrencyType>
		<cfset local.mainhostname = arguments.mainhostname>
		<cfset local.defaultTimeZoneID = arguments.defaultTimeZoneID>
		<cfset local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=local.defaultTimeZoneID)>
		<cfset local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=local.defaultTimeZoneID)>
		
	
		<cfif arguments.SMSApplicationType eq 'referrals'>		
			<cfset local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals")>
			<cfset local.objReferrals = CreateObject("component","model.referrals.referrals")>
			<cfset local.qryReferralSettings = local.objAdminReferrals.getReferralSettings(local.siteID)>
			<cfset local.applicationInstanceID = local.qryReferralSettings.applicationInstanceID>
			<cfset local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.applicationInstanceID,siteID=local.siteID)>
			<cfset local.memberLocalLink = "/?" & local.appBaseLink >
			<cfset local.memberExternalLink = "http://" & local.mainhostname & local.memberLocalLink>


			<cfset local.qryGetClientData = local.objAdminReferrals.getClient(clientReferralID=val(arguments.IDToGetClientDetails))>
			
			<cfif local.qryGetClientData.recordcount is 0>
				<cfset local.data.success = false>
				<cfreturn local.data>
			</cfif>

			<cfset local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=local.siteid)>
			<cfset local.thisPostTypeFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=local.siteID, resourceType='ClientReferrals', areaName='ClientReferrals', csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=0)>


			<cfset local.arrThisPostTypeFields = xmlParse(local.thisPostTypeFieldsXML.returnXML).xmlRoot.xmlChildren>

			<cfset local.callUID = local.qryGetClientData.callUID />
			<cfset local.qryGetReferralData = local.objAdminReferrals.getReferralData(orgID=local.orgID,callUID=local.callUID)>
			<cfset local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.qryGetReferralData.clientID)>
			
			<cfset local.communicateLanguageID = local.qryGetClientData.communicateLanguageID>
			<cfset local.qryMember = application.objMember.getMemberInfo(memberID=local.qryGetReferralData.memberID)>

			<cfset local.renderedEmailContent = ''>
			<cfset local.panelid1 = "" />
			<cfset local.subpanelid1 = "" />				
			<cfloop query="local.qryGetReferralFilterData">
				<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
					<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
						<cfcase value="panelid1">
							<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
							<cfset local.panelid1 = local.qryGetPanelInfo.name />
						</cfcase>
						<cfcase value="subpanelid1">
							<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
								<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
								<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
							</cfloop>
						</cfcase>
					</cfswitch>
				</cfif>
			</cfloop>	

			<cfset local.isPreview = 0>
			<cfset local.questionAnswerPath = ''>			

			<cfset local.referraldata = { 
				referralID=local.qryReferralSettings.referralID
				,siteID = local.siteID
				,sitename = local.sitename
				,orgId = local.orgID
				,orgcode = local.orgCode
				,siteCode = local.siteCode
				,clientID = local.qryGetReferralData.clientID
				,callUID = local.callUID
				,clientFirstName = local.qryGetReferralData.firstName
				,clientLastName = local.qryGetReferralData.lastName
				,clientFullName = local.qryGetReferralData.firstName &' '&local.qryGetReferralData.middleName &' '&local.qryGetReferralData.lastName
				,memberPrefix = local.qryMember.prefix
				,memberFirstName = local.qryMember.firstName
				,memberMiddleName = local.qryMember.middleName
				,memberLastName = local.qryMember.lastname
				,memberFullName = local.qryMember.firstName &' '&local.qryMember.middleName &' '&local.qryMember.lastname
				,memberCompany = local.qryMember.company
				,memberMemberNumber = local.qryMember.memberNumber
				,primaryPanelName = local.panelid1
				,clientEmail = local.qryGetReferralData.email
				,clientHomePhone= local.qryGetReferralData.homePhone
				,clientCellPhone= local.qryGetReferralData.cellPhone
				,clientAlternatePhone= local.qryGetReferralData.alternatePhone
				,clientBusiness = local.qryGetReferralData.businessName
				,clientAddress1 = local.qryGetReferralData.address1
				,clientAddress2 = local.qryGetReferralData.address2
				,clientCity = local.qryGetReferralData.city
				,clientState = local.qryGetReferralData.clientState
				,clientZipCode = local.qryGetReferralData.postalCode
				,representativeId= local.qryGetReferralData.repID
				,repFirstName= local.qryGetReferralData.repFirstName
				,repLastName= local.qryGetReferralData.repLastName
				,repEmail= local.qryGetReferralData.repEmail
				,repHomePhone= local.qryGetReferralData.repHomePhone
				,repCellPhone= local.qryGetReferralData.repCellPhone
				,repAlternatePhone= local.qryGetReferralData.repAlternatePhone
				,interviewerName= local.qryGetReferralData.interviewerName
				,clientReferralID= local.qryGetReferralData.clientReferralID
				,referralDate= local.qryGetReferralData.clientReferralDate
				,regTimeZoneName= local.regTimeZoneName
				,refIssueDesc= local.qryGetReferralData.issueDesc
				,memberExternalLink= local.memberExternalLink
				,adminLink="http://" & local.mainhostname & CreateObject('model.admin.admin').buildLinkToTool(toolType='ReferralsAdmin',mca_ta='editClient')
				,questionAnswerPath = local.questionAnswerPath
				,transactionDate = '#dateFormat(now(),"mm/dd/yyyy")#'
				,showCurrencyType = local.showCurrencyType 
				,defaultCurrencyType = local.defaultCurrencyType 
				,isSampleEmail = local.isPreview
				,memberNumber= local.qryMember.memberNumber
			}>

			<cfset local.arrClientRefCustomFields = local.objReferrals.getClientReferralCustomFieldWithData(itemID=local.qryGetClientData.clientReferralID, itemType='ClientRefCustom', arrResourceFields=local.arrThisPostTypeFields, objCustomFields=local.objResourceCustomFields)>
			<cfloop array="#local.arrClientRefCustomFields#" index="local.thisField">
				<cfsavecontent variable="local.referraldata['#local.thisField.attributes.fieldReference#']">
					<cfoutput>
					<cfif structKeyExists(local.thisField, "value")>
						<cfif isArray(local.thisField.value)>
							<cfloop array="#local.thisField.value#" index="local.thisItem">
								#local.thisItem#
							</cfloop>
						<cfelse>
							#local.thisField.value#
						</cfif>
					</cfif>
					</cfoutput>
				</cfsavecontent>
			</cfloop>

			<cfquery name="local.qryTemplate" datasource="#application.dsn.platformMail.dsn#">
				select et.templateID, et.templateName,et.templateDescription,et.languageID,
				et.dateCreated, smsTemplateContent.rawContent,et.contentID,sat.activeTemplateID,sat.isDefault
				from dbo.sms_usageTypeTemplates et
				left join dbo.sms_subuserMessagingServiceUsageActiveTemplates sat on sat.templateID = et.templateID
				cross apply membercentral.dbo.fn_getContent(et.contentID,1) as smsTemplateContent
				where et.templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
				and et.triggerID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.triggerID#">
				and et.usageTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageTypeID#">
				and et.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
			</cfquery>
			
			<cfif local.qryTemplate.recordcount is 0>	
				<cfset local.data.success = false>
				<cfreturn local.data>
			</cfif>

			<cfset local.content = local.qryTemplate.rawContent >

			<cfif application.MCEnvironment eq "production">
				<cfset local.thisHostname = local.rc.mainHostName>
			<cfelse>
				<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			</cfif>

			<!--- Member data --->
			<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.orgID, memberID=local.qryGetReferralData.memberID, content="")>
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.siteCode)>
		
			<cfset local.tempMemberData = { memberid=local.qryGetReferralData.memberID, firstName=local.qryGetReferralData.FirstName, 
											middleName=local.qryGetReferralData.MiddleName, lastName=local.qryGetReferralData.LastName, 
											company=local.qryGetReferralData.Company, suffix=local.qryGetReferralData.Suffix, 
											professionalsuffix=local.qryGetReferralData.professionalsuffix, 
											prefix=local.qryGetReferralData.Prefix, membernumber=local.qryGetReferralData.membernumber, 
											orgcode=local.orgcode, siteID=local.siteID,
											hostname=local.thisHostname, useRemoteLogin=local.mc_siteInfo.useRemoteLogin}>
			
											<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
				<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
					<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.qryMemberFields[local.thisColumn.Name][1],true)>
				</cfif>	
			</cfloop>							
		
			<cfset local.strArgs = { content=local.content, memberdata=local.tempMemberData, referraldata=local.referraldata, orgcode=local.orgcode, sitecode=local.siteCode}>	
			<cfset local.renderedEmailContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).CONTENT>
			
			<cfset local.data.rawContent = renderedEmailContent>
			<cfset local.data.success = true>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getDefaultTemplates" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="templateID" type="numeric" required="true">
		<cfargument name="triggerID" type="numeric" required="true">
		<cfargument name="usageTypeID" type="numeric" required="true">

		<cfquery name="local.qryDefaultTemplate" datasource="#application.dsn.platformMail.dsn#">
			select et.templateID, sua.isDefault 
			from dbo.sms_usageTypeTemplates et
			inner join dbo.sms_subuserMessagingServiceUsageActiveTemplates sua ON sua.templateID = et.templateID 
				and sua.templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
			where et.triggerID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.triggerID#">
			and et.usageTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageTypeID#">
			and et.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>	
		
		<cfreturn local.qryDefaultTemplate>
	</cffunction>

	<cffunction name="getMessagingService" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="usageTypeCode" type="string" required="yes">

		<cfset var local.qrySMSSetup = "">

		<cfquery name="local.qrySMSSetup" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @usageTypeCode varchar(100);

			<cfset local.usageTypeCode = arguments.usageTypeCode>		

			SELECT sms.messagingServiceID
			FROM dbo.sms_usageTypes t
			INNER JOIN sms_subuserMessagingServiceAllowedUsages smsau 
				ON t.usageTypeID = smsau.usageTypeID
				AND t.usageTypeCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.usageTypeCode#">
			INNER JOIN sms_subuserMessagingServices sms 
				ON smsau.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				AND sms.isActive = 1
				AND smsau.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				AND sms.messagingServiceID = smsau.messagingServiceID;			

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySMSSetup>
	</cffunction>

	<cffunction name="setTextMessageNotification" access="public" output="false" returntype="query">
		<cfargument name="messagingServiceID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="controllingSiteResourceID" type="numeric" required="yes">
		<cfargument name="name" type="string" required="yes">
		<cfargument name="status" type="string" required="yes">
		<cfargument name="verificationRequired" type="numeric" required="yes">
		<cfargument name="usageTypeCode" type="string" required="yes">
		<cfargument name="referenceID" type="numeric" required="yes">

		<cfset var local.qrySMSSetup = "">

		<cfquery name="local.qrySMSSetup" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @usageTypeID INT;

			<cfset local.usageTypeCode = arguments.usageTypeCode>		

			SELECT TOP 1  @usageTypeID = t.usageTypeID 
			FROM dbo.sms_usageTypes t
			INNER JOIN sms_subuserMessagingServiceAllowedUsages smsau 
				ON t.usageTypeID = smsau.usageTypeID
				AND t.usageTypeCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.usageTypeCode#">
			INNER JOIN sms_subuserMessagingServices sms 
				ON smsau.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				AND sms.isActive = 1
				AND smsau.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				AND sms.messagingServiceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messagingServiceID#">

			<cfstoredproc datasource="#application.dsn.platformMail.dsn#" procedure="sms_updateSubuserMessagingServiceUsages">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.messagingServiceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.controllingSiteResourceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.name#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.verificationRequired#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageTypeCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
			</cfstoredproc>

			SELECT 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfreturn qrySMSSetup>
	</cffunction>

	<cffunction name="getMessagingServiceUsageByTypeCode" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="usageTypeCode" type="string" required="yes">

		<cfset var local.qrySMSSetup = "">

		<cfquery name="local.qrySMSSetup" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			<cfset local.usageTypeCode = arguments.usageTypeCode>		

			SELECT smsu.messagingServiceID
			FROM sms_usageTypes t
			INNER JOIN sms_subuserMessagingServiceUsages smsu 
			ON t.usageTypeID= smsu.usageTypeID AND smsu.status = 'A'
			AND t.usageTypeCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.usageTypeCode#">
			AND smsu.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			INNER JOIN sms_subuserMessagingServiceAllowedUsages smsau
			ON t.usageTypeID= smsau.usageTypeID
			AND smsau.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND smsau.messagingServiceID = smsu.messagingServiceID
			AND smsau.isApprovedByProvider = 1
			INNER JOIN sms_subuserMessagingServices sms 
			ON sms.isActive = 1
			AND sms.messagingServiceID = smsau.messagingServiceID

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySMSSetup>
	</cffunction>

	<cffunction name="getTemplateTriggersList" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="usageTypeCode" type="string" required="yes">

		<cfset var local = {}>

		<cfquery name="local.qryTemplateTriggersList" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;


			SELECT st.triggerID, st.triggerName, sut.usageTypeID, sut.usageType 
			FROM dbo.sms_usageTypeTriggers st
			INNER JOIN sms_usageTypes sut ON sut.usageTypeID = st.usageTypeID
			INNER JOIN sms_subuserMessagingServiceAllowedUsages ssu ON ssu.usageTypeID = st.usageTypeID
				AND ssu.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			WHERE usageTypeCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageTypeCode#"> 	

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryTemplateTriggersList>
	</cffunction>

	<cffunction name="getSMSTemplateDetails" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="usageTypeCode" type="string" required="yes">
		<cfargument name="triggerCode" type="string" required="yes">

		<cfquery name="local.qryActiveSMSTemplates" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE 
			@usageTypeCode VARCHAR(15) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageTypeCode#">,
			@triggerCode VARCHAR(50) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.triggerCode#">,
			@siteID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;			


			SELECT smsu.messagingServiceID,smsutt1.templateID, smsutt1.contentID, smsutt1.languageID, smsu.status
			FROM dbo.sms_usageTypes t
			INNER JOIN sms_usageTypeTriggers stg 
				ON stg.usageTypeID = t.usageTypeID 
				AND stg.triggerCode = @triggerCode
			INNER JOIN sms_subuserMessagingServiceUsages smsu 
				ON smsu.usageTypeID= stg.usageTypeID AND smsu.siteID = @siteID
			INNER JOIN dbo.sms_subuserMessagingServiceAllowedUsages smsau
				ON t.usageTypeID= smsau.usageTypeID
				AND smsau.siteID = @siteID
				AND smsau.messagingServiceID = smsu.messagingServiceID
				AND smsau.isApprovedByProvider = 1
			INNER JOIN dbo.sms_subuserMessagingServices sms 
				ON sms.messagingServiceID =  smsau.messagingServiceID  
				AND sms.isActive = 1
				AND sms.messagingServiceID = smsau.messagingServiceID
			INNER JOIN dbo.sms_subuserMessagingServiceUsageActiveTemplates smsuat
				ON smsuat.siteID = @siteID AND smsuat.usageID = smsu.usageID
				AND smsuat.usageID = smsu.usageID
			INNER JOIN sms_usageTypeTemplates smsutt1
				ON smsutt1.siteID = @siteID
				AND smsutt1.usageTypeID = t.usageTypeID
				AND smsutt1.triggerID = smsuat.triggerID
				AND smsutt1.status = 1 
				AND smsutt1.templateID = smsuat.templateID		
			WHERE t.usageTypeCode = @usageTypeCode
							

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryActiveSMSTemplates>
	</cffunction>

	<cffunction name="saveSMSUsageParticipants" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="usageTypeCode" type="string" required="yes">
		<cfargument name="phoneNumberE164" type="string" required="yes">
		<cfargument name="preferredLanguageID" type="numeric" required="yes">
		<cfargument name="statusID" type="numeric" required="yes">
		<cfargument name="referenceType" type="string" required="yes">
		<cfargument name="referenceID" type="numeric" required="yes">

		<cfset local.qrySMSSetup = getMessagingServiceUsageByTypeCode(arguments.siteID,arguments.usageTypeCode)>

		<cfif local.qrySMSSetup.recordcount gt 0>
			<cfquery name="local.qryActiveSMSTemplates" datasource="#application.dsn.platformMail.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @usageID  INT, @usageTypeID INT;

				SELECT TOP 1 @usageID = su.usageID, @usageTypeID = ut.usageTypeID 
				FROM dbo.sms_subuserMessagingServiceUsages su
				INNER JOIN dbo.sms_usageTypes ut
				ON ut.usageTypeID = su.usageTypeID 
					AND ut.usageTypeCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageTypeCode#">
				WHERE su.siteID  =  <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;							

				INSERT INTO dbo.sms_subuserMessagingServiceUsageParticipants
				(usageID,messagingServiceID,siteID,phoneNumberE164,preferredLangageID,statusID,dateCreated,dateUpdated,
				referenceType,referenceID) 
				VALUES (
					@usageID,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySMSSetup['messagingServiceID']#">,					
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.phoneNumberE164#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.preferredLanguageID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.statusID#">,
					getdate(),getdate(),
					<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">					
				);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>		

		<cfreturn true>
	</cffunction>

	<cffunction name="getMessagingServiceRecipientStatusIDByName" access="public" output="false" returntype="numeric">
		<cfargument name="statusName" type="string" required="yes">

		<cfquery name="local.qryMessagingServiceRecipientStatus" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT statusID FROM dbo.sms_messagingServiceRecipientStatuses WHERE
			status = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.statusName#">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.statusID = 0>
		<cfif local.qryMessagingServiceRecipientStatus.recordcount gt 0>
			<cfset local.statusID = local.qryMessagingServiceRecipientStatus.statusID>
		</cfif>

		<cfreturn local.statusID>
	</cffunction>
</cfcomponent>