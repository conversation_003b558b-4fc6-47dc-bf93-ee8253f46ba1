<cfoutput>
<cfloop query="local.qrySponsors">
	<cfset local.imageSource = "">
	<cfif val(local.qrySponsors.featureImageID) gt 0 AND fileExists("#local.featuredThumbImageFullRootPath##local.qrySponsors.featureImageID#-#local.qrySponsors.featureImageSizeID#.#local.qrySponsors.fileExtension#")>
		<cfset local.imageSource = "#local.featuredThumbImageRootPath##local.qrySponsors.featureImageID#-#local.qrySponsors.featureImageSizeID#.#local.qrySponsors.fileExtension#">
	</cfif>

	<table style="margin-bottom:10px;">
		<tr>
			<cfif len(local.imageSource)>
				<td width="25%" style="vertical-align:top;padding-right:10px;">
					<cfif len(local.qrySponsors.sponsorUrl)>
						<a href="#local.qrySponsors.sponsorUrl#" target="_blank"><img src="#local.imageSource#" alt="" style="width:auto;"></a>
					<cfelse>
						<img src="#local.imageSource#" alt="" style="width:auto;">
					</cfif>
				</td>
			</cfif>
			<td style="vertical-align:top;">
				<cfif len(local.qrySponsors.sponsorUrl)>
					<a href="#local.qrySponsors.sponsorUrl#" target="_blank"><b>#local.qrySponsors.sponsorName#</b></a>
				<cfelse>
					<b>#local.qrySponsors.sponsorName#</b>
				</cfif>
				<cfif len(local.qrySponsors.sponsorContent)>
					<div>#local.qrySponsors.sponsorContent#</div>
				</cfif>
			</td>
		</tr>
	</table>
</cfloop>
</cfoutput>