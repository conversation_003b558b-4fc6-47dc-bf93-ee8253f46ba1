/* tsApps styles and overrides */
@import url("/assets/common/css/tsApps.css");


/* Include in Editor: Start */
.Header1{font-family: 'Raleway-Medium';font-size: 24px;color:#66C8D8;font-weight:400}
.Header2{font-family: 'Raleway-Medium';font-size: 31.5px;color:#333333;font-weight:700}
.Header3{font-family: 'Raleway-Medium';font-size: 24.5px;color:#333333;font-weight:700}
.Header4{font-family: 'Raleway-Medium';font-size: 17.5px;color:#333333;font-weight:700}
.Header5{font-family: 'Raleway-Medium';font-size: 14px;color:#333333;font-weight:700}
.Header6{font-family: 'Raleway-Medium';font-size: 16px;color:#333333;font-weight:700}
.Normal{font-size: 14px;font-family: 'Raleway-Medium';color:#333333;}
.Address{font-size: 14px;font-family: 'Raleway-Medium';color:#333333;}
.Formatted{font:13px Monaco;color:#333333;}
.Normal div{font-size: 14px;font-family: 'Raleway-Medium';color:#333333;}

.BodyText{font:13px verdana;color:#555555;}

.HeaderText{font:21px georgia;color:#555555}
.HeaderTextBlue{font:21px georgia;color:#425C78;}

.HomepageLeftHeader{ font-family:Georgia, "Times New Roman", Times, serif; font-size:21px; color:#555555; }
.HomepageLeftContent{ font-family:Verdana, Geneva, sans-serif; font-size:12px; line-height:17px; color:#555555; }

.CustomerStoriesHeader{ font-family:Georgia, "Times New Roman", Times, serif; font-size:21px; color:#425c78; }
.CustomerStoriesContent{ font-family:Verdana, Geneva, sans-serif; font-size:12px; line-height:17px; color:#555555; }

.WhatsNewHeader{ font-family:Georgia, "Times New Roman", Times, serif; font-size:21px; color:#657f9a; }
.WhatsNewContent{ font-family:Verdana, Geneva, sans-serif; font-size:12px; line-height:17px; color:#657f9a; }

.BlueSectionHeader{ font-size: 23px; font-family: georgia; color: #ffffff; }
.BlueSectionContent{ font-size: 14px; font-family: verdana; color: #ffffff; }

.MainSubHeader{ color: #425c78; font-size: 24px; font-family: georgia; margin-bottom: 10px; }
.MainSubContent{ font-family:verdana; color: #555555; font-size: 13px; }
.MainSubSideHeader{ font-family:verdana; color: #555555; font-size: 16px; }

.GreySectionContent{ font-family:Verdana, Geneva, sans-serif; font-size:12px; line-height:17px; color:#eeeeee; }

.Link{ font-family:Verdana, Geneva, sans-serif; font-size:12px; color:#6d9e41; text-decoration:underline; }
/* Include in Editor: Stop */

a .Header1, .Header1 a {color:#66C8D8 }
a .Header1:hover, .Header1 a:hover { color:#1389CA}