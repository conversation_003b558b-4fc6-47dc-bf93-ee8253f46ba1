<cfoutput>
<cfloop query="local.qrySponsors">
	<cfset local.imageSource = "">
	<cfif val(local.qrySponsors.featureImageID) gt 0 AND fileExists("#local.featuredThumbImageFullRootPath##local.qrySponsors.featureImageID#-#local.qrySponsors.featureImageSizeID#.#local.qrySponsors.fileExtension#")>
		<cfset local.imageSource = "#local.featuredThumbImageRootPath##local.qrySponsors.featureImageID#-#local.qrySponsors.featureImageSizeID#.#local.qrySponsors.fileExtension#">
	</cfif>
	
	<div class="sponsorContainer sw-mb-3">
		<div class="sw-mb-2">
			<cfif len(local.qrySponsors.sponsorUrl)>
				<a href="#local.qrySponsors.sponsorUrl#" class="swPrimary" target="_blank"><b>#local.qrySponsors.sponsorName#</b></a>
			<cfelse>
				<b>#local.qrySponsors.sponsorName#</b>
			</cfif>
		</div>
		<cfif len(local.imageSource) or len(local.qrySponsors.sponsorContent)>
			<div class="row-fluid">
				<cfif len(local.imageSource)>
					<div class="span2 sponsorImage">
						<cfif len(local.qrySponsors.sponsorUrl)>
							<a href="#local.qrySponsors.sponsorUrl#" target="_blank"><img src="#local.imageSource#" alt=""></a>
						<cfelse>
							<img src="#local.imageSource#" alt="">
						</cfif>
					</div>
				</cfif>
				<cfif len(local.qrySponsors.sponsorContent)>
					<div class="<cfif len(local.imageSource)>span10<cfelse>span12</cfif> sponsorDesc">
						#local.qrySponsors.sponsorContent#
					</div>
				</cfif>
			</div>
		</cfif>
	</div>
</cfloop>
</cfoutput>