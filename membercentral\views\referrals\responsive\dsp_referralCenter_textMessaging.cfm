<cfoutput>
    <style type="text/css">
        .smsCntWrap{
            position: relative;
            border: 1px solid ##9E9E9E;
            padding-top: 10px;
            min-height: 50px;
            padding-left: 10px;
        }
        .smsCntWrap .addNumberBtn{
            text-decoration:underline;
            cursor:pointer;
            color:##08c !important
        }
        .smsCntWrap img.addPhoneIcon{
            cursor:pointer;
        }
        .hidden{
            display:none;
        }
        .divWrap{
            padding: 11px;
            border: 1px solid ##cccccc;
            margin: 8px;
            border-radius: 8px;
            box-shadow: 0 4px 29px rgba(0, 0, 0, 2%), 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        .phoneLabel{
            padding-top:3px;
            padding-bottom:3px;
        }
        ##MFAPhNo{
            margin-top:3px;
            margin-bottom:3px;
        }
        .phoneNumberCol {
            float: left;
            width: 30.33%;
            padding: 10px;
            }

        .phoneNumberRow:after {
            content: "";
            display: table;
            clear: both;
        }
        .phoneNumberRow .strPhoneVerificationLabel{
            font-size:18px;
            font-weight:bold;
            padding-left: 5px;
        }
        .phoneNumberRow .strPhoneNumberLabel{
            font-size:18px;
        }
        .phoneNumberColAction{
            text-align:right;
        }
        .verifyIcon{
            margin-top: -5px;
        }

        @media only screen and (min-width: 720px) and (max-width: 978px) {

            .phoneNumberCol{
                width:26.33%;
            }
        }
        @media only screen and  (max-width: 719px) {

            .phoneNumberCol{
                width:100% !important;
            }
            .phoneNumberCol.phoneNumberColAction{
                text-align:left !important;
            }
        }
        ##sw_listing > div.login-card.login-p-3.smsCntWrap{
            padding:10px !important;
        }
    </style>
    <script>
        var viewDirectory = '';
    </script>
    <div class="form-group row-fluid">
        <div class="span12" style="text-align:left;">
            <h4><b>Text Messaging</b></h4>
        </div>
        #variables.structData.feReferralCenterInstruction#	
    </div>
    <div class="login-card login-p-3 smsCntWrap">
        <img class="addPhoneIcon" src="/assets/common/images/details_open.png" width="23" height="19" align="absmiddle" border="0">
        &nbsp; <a href="javascript:void(0);" class="addNumberBtn"> Add Number </a>
        <div id="editMFAPhNo" class="login-card login-p-3 MFAPhNoTool divWrap hidden editDivWrap">
            <div class="login-mb-1 phoneLabel">Enter your phone number</div>
            <div class="login-d-flex">
                <div class="login-col-auto">
                    <input type="tel" id="MFAPhNo" name="MFAPhNo" value="" style="height:auto;">
                </div>
                <div class="login-col-auto" style="margin-top:5px !important;">
                    <button type="button" id="btnVerifyMFAPhNo" class="btn btn-primary" onclick="addNumber();">Add</button>
                    <button type="button" id="btnCancelMFAPhNo" class="btn btn-default" onclick="cancelAdd();">Cancel</button>
                </div>
            </div>
            <div class="login-mt-2 login-text-center" style="width:210px;margin-top:5px;">
                <div id="MFAPhNoValidMsg" class="alert alert-success" style="display:none;">Valid</div>
                <div id="MFAPhNoErrMsg" class="alert alert-error" style="display:none;"></div>
            </div>
            <br/>
        </div>
        
        <cfloop query="variables.structData.queryMSPhonenumbers">
            <div class="login-card login-p-3 MFAPhNoTool  divWrap">

		
                <div class="phoneNumberRow">
                    <div class="phoneNumberCol">
                        <cfif variables.structData.queryMSPhonenumbers.isVerified eq 1>
                            <img class="verifyIcon" src="/assets/common/images/sheild-login-good.png" width="21" height="17" align="absmiddle" border="0">
                        <cfelse>
                            <img class="verifyIcon" src="/assets/common/images/sheild-login-danger-x-mark.png" width="21" height="17" align="absmiddle" border="0">
                          </cfif>
                        <span class="strPhoneVerificationLabel">Phone Verification</span>
                    </div>
                    <div class="phoneNumberCol">
                        
                        <span class="strPhoneNumberLabel">
                            #variables.structData.queryMSPhonenumbers.phoneNumber#
                        </span>
                    </div>
                    <div class="phoneNumberCol phoneNumberColAction">
                        <button type="button" id="btnEditMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="btn btn-primary" onclick="initializeEditNumber(#variables.structData.queryMSPhonenumbers.smsNotificationID#);">Edit</button>
                        &nbsp;<button type="button" id="btnDeleteMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="btn btn-danger" onclick="deleteNumber(#variables.structData.queryMSPhonenumbers.smsNotificationID#,'#variables.structData.queryMSPhonenumbers.phoneNumber#');">Delete</button>
                    </div>
                  </div>

                
            
                <div  id="editMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="hidden editDivWrap">
                    <div class="login-mb-1 phoneLabel">Enter your phone number</div>
                    <div class="login-d-flex">
                        <div class="login-col-auto">
                            <input type="tel" id="MFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" name="MFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" value="#variables.structData.queryMSPhonenumbers.phoneNumber#" style="height:auto;">
                        </div>
                        <div class="login-col-auto" style="margin-top:5px !important;">
                            <button type="button" id="btnVerifyMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="btn btn-primary" onclick="updateNumber(#variables.structData.queryMSPhonenumbers.smsNotificationID#);">Save</button>
                            <button type="button" id="btnCancelMFAPhNo#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="btn btn-default" onclick="cancelEditList(#variables.structData.queryMSPhonenumbers.smsNotificationID#);">Cancel</button>
                        </div>
                    </div>
                    <div class="login-mt-2 login-text-center" style="width:210px;margin-top:5px !important;">
                        <div id="MFAPhNoValidMsg" class="login-text-success" style="display:none;">Valid</div>
                        <div id="MFAPhNoErrMsg#variables.structData.queryMSPhonenumbers.smsNotificationID#" class="alert alert-error" style="display:none;"></div>
                    </div>
                    <br/>
                </div>
            </div>


        </cfloop>
    </div>
    
    </cfoutput>
    <cfinclude template="../commonMFASMSJS.cfm">