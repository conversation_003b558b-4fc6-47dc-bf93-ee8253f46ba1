/*
 RequireJS 2.1.17 Copyright (c) 2010-2015, The Dojo Foundation All Rights Reserved.
 Available via the MIT or new BSD license.
 see: http://github.com/jrburke/requirejs for details
*/

/*! @source http://purl.eligrey.com/github/classList.js/blob/master/classList.js*/

/*! @source http://purl.eligrey.com/github/Xccessors/blob/master/xccessors-standard.js*/

/*!
 * VERSION: beta 0.2.3
 * DATE: 2013-07-10
 * UPDATES AND DOCS AT: http://www.greensock.com
 *
 * @license Copyright (c) 2008-2013, GreenSock. All rights reserved.
 * SplitText is a Club GreenSock membership benefit; You must have a valid membership to use
 * this code without violating the terms of use. Visit http://www.greensock.com/club/ to sign up or get more details.
 * This work is subject to the software agreement that was issued with your membership.
 * 
 * @author: <PERSON>, <EMAIL>
 */

/**
 * @license
 * Lo-Dash 2.4.1 (Custom Build) lodash.com/license | Underscore.js 1.5.2 underscorejs.org/LICENSE
 * Build: `lodash modern -o ./dist/lodash.js`
 */

/*! jQuery v2.1.1 -css,-css/addGetHookIf,-css/curCSS,-css/defaultDisplay,-css/hiddenVisibleSelectors,-css/support,-css/swap,-css/var/cssExpand,-css/var/getStyles,-css/var/isHidden,-css/var/rmargin,-css/var/rnumnonpx,-effects,-effects/Tween,-effects/animatedSelector,-dimensions,-offset,-deprecated,-event-alias,-wrap | (c) 2005, 2014 jQuery Foundation, Inc. | jquery.org/license */

/*!
 * Exoskeleton.js 0.6.3
 * (c) 2013 Paul Miller <http://paulmillr.com>
 * Based on Backbone.js
 * (c) 2010-2013 Jeremy Ashkenas, DocumentCloud
 * Exoskeleton may be freely distributed under the MIT license.
 * For all details and documentation: <http://exosjs.com>
 */

//     (c) 2015 Superhero Cheesecake, Rian Verhagen
//     Superhero.js depends on Backbone http://backbone.js

/*!
 * VERSION: 1.16.1
 * DATE: 2015-03-13
 * UPDATES AND DOCS AT: http://greensock.com
 * 
 * Includes all of the following: TweenLite, TweenMax, TimelineLite, TimelineMax, EasePack, CSSPlugin, RoundPropsPlugin, BezierPlugin, AttrPlugin, DirectionalRotationPlugin
 *
 * @license Copyright (c) 2008-2015, GreenSock. All rights reserved.
 * This work is subject to the terms at http://greensock.com/standard-license or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 * 
 * @author: Jack Doyle, <EMAIL>
 **/

// Snap.svg 0.4.1
//
// Copyright (c) 2013 – 2015 Adobe Systems Incorporated. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// build: 2015-04-13

// Copyright (c) 2013 Adobe Systems Incorporated. All rights reserved.
// 
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// 
// http://www.apache.org/licenses/LICENSE-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// ┌────────────────────────────────────────────────────────────┐ \\
// │ Eve 0.4.2 - JavaScript Events Library                      │ \\
// ├────────────────────────────────────────────────────────────┤ \\
// │ Author Dmitry Baranovskiy (http://dmitry.baranovskiy.com/) │ \\
// └────────────────────────────────────────────────────────────┘ \\

// Copyright (c) 2013 Adobe Systems Incorporated. All rights reserved.
// 
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// 
// http://www.apache.org/licenses/LICENSE-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Copyright (c) 2013 - 2015 Adobe Systems Incorporated. All rights reserved.
// 
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// 
// http://www.apache.org/licenses/LICENSE-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Copyright (c) 2013 Adobe Systems Incorporated. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Copyright (c) 2014 Adobe Systems Incorporated. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
* @license PreloadJS
* Visit http://createjs.com/ for documentation, updates and examples.
*
* Copyright (c) 2011-2013 gskinner.com, inc.
*
* Distributed under the terms of the MIT license.
* http://www.opensource.org/licenses/mit-license.html
*
* This notice shall be included in all copies or substantial portions of the Software.
*/

var requirejs,require,define;(function(ba){function G(e){return"[object Function]"===K.call(e)}function H(e){return"[object Array]"===K.call(e)}function v(e,t){if(e){var n;for(n=0;n<e.length&&(!e[n]||!t(e[n],n,e));n+=1);}}function T(e,t){if(e){var n;for(n=e.length-1;-1<n&&(!e[n]||!t(e[n],n,e));n-=1);}}function t(e,t){return fa.call(e,t)}function m(e,n){return t(e,n)&&e[n]}function B(e,n){for(var r in e)if(t(e,r)&&n(e[r],r))break}function U(e,n,r,i){return n&&B(n,function(n,s){if(r||!t(e,s))i&&"object"==typeof n&&n&&!H(n)&&!G(n)&&!(n instanceof RegExp)?(e[s]||(e[s]={}),U(e[s],n,r,i)):e[s]=n}),e}function u(e,t){return function(){return t.apply(e,arguments)}}function ca(e){throw e}function da(e){if(!e)return e;var t=ba;return v(e.split("."),function(e){t=t[e]}),t}function C(e,t,n,r){return t=Error(t+"\nhttp://requirejs.org/docs/errors.html#"+e),t.requireType=e,t.requireModules=r,n&&(t.originalError=n),t}function ga(e){function n(e,t,n){var r,i,s,o,u,a,f,l,t=t&&t.split("/"),c=A.map,h=c&&c["*"];if(e){e=e.split("/"),i=e.length-1,A.nodeIdCompat&&Q.test(e[i])&&(e[i]=e[i].replace(Q,"")),"."===e[0].charAt(0)&&t&&(i=t.slice(0,t.length-1),e=i.concat(e)),i=e;for(s=0;s<i.length;s++)(o=i[s],"."===o)?(i.splice(s,1),s-=1):".."===o&&0!==s&&(1!==s||".."!==i[2])&&".."!==i[s-1]&&0<s&&(i.splice(s-1,2),s-=2);e=e.join("/")}if(n&&c&&(t||h)){i=e.split("/"),s=i.length;e:for(;0<s;s-=1){u=i.slice(0,s).join("/");if(t)for(o=t.length;0<o;o-=1)if(n=m(c,t.slice(0,o).join("/")))if(n=m(n,u)){r=n,a=s;break e}!f&&h&&m(h,u)&&(f=m(h,u),l=s)}!r&&f&&(r=f,a=l),r&&(i.splice(0,a,r),e=i.join("/"))}return(r=m(A.pkgs,e))?r:e}function r(e){z&&v(document.getElementsByTagName("script"),function(t){if(t.getAttribute("data-requiremodule")===e&&t.getAttribute("data-requirecontext")===x.contextName)return t.parentNode.removeChild(t),!0})}function i(e){var t=m(A.paths,e);if(t&&H(t)&&1<t.length)return t.shift(),x.require.undef(e),x.makeRequire(null,{skipMap:!0})([e]),!0}function s(e){var t,n=e?e.indexOf("!"):-1;return-1<n&&(t=e.substring(0,n),e=e.substring(n+1,e.length)),[t,e]}function o(e,t,r,i){var o,u,a=null,f=t?t.name:null,l=e,c=!0,h="";return e||(c=!1,e="_@r"+(q+=1)),e=s(e),a=e[0],e=e[1],a&&(a=n(a,f,i),u=m(j,a)),e&&(a?h=u&&u.normalize?u.normalize(e,function(e){return n(e,f,i)}):-1===e.indexOf("!")?n(e,f,i):e:(h=n(e,f,i),e=s(h),a=e[0],h=e[1],r=!0,o=x.nameToUrl(h))),r=a&&!u&&!r?"_unnormalized"+(W+=1):"",{prefix:a,name:h,parentMap:t,unnormalized:!!r,url:o,originalName:l,isDefine:c,id:(a?a+"!"+h:h)+r}}function a(e){var t=e.id,n=m(O,t);return n||(n=O[t]=new x.Module(e)),n}function f(e,n,r){var i=e.id,s=m(O,i);t(j,i)&&(!s||s.defineEmitComplete)?"defined"===n&&r(j[i]):(s=a(e),s.error&&"error"===n)?r(s.error):s.on(n,r)}function l(e,t){var n=e.requireModules,r=!1;t?t(e):(v(n,function(t){if(t=m(O,t))t.error=e,t.events.error&&(r=!0,t.emit("error",e))}),!r)&&g.onError(e)}function c(){R.length&&(ha.apply(P,[P.length,0].concat(R)),R=[])}function h(e){delete O[e],delete _[e]}function p(e,t,n){var r=e.map.id;e.error?e.emit("error",e.error):(t[r]=!0,v(e.depMaps,function(r,i){var s=r.id,o=m(O,s);o&&!e.depMatched[i]&&!n[s]&&(m(t,s)?(e.defineDep(i,j[s]),e.check()):p(o,t,n))}),n[r]=!0)}function d(){var e,t,n=(e=1e3*A.waitSeconds)&&x.startTime+e<(new Date).getTime(),s=[],o=[],u=!1,a=!0;if(!E){E=!0,B(_,function(e){var f=e.map,l=f.id;if(e.enabled&&(f.isDefine||o.push(e),!e.error))if(!e.inited&&n)i(l)?u=t=!0:(s.push(l),r(l));else if(!e.inited&&e.fetched&&f.isDefine&&(u=!0,!f.prefix))return a=!1});if(n&&s.length)return e=C("timeout","Load timeout for modules: "+s,null,s),e.contextName=x.contextName,l(e);a&&v(o,function(e){p(e,{},{})}),(!n||t)&&u&&(z||ea)&&!L&&(L=setTimeout(function(){L=0,d()},50)),E=!1}}function y(e){t(j,e[0])||a(o(e[0],null,!0)).init(e[1],e[2])}function b(e){var e=e.currentTarget||e.srcElement,t=x.onScriptLoad;return e.detachEvent&&!Y?e.detachEvent("onreadystatechange",t):e.removeEventListener("load",t,!1),t=x.onScriptError,(!e.detachEvent||Y)&&e.removeEventListener("error",t,!1),{node:e,id:e&&e.getAttribute("data-requiremodule")}}function w(){var e;for(c();P.length;){e=P.shift();if(null===e[0])return l(C("mismatch","Mismatched anonymous define() module: "+e[e.length-1]));y(e)}}var E,S,x,k,L,A={waitSeconds:7,baseUrl:"./",paths:{},bundles:{},pkgs:{},shim:{},config:{}},O={},_={},D={},P=[],j={},F={},I={},q=1,W=1;return k={require:function(e){return e.require?e.require:e.require=x.makeRequire(e.map)},exports:function(e){e.usingExports=!0;if(e.map.isDefine)return e.exports?j[e.map.id]=e.exports:e.exports=j[e.map.id]={}},module:function(e){return e.module?e.module:e.module={id:e.map.id,uri:e.map.url,config:function(){return m(A.config,e.map.id)||{}},exports:e.exports||(e.exports={})}}},S=function(e){this.events=m(D,e.id)||{},this.map=e,this.shim=m(A.shim,e.id),this.depExports=[],this.depMaps=[],this.depMatched=[],this.pluginMaps={},this.depCount=0},S.prototype={init:function(e,t,n,r){r=r||{},this.inited||(this.factory=t,n?this.on("error",n):this.events.error&&(n=u(this,function(e){this.emit("error",e)})),this.depMaps=e&&e.slice(0),this.errback=n,this.inited=!0,this.ignore=r.ignore,r.enabled||this.enabled?this.enable():this.check())},defineDep:function(e,t){this.depMatched[e]||(this.depMatched[e]=!0,this.depCount-=1,this.depExports[e]=t)},fetch:function(){if(!this.fetched){this.fetched=!0,x.startTime=(new Date).getTime();var e=this.map;if(!this.shim)return e.prefix?this.callPlugin():this.load();x.makeRequire(this.map,{enableBuildCallback:!0})(this.shim.deps||[],u(this,function(){return e.prefix?this.callPlugin():this.load()}))}},load:function(){var e=this.map.url;F[e]||(F[e]=!0,x.load(this.map.id,e))},check:function(){if(this.enabled&&!this.enabling){var e,t,n=this.map.id;t=this.depExports;var r=this.exports,i=this.factory;if(this.inited){if(this.error)this.emit("error",this.error);else if(!this.defining){this.defining=!0;if(1>this.depCount&&!this.defined){if(G(i)){if(this.events.error&&this.map.isDefine||g.onError!==ca)try{r=x.execCb(n,i,t,r)}catch(s){e=s}else r=x.execCb(n,i,t,r);this.map.isDefine&&void 0===r&&((t=this.module)?r=t.exports:this.usingExports&&(r=this.exports));if(e)return e.requireMap=this.map,e.requireModules=this.map.isDefine?[this.map.id]:null,e.requireType=this.map.isDefine?"define":"require",l(this.error=e)}else r=i;this.exports=r,this.map.isDefine&&!this.ignore&&(j[n]=r,g.onResourceLoad)&&g.onResourceLoad(x,this.map,this.depMaps),h(n),this.defined=!0}this.defining=!1,this.defined&&!this.defineEmitted&&(this.defineEmitted=!0,this.emit("defined",this.exports),this.defineEmitComplete=!0)}}else this.fetch()}},callPlugin:function(){var e=this.map,r=e.id,i=o(e.prefix);this.depMaps.push(i),f(i,"defined",u(this,function(i){var s,c;c=m(I,this.map.id);var p=this.map.name,d=this.map.parentMap?this.map.parentMap.name:null,v=x.makeRequire(e.parentMap,{enableBuildCallback:!0});if(this.map.unnormalized){if(i.normalize&&(p=i.normalize(p,function(e){return n(e,d,!0)})||""),i=o(e.prefix+"!"+p,this.map.parentMap),f(i,"defined",u(this,function(e){this.init([],function(){return e},null,{enabled:!0,ignore:!0})})),c=m(O,i.id))this.depMaps.push(i),this.events.error&&c.on("error",u(this,function(e){this.emit("error",e)})),c.enable()}else c?(this.map.url=x.nameToUrl(c),this.load()):(s=u(this,function(e){this.init([],function(){return e},null,{enabled:!0})}),s.error=u(this,function(e){this.inited=!0,this.error=e,e.requireModules=[r],B(O,function(e){0===e.map.id.indexOf(r+"_unnormalized")&&h(e.map.id)}),l(e)}),s.fromText=u(this,function(n,i){var u=e.name,f=o(u),c=M;i&&(n=i),c&&(M=!1),a(f),t(A.config,r)&&(A.config[u]=A.config[r]);try{g.exec(n)}catch(h){return l(C("fromtexteval","fromText eval for "+r+" failed: "+h,h,[r]))}c&&(M=!0),this.depMaps.push(f),x.completeLoad(u),v([u],s)}),i.load(e.name,v,s,A))})),x.enable(i,this),this.pluginMaps[i.id]=i},enable:function(){_[this.map.id]=this,this.enabling=this.enabled=!0,v(this.depMaps,u(this,function(e,n){var r,i;if("string"==typeof e){e=o(e,this.map.isDefine?this.map:this.map.parentMap,!1,!this.skipMap),this.depMaps[n]=e;if(r=m(k,e.id)){this.depExports[n]=r(this);return}this.depCount+=1,f(e,"defined",u(this,function(e){this.defineDep(n,e),this.check()})),this.errback?f(e,"error",u(this,this.errback)):this.events.error&&f(e,"error",u(this,function(e){this.emit("error",e)}))}r=e.id,i=O[r],!t(k,r)&&i&&!i.enabled&&x.enable(e,this)})),B(this.pluginMaps,u(this,function(e){var t=m(O,e.id);t&&!t.enabled&&x.enable(e,this)})),this.enabling=!1,this.check()},on:function(e,t){var n=this.events[e];n||(n=this.events[e]=[]),n.push(t)},emit:function(e,t){v(this.events[e],function(e){e(t)}),"error"===e&&delete this.events[e]}},x={config:A,contextName:e,registry:O,defined:j,urlFetched:F,defQueue:P,Module:S,makeModuleMap:o,nextTick:g.nextTick,onError:l,configure:function(e){e.baseUrl&&"/"!==e.baseUrl.charAt(e.baseUrl.length-1)&&(e.baseUrl+="/");var t=A.shim,n={paths:!0,bundles:!0,config:!0,map:!0};B(e,function(e,t){n[t]?(A[t]||(A[t]={}),U(A[t],e,!0,!0)):A[t]=e}),e.bundles&&B(e.bundles,function(e,t){v(e,function(e){e!==t&&(I[e]=t)})}),e.shim&&(B(e.shim,function(e,n){H(e)&&(e={deps:e}),(e.exports||e.init)&&!e.exportsFn&&(e.exportsFn=x.makeShimExports(e)),t[n]=e}),A.shim=t),e.packages&&v(e.packages,function(e){var t,e="string"==typeof e?{name:e}:e;t=e.name,e.location&&(A.paths[t]=e.location),A.pkgs[t]=e.name+"/"+(e.main||"main").replace(ia,"").replace(Q,"")}),B(O,function(e,t){!e.inited&&!e.map.unnormalized&&(e.map=o(t))}),(e.deps||e.callback)&&x.require(e.deps||[],e.callback)},makeShimExports:function(e){return function(){var t;return e.init&&(t=e.init.apply(ba,arguments)),t||e.exports&&da(e.exports)}},makeRequire:function(i,s){function u(n,r,f){var c,h;return s.enableBuildCallback&&r&&G(r)&&(r.__requireJsBuild=!0),"string"==typeof n?G(r)?l(C("requireargs","Invalid require call"),f):i&&t(k,n)?k[n](O[i.id]):g.get?g.get(x,n,i,u):(c=o(n,i,!1,!0),c=c.id,t(j,c)?j[c]:l(C("notloaded",'Module name "'+c+'" has not been loaded yet for context: '+e+(i?"":". Use require([])")))):(w(),x.nextTick(function(){w(),h=a(o(null,i)),h.skipMap=s.skipMap,h.init(n,r,f,{enabled:!0}),d()}),u)}return s=s||{},U(u,{isBrowser:z,toUrl:function(e){var t,r=e.lastIndexOf("."),s=e.split("/")[0];return-1!==r&&("."!==s&&".."!==s||1<r)&&(t=e.substring(r,e.length),e=e.substring(0,r)),x.nameToUrl(n(e,i&&i.id,!0),t,!0)},defined:function(e){return t(j,o(e,i,!1,!0).id)},specified:function(e){return e=o(e,i,!1,!0).id,t(j,e)||t(O,e)}}),i||(u.undef=function(e){c();var t=o(e,i,!0),n=m(O,e);r(e),delete j[e],delete F[t.url],delete D[e],T(P,function(t,n){t[0]===e&&P.splice(n,1)}),n&&(n.events.defined&&(D[e]=n.events),h(e))}),u},enable:function(e){m(O,e.id)&&a(e).enable()},completeLoad:function(e){var n,r,s=m(A.shim,e)||{},o=s.exports;for(c();P.length;){r=P.shift();if(null===r[0]){r[0]=e;if(n)break;n=!0}else r[0]===e&&(n=!0);y(r)}r=m(O,e);if(!n&&!t(j,e)&&r&&!r.inited){if(A.enforceDefine&&(!o||!da(o)))return i(e)?void 0:l(C("nodefine","No define call for "+e,null,[e]));y([e,s.deps||[],s.exportsFn])}d()},nameToUrl:function(e,t,n){var r,i,s;(r=m(A.pkgs,e))&&(e=r);if(r=m(I,e))return x.nameToUrl(r,t,n);if(g.jsExtRegExp.test(e))r=e+(t||"");else{r=A.paths,e=e.split("/");for(i=e.length;0<i;i-=1)if(s=e.slice(0,i).join("/"),s=m(r,s)){H(s)&&(s=s[0]),e.splice(0,i,s);break}r=e.join("/"),r+=t||(/^data\:|\?/.test(r)||n?"":".js"),r=("/"===r.charAt(0)||r.match(/^[\w\+\.\-]+:/)?"":A.baseUrl)+r}return A.urlArgs?r+((-1===r.indexOf("?")?"?":"&")+A.urlArgs):r},load:function(e,t){g.load(x,e,t)},execCb:function(e,t,n,r){return t.apply(r,n)},onScriptLoad:function(e){if("load"===e.type||ja.test((e.currentTarget||e.srcElement).readyState))N=null,e=b(e),x.completeLoad(e.id)},onScriptError:function(e){var t=b(e);if(!i(t.id))return l(C("scripterror","Script error for: "+t.id,e,[t.id]))}},x.require=x.makeRequire(),x}var g,x,y,D,I,E,N,J,s,O,ka=/(\/\*([\s\S]*?)\*\/|([^:]|^)\/\/(.*)$)/mg,la=/[^.]\s*require\s*\(\s*["']([^'"\s]+)["']\s*\)/g,Q=/\.js$/,ia=/^\.\//;x=Object.prototype;var K=x.toString,fa=x.hasOwnProperty,ha=Array.prototype.splice,z="undefined"!=typeof window&&"undefined"!=typeof navigator&&!!window.document,ea=!z&&"undefined"!=typeof importScripts,ja=z&&"PLAYSTATION 3"===navigator.platform?/^complete$/:/^(complete|loaded)$/,Y="undefined"!=typeof opera&&"[object Opera]"===opera.toString(),F={},q={},R=[],M=!1;if("undefined"==typeof define){if("undefined"!=typeof requirejs){if(G(requirejs))return;q=requirejs,requirejs=void 0}"undefined"!=typeof require&&!G(require)&&(q=require,require=void 0),g=requirejs=function(e,t,n,r){var i,s="_";return!H(e)&&"string"!=typeof e&&(i=e,H(t)?(e=t,t=n,n=r):e=[]),i&&i.context&&(s=i.context),(r=m(F,s))||(r=F[s]=g.s.newContext(s)),i&&r.configure(i),r.require(e,t,n)},g.config=function(e){return g(e)},g.nextTick="undefined"!=typeof setTimeout?function(e){setTimeout(e,4)}:function(e){e()},require||(require=g),g.version="2.1.17",g.jsExtRegExp=/^\/|:|\?|\.js$/,g.isBrowser=z,x=g.s={contexts:F,newContext:ga},g({}),v(["toUrl","undef","defined","specified"],function(e){g[e]=function(){var t=F._;return t.require[e].apply(t,arguments)}}),z&&(y=x.head=document.getElementsByTagName("head")[0],D=document.getElementsByTagName("base")[0])&&(y=x.head=D.parentNode),g.onError=ca,g.createNode=function(e){var t=e.xhtml?document.createElementNS("http://www.w3.org/1999/xhtml","html:script"):document.createElement("script");return t.type=e.scriptType||"text/javascript",t.charset="utf-8",t.async=!0,t},g.load=function(e,t,n){var r=e&&e.config||{};if(z)return r=g.createNode(r,t,n),r.setAttribute("data-requirecontext",e.contextName),r.setAttribute("data-requiremodule",t),r.attachEvent&&!(r.attachEvent.toString&&0>r.attachEvent.toString().indexOf("[native code"))&&!Y?(M=!0,r.attachEvent("onreadystatechange",e.onScriptLoad)):(r.addEventListener("load",e.onScriptLoad,!1),r.addEventListener("error",e.onScriptError,!1)),r.src=n,J=r,D?y.insertBefore(r,D):y.appendChild(r),J=null,r;if(ea)try{importScripts(n),e.completeLoad(t)}catch(i){e.onError(C("importscripts","importScripts failed for "+t+" at "+n,i,[t]))}},z&&!q.skipDataMain&&T(document.getElementsByTagName("script"),function(e){y||(y=e.parentNode);if(I=e.getAttribute("data-main"))return s=I,q.baseUrl||(E=s.split("/"),s=E.pop(),O=E.length?E.join("/")+"/":"./",q.baseUrl=O),s=s.replace(Q,""),g.jsExtRegExp.test(s)&&(s=I),q.deps=q.deps?q.deps.concat(s):[s],!0}),define=function(e,t,n){var r,i;"string"!=typeof e&&(n=t,t=e,e=null),H(t)||(n=t,t=null),!t&&G(n)&&(t=[],n.length&&(n.toString().replace(ka,"").replace(la,function(e,n){t.push(n)}),t=(1===n.length?["require"]:["require","exports","module"]).concat(t))),M&&((r=J)||(N&&"interactive"===N.readyState||T(document.getElementsByTagName("script"),function(e){if("interactive"===e.readyState)return N=e}),r=N),r&&(e||(e=r.getAttribute("data-requiremodule")),i=F[r.getAttribute("data-requirecontext")])),(i?i.defQueue:R).push([e,t,n])},define.amd={jQuery:!0},g.exec=function(b){return eval(b)},g(q)}})(this),define("vendor/require-2.1.17.min.js",function(){}),"document"in self&&!("classList"in document.createElement("_"))&&function(e){"use strict";if(!("Element"in e))return;var t="classList",n="prototype",r=e.Element[n],i=Object,s=String[n].trim||function(){return this.replace(/^\s+|\s+$/g,"")},o=Array[n].indexOf||function(e){var t=0,n=this.length;for(;t<n;t++)if(t in this&&this[t]===e)return t;return-1},u=function(e,t){this.name=e,this.code=DOMException[e],this.message=t},a=function(e,t){if(t==="")throw new u("SYNTAX_ERR","An invalid or illegal string was specified");if(/\s/.test(t))throw new u("INVALID_CHARACTER_ERR","String contains an invalid character");return o.call(e,t)},f=function(e){var t=s.call(e.getAttribute("class")||""),n=t?t.split(/\s+/):[],r=0,i=n.length;for(;r<i;r++)this.push(n[r]);this._updateClassName=function(){e.setAttribute("class",this.toString())}},l=f[n]=[],c=function(){return new f(this)};u[n]=Error[n],l.item=function(e){return this[e]||null},l.contains=function(e){return e+="",a(this,e)!==-1},l.add=function(){var e=arguments,t=0,n=e.length,r,i=!1;do r=e[t]+"",a(this,r)===-1&&(this.push(r),i=!0);while(++t<n);i&&this._updateClassName()},l.remove=function(){var e=arguments,t=0,n=e.length,r,i=!1;do{r=e[t]+"";var s=a(this,r);s!==-1&&(this.splice(s,1),i=!0)}while(++t<n);i&&this._updateClassName()},l.toggle=function(e,t){e+="";var n=this.contains(e),r=n?t!==!0&&"remove":t!==!1&&"add";return r&&this[r](e),!n},l.toString=function(){return this.join(" ")};if(i.defineProperty){var h={get:c,enumerable:!0,configurable:!0};try{i.defineProperty(r,t,h)}catch(p){p.number===-2146823252&&(h.enumerable=!1,i.defineProperty(r,t,h))}}else i[n].__defineGetter__&&r.__defineGetter__(t,c)}(self),define("vendor/polyfills/classlist.min",function(){}),function(e){"use strict";function t(e,t,n){return e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent?e.attachEvent("on"+t,n):void 0}function n(e,t){var n,r;for(n=0,r=e.length;r>n;n++)if(e[n]===t)return!0;return!1}function r(e,t){var n;e.createTextRange?(n=e.createTextRange(),n.move("character",t),n.select()):e.selectionStart&&(e.focus(),e.setSelectionRange(t,t))}function i(e,t){try{return e.type=t,!0}catch(n){return!1}}e.Placeholders={Utils:{addEventListener:t,inArray:n,moveCaret:r,changeType:i}}}(this),function(e){"use strict";function t(){}function n(){try{return document.activeElement}catch(e){}}function r(e,t){var n,r,i=!!t&&e.value!==t,s=e.value===e.getAttribute(D);return(i||s)&&"true"===e.getAttribute(P)?(e.removeAttribute(P),e.value=e.value.replace(e.getAttribute(D),""),e.className=e.className.replace(_,""),r=e.getAttribute(q),parseInt(r,10)>=0&&(e.setAttribute("maxLength",r),e.removeAttribute(q)),n=e.getAttribute(H),n&&(e.type=n),!0):!1}function i(e){var t,n,r=e.getAttribute(D);return""===e.value&&r?(e.setAttribute(P,"true"),e.value=r,e.className+=" "+M,n=e.getAttribute(q),n||(e.setAttribute(q,e.maxLength),e.removeAttribute("maxLength")),t=e.getAttribute(H),t?e.type="text":"password"===e.type&&X.changeType(e,"text")&&e.setAttribute(H,"password"),!0):!1}function s(e,t){var n,r,i,s,o,u,a;if(e&&e.getAttribute(D))t(e);else for(i=e?e.getElementsByTagName("input"):v,s=e?e.getElementsByTagName("textarea"):m,n=i?i.length:0,r=s?s.length:0,a=0,u=n+r;u>a;a++)o=n>a?i[a]:s[a-n],t(o)}function o(e){s(e,r)}function u(e){s(e,i)}function a(e){return function(){g&&e.value===e.getAttribute(D)&&"true"===e.getAttribute(P)?X.moveCaret(e,0):r(e)}}function f(e){return function(){i(e)}}function l(e){return function(t){return b=e.value,"true"===e.getAttribute(P)&&b===e.getAttribute(D)&&X.inArray(A,t.keyCode)?(t.preventDefault&&t.preventDefault(),!1):void 0}}function c(e){return function(){r(e,b),""===e.value&&(e.blur(),X.moveCaret(e,0))}}function h(e){return function(){e===n()&&e.value===e.getAttribute(D)&&"true"===e.getAttribute(P)&&X.moveCaret(e,0)}}function p(e){return function(){o(e)}}function d(e){e.form&&(T=e.form,"string"==typeof T&&(T=document.getElementById(T)),T.getAttribute(B)||(X.addEventListener(T,"submit",p(T)),T.setAttribute(B,"true"))),X.addEventListener(e,"focus",a(e)),X.addEventListener(e,"blur",f(e)),g&&(X.addEventListener(e,"keydown",l(e)),X.addEventListener(e,"keyup",c(e)),X.addEventListener(e,"click",h(e))),e.setAttribute(j,"true"),e.setAttribute(D,S),(g||e!==n())&&i(e)}var v,m,g,y,b,w,E,S,x,T,N,C,k,L=["text","search","url","tel","email","password","number","textarea"],A=[27,33,34,35,36,37,38,39,40,8,46],O="#ccc",M="placeholdersjs",_=RegExp("(?:^|\\s)"+M+"(?!\\S)"),D="data-placeholder-value",P="data-placeholder-active",H="data-placeholder-type",B="data-placeholder-submit",j="data-placeholder-bound",F="data-placeholder-focus",I="data-placeholder-live",q="data-placeholder-maxlength",R=document.createElement("input"),U=document.getElementsByTagName("head")[0],z=document.documentElement,W=e.Placeholders,X=W.Utils;if(W.nativeSupport=void 0!==R.placeholder,!W.nativeSupport){for(v=document.getElementsByTagName("input"),m=document.getElementsByTagName("textarea"),g="false"===z.getAttribute(F),y="false"!==z.getAttribute(I),w=document.createElement("style"),w.type="text/css",E=document.createTextNode("."+M+" { color:"+O+"; }"),w.styleSheet?w.styleSheet.cssText=E.nodeValue:w.appendChild(E),U.insertBefore(w,U.firstChild),k=0,C=v.length+m.length;C>k;k++)N=v.length>k?v[k]:m[k-v.length],S=N.attributes.placeholder,S&&(S=S.nodeValue,S&&X.inArray(L,N.type)&&d(N));x=setInterval(function(){for(k=0,C=v.length+m.length;C>k;k++)N=v.length>k?v[k]:m[k-v.length],S=N.attributes.placeholder,S?(S=S.nodeValue,S&&X.inArray(L,N.type)&&(N.getAttribute(j)||d(N),(S!==N.getAttribute(D)||"password"===N.type&&!N.getAttribute(H))&&("password"===N.type&&!N.getAttribute(H)&&X.changeType(N,"text")&&N.setAttribute(H,"password"),N.value===N.getAttribute(D)&&(N.value=S),N.setAttribute(D,S)))):N.getAttribute(P)&&(r(N),N.removeAttribute(D));y||clearInterval(x)},100)}X.addEventListener(e,"beforeunload",function(){W.disable()}),W.disable=W.nativeSupport?t:o,W.enable=W.nativeSupport?t:u}(this),define("vendor/polyfills/placeholders.min",function(){}),Function.prototype.bind||(Function.prototype.bind=function(e){"use strict";if(typeof this!="function")throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var t=Array.prototype.slice.call(arguments,1),n=this,r=function(){},i=function(){return n.apply(this instanceof r&&e?this:e,t.concat(Array.prototype.slice.call(arguments)))};return r.prototype=this.prototype,i.prototype=new r,i}),function(){"use strict";var e=Object.prototype,t=e.__defineGetter__,n=e.__defineSetter__,r=e.__lookupGetter__,i=e.__lookupSetter__,s=e.hasOwnProperty;t&&n&&r&&i&&(Object.defineProperty||(Object.defineProperty=function(e,o,u){if(arguments.length<3)throw new TypeError("Arguments not optional");o+="";if(s.call(u,"value")){!r.call(e,o)&&!i.call(e,o)&&(e[o]=u.value);if(s.call(u,"get")||s.call(u,"set"))throw new TypeError("Cannot specify an accessor and a value")}if(!(u.writable&&u.enumerable&&u.configurable))throw new TypeError("This implementation of Object.defineProperty does not support false for configurable, enumerable, or writable.");return u.get&&t.call(e,o,u.get),u.set&&n.call(e,o,u.set),e}),Object.getOwnPropertyDescriptor||(Object.getOwnPropertyDescriptor=function(e,t){if(arguments.length<2)throw new TypeError("Arguments not optional.");t+="";var n={configurable:!0,enumerable:!0,writable:!0},o=r.call(e,t),u=i.call(e,t);return s.call(e,t)?!o&&!u?(n.value=e[t],n):(delete n.writable,n.get=n.set=undefined,o&&(n.get=o),u&&(n.set=u),n):n}),Object.defineProperties||(Object.defineProperties=function(e,t){var n;for(n in t)s.call(t,n)&&Object.defineProperty(e,n,t[n])}))}();if(!document.documentElement.dataset&&(!Object.getOwnPropertyDescriptor(Element.prototype,"dataset")||!Object.getOwnPropertyDescriptor(Element.prototype,"dataset").get)){var propDescriptor={enumerable:!0,get:function(){"use strict";var e,t=this,n,r,i,s,o,u=this.attributes,a=u.length,f=function(e){return e.charAt(1).toUpperCase()},l=function(){return this},c=function(e,t){return typeof t!="undefined"?this.setAttribute(e,t):this.removeAttribute(e)};try{(({})).__defineGetter__("test",function(){}),n={}}catch(h){n=document.createElement("div")}for(e=0;e<a;e++){o=u[e];if(o&&o.name&&/^data-\w[\w\-]*$/.test(o.name)){r=o.value,i=o.name,s=i.substr(5).replace(/-./g,f);try{Object.defineProperty(n,s,{enumerable:this.enumerable,get:l.bind(r||""),set:c.bind(t,i)})}catch(p){n[s]=r}}}return n}};try{Object.defineProperty(Element.prototype,"dataset",propDescriptor)}catch(e){propDescriptor.enumerable=!1,Object.defineProperty(Element.prototype,"dataset",propDescriptor)}}define("vendor/polyfills/html5-dataset",function(){}),function(e){"use strict";var t=e.GreenSockGlobals||e,n=function(e){var n,r=e.split("."),i=t;for(n=0;r.length>n;n++)i[r[n]]=i=i[r[n]]||{};return i},r=n("com.greensock.utils"),i=function(e){var t=e.nodeType,n="";if(1===t||9===t||11===t){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=i(e)}else if(3===t||4===t)return e.nodeValue;return n},s=document,o=s.defaultView?s.defaultView.getComputedStyle:function(){},u=/([A-Z])/g,a=function(e,t,n,r){var i;return(n=n||o(e,null))?(e=n.getPropertyValue(t.replace(u,"-$1").toLowerCase()),i=e||n.length?e:n[t]):e.currentStyle&&(n=e.currentStyle,i=n[t]),r?i:parseInt(i,10)||0},f=function(e){return e.length&&e[0]&&(e[0].nodeType&&e[0].style&&!e.nodeType||e[0].length&&e[0][0])?!0:!1},l=function(e){var t,n,r,i=[],s=e.length;for(t=0;s>t;t++)if(n=e[t],f(n))for(r=n.length,r=0;n.length>r;r++)i.push(n[r]);else i.push(n);return i},c=")eefec303079ad17405c",h=/(?:<br>|<br\/>|<br \/>)/gi,p=s.all&&!s.addEventListener,d="<div style='position:relative;display:inline-block;"+(p?"*display:inline;*zoom:1;'":"'"),v=function(e){e=e||"";var t=-1!==e.indexOf("++"),n=1;return t&&(e=e.split("++").join("")),function(){return d+(e?" class='"+e+(t?n++:"")+"'>":">")}},m=r.SplitText=t.SplitText=function(e,t){if("string"==typeof e&&(e=m.selector(e)),!e)throw"cannot split a null element.";this.elements=f(e)?l(e):[e],this.chars=[],this.words=[],this.lines=[],this._originals=[],this.vars=t||{},this.split(t)},g=function(e,t,n,r,u){h.test(e.innerHTML)&&(e.innerHTML=e.innerHTML.replace(h,c));var f,l,p,d,m,g,y,b,w,E,S,x,T,N=i(e),C=t.type||t.split||"chars,words,lines",k=-1!==C.indexOf("lines")?[]:null,L=-1!==C.indexOf("words"),A=-1!==C.indexOf("chars"),O="absolute"===t.position||t.absolute===!0,M=O?"&#173; ":" ",_=-999,D=o(e),P=a(e,"paddingLeft",D),H=a(e,"borderBottomWidth",D)+a(e,"borderTopWidth",D),B=a(e,"borderLeftWidth",D)+a(e,"borderRightWidth",D),j=a(e,"paddingTop",D)+a(e,"paddingBottom",D),F=a(e,"paddingLeft",D)+a(e,"paddingRight",D),I=a(e,"textAlign",D,!0),q=e.clientHeight,R=e.clientWidth,U=N.length,z="</div>",W=v(t.wordsClass),X=v(t.charsClass),V=-1!==(t.linesClass||"").indexOf("++"),$=t.linesClass;for(V&&($=$.split("++").join("")),p=W(),d=0;U>d;d++)g=N.charAt(d),")"===g&&N.substr(d,20)===c?(p+=z+"<BR/>",d!==U-1&&(p+=" "+W()),d+=19):" "===g&&" "!==N.charAt(d-1)&&d!==U-1?(p+=z,d!==U-1&&(p+=M+W())):p+=A&&" "!==g?X()+g+"</div>":g;for(e.innerHTML=p+z,m=e.getElementsByTagName("*"),U=m.length,y=[],d=0;U>d;d++)y[d]=m[d];if(k||O)for(d=0;U>d;d++)b=y[d],l=b.parentNode===e,(l||O||A&&!L)&&(w=b.offsetTop,k&&l&&w!==_&&"BR"!==b.nodeName&&(f=[],k.push(f),_=w),O&&(b._x=b.offsetLeft,b._y=w,b._w=b.offsetWidth,b._h=b.offsetHeight),k&&(L!==l&&A||(f.push(b),b._x-=P),l&&d&&(y[d-1]._wordEnd=!0)));for(d=0;U>d;d++)b=y[d],l=b.parentNode===e,"BR"!==b.nodeName?(O&&(S=b.style,L||l||(b._x+=b.parentNode._x,b._y+=b.parentNode._y),S.left=b._x+"px",S.top=b._y+"px",S.position="absolute",S.display="block",S.width=b._w+1+"px",S.height=b._h+"px"),L?l?r.push(b):A&&n.push(b):l?(e.removeChild(b),y.splice(d--,1),U--):!l&&A&&(w=!k&&!O&&b.nextSibling,e.appendChild(b),w||e.appendChild(s.createTextNode(" ")),n.push(b))):k||O?(e.removeChild(b),y.splice(d--,1),U--):L||e.appendChild(b);if(k){for(O&&(E=s.createElement("div"),e.appendChild(E),x=E.offsetWidth+"px",w=E.offsetParent===e?0:e.offsetLeft,e.removeChild(E)),S=e.style.cssText,e.style.cssText="display:none;";e.firstChild;)e.removeChild(e.firstChild);for(T=!O||!L&&!A,d=0;k.length>d;d++){for(f=k[d],E=s.createElement("div"),E.style.cssText="display:block;text-align:"+I+";position:"+(O?"absolute;":"relative;"),$&&(E.className=$+(V?d+1:"")),u.push(E),U=f.length,m=0;U>m;m++)"BR"!==f[m].nodeName&&(b=f[m],E.appendChild(b),T&&(b._wordEnd||L)&&E.appendChild(s.createTextNode(" ")),O&&(0===m&&(E.style.top=b._y+"px",E.style.left=P+w+"px"),b.style.top="0px",w&&(b.style.left=b._x-w+"px")));L||A||(E.innerHTML=i(E).split(String.fromCharCode(160)).join(" ")),O&&(E.style.width=x,E.style.height=b._h+"px"),e.appendChild(E)}e.style.cssText=S}O&&(q>e.clientHeight&&(e.style.height=q-j+"px",q>e.clientHeight&&(e.style.height=q+H+"px")),R>e.clientWidth&&(e.style.width=R-F+"px",R>e.clientWidth&&(e.style.width=R+B+"px")))},y=m.prototype;y.split=function(e){this.isSplit&&this.revert(),this.vars=e||this.vars,this._originals.length=this.chars.length=this.words.length=this.lines.length=0;for(var t=0;this.elements.length>t;t++)this._originals[t]=this.elements[t].innerHTML,g(this.elements[t],this.vars,this.chars,this.words,this.lines);return this.isSplit=!0,this},y.revert=function(){if(!this._originals)throw"revert() call wasn't scoped properly.";for(var e=this._originals.length;--e>-1;)this.elements[e].innerHTML=this._originals[e];return this.chars=[],this.words=[],this.lines=[],this.isSplit=!1,this},m.selector=e.$||e.jQuery||function(t){return e.$?(m.selector=e.$,e.$(t)):s?s.getElementById("#"===t.charAt(0)?t.substr(1):t):t}}(window||{}),define("vendor/splittext",function(){}),function(){function e(e,t,n){n=(n||0)-1;for(var r=e?e.length:0;++n<r;)if(e[n]===t)return n;return-1}function t(t,n){var r=typeof n;if(t=t.l,"boolean"==r||null==n)return t[n]?0:-1;"number"!=r&&"string"!=r&&(r="object");var i="number"==r?n:g+n;return t=(t=t[r])&&t[i],"object"==r?t&&-1<e(t,n)?0:-1:t?0:-1}function n(e){var t=this.l,n=typeof e;if("boolean"==n||null==e)t[e]=!0;else{"number"!=n&&"string"!=n&&(n="object");var r="number"==n?e:g+e,t=t[n]||(t[n]={});"object"==n?(t[r]||(t[r]=[])).push(e):t[r]=!0}}function r(e){return e.charCodeAt(0)}function i(e,t){for(var n=e.m,r=t.m,i=-1,s=n.length;++i<s;){var o=n[i],u=r[i];if(o!==u){if(o>u||typeof o=="undefined")return 1;if(o<u||typeof u=="undefined")return-1}}return e.n-t.n}function s(e){var t=-1,r=e.length,i=e[0],s=e[r/2|0],o=e[r-1];if(i&&typeof i=="object"&&s&&typeof s=="object"&&o&&typeof o=="object")return!1;for(i=a(),i["false"]=i["null"]=i["true"]=i.undefined=!1,s=a(),s.k=e,s.l=i,s.push=n;++t<r;)s.push(e[t]);return s}function o(e){return"\\"+V[e]}function u(){return d.pop()||[]}function a(){return v.pop()||{k:null,l:null,m:null,"false":!1,n:0,"null":!1,number:null,object:null,push:null,string:null,"true":!1,"undefined":!1,o:null}}function f(e){e.length=0,d.length<b&&d.push(e)}function l(e){var t=e.l;t&&l(t),e.k=e.l=e.m=e.object=e.number=e.string=e.o=null,v.length<b&&v.push(e)}function c(e,t,n){t||(t=0),typeof n=="undefined"&&(n=e?e.length:0);var r=-1;n=n-t||0;for(var i=Array(0>n?0:n);++r<n;)i[r]=e[t+r];return i}function h(n){function d(e,t,n){if(!e||!X[typeof e])return e;t=t&&typeof n=="undefined"?t:tt(t,n,3);for(var r=-1,i=X[typeof e]&&jn(e),s=i?i.length:0;++r<s&&(n=i[r],!1!==t(e[n],n,e)););return e}function v(e,t,n){var r;if(!e||!X[typeof e])return e;t=t&&typeof n=="undefined"?t:tt(t,n,3);for(r in e)if(!1===t(e[r],r,e))break;return e}function b(e,t,n){var r,i=e,s=i;if(!i)return s;for(var o=arguments,u=0,a=typeof n=="number"?2:o.length;++u<a;)if((i=o[u])&&X[typeof i])for(var f=-1,l=X[typeof i]&&jn(i),c=l?l.length:0;++f<c;)r=l[f],"undefined"==typeof s[r]&&(s[r]=i[r]);return s}function V(e,t,n){var r,i=e,s=i;if(!i)return s;var o=arguments,u=0,a=typeof n=="number"?2:o.length;if(3<a&&"function"==typeof o[a-2])var f=tt(o[--a-1],o[a--],2);else 2<a&&"function"==typeof o[a-1]&&(f=o[--a]);for(;++u<a;)if((i=o[u])&&X[typeof i])for(var l=-1,c=X[typeof i]&&jn(i),h=c?c.length:0;++l<h;)r=c[l],s[r]=f?f(s[r],i[r]):i[r];return s}function J(e){var t,n=[];if(!e||!X[typeof e])return n;for(t in e)yn.call(e,t)&&n.push(t);return n}function K(e){return e&&typeof e=="object"&&!Bn(e)&&yn.call(e,"__wrapped__")?e:new Q(e)}function Q(e,t){this.__chain__=!!t,this.__wrapped__=e}function G(e){function t(){if(r){var e=c(r);bn.apply(e,arguments)}if(this instanceof t){var s=et(n.prototype),e=n.apply(s,e||arguments);return Et(e)?e:s}return n.apply(i,e||arguments)}var n=e[0],r=e[2],i=e[4];return Hn(t,e),t}function Z(e,t,n,r,i){if(n){var s=n(e);if(typeof s!="undefined")return s}if(!Et(e))return e;var o=cn.call(e);if(!U[o])return e;var a=Dn[o];switch(o){case H:case B:return new a(+e);case F:case R:return new a(e);case q:return s=a(e.source,N.exec(e)),s.lastIndex=e.lastIndex,s}if(o=Bn(e),t){var l=!r;r||(r=u()),i||(i=u());for(var h=r.length;h--;)if(r[h]==e)return i[h];s=o?a(e.length):{}}else s=o?c(e):V({},e);return o&&(yn.call(e,"index")&&(s.index=e.index),yn.call(e,"input")&&(s.input=e.input)),t?(r.push(e),i.push(s),(o?At:d)(e,function(e,o){s[o]=Z(e,t,n,r,i)}),l&&(f(r),f(i)),s):s}function et(e){return Et(e)?Tn(e):{}}function tt(e,t,n){if(typeof e!="function")return Vt;if(typeof t!="undefined"&&"prototype"in e){var r=e.__bindData__;if(typeof r=="undefined"&&(Pn.funcNames&&(r=!e.name),r=r||!Pn.funcDecomp,!r)){var i=mn.call(e);Pn.funcNames||(r=!C.test(i)),r||(r=O.test(i),Hn(e,r))}if(!1===r||!0!==r&&1&r[1])return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)};case 4:return function(n,r,i,s){return e.call(t,n,r,i,s)}}return Wt(e,t)}return e}function nt(e){function t(){var e=a?o:this;if(i){var d=c(i);bn.apply(d,arguments)}return(s||l)&&(d||(d=c(arguments)),s&&bn.apply(d,s),l&&d.length<u)?(r|=16,nt([n,h?r:-4&r,d,null,o,u])):(d||(d=arguments),f&&(n=e[p]),this instanceof t?(e=et(n.prototype),d=n.apply(e,d),Et(d)?d:e):n.apply(e,d))}var n=e[0],r=e[1],i=e[2],s=e[3],o=e[4],u=e[5],a=1&r,f=2&r,l=4&r,h=8&r,p=n;return Hn(t,e),t}function rt(n,r){var i=-1,o=ht(),u=n?n.length:0,a=u>=y&&o===e,f=[];if(a){var c=s(r);c?(o=t,r=c):a=!1}for(;++i<u;)c=n[i],0>o(r,c)&&f.push(c);return a&&l(r),f}function it(e,t,n,r){r=(r||0)-1;for(var i=e?e.length:0,s=[];++r<i;){var o=e[r];if(o&&typeof o=="object"&&typeof o.length=="number"&&(Bn(o)||mt(o))){t||(o=it(o,t,n));var u=-1,a=o.length,f=s.length;for(s.length+=a;++u<a;)s[f++]=o[u]}else n||s.push(o)}return s}function st(e,t,n,r,i,s){if(n){var o=n(e,t);if(typeof o!="undefined")return!!o}if(e===t)return 0!==e||1/e==1/t;if(e===e&&!(e&&X[typeof e]||t&&X[typeof t]))return!1;if(null==e||null==t)return e===t;var a=cn.call(e),l=cn.call(t);if(a==D&&(a=I),l==D&&(l=I),a!=l)return!1;switch(a){case H:case B:return+e==+t;case F:return e!=+e?t!=+t:0==e?1/e==1/t:e==+t;case q:case R:return e==on(t)}if(l=a==P,!l){var c=yn.call(e,"__wrapped__"),h=yn.call(t,"__wrapped__");if(c||h)return st(c?e.__wrapped__:e,h?t.__wrapped__:t,n,r,i,s);if(a!=I)return!1;if(a=e.constructor,c=t.constructor,a!=c&&!(wt(a)&&a instanceof a&&wt(c)&&c instanceof c)&&"constructor"in e&&"constructor"in t)return!1}for(a=!i,i||(i=u()),s||(s=u()),c=i.length;c--;)if(i[c]==e)return s[c]==t;var p=0,o=!0;if(i.push(e),s.push(t),l){if(c=e.length,p=t.length,(o=p==c)||r)for(;p--;)if(l=c,h=t[p],r)for(;l--&&!(o=st(e[l],h,n,r,i,s)););else if(!(o=st(e[p],h,n,r,i,s)))break}else v(t,function(t,u,a){return yn.call(a,u)?(p++,o=yn.call(e,u)&&st(e[u],t,n,r,i,s)):void 0}),o&&!r&&v(e,function(e,t,n){return yn.call(n,t)?o=-1<--p:void 0});return i.pop(),s.pop(),a&&(f(i),f(s)),o}function ot(e,t,n,r,i){(Bn(t)?At:d)(t,function(t,s){var o,u,a=t,f=e[s];if(t&&((u=Bn(t))||Un(t))){for(a=r.length;a--;)if(o=r[a]==t){f=i[a];break}if(!o){var l;n&&(a=n(f,t),l=typeof a!="undefined")&&(f=a),l||(f=u?Bn(f)?f:[]:Un(f)?f:{}),r.push(t),i.push(f),l||ot(f,t,n,r,i)}}else n&&(a=n(f,t),typeof a=="undefined"&&(a=t)),typeof a!="undefined"&&(f=a);e[s]=f})}function ut(e,t){return e+vn(_n()*(t-e+1))}function at(n,r,i){var o=-1,a=ht(),c=n?n.length:0,h=[],p=!r&&c>=y&&a===e,d=i||p?u():h;for(p&&(d=s(d),a=t);++o<c;){var v=n[o],m=i?i(v,o,n):v;(r?!o||d[d.length-1]!==m:0>a(d,m))&&((i||p)&&d.push(m),h.push(v))}return p?(f(d.k),l(d)):i&&f(d),h}function ft(e){return function(t,n,r){var i={};n=K.createCallback(n,r,3),r=-1;var s=t?t.length:0;if(typeof s=="number")for(;++r<s;){var o=t[r];e(i,o,n(o,r,t),t)}else d(t,function(t,r,s){e(i,t,n(t,r,s),s)});return i}}function lt(e,t,n,r,i,s){var o=1&t,u=4&t,a=16&t,f=32&t;if(!(2&t||wt(e)))throw new un;a&&!n.length&&(t&=-17,a=n=!1),f&&!r.length&&(t&=-33,f=r=!1);var l=e&&e.__bindData__;return l&&!0!==l?(l=c(l),l[2]&&(l[2]=c(l[2])),l[3]&&(l[3]=c(l[3])),!o||1&l[1]||(l[4]=i),!o&&1&l[1]&&(t|=8),!u||4&l[1]||(l[5]=s),a&&bn.apply(l[2]||(l[2]=[]),n),f&&Sn.apply(l[3]||(l[3]=[]),r),l[1]|=t,lt.apply(null,l)):(1==t||17===t?G:nt)([e,t,n,r,i,s])}function ct(e){return Fn[e]}function ht(){var t=(t=K.indexOf)===Ft?e:t;return t}function pt(e){return typeof e=="function"&&hn.test(e)}function dt(e){var t,n;return e&&cn.call(e)==I&&(t=e.constructor,!wt(t)||t instanceof t)?(v(e,function(e,t){n=t}),typeof n=="undefined"||yn.call(e,n)):!1}function vt(e){return In[e]}function mt(e){return e&&typeof e=="object"&&typeof e.length=="number"&&cn.call(e)==D||!1}function gt(e,t,n){var r=jn(e),i=r.length;for(t=tt(t,n,3);i--&&(n=r[i],!1!==t(e[n],n,e)););return e}function yt(e){var t=[];return v(e,function(e,n){wt(e)&&t.push(n)}),t.sort()}function bt(e){for(var t=-1,n=jn(e),r=n.length,i={};++t<r;){var s=n[t];i[e[s]]=s}return i}function wt(e){return typeof e=="function"}function Et(e){return!!e&&!!X[typeof e]}function St(e){return typeof e=="number"||e&&typeof e=="object"&&cn.call(e)==F||!1}function xt(e){return typeof e=="string"||e&&typeof e=="object"&&cn.call(e)==R||!1}function Tt(e){for(var t=-1,n=jn(e),r=n.length,i=Gt(r);++t<r;)i[t]=e[n[t]];return i}function Nt(e,t,n){var r=-1,i=ht(),s=e?e.length:0,o=!1;return n=(0>n?An(0,s+n):n)||0,Bn(e)?o=-1<i(e,t,n):typeof s=="number"?o=-1<(xt(e)?e.indexOf(t,n):i(e,t,n)):d(e,function(e){return++r<n?void 0:!(o=e===t)}),o}function Ct(e,t,n){var r=!0;t=K.createCallback(t,n,3),n=-1;var i=e?e.length:0;if(typeof i=="number")for(;++n<i&&(r=!!t(e[n],n,e)););else d(e,function(e,n,i){return r=!!t(e,n,i)});return r}function kt(e,t,n){var r=[];t=K.createCallback(t,n,3),n=-1;var i=e?e.length:0;if(typeof i=="number")for(;++n<i;){var s=e[n];t(s,n,e)&&r.push(s)}else d(e,function(e,n,i){t(e,n,i)&&r.push(e)});return r}function Lt(e,t,n){t=K.createCallback(t,n,3),n=-1;var r=e?e.length:0;if(typeof r!="number"){var i;return d(e,function(e,n,r){return t(e,n,r)?(i=e,!1):void 0}),i}for(;++n<r;){var s=e[n];if(t(s,n,e))return s}}function At(e,t,n){var r=-1,i=e?e.length:0;if(t=t&&typeof n=="undefined"?t:tt(t,n,3),typeof i=="number")for(;++r<i&&!1!==t(e[r],r,e););else d(e,t);return e}function Ot(e,t,n){var r=e?e.length:0;if(t=t&&typeof n=="undefined"?t:tt(t,n,3),typeof r=="number")for(;r--&&!1!==t(e[r],r,e););else{var i=jn(e),r=i.length;d(e,function(e,n,s){return n=i?i[--r]:--r,t(s[n],n,s)})}return e}function Mt(e,t,n){var r=-1,i=e?e.length:0;if(t=K.createCallback(t,n,3),typeof i=="number")for(var s=Gt(i);++r<i;)s[r]=t(e[r],r,e);else s=[],d(e,function(e,n,i){s[++r]=t(e,n,i)});return s}function _t(e,t,n){var i=-1/0,s=i;if(typeof t!="function"&&n&&n[t]===e&&(t=null),null==t&&Bn(e)){n=-1;for(var o=e.length;++n<o;){var u=e[n];u>s&&(s=u)}}else t=null==t&&xt(e)?r:K.createCallback(t,n,3),At(e,function(e,n,r){n=t(e,n,r),n>i&&(i=n,s=e)});return s}function Dt(e,t,n,r){if(!e)return n;var i=3>arguments.length;t=K.createCallback(t,r,4);var s=-1,o=e.length;if(typeof o=="number")for(i&&(n=e[++s]);++s<o;)n=t(n,e[s],s,e);else d(e,function(e,r,s){n=i?(i=!1,e):t(n,e,r,s)});return n}function Pt(e,t,n,r){var i=3>arguments.length;return t=K.createCallback(t,r,4),Ot(e,function(e,r,s){n=i?(i=!1,e):t(n,e,r,s)}),n}function Ht(e){var t=-1,n=e?e.length:0,r=Gt(typeof n=="number"?n:0);return At(e,function(e){var n=ut(0,++t);r[t]=r[n],r[n]=e}),r}function Bt(e,t,n){var r;t=K.createCallback(t,n,3),n=-1;var i=e?e.length:0;if(typeof i=="number")for(;++n<i&&!(r=t(e[n],n,e)););else d(e,function(e,n,i){return!(r=t(e,n,i))});return!!r}function jt(e,t,n){var r=0,i=e?e.length:0;if(typeof t!="number"&&null!=t){var s=-1;for(t=K.createCallback(t,n,3);++s<i&&t(e[s],s,e);)r++}else if(r=t,null==r||n)return e?e[0]:p;return c(e,0,On(An(0,r),i))}function Ft(t,n,r){if(typeof r=="number"){var i=t?t.length:0;r=0>r?An(0,i+r):r||0}else if(r)return r=qt(t,n),t[r]===n?r:-1;return e(t,n,r)}function It(e,t,n){if(typeof t!="number"&&null!=t){var r=0,i=-1,s=e?e.length:0;for(t=K.createCallback(t,n,3);++i<s&&t(e[i],i,e);)r++}else r=null==t||n?1:An(0,t);return c(e,r)}function qt(e,t,n,r){var i=0,s=e?e.length:i;for(n=n?K.createCallback(n,r,1):Vt,t=n(t);i<s;)r=i+s>>>1,n(e[r])<t?i=r+1:s=r;return i}function Rt(e,t,n,r){return typeof t!="boolean"&&null!=t&&(r=n,n=typeof t!="function"&&r&&r[t]===e?null:t,t=!1),null!=n&&(n=K.createCallback(n,r,3)),at(e,t,n)}function Ut(){for(var e=1<arguments.length?arguments:arguments[0],t=-1,n=e?_t(Vn(e,"length")):0,r=Gt(0>n?0:n);++t<n;)r[t]=Vn(e,t);return r}function zt(e,t){var n=-1,r=e?e.length:0,i={};for(t||!r||Bn(e[0])||(t=[]);++n<r;){var s=e[n];t?i[s]=t[n]:s&&(i[s[0]]=s[1])}return i}function Wt(e,t){return 2<arguments.length?lt(e,17,c(arguments,2),null,t):lt(e,1,null,null,t)}function Xt(e,t,n){function r(){l&&dn(l),o=l=c=p,(v||d!==t)&&(h=$n(),u=e.apply(f,s),l||o||(s=f=null))}function i(){var n=t-($n()-a);0<n?l=wn(i,n):(o&&dn(o),n=c,o=l=c=p,n&&(h=$n(),u=e.apply(f,s),l||o||(s=f=null)))}var s,o,u,a,f,l,c,h=0,d=!1,v=!0;if(!wt(e))throw new un;if(t=An(0,t)||0,!0===n)var m=!0,v=!1;else Et(n)&&(m=n.leading,d="maxWait"in n&&(An(t,n.maxWait)||0),v="trailing"in n?n.trailing:v);return function(){if(s=arguments,a=$n(),f=this,c=v&&(l||!m),!1===d)var n=m&&!l;else{o||m||(h=a);var p=d-(a-h),g=0>=p;g?(o&&(o=dn(o)),h=a,u=e.apply(f,s)):o||(o=wn(r,p))}return g&&l?l=dn(l):l||t===d||(l=wn(i,t)),n&&(g=!0,u=e.apply(f,s)),!g||l||o||(s=f=null),u}}function Vt(e){return e}function $t(e,t,n){var r=!0,i=t&&yt(t);t&&(n||i.length)||(null==n&&(n=t),s=Q,t=e,e=K,i=yt(t)),!1===n?r=!1:Et(n)&&"chain"in n&&(r=n.chain);var s=e,o=wt(s);At(i,function(n){var i=e[n]=t[n];o&&(s.prototype[n]=function(){var t=this.__chain__,n=this.__wrapped__,o=[n];if(bn.apply(o,arguments),o=i.apply(e,o),r||t){if(n===o&&Et(o))return this;o=new s(o),o.__chain__=t}return o})})}function Jt(){}function Kt(e){return function(t){return t[e]}}function Qt(){return this.__wrapped__}n=n?Y.defaults($.Object(),n,Y.pick($,_)):$;var Gt=n.Array,Yt=n.Boolean,Zt=n.Date,en=n.Function,tn=n.Math,nn=n.Number,rn=n.Object,sn=n.RegExp,on=n.String,un=n.TypeError,an=[],fn=rn.prototype,ln=n._,cn=fn.toString,hn=sn("^"+on(cn).replace(/[.*+?^${}()|[\]\\]/g,"\\$&").replace(/toString| for [^\]]+/g,".*?")+"$"),pn=tn.ceil,dn=n.clearTimeout,vn=tn.floor,mn=en.prototype.toString,gn=pt(gn=rn.getPrototypeOf)&&gn,yn=fn.hasOwnProperty,bn=an.push,wn=n.setTimeout,En=an.splice,Sn=an.unshift,xn=function(){try{var e={},t=pt(t=rn.defineProperty)&&t,n=t(e,e,e)&&t}catch(r){}return n}(),Tn=pt(Tn=rn.create)&&Tn,Nn=pt(Nn=Gt.isArray)&&Nn,Cn=n.isFinite,kn=n.isNaN,Ln=pt(Ln=rn.keys)&&Ln,An=tn.max,On=tn.min,Mn=n.parseInt,_n=tn.random,Dn={};Dn[P]=Gt,Dn[H]=Yt,Dn[B]=Zt,Dn[j]=en,Dn[I]=rn,Dn[F]=nn,Dn[q]=sn,Dn[R]=on,Q.prototype=K.prototype;var Pn=K.support={};Pn.funcDecomp=!pt(n.a)&&O.test(h),Pn.funcNames=typeof en.name=="string",K.templateSettings={escape:/<%-([\s\S]+?)%>/g,evaluate:/<%([\s\S]+?)%>/g,interpolate:k,variable:"",imports:{_:K}},Tn||(et=function(){function e(){}return function(t){if(Et(t)){e.prototype=t;var r=new e;e.prototype=null}return r||n.Object()}}());var Hn=xn?function(e,t){W.value=t,xn(e,"__bindData__",W)}:Jt,Bn=Nn||function(e){return e&&typeof e=="object"&&typeof e.length=="number"&&cn.call(e)==P||!1},jn=Ln?function(e){return Et(e)?Ln(e):[]}:J,Fn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},In=bt(Fn),qn=sn("("+jn(In).join("|")+")","g"),Rn=sn("["+jn(Fn).join("")+"]","g"),Un=gn?function(e){if(!e||cn.call(e)!=I)return!1;var t=e.valueOf,n=pt(t)&&(n=gn(t))&&gn(n);return n?e==n||gn(e)==n:dt(e)}:dt,zn=ft(function(e,t,n){yn.call(e,n)?e[n]++:e[n]=1}),Wn=ft(function(e,t,n){(yn.call(e,n)?e[n]:e[n]=[]).push(t)}),Xn=ft(function(e,t,n){e[n]=t}),Vn=Mt,$n=pt($n=Zt.now)&&$n||function(){return(new Zt).getTime()},Jn=8==Mn(w+"08")?Mn:function(e,t){return Mn(xt(e)?e.replace(L,""):e,t||0)};return K.after=function(e,t){if(!wt(t))throw new un;return function(){return 1>--e?t.apply(this,arguments):void 0}},K.assign=V,K.at=function(e){for(var t=arguments,n=-1,r=it(t,!0,!1,1),t=t[2]&&t[2][t[1]]===e?1:r.length,i=Gt(t);++n<t;)i[n]=e[r[n]];return i},K.bind=Wt,K.bindAll=function(e){for(var t=1<arguments.length?it(arguments,!0,!1,1):yt(e),n=-1,r=t.length;++n<r;){var i=t[n];e[i]=lt(e[i],1,null,null,e)}return e},K.bindKey=function(e,t){return 2<arguments.length?lt(t,19,c(arguments,2),null,e):lt(t,3,null,null,e)},K.chain=function(e){return e=new Q(e),e.__chain__=!0,e},K.compact=function(e){for(var t=-1,n=e?e.length:0,r=[];++t<n;){var i=e[t];i&&r.push(i)}return r},K.compose=function(){for(var e=arguments,t=e.length;t--;)if(!wt(e[t]))throw new un;return function(){for(var t=arguments,n=e.length;n--;)t=[e[n].apply(this,t)];return t[0]}},K.constant=function(e){return function(){return e}},K.countBy=zn,K.create=function(e,t){var n=et(e);return t?V(n,t):n},K.createCallback=function(e,t,n){var r=typeof e;if(null==e||"function"==r)return tt(e,t,n);if("object"!=r)return Kt(e);var i=jn(e),s=i[0],o=e[s];return 1!=i.length||o!==o||Et(o)?function(t){for(var n=i.length,r=!1;n--&&(r=st(t[i[n]],e[i[n]],null,!0)););return r}:function(e){return e=e[s],o===e&&(0!==o||1/o==1/e)}},K.curry=function(e,t){return t=typeof t=="number"?t:+t||e.length,lt(e,4,null,null,null,t)},K.debounce=Xt,K.defaults=b,K.defer=function(e){if(!wt(e))throw new un;var t=c(arguments,1);return wn(function(){e.apply(p,t)},1)},K.delay=function(e,t){if(!wt(e))throw new un;var n=c(arguments,2);return wn(function(){e.apply(p,n)},t)},K.difference=function(e){return rt(e,it(arguments,!0,!0,1))},K.filter=kt,K.flatten=function(e,t,n,r){return typeof t!="boolean"&&null!=t&&(r=n,n=typeof t!="function"&&r&&r[t]===e?null:t,t=!1),null!=n&&(e=Mt(e,n,r)),it(e,t)},K.forEach=At,K.forEachRight=Ot,K.forIn=v,K.forInRight=function(e,t,n){var r=[];v(e,function(e,t){r.push(t,e)});var i=r.length;for(t=tt(t,n,3);i--&&!1!==t(r[i--],r[i],e););return e},K.forOwn=d,K.forOwnRight=gt,K.functions=yt,K.groupBy=Wn,K.indexBy=Xn,K.initial=function(e,t,n){var r=0,i=e?e.length:0;if(typeof t!="number"&&null!=t){var s=i;for(t=K.createCallback(t,n,3);s--&&t(e[s],s,e);)r++}else r=null==t||n?1:t||r;return c(e,0,On(An(0,i-r),i))},K.intersection=function(){for(var n=[],r=-1,i=arguments.length,o=u(),a=ht(),c=a===e,h=u();++r<i;){var p=arguments[r];(Bn(p)||mt(p))&&(n.push(p),o.push(c&&p.length>=y&&s(r?n[r]:h)))}var c=n[0],d=-1,v=c?c.length:0,m=[];e:for(;++d<v;){var g=o[0],p=c[d];if(0>(g?t(g,p):a(h,p))){for(r=i,(g||h).push(p);--r;)if(g=o[r],0>(g?t(g,p):a(n[r],p)))continue e;m.push(p)}}for(;i--;)(g=o[i])&&l(g);return f(o),f(h),m},K.invert=bt,K.invoke=function(e,t){var n=c(arguments,2),r=-1,i=typeof t=="function",s=e?e.length:0,o=Gt(typeof s=="number"?s:0);return At(e,function(e){o[++r]=(i?t:e[t]).apply(e,n)}),o},K.keys=jn,K.map=Mt,K.mapValues=function(e,t,n){var r={};return t=K.createCallback(t,n,3),d(e,function(e,n,i){r[n]=t(e,n,i)}),r},K.max=_t,K.memoize=function(e,t){function n(){var r=n.cache,i=t?t.apply(this,arguments):g+arguments[0];return yn.call(r,i)?r[i]:r[i]=e.apply(this,arguments)}if(!wt(e))throw new un;return n.cache={},n},K.merge=function(e){var t=arguments,n=2;if(!Et(e))return e;if("number"!=typeof t[2]&&(n=t.length),3<n&&"function"==typeof t[n-2])var r=tt(t[--n-1],t[n--],2);else 2<n&&"function"==typeof t[n-1]&&(r=t[--n]);for(var t=c(arguments,1,n),i=-1,s=u(),o=u();++i<n;)ot(e,t[i],r,s,o);return f(s),f(o),e},K.min=function(e,t,n){var i=1/0,s=i;if(typeof t!="function"&&n&&n[t]===e&&(t=null),null==t&&Bn(e)){n=-1;for(var o=e.length;++n<o;){var u=e[n];u<s&&(s=u)}}else t=null==t&&xt(e)?r:K.createCallback(t,n,3),At(e,function(e,n,r){n=t(e,n,r),n<i&&(i=n,s=e)});return s},K.omit=function(e,t,n){var r={};if(typeof t!="function"){var i=[];v(e,function(e,t){i.push(t)});for(var i=rt(i,it(arguments,!0,!1,1)),s=-1,o=i.length;++s<o;){var u=i[s];r[u]=e[u]}}else t=K.createCallback(t,n,3),v(e,function(e,n,i){t(e,n,i)||(r[n]=e)});return r},K.once=function(e){var t,n;if(!wt(e))throw new un;return function(){return t?n:(t=!0,n=e.apply(this,arguments),e=null,n)}},K.pairs=function(e){for(var t=-1,n=jn(e),r=n.length,i=Gt(r);++t<r;){var s=n[t];i[t]=[s,e[s]]}return i},K.partial=function(e){return lt(e,16,c(arguments,1))},K.partialRight=function(e){return lt(e,32,null,c(arguments,1))},K.pick=function(e,t,n){var r={};if(typeof t!="function")for(var i=-1,s=it(arguments,!0,!1,1),o=Et(e)?s.length:0;++i<o;){var u=s[i];u in e&&(r[u]=e[u])}else t=K.createCallback(t,n,3),v(e,function(e,n,i){t(e,n,i)&&(r[n]=e)});return r},K.pluck=Vn,K.property=Kt,K.pull=function(e){for(var t=arguments,n=0,r=t.length,i=e?e.length:0;++n<r;)for(var s=-1,o=t[n];++s<i;)e[s]===o&&(En.call(e,s--,1),i--);return e},K.range=function(e,t,n){e=+e||0,n=typeof n=="number"?n:+n||1,null==t&&(t=e,e=0);var r=-1;t=An(0,pn((t-e)/(n||1)));for(var i=Gt(t);++r<t;)i[r]=e,e+=n;return i},K.reject=function(e,t,n){return t=K.createCallback(t,n,3),kt(e,function(e,n,r){return!t(e,n,r)})},K.remove=function(e,t,n){var r=-1,i=e?e.length:0,s=[];for(t=K.createCallback(t,n,3);++r<i;)n=e[r],t(n,r,e)&&(s.push(n),En.call(e,r--,1),i--);return s},K.rest=It,K.shuffle=Ht,K.sortBy=function(e,t,n){var r=-1,s=Bn(t),o=e?e.length:0,c=Gt(typeof o=="number"?o:0);for(s||(t=K.createCallback(t,n,3)),At(e,function(e,n,i){var o=c[++r]=a();s?o.m=Mt(t,function(t){return e[t]}):(o.m=u())[0]=t(e,n,i),o.n=r,o.o=e}),o=c.length,c.sort(i);o--;)e=c[o],c[o]=e.o,s||f(e.m),l(e);return c},K.tap=function(e,t){return t(e),e},K.throttle=function(e,t,n){var r=!0,i=!0;if(!wt(e))throw new un;return!1===n?r=!1:Et(n)&&(r="leading"in n?n.leading:r,i="trailing"in n?n.trailing:i),z.leading=r,z.maxWait=t,z.trailing=i,Xt(e,t,z)},K.times=function(e,t,n){e=-1<(e=+e)?e:0;var r=-1,i=Gt(e);for(t=tt(t,n,1);++r<e;)i[r]=t(r);return i},K.toArray=function(e){return e&&typeof e.length=="number"?c(e):Tt(e)},K.transform=function(e,t,n,r){var i=Bn(e);if(null==n)if(i)n=[];else{var s=e&&e.constructor;n=et(s&&s.prototype)}return t&&(t=K.createCallback(t,r,4),(i?At:d)(e,function(e,r,i){return t(n,e,r,i)})),n},K.union=function(){return at(it(arguments,!0,!0))},K.uniq=Rt,K.values=Tt,K.where=kt,K.without=function(e){return rt(e,c(arguments,1))},K.wrap=function(e,t){return lt(t,16,[e])},K.xor=function(){for(var e=-1,t=arguments.length;++e<t;){var n=arguments[e];if(Bn(n)||mt(n))var r=r?at(rt(r,n).concat(rt(n,r))):n}return r||[]},K.zip=Ut,K.zipObject=zt,K.collect=Mt,K.drop=It,K.each=At,K.eachRight=Ot,K.extend=V,K.methods=yt,K.object=zt,K.select=kt,K.tail=It,K.unique=Rt,K.unzip=Ut,$t(K),K.clone=function(e,t,n,r){return typeof t!="boolean"&&null!=t&&(r=n,n=t,t=!1),Z(e,t,typeof n=="function"&&tt(n,r,1))},K.cloneDeep=function(e,t,n){return Z(e,!0,typeof t=="function"&&tt(t,n,1))},K.contains=Nt,K.escape=function(e){return null==e?"":on(e).replace(Rn,ct)},K.every=Ct,K.find=Lt,K.findIndex=function(e,t,n){var r=-1,i=e?e.length:0;for(t=K.createCallback(t,n,3);++r<i;)if(t(e[r],r,e))return r;return-1},K.findKey=function(e,t,n){var r;return t=K.createCallback(t,n,3),d(e,function(e,n,i){return t(e,n,i)?(r=n,!1):void 0}),r},K.findLast=function(e,t,n){var r;return t=K.createCallback(t,n,3),Ot(e,function(e,n,i){return t(e,n,i)?(r=e,!1):void 0}),r},K.findLastIndex=function(e,t,n){var r=e?e.length:0;for(t=K.createCallback(t,n,3);r--;)if(t(e[r],r,e))return r;return-1},K.findLastKey=function(e,t,n){var r;return t=K.createCallback(t,n,3),gt(e,function(e,n,i){return t(e,n,i)?(r=n,!1):void 0}),r},K.has=function(e,t){return e?yn.call(e,t):!1},K.identity=Vt,K.indexOf=Ft,K.isArguments=mt,K.isArray=Bn,K.isBoolean=function(e){return!0===e||!1===e||e&&typeof e=="object"&&cn.call(e)==H||!1},K.isDate=function(e){return e&&typeof e=="object"&&cn.call(e)==B||!1},K.isElement=function(e){return e&&1===e.nodeType||!1},K.isEmpty=function(e){var t=!0;if(!e)return t;var n=cn.call(e),r=e.length;return n==P||n==R||n==D||n==I&&typeof r=="number"&&wt(e.splice)?!r:(d(e,function(){return t=!1}),t)},K.isEqual=function(e,t,n,r){return st(e,t,typeof n=="function"&&tt(n,r,2))},K.isFinite=function(e){return Cn(e)&&!kn(parseFloat(e))},K.isFunction=wt,K.isNaN=function(e){return St(e)&&e!=+e},K.isNull=function(e){return null===e},K.isNumber=St,K.isObject=Et,K.isPlainObject=Un,K.isRegExp=function(e){return e&&typeof e=="object"&&cn.call(e)==q||!1},K.isString=xt,K.isUndefined=function(e){return typeof e=="undefined"},K.lastIndexOf=function(e,t,n){var r=e?e.length:0;for(typeof n=="number"&&(r=(0>n?An(0,r+n):On(n,r-1))+1);r--;)if(e[r]===t)return r;return-1},K.mixin=$t,K.noConflict=function(){return n._=ln,this},K.noop=Jt,K.now=$n,K.parseInt=Jn,K.random=function(e,t,n){var r=null==e,i=null==t;return null==n&&(typeof e=="boolean"&&i?(n=e,e=1):i||typeof t!="boolean"||(n=t,i=!0)),r&&i&&(t=1),e=+e||0,i?(t=e,e=0):t=+t||0,n||e%1||t%1?(n=_n(),On(e+n*(t-e+parseFloat("1e-"+((n+"").length-1))),t)):ut(e,t)},K.reduce=Dt,K.reduceRight=Pt,K.result=function(e,t){if(e){var n=e[t];return wt(n)?e[t]():n}},K.runInContext=h,K.size=function(e){var t=e?e.length:0;return typeof t=="number"?t:jn(e).length},K.some=Bt,K.sortedIndex=qt,K.template=function(e,t,n){var r=K.templateSettings;e=on(e||""),n=b({},n,r);var i,s=b({},n.imports,r.imports),r=jn(s),s=Tt(s),u=0,a=n.interpolate||A,f="__p+='",a=sn((n.escape||A).source+"|"+a.source+"|"+(a===k?T:A).source+"|"+(n.evaluate||A).source+"|$","g");e.replace(a,function(t,n,r,s,a,l){return r||(r=s),f+=e.slice(u,l).replace(M,o),n&&(f+="'+__e("+n+")+'"),a&&(i=!0,f+="';"+a+";\n__p+='"),r&&(f+="'+((__t=("+r+"))==null?'':__t)+'"),u=l+t.length,t}),f+="';",a=n=n.variable,a||(n="obj",f="with("+n+"){"+f+"}"),f=(i?f.replace(E,""):f).replace(S,"$1").replace(x,"$1;"),f="function("+n+"){"+(a?"":n+"||("+n+"={});")+"var __t,__p='',__e=_.escape"+(i?",__j=Array.prototype.join;function print(){__p+=__j.call(arguments,'')}":";")+f+"return __p}";try{var l=en(r,"return "+f).apply(p,s)}catch(c){throw c.source=f,c}return t?l(t):(l.source=f,l)},K.unescape=function(e){return null==e?"":on(e).replace(qn,vt)},K.uniqueId=function(e){var t=++m;return on(null==e?"":e)+t},K.all=Ct,K.any=Bt,K.detect=Lt,K.findWhere=Lt,K.foldl=Dt,K.foldr=Pt,K.include=Nt,K.inject=Dt,$t(function(){var e={};return d(K,function(t,n){K.prototype[n]||(e[n]=t)}),e}(),!1),K.first=jt,K.last=function(e,t,n){var r=0,i=e?e.length:0;if(typeof t!="number"&&null!=t){var s=i;for(t=K.createCallback(t,n,3);s--&&t(e[s],s,e);)r++}else if(r=t,null==r||n)return e?e[i-1]:p;return c(e,An(0,i-r))},K.sample=function(e,t,n){return e&&typeof e.length!="number"&&(e=Tt(e)),null==t||n?e?e[ut(0,e.length-1)]:p:(e=Ht(e),e.length=On(An(0,t),e.length),e)},K.take=jt,K.head=jt,d(K,function(e,t){var n="sample"!==t;K.prototype[t]||(K.prototype[t]=function(t,r){var i=this.__chain__,s=e(this.__wrapped__,t,r);return i||null!=t&&(!r||n&&typeof t=="function")?new Q(s,i):s})}),K.VERSION="2.4.1",K.prototype.chain=function(){return this.__chain__=!0,this},K.prototype.toString=function(){return on(this.__wrapped__)},K.prototype.value=Qt,K.prototype.valueOf=Qt,At(["join","pop","shift"],function(e){var t=an[e];K.prototype[e]=function(){var e=this.__chain__,n=t.apply(this.__wrapped__,arguments);return e?new Q(n,e):n}}),At(["push","reverse","sort","unshift"],function(e){var t=an[e];K.prototype[e]=function(){return t.apply(this.__wrapped__,arguments),this}}),At(["concat","slice","splice"],function(e){var t=an[e];K.prototype[e]=function(){return new Q(t.apply(this.__wrapped__,arguments),this.__chain__)}}),K}var p,d=[],v=[],m=0,g=+(new Date)+"",y=75,b=40,w=" 	\f ﻿\n\r\u2028\u2029 ᠎             　",E=/\b__p\+='';/g,S=/\b(__p\+=)''\+/g,x=/(__e\(.*?\)|\b__t\))\+'';/g,T=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,N=/\w*$/,C=/^\s*function[ \n\r\t]+\w/,k=/<%=([\s\S]+?)%>/g,L=RegExp("^["+w+"]*0+(?=.$)"),A=/($^)/,O=/\bthis\b/,M=/['\n\r\t\u2028\u2029\\]/g,_="Array Boolean Date Function Math Number Object RegExp String _ attachEvent clearTimeout isFinite isNaN parseInt setTimeout".split(" "),D="[object Arguments]",P="[object Array]",H="[object Boolean]",B="[object Date]",j="[object Function]",F="[object Number]",I="[object Object]",q="[object RegExp]",R="[object String]",U={};U[j]=!1,U[D]=U[P]=U[H]=U[B]=U[F]=U[I]=U[q]=U[R]=!0;var z={leading:!1,maxWait:0,trailing:!1},W={configurable:!1,enumerable:!1,value:null,writable:!1},X={"boolean":!1,"function":!0,object:!0,number:!1,string:!1,"undefined":!1},V={"\\":"\\","'":"'","\n":"n","\r":"r","	":"t","\u2028":"u2028","\u2029":"u2029"},$=X[typeof window]&&window||this,J=X[typeof exports]&&exports&&!exports.nodeType&&exports,K=X[typeof module]&&module&&!module.nodeType&&module,Q=K&&K.exports===J&&J,G=X[typeof global]&&global;!G||G.global!==G&&G.window!==G||($=G);var Y=h();typeof define=="function"&&typeof define.amd=="object"&&define.amd?($._=Y,define("underscore",[],function(){return Y})):J&&K?Q?(K.exports=Y)._=Y:J._=Y:$._=Y}.call(this),!function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(a,b){function s(e){var t=e.length,r=n.type(e);return"function"===r||n.isWindow(e)?!1:1===e.nodeType&&t?!0:"array"===r||0===t||"number"==typeof t&&t>0&&t-1 in e}function A(e,t,r){if(n.isFunction(t))return n.grep(e,function(e,n){return!!t.call(e,n,e)!==r});if(t.nodeType)return n.grep(e,function(e){return e===t!==r});if("string"==typeof t){if(z.test(t))return n.filter(t,e,r);t=n.filter(t,e)}return n.grep(e,function(e){return g.call(t,e)>=0!==r})}function G(e,t){while((e=e[t])&&1!==e.nodeType);return e}function J(e){var t=I[e]={};return n.each(e.match(H)||[],function(e,n){t[n]=!0}),t}function L(){l.removeEventListener("DOMContentLoaded",L,!1),a.removeEventListener("load",L,!1),n.ready()}function N(){Object.defineProperty(this.cache={},0,{get:function(){return{}}}),this.expando=n.expando+Math.random()}function S(e,t,r){var i;if(void 0===r&&1===e.nodeType)if(i="data-"+t.replace(R,"-$1").toLowerCase(),r=e.getAttribute(i),"string"==typeof r){try{r="true"===r?!0:"false"===r?!1:"null"===r?null:+r+""===r?+r:Q.test(r)?n.parseJSON(r):r}catch(s){}P.set(e,t,r)}else r=void 0;return r}function $(){return!0}function _(){return!1}function ab(){try{return l.activeElement}catch(e){}}function kb(e,t){return n.nodeName(e,"table")&&n.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function lb(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function mb(e){var t=hb.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function nb(e,t){for(var n=0,r=e.length;r>n;n++)O.set(e[n],"globalEval",!t||O.get(t[n],"globalEval"))}function ob(e,t){var r,i,s,o,u,a,f,l;if(1===t.nodeType){if(O.hasData(e)&&(o=O.access(e),u=O.set(t,o),l=o.events)){delete u.handle,u.events={};for(s in l)for(r=0,i=l[s].length;i>r;r++)n.event.add(t,s,l[s][r])}P.hasData(e)&&(a=P.access(e),f=n.extend({},a),P.set(t,f))}}function pb(e,t){var r=e.getElementsByTagName?e.getElementsByTagName(t||"*"):e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&n.nodeName(e,t)?n.merge([e],r):r}function qb(e,t){var n=t.nodeName.toLowerCase();"input"===n&&U.test(e.type)?t.checked=e.checked:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}function Mb(e){return function(t,r){"string"!=typeof t&&(r=t,t="*");var i,s=0,o=t.toLowerCase().match(H)||[];if(n.isFunction(r))while(i=o[s++])"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(r)):(e[i]=e[i]||[]).push(r)}}function Nb(e,t,r,i){function u(l){var h;return s[l]=!0,n.each(e[l]||[],function(e,n){var a=n(t,r,i);return"string"!=typeof a||o||s[a]?o?!(h=a):void 0:(t.dataTypes.unshift(a),u(a),!1)}),h}var s={},o=e===Jb;return u(t.dataTypes[0])||!s["*"]&&u("*")}function Ob(e,t){var r,i,s=n.ajaxSettings.flatOptions||{};for(r in t)void 0!==t[r]&&((s[r]?e:i||(i={}))[r]=t[r]);return i&&n.extend(!0,e,i),e}function Pb(e,t,n){var r,i,s,o,u=e.contents,a=e.dataTypes;while("*"===a[0])a.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in u)if(u[i]&&u[i].test(r)){a.unshift(i);break}if(a[0]in n)s=a[0];else{for(i in n){if(!a[0]||e.converters[i+" "+a[0]]){s=i;break}o||(o=i)}s=s||o}return s?(s!==a[0]&&a.unshift(s),n[s]):void 0}function Qb(e,t,n,r){var i,s,o,u,a,f={},l=e.dataTypes.slice();if(l[1])for(o in e.converters)f[o.toLowerCase()]=e.converters[o];s=l.shift();while(s)if(e.responseFields[s]&&(n[e.responseFields[s]]=t),!a&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),a=s,s=l.shift())if("*"===s)s=a;else if("*"!==a&&a!==s){if(o=f[a+" "+s]||f["* "+s],!o)for(i in f)if(u=i.split(" "),u[1]===s&&(o=f[a+" "+u[0]]||f["* "+u[0]])){o===!0?o=f[i]:f[i]!==!0&&(s=u[0],l.unshift(u[1]));break}if(o!==!0)if(o&&e["throws"])t=o(t);else try{t=o(t)}catch(c){return{state:"parsererror",error:o?c:"No conversion from "+a+" to "+s}}}return{state:"success",data:t}}function Wb(e,t,r,i){var s;if(n.isArray(t))n.each(t,function(t,n){r||Sb.test(e)?i(e,n):Wb(e+"["+("object"==typeof n?t:"")+"]",n,r,i)});else if(r||"object"!==n.type(t))i(e,t);else for(s in t)Wb(e+"["+s+"]",t[s],r,i)}var c=[],d=c.slice,e=c.concat,f=c.push,g=c.indexOf,h={},i=h.toString,j=h.hasOwnProperty,k={},l=a.document,m="2.1.1 -css,-css/addGetHookIf,-css/curCSS,-css/defaultDisplay,-css/hiddenVisibleSelectors,-css/support,-css/swap,-css/var/cssExpand,-css/var/getStyles,-css/var/isHidden,-css/var/rmargin,-css/var/rnumnonpx,-effects,-effects/Tween,-effects/animatedSelector,-dimensions,-offset,-deprecated,-event-alias,-wrap",n=function(e,t){return new n.fn.init(e,t)},o=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,p=/^-ms-/,q=/-([\da-z])/gi,r=function(e,t){return t.toUpperCase()};n.fn=n.prototype={jquery:m,constructor:n,selector:"",length:0,toArray:function(){return d.call(this)},get:function(e){return null!=e?0>e?this[e+this.length]:this[e]:d.call(this)},pushStack:function(e){var t=n.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return n.each(this,e,t)},map:function(e){return this.pushStack(n.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(d.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:f,sort:c.sort,splice:c.splice},n.extend=n.fn.extend=function(){var e,t,r,i,s,o,u=arguments[0]||{},a=1,f=arguments.length,l=!1;for("boolean"==typeof u&&(l=u,u=arguments[a]||{},a++),"object"==typeof u||n.isFunction(u)||(u={}),a===f&&(u=this,a--);f>a;a++)if(null!=(e=arguments[a]))for(t in e)r=u[t],i=e[t],u!==i&&(l&&i&&(n.isPlainObject(i)||(s=n.isArray(i)))?(s?(s=!1,o=r&&n.isArray(r)?r:[]):o=r&&n.isPlainObject(r)?r:{},u[t]=n.extend(l,o,i)):void 0!==i&&(u[t]=i));return u},n.extend({expando:"jQuery"+(m+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===n.type(e)},isArray:Array.isArray,isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){return!n.isArray(e)&&e-parseFloat(e)>=0},isPlainObject:function(e){return"object"!==n.type(e)||e.nodeType||n.isWindow(e)?!1:e.constructor&&!j.call(e.constructor.prototype,"isPrototypeOf")?!1:!0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?h[i.call(e)]||"object":typeof e},globalEval:function(a){var b,c=eval;a=n.trim(a),a&&(1===a.indexOf("use strict")?(b=l.createElement("script"),b.text=a,l.head.appendChild(b).parentNode.removeChild(b)):c(a))},camelCase:function(e){return e.replace(p,"ms-").replace(q,r)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var r,i=0,o=e.length,u=s(e);if(n){if(u){for(;o>i;i++)if(r=t.apply(e[i],n),r===!1)break}else for(i in e)if(r=t.apply(e[i],n),r===!1)break}else if(u){for(;o>i;i++)if(r=t.call(e[i],i,e[i]),r===!1)break}else for(i in e)if(r=t.call(e[i],i,e[i]),r===!1)break;return e},trim:function(e){return null==e?"":(e+"").replace(o,"")},makeArray:function(e,t){var r=t||[];return null!=e&&(s(Object(e))?n.merge(r,"string"==typeof e?[e]:e):f.call(r,e)),r},inArray:function(e,t,n){return null==t?-1:g.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;n>r;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r,i=[],s=0,o=e.length,u=!n;o>s;s++)r=!t(e[s],s),r!==u&&i.push(e[s]);return i},map:function(t,n,r){var i,o=0,u=t.length,a=s(t),f=[];if(a)for(;u>o;o++)i=n(t[o],o,r),null!=i&&f.push(i);else for(o in t)i=n(t[o],o,r),null!=i&&f.push(i);return e.apply([],f)},guid:1,proxy:function(e,t){var r,i,s;return"string"==typeof t&&(r=e[t],t=e,e=r),n.isFunction(e)?(i=d.call(arguments,2),s=function(){return e.apply(t||this,i.concat(d.call(arguments)))},s.guid=e.guid=e.guid||n.guid++,s):void 0},now:Date.now,support:k}),n.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){h["[object "+t+"]"]=t.toLowerCase()});var t=a.document.documentElement,u,v=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.oMatchesSelector||t.msMatchesSelector,w=function(e,t){if(e===t)return u=!0,0;var r=t.compareDocumentPosition&&e.compareDocumentPosition&&e.compareDocumentPosition(t);return r?1&r?e===l||n.contains(l,e)?-1:t===l||n.contains(l,t)?1:0:4&r?-1:1:e.compareDocumentPosition?-1:1};n.extend({find:function(e,t,r,i){var s,o,u=0;if(r=r||[],t=t||l,!e||"string"!=typeof e)return r;if(1!==(o=t.nodeType)&&9!==o)return[];if(i)while(s=i[u++])n.find.matchesSelector(s,e)&&r.push(s);else n.merge(r,t.querySelectorAll(e));return r},unique:function(e){var t,n=[],r=0,i=0;if(u=!1,e.sort(w),u){while(t=e[r++])t===e[r]&&(i=n.push(r));while(i--)e.splice(n[i],1)}return e},text:function(e){var t,r="",i=0,s=e.nodeType;if(s){if(1===s||9===s||11===s)return e.textContent;if(3===s||4===s)return e.nodeValue}else while(t=e[i++])r+=n.text(t);return r},contains:function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!!r&&1===r.nodeType&&!!n.contains(r)},isXMLDoc:function(e){return"HTML"!==(e.ownerDocument||e).documentElement.nodeName},expr:{attrHandle:{},match:{bool:/^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$/i,needsContext:/^[\x20\t\r\n\f]*[>+~]/}}}),n.extend(n.find,{matches:function(e,t){return n.find(e,null,null,t)},matchesSelector:function(e,t){return v.call(e,t)},attr:function(e,t){return e.getAttribute(t)}});var x=n.expr.match.needsContext,y=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,z=/^.[^:#\[\.,]*$/;n.filter=function(e,t,r){var i=t[0];return r&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?n.find.matchesSelector(i,e)?[i]:[]:n.find.matches(e,n.grep(t,function(e){return 1===e.nodeType}))},n.fn.extend({find:function(e){var t,r=this.length,i=[],s=this;if("string"!=typeof e)return this.pushStack(n(e).filter(function(){for(t=0;r>t;t++)if(n.contains(s[t],this))return!0}));for(t=0;r>t;t++)n.find(e,s[t],i);return i=this.pushStack(r>1?n.unique(i):i),i.selector=this.selector?this.selector+" "+e:e,i},filter:function(e){return this.pushStack(A(this,e||[],!1))},not:function(e){return this.pushStack(A(this,e||[],!0))},is:function(e){return!!A(this,"string"==typeof e&&x.test(e)?n(e):e||[],!1).length}});var B,C=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,D=n.fn.init=function(e,t){var r,i;if(!e)return this;if("string"==typeof e){if(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:C.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||B).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof n?t[0]:t,n.merge(this,n.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:l,!0)),y.test(r[1])&&n.isPlainObject(t))for(r in t)n.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return i=l.getElementById(r[2]),i&&i.parentNode&&(this.length=1,this[0]=i),this.context=l,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):n.isFunction(e)?"undefined"!=typeof B.ready?B.ready(e):e(n):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),n.makeArray(e,this))};D.prototype=n.fn,B=n(l);var E=/^(?:parents|prev(?:Until|All))/,F={children:!0,contents:!0,next:!0,prev:!0};n.extend({dir:function(e,t,r){var i=[],s=void 0!==r;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(s&&n(e).is(r))break;i.push(e)}return i},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}}),n.fn.extend({has:function(e){var t=n(e,this),r=t.length;return this.filter(function(){for(var e=0;r>e;e++)if(n.contains(this,t[e]))return!0})},closest:function(e,t){for(var r,i=0,s=this.length,o=[],u=x.test(e)||"string"!=typeof e?n(e,t||this.context):0;s>i;i++)for(r=this[i];r&&r!==t;r=r.parentNode)if(r.nodeType<11&&(u?u.index(r)>-1:1===r.nodeType&&n.find.matchesSelector(r,e))){o.push(r);break}return this.pushStack(o.length>1?n.unique(o):o)},index:function(e){return e?"string"==typeof e?g.call(n(e),this[0]):g.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(n.unique(n.merge(this.get(),n(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),n.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return n.dir(e,"parentNode")},parentsUntil:function(e,t,r){return n.dir(e,"parentNode",r)},next:function(e){return G(e,"nextSibling")},prev:function(e){return G(e,"previousSibling")},nextAll:function(e){return n.dir(e,"nextSibling")},prevAll:function(e){return n.dir(e,"previousSibling")},nextUntil:function(e,t,r){return n.dir(e,"nextSibling",r)},prevUntil:function(e,t,r){return n.dir(e,"previousSibling",r)},siblings:function(e){return n.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return n.sibling(e.firstChild)},contents:function(e){return e.contentDocument||n.merge([],e.childNodes)}},function(e,t){n.fn[e]=function(r,i){var s=n.map(this,t,r);return"Until"!==e.slice(-5)&&(i=r),i&&"string"==typeof i&&(s=n.filter(i,s)),this.length>1&&(F[e]||n.unique(s),E.test(e)&&s.reverse()),this.pushStack(s)}});var H=/\S+/g,I={};n.Callbacks=function(e){e="string"==typeof e?I[e]||J(e):n.extend({},e);var t,r,i,s,o,u,a=[],f=!e.once&&[],l=function(n){for(t=e.memory&&n,r=!0,u=s||0,s=0,o=a.length,i=!0;a&&o>u;u++)if(a[u].apply(n[0],n[1])===!1&&e.stopOnFalse){t=!1;break}i=!1,a&&(f?f.length&&l(f.shift()):t?a=[]:c.disable())},c={add:function(){if(a){var r=a.length;!function u(t){n.each(t,function(t,r){var i=n.type(r);"function"===i?e.unique&&c.has(r)||a.push(r):r&&r.length&&"string"!==i&&u(r)})}(arguments),i?o=a.length:t&&(s=r,l(t))}return this},remove:function(){return a&&n.each(arguments,function(e,t){var r;while((r=n.inArray(t,a,r))>-1)a.splice(r,1),i&&(o>=r&&o--,u>=r&&u--)}),this},has:function(e){return e?n.inArray(e,a)>-1:!!a&&!!a.length},empty:function(){return a=[],o=0,this},disable:function(){return a=f=t=void 0,this},disabled:function(){return!a},lock:function(){return f=void 0,t||c.disable(),this},locked:function(){return!f},fireWith:function(e,t){return!a||r&&!f||(t=t||[],t=[e,t.slice?t.slice():t],i?f.push(t):l(t)),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},n.extend({Deferred:function(e){var t=[["resolve","done",n.Callbacks("once memory"),"resolved"],["reject","fail",n.Callbacks("once memory"),"rejected"],["notify","progress",n.Callbacks("memory")]],r="pending",i={state:function(){return r},always:function(){return s.done(arguments).fail(arguments),this},then:function(){var e=arguments;return n.Deferred(function(r){n.each(t,function(t,o){var u=n.isFunction(e[t])&&e[t];s[o[1]](function(){var e=u&&u.apply(this,arguments);e&&n.isFunction(e.promise)?e.promise().done(r.resolve).fail(r.reject).progress(r.notify):r[o[0]+"With"](this===i?r.promise():this,u?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?n.extend(e,i):i}},s={};return i.pipe=i.then,n.each(t,function(e,n){var o=n[2],u=n[3];i[n[1]]=o.add,u&&o.add(function(){r=u},t[1^e][2].disable,t[2][2].lock),s[n[0]]=function(){return s[n[0]+"With"](this===s?i:this,arguments),this},s[n[0]+"With"]=o.fireWith}),i.promise(s),e&&e.call(s,s),s},when:function(e){var t=0,r=d.call(arguments),i=r.length,s=1!==i||e&&n.isFunction(e.promise)?i:0,o=1===s?e:n.Deferred(),u=function(e,t,n){return function(r){t[e]=this,n[e]=arguments.length>1?d.call(arguments):r,n===a?o.notifyWith(t,n):--s||o.resolveWith(t,n)}},a,f,l;if(i>1)for(a=new Array(i),f=new Array(i),l=new Array(i);i>t;t++)r[t]&&n.isFunction(r[t].promise)?r[t].promise().done(u(t,l,r)).fail(o.reject).progress(u(t,f,a)):--s;return s||o.resolveWith(l,r),o.promise()}});var K;n.fn.ready=function(e){return n.ready.promise().done(e),this},n.extend({isReady:!1,readyWait:1,holdReady:function(e){e?n.readyWait++:n.ready(!0)},ready:function(e){(e===!0?--n.readyWait:n.isReady)||(n.isReady=!0,e!==!0&&--n.readyWait>0||(K.resolveWith(l,[n]),n.fn.triggerHandler&&(n(l).triggerHandler("ready"),n(l).off("ready"))))}}),n.ready.promise=function(e){return K||(K=n.Deferred(),"complete"===l.readyState?setTimeout(n.ready):(l.addEventListener("DOMContentLoaded",L,!1),a.addEventListener("load",L,!1))),K.promise(e)},n.ready.promise();var M=n.access=function(e,t,r,i,s,o,u){var a=0,f=e.length,l=null==r;if("object"===n.type(r)){s=!0;for(a in r)n.access(e,t,a,r[a],!0,o,u)}else if(void 0!==i&&(s=!0,n.isFunction(i)||(u=!0),l&&(u?(t.call(e,i),t=null):(l=t,t=function(e,t,r){return l.call(n(e),r)})),t))for(;f>a;a++)t(e[a],r,u?i:i.call(e[a],a,t(e[a],r)));return s?e:l?t.call(e):f?t(e[0],r):o};n.acceptData=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType},N.uid=1,N.accepts=n.acceptData,N.prototype={key:function(e){if(!N.accepts(e))return 0;var t={},r=e[this.expando];if(!r){r=N.uid++;try{t[this.expando]={value:r},Object.defineProperties(e,t)}catch(i){t[this.expando]=r,n.extend(e,t)}}return this.cache[r]||(this.cache[r]={}),r},set:function(e,t,r){var i,s=this.key(e),o=this.cache[s];if("string"==typeof t)o[t]=r;else if(n.isEmptyObject(o))n.extend(this.cache[s],t);else for(i in t)o[i]=t[i];return o},get:function(e,t){var n=this.cache[this.key(e)];return void 0===t?n:n[t]},access:function(e,t,r){var i;return void 0===t||t&&"string"==typeof t&&void 0===r?(i=this.get(e,t),void 0!==i?i:this.get(e,n.camelCase(t))):(this.set(e,t,r),void 0!==r?r:t)},remove:function(e,t){var r,i,s,o=this.key(e),u=this.cache[o];if(void 0===t)this.cache[o]={};else{n.isArray(t)?i=t.concat(t.map(n.camelCase)):(s=n.camelCase(t),t in u?i=[t,s]:(i=s,i=i in u?[i]:i.match(H)||[])),r=i.length;while(r--)delete u[i[r]]}},hasData:function(e){return!n.isEmptyObject(this.cache[e[this.expando]]||{})},discard:function(e){e[this.expando]&&delete this.cache[e[this.expando]]}};var O=new N,P=new N,Q=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,R=/([A-Z])/g;n.extend({hasData:function(e){return P.hasData(e)||O.hasData(e)},data:function(e,t,n){return P.access(e,t,n)},removeData:function(e,t){P.remove(e,t)},_data:function(e,t,n){return O.access(e,t,n)},_removeData:function(e,t){O.remove(e,t)}}),n.fn.extend({data:function(e,t){var r,i,s,o=this[0],u=o&&o.attributes;if(void 0===e){if(this.length&&(s=P.get(o),1===o.nodeType&&!O.get(o,"hasDataAttrs"))){r=u.length;while(r--)u[r]&&(i=u[r].name,0===i.indexOf("data-")&&(i=n.camelCase(i.slice(5)),S(o,i,s[i])));O.set(o,"hasDataAttrs",!0)}return s}return"object"==typeof e?this.each(function(){P.set(this,e)}):M(this,function(t){var r,i=n.camelCase(e);if(o&&void 0===t){if(r=P.get(o,e),void 0!==r)return r;if(r=P.get(o,i),void 0!==r)return r;if(r=S(o,i,void 0),void 0!==r)return r}else this.each(function(){var n=P.get(this,i);P.set(this,i,t),-1!==e.indexOf("-")&&void 0!==n&&P.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){P.remove(this,e)})}}),n.extend({queue:function(e,t,r){var i;return e?(t=(t||"fx")+"queue",i=O.get(e,t),r&&(!i||n.isArray(r)?i=O.access(e,t,n.makeArray(r)):i.push(r)),i||[]):void 0},dequeue:function(e,t){t=t||"fx";var r=n.queue(e,t),i=r.length,s=r.shift(),o=n._queueHooks(e,t),u=function(){n.dequeue(e,t)};"inprogress"===s&&(s=r.shift(),i--),s&&("fx"===t&&r.unshift("inprogress"),delete o.stop,s.call(e,u,o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var r=t+"queueHooks";return O.get(e,r)||O.access(e,r,{empty:n.Callbacks("once memory").add(function(){O.remove(e,[t+"queue",r])})})}}),n.fn.extend({queue:function(e,t){var r=2;return"string"!=typeof e&&(t=e,e="fx",r--),arguments.length<r?n.queue(this[0],e):void 0===t?this:this.each(function(){var r=n.queue(this,e,t);n._queueHooks(this,e),"fx"===e&&"inprogress"!==r[0]&&n.dequeue(this,e)})},dequeue:function(e){return this.each(function(){n.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var r,i=1,s=n.Deferred(),o=this,u=this.length,a=function(){--i||s.resolveWith(o,[o])};"string"!=typeof e&&(t=e,e=void 0),e=e||"fx";while(u--)r=O.get(o[u],e+"queueHooks"),r&&r.empty&&(i++,r.empty.add(a));return a(),s.promise(t)}});var T=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,U=/^(?:checkbox|radio)$/i;!function(){var e=l.createDocumentFragment(),t=e.appendChild(l.createElement("div")),n=l.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),k.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",k.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue}();var V="undefined";k.focusinBubbles="onfocusin"in a;var W=/^key/,X=/^(?:mouse|pointer|contextmenu)|click/,Y=/^(?:focusinfocus|focusoutblur)$/,Z=/^([^.]*)(?:\.(.+)|)$/;n.event={global:{},add:function(e,t,r,i,s){var o,u,a,f,l,c,h,p,d,v,m,g=O.get(e);if(g){r.handler&&(o=r,r=o.handler,s=o.selector),r.guid||(r.guid=n.guid++),(f=g.events)||(f=g.events={}),(u=g.handle)||(u=g.handle=function(t){return typeof n!==V&&n.event.triggered!==t.type?n.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(H)||[""],l=t.length;while(l--)a=Z.exec(t[l])||[],d=m=a[1],v=(a[2]||"").split(".").sort(),d&&(h=n.event.special[d]||{},d=(s?h.delegateType:h.bindType)||d,h=n.event.special[d]||{},c=n.extend({type:d,origType:m,data:i,handler:r,guid:r.guid,selector:s,needsContext:s&&n.expr.match.needsContext.test(s),namespace:v.join(".")},o),(p=f[d])||(p=f[d]=[],p.delegateCount=0,h.setup&&h.setup.call(e,i,v,u)!==!1||e.addEventListener&&e.addEventListener(d,u,!1)),h.add&&(h.add.call(e,c),c.handler.guid||(c.handler.guid=r.guid)),s?p.splice(p.delegateCount++,0,c):p.push(c),n.event.global[d]=!0)}},remove:function(e,t,r,i,s){var o,u,a,f,l,c,h,p,d,v,m,g=O.hasData(e)&&O.get(e);if(g&&(f=g.events)){t=(t||"").match(H)||[""],l=t.length;while(l--)if(a=Z.exec(t[l])||[],d=m=a[1],v=(a[2]||"").split(".").sort(),d){h=n.event.special[d]||{},d=(i?h.delegateType:h.bindType)||d,p=f[d]||[],a=a[2]&&new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"),u=o=p.length;while(o--)c=p[o],!s&&m!==c.origType||r&&r.guid!==c.guid||a&&!a.test(c.namespace)||i&&i!==c.selector&&("**"!==i||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,h.remove&&h.remove.call(e,c));u&&!p.length&&(h.teardown&&h.teardown.call(e,v,g.handle)!==!1||n.removeEvent(e,d,g.handle),delete f[d])}else for(d in f)n.event.remove(e,d+t[l],r,i,!0);n.isEmptyObject(f)&&(delete g.handle,O.remove(e,"events"))}},trigger:function(e,t,r,i){var s,o,u,f,c,h,p,d=[r||l],v=j.call(e,"type")?e.type:e,m=j.call(e,"namespace")?e.namespace.split("."):[];if(o=u=r=r||l,3!==r.nodeType&&8!==r.nodeType&&!Y.test(v+n.event.triggered)&&(v.indexOf(".")>=0&&(m=v.split("."),v=m.shift(),m.sort()),c=v.indexOf(":")<0&&"on"+v,e=e[n.expando]?e:new n.Event(v,"object"==typeof e&&e),e.isTrigger=i?2:3,e.namespace=m.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=r),t=null==t?[e]:n.makeArray(t,[e]),p=n.event.special[v]||{},i||!p.trigger||p.trigger.apply(r,t)!==!1)){if(!i&&!p.noBubble&&!n.isWindow(r)){for(f=p.delegateType||v,Y.test(f+v)||(o=o.parentNode);o;o=o.parentNode)d.push(o),u=o;u===(r.ownerDocument||l)&&d.push(u.defaultView||u.parentWindow||a)}s=0;while((o=d[s++])&&!e.isPropagationStopped())e.type=s>1?f:p.bindType||v,h=(O.get(o,"events")||{})[e.type]&&O.get(o,"handle"),h&&h.apply(o,t),h=c&&o[c],h&&h.apply&&n.acceptData(o)&&(e.result=h.apply(o,t),e.result===!1&&e.preventDefault());return e.type=v,i||e.isDefaultPrevented()||p._default&&p._default.apply(d.pop(),t)!==!1||!n.acceptData(r)||c&&n.isFunction(r[v])&&!n.isWindow(r)&&(u=r[c],u&&(r[c]=null),n.event.triggered=v,r[v](),n.event.triggered=void 0,u&&(r[c]=u)),e.result}},dispatch:function(e){e=n.event.fix(e);var t,r,i,s,o,u=[],a=d.call(arguments),f=(O.get(this,"events")||{})[e.type]||[],l=n.event.special[e.type]||{};if(a[0]=e,e.delegateTarget=this,!l.preDispatch||l.preDispatch.call(this,e)!==!1){u=n.event.handlers.call(this,e,f),t=0;while((s=u[t++])&&!e.isPropagationStopped()){e.currentTarget=s.elem,r=0;while((o=s.handlers[r++])&&!e.isImmediatePropagationStopped())(!e.namespace_re||e.namespace_re.test(o.namespace))&&(e.handleObj=o,e.data=o.data,i=((n.event.special[o.origType]||{}).handle||o.handler).apply(s.elem,a),void 0!==i&&(e.result=i)===!1&&(e.preventDefault(),e.stopPropagation()))}return l.postDispatch&&l.postDispatch.call(this,e),e.result}},handlers:function(e,t){var r,i,s,o,u=[],a=t.delegateCount,f=e.target;if(a&&f.nodeType&&(!e.button||"click"!==e.type))for(;f!==this;f=f.parentNode||this)if(f.disabled!==!0||"click"!==e.type){for(i=[],r=0;a>r;r++)o=t[r],s=o.selector+" ",void 0===i[s]&&(i[s]=o.needsContext?n(s,this).index(f)>=0:n.find(s,this,null,[f]).length),i[s]&&i.push(o);i.length&&u.push({elem:f,handlers:i})}return a<t.length&&u.push({elem:this,handlers:t.slice(a)}),u},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i,s=t.button;return null==e.pageX&&null!=t.clientX&&(n=e.target.ownerDocument||l,r=n.documentElement,i=n.body,e.pageX=t.clientX+(r&&r.scrollLeft||i&&i.scrollLeft||0)-(r&&r.clientLeft||i&&i.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||i&&i.scrollTop||0)-(r&&r.clientTop||i&&i.clientTop||0)),e.which||void 0===s||(e.which=1&s?1:2&s?3:4&s?2:0),e}},fix:function(e){if(e[n.expando])return e;var t,r,i,s=e.type,o=e,u=this.fixHooks[s];u||(this.fixHooks[s]=u=X.test(s)?this.mouseHooks:W.test(s)?this.keyHooks:{}),i=u.props?this.props.concat(u.props):this.props,e=new n.Event(o),t=i.length;while(t--)r=i[t],e[r]=o[r];return e.target||(e.target=l),3===e.target.nodeType&&(e.target=e.target.parentNode),u.filter?u.filter(e,o):e},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==ab()&&this.focus?(this.focus(),!1):void 0},delegateType:"focusin"},blur:{trigger:function(){return this===ab()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&n.nodeName(this,"input")?(this.click(),!1):void 0},_default:function(e){return n.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,r,i){var s=n.extend(new n.Event,r,{type:e,isSimulated:!0,originalEvent:{}});i?n.event.trigger(s,null,t):n.event.dispatch.call(t,s),s.isDefaultPrevented()&&r.preventDefault()}},n.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)},n.Event=function(e,t){return this instanceof n.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?$:_):this.type=e,t&&n.extend(this,t),this.timeStamp=e&&e.timeStamp||n.now(),void (this[n.expando]=!0)):new n.Event(e,t)},n.Event.prototype={isDefaultPrevented:_,isPropagationStopped:_,isImmediatePropagationStopped:_,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=$,e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=$,e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=$,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},n.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){n.event.special[e]={delegateType:t,bindType:t,handle:function(e){var r,i=this,s=e.relatedTarget,o=e.handleObj;return(!s||s!==i&&!n.contains(i,s))&&(e.type=o.origType,r=o.handler.apply(this,arguments),e.type=t),r}}}),k.focusinBubbles||n.each({focus:"focusin",blur:"focusout"},function(e,t){var r=function(e){n.event.simulate(t,e.target,n.event.fix(e),!0)};n.event.special[t]={setup:function(){var n=this.ownerDocument||this,i=O.access(n,t);i||n.addEventListener(e,r,!0),O.access(n,t,(i||0)+1)},teardown:function(){var n=this.ownerDocument||this,i=O.access(n,t)-1;i?O.access(n,t,i):(n.removeEventListener(e,r,!0),O.remove(n,t))}}}),n.fn.extend({on:function(e,t,r,i,s){var o,u;if("object"==typeof e){"string"!=typeof t&&(r=r||t,t=void 0);for(u in e)this.on(u,t,r,e[u],s);return this}if(null==r&&null==i?(i=t,r=t=void 0):null==i&&("string"==typeof t?(i=r,r=void 0):(i=r,r=t,t=void 0)),i===!1)i=_;else if(!i)return this;return 1===s&&(o=i,i=function(e){return n().off(e),o.apply(this,arguments)},i.guid=o.guid||(o.guid=n.guid++)),this.each(function(){n.event.add(this,e,i,r,t)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,r){var i,s;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,n(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(s in e)this.off(s,t,e[s]);return this}return(t===!1||"function"==typeof t)&&(r=t,t=void 0),r===!1&&(r=_),this.each(function(){n.event.remove(this,e,r,t)})},trigger:function(e,t){return this.each(function(){n.event.trigger(e,t,this)})},triggerHandler:function(e,t){var r=this[0];return r?n.event.trigger(e,t,r,!0):void 0}});var bb=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,cb=/<([\w:]+)/,db=/<|&#?\w+;/,eb=/<(?:script|style|link)/i,fb=/checked\s*(?:[^=]|=\s*.checked.)/i,gb=/^$|\/(?:java|ecma)script/i,hb=/^true\/(.*)/,ib=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,jb={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};jb.optgroup=jb.option,jb.tbody=jb.tfoot=jb.colgroup=jb.caption=jb.thead,jb.th=jb.td,n.extend({clone:function(e,t,r){var i,s,o,u,a=e.cloneNode(!0),f=n.contains(e.ownerDocument,e);if(!(k.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||n.isXMLDoc(e)))for(u=pb(a),o=pb(e),i=0,s=o.length;s>i;i++)qb(o[i],u[i]);if(t)if(r)for(o=o||pb(e),u=u||pb(a),i=0,s=o.length;s>i;i++)ob(o[i],u[i]);else ob(e,a);return u=pb(a,"script"),u.length>0&&nb(u,!f&&pb(e,"script")),a},buildFragment:function(e,t,r,i){for(var s,o,u,a,f,l,c=t.createDocumentFragment(),h=[],p=0,d=e.length;d>p;p++)if(s=e[p],s||0===s)if("object"===n.type(s))n.merge(h,s.nodeType?[s]:s);else if(db.test(s)){o=o||c.appendChild(t.createElement("div")),u=(cb.exec(s)||["",""])[1].toLowerCase(),a=jb[u]||jb._default,o.innerHTML=a[1]+s.replace(bb,"<$1></$2>")+a[2],l=a[0];while(l--)o=o.lastChild;n.merge(h,o.childNodes),o=c.firstChild,o.textContent=""}else h.push(t.createTextNode(s));c.textContent="",p=0;while(s=h[p++])if((!i||-1===n.inArray(s,i))&&(f=n.contains(s.ownerDocument,s),o=pb(c.appendChild(s),"script"),f&&nb(o),r)){l=0;while(s=o[l++])gb.test(s.type||"")&&r.push(s)}return c},cleanData:function(e){for(var t,r,i,s,o=n.event.special,u=0;void 0!==(r=e[u]);u++){if(n.acceptData(r)&&(s=r[O.expando],s&&(t=O.cache[s]))){if(t.events)for(i in t.events)o[i]?n.event.remove(r,i):n.removeEvent(r,i,t.handle);O.cache[s]&&delete O.cache[s]}delete P.cache[r[P.expando]]}}}),n.fn.extend({text:function(e){return M(this,function(e){return void 0===e?n.text(this):this.empty().each(function(){(1===this.nodeType||11===this.nodeType||9===this.nodeType)&&(this.textContent=e)})},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=kb(this,e);t.appendChild(e)}})},prepend:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=kb(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){for(var r,i=e?n.filter(e,this):this,s=0;null!=(r=i[s]);s++)t||1!==r.nodeType||n.cleanData(pb(r)),r.parentNode&&(t&&n.contains(r.ownerDocument,r)&&nb(pb(r,"script")),r.parentNode.removeChild(r));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(n.cleanData(pb(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null==e?!1:e,t=null==t?e:t,this.map(function(){return n.clone(this,e,t)})},html:function(e){return M(this,function(e){var t=this[0]||{},r=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!eb.test(e)&&!jb[(cb.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(bb,"<$1></$2>");try{for(;i>r;r++)t=this[r]||{},1===t.nodeType&&(n.cleanData(pb(t,!1)),t.innerHTML=e);t=0}catch(s){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=arguments[0];return this.domManip(arguments,function(t){e=this.parentNode,n.cleanData(pb(this)),e&&e.replaceChild(t,this)}),e&&(e.length||e.nodeType)?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(t,r){t=e.apply([],t);var i,s,o,u,a,f,l=0,c=this.length,h=this,p=c-1,d=t[0],v=n.isFunction(d);if(v||c>1&&"string"==typeof d&&!k.checkClone&&fb.test(d))return this.each(function(e){var n=h.eq(e);v&&(t[0]=d.call(this,e,n.html())),n.domManip(t,r)});if(c&&(i=n.buildFragment(t,this[0].ownerDocument,!1,this),s=i.firstChild,1===i.childNodes.length&&(i=s),s)){for(o=n.map(pb(i,"script"),lb),u=o.length;c>l;l++)a=i,l!==p&&(a=n.clone(a,!0,!0),u&&n.merge(o,pb(a,"script"))),r.call(this[l],a,l);if(u)for(f=o[o.length-1].ownerDocument,n.map(o,mb),l=0;u>l;l++)a=o[l],gb.test(a.type||"")&&!O.access(a,"globalEval")&&n.contains(f,a)&&(a.src?n._evalUrl&&n._evalUrl(a.src):n.globalEval(a.textContent.replace(ib,"")))}return this}}),n.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){n.fn[e]=function(e){for(var r,i=[],s=n(e),o=s.length-1,u=0;o>=u;u++)r=u===o?this:this.clone(!0),n(s[u])[t](r),f.apply(i,r.get());return this.pushStack(i)}}),n.fn.delay=function(e,t){return e=n.fx?n.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}})},function(){var e=l.createElement("input"),t=l.createElement("select"),n=t.appendChild(l.createElement("option"));e.type="checkbox",k.checkOn=""!==e.value,k.optSelected=n.selected,t.disabled=!0,k.optDisabled=!n.disabled,e=l.createElement("input"),e.value="t",e.type="radio",k.radioValue="t"===e.value}();var rb,sb,tb=n.expr.attrHandle;n.fn.extend({attr:function(e,t){return M(this,n.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){n.removeAttr(this,e)})}}),n.extend({attr:function(e,t,r){var i,s,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===V?n.prop(e,t,r):(1===o&&n.isXMLDoc(e)||(t=t.toLowerCase(),i=n.attrHooks[t]||(n.expr.match.bool.test(t)?sb:rb)),void 0===r?i&&"get"in i&&null!==(s=i.get(e,t))?s:(s=n.find.attr(e,t),null==s?void 0:s):null!==r?i&&"set"in i&&void 0!==(s=i.set(e,r,t))?s:(e.setAttribute(t,r+""),r):void n.removeAttr(e,t))},removeAttr:function(e,t){var r,i,s=0,o=t&&t.match(H);if(o&&1===e.nodeType)while(r=o[s++])i=n.propFix[r]||r,n.expr.match.bool.test(r)&&(e[i]=!1),e.removeAttribute(r)},attrHooks:{type:{set:function(e,t){if(!k.radioValue&&"radio"===t&&n.nodeName(e,"input")){var r=e.value;return e.setAttribute("type",t),r&&(e.value=r),t}}}}}),sb={set:function(e,t,r){return t===!1?n.removeAttr(e,r):e.setAttribute(r,r),r}},n.each(n.expr.match.bool.source.match(/\w+/g),function(e,t){var r=tb[t]||n.find.attr;tb[t]=function(e,t,n){var i,s;return n||(s=tb[t],tb[t]=i,i=null!=r(e,t,n)?t.toLowerCase():null,tb[t]=s),i}});var ub=/^(?:input|select|textarea|button)$/i;n.fn.extend({prop:function(e,t){return M(this,n.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[n.propFix[e]||e]})}}),n.extend({propFix:{"for":"htmlFor","class":"className"},prop:function(e,t,r){var i,s,o,u=e.nodeType;if(e&&3!==u&&8!==u&&2!==u)return o=1!==u||!n.isXMLDoc(e),o&&(t=n.propFix[t]||t,s=n.propHooks[t]),void 0!==r?s&&"set"in s&&void 0!==(i=s.set(e,r,t))?i:e[t]=r:s&&"get"in s&&null!==(i=s.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){return e.hasAttribute("tabindex")||ub.test(e.nodeName)||e.href?e.tabIndex:-1}}}}),k.optSelected||(n.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null}}),n.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){n.propFix[this.toLowerCase()]=this});var vb=/[\t\r\n\f]/g;n.fn.extend({addClass:function(e){var t,r,i,s,o,u,a="string"==typeof e&&e,f=0,l=this.length;if(n.isFunction(e))return this.each(function(t){n(this).addClass(e.call(this,t,this.className))});if(a)for(t=(e||"").match(H)||[];l>f;f++)if(r=this[f],i=1===r.nodeType&&(r.className?(" "+r.className+" ").replace(vb," "):" ")){o=0;while(s=t[o++])i.indexOf(" "+s+" ")<0&&(i+=s+" ");u=n.trim(i),r.className!==u&&(r.className=u)}return this},removeClass:function(e){var t,r,i,s,o,u,a=0===arguments.length||"string"==typeof e&&e,f=0,l=this.length;if(n.isFunction(e))return this.each(function(t){n(this).removeClass(e.call(this,t,this.className))});if(a)for(t=(e||"").match(H)||[];l>f;f++)if(r=this[f],i=1===r.nodeType&&(r.className?(" "+r.className+" ").replace(vb," "):"")){o=0;while(s=t[o++])while(i.indexOf(" "+s+" ")>=0)i=i.replace(" "+s+" "," ");u=e?n.trim(i):"",r.className!==u&&(r.className=u)}return this},toggleClass:function(e,t){var r=typeof e;return"boolean"==typeof t&&"string"===r?t?this.addClass(e):this.removeClass(e):this.each(n.isFunction(e)?function(r){n(this).toggleClass(e.call(this,r,this.className,t),t)}:function(){if("string"===r){var t,i=0,s=n(this),o=e.match(H)||[];while(t=o[i++])s.hasClass(t)?s.removeClass(t):s.addClass(t)}else(r===V||"boolean"===r)&&(this.className&&O.set(this,"__className__",this.className),this.className=this.className||e===!1?"":O.get(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,r=this.length;r>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(vb," ").indexOf(t)>=0)return!0;return!1}});var wb=/\r/g;n.fn.extend({val:function(e){var t,r,i,s=this[0];if(arguments.length)return i=n.isFunction(e),this.each(function(r){var s;1===this.nodeType&&(s=i?e.call(this,r,n(this).val()):e,null==s?s="":"number"==typeof s?s+="":n.isArray(s)&&(s=n.map(s,function(e){return null==e?"":e+""})),t=n.valHooks[this.type]||n.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,s,"value")||(this.value=s))});if(s)return t=n.valHooks[s.type]||n.valHooks[s.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(r=t.get(s,"value"))?r:(r=s.value,"string"==typeof r?r.replace(wb,""):null==r?"":r)}}),n.extend({valHooks:{option:{get:function(e){var t=n.find.attr(e,"value");return null!=t?t:n.trim(n.text(e))}},select:{get:function(e){for(var t,r,i=e.options,s=e.selectedIndex,o="select-one"===e.type||0>s,u=o?null:[],a=o?s+1:i.length,f=0>s?a:o?s:0;a>f;f++)if(r=i[f],!(!r.selected&&f!==s||(k.optDisabled?r.disabled:null!==r.getAttribute("disabled"))||r.parentNode.disabled&&n.nodeName(r.parentNode,"optgroup"))){if(t=n(r).val(),o)return t;u.push(t)}return u},set:function(e,t){var r,i,s=e.options,o=n.makeArray(t),u=s.length;while(u--)i=s[u],(i.selected=n.inArray(i.value,o)>=0)&&(r=!0);return r||(e.selectedIndex=-1),o}}}}),n.each(["radio","checkbox"],function(){n.valHooks[this]={set:function(e,t){return n.isArray(t)?e.checked=n.inArray(n(e).val(),t)>=0:void 0}},k.checkOn||(n.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),n.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){n.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),n.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var xb=n.now(),yb=/\?/;n.parseJSON=function(e){return JSON.parse(e+"")},n.parseXML=function(e){var t,r;if(!e||"string"!=typeof e)return null;try{r=new DOMParser,t=r.parseFromString(e,"text/xml")}catch(i){t=void 0}return(!t||t.getElementsByTagName("parsererror").length)&&n.error("Invalid XML: "+e),t};var zb,Ab,Bb=/#.*$/,Cb=/([?&])_=[^&]*/,Db=/^(.*?):[ \t]*([^\r\n]*)$/gm,Eb=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Fb=/^(?:GET|HEAD)$/,Gb=/^\/\//,Hb=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Ib={},Jb={},Kb="*/".concat("*");try{Ab=location.href}catch(Lb){Ab=l.createElement("a"),Ab.href="",Ab=Ab.href}zb=Hb.exec(Ab.toLowerCase())||[],n.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ab,type:"GET",isLocal:Eb.test(zb[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Kb,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":n.parseJSON,"text xml":n.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Ob(Ob(e,n.ajaxSettings),t):Ob(n.ajaxSettings,e)},ajaxPrefilter:Mb(Ib),ajaxTransport:Mb(Jb),ajax:function(e,t){function x(e,t,o,a){var l,g,y,w,S,x=t;2!==b&&(b=2,u&&clearTimeout(u),r=void 0,s=a||"",E.readyState=e>0?4:0,l=e>=200&&300>e||304===e,o&&(w=Pb(c,E,o)),w=Qb(c,w,E,l),l?(c.ifModified&&(S=E.getResponseHeader("Last-Modified"),S&&(n.lastModified[i]=S),S=E.getResponseHeader("etag"),S&&(n.etag[i]=S)),204===e||"HEAD"===c.type?x="nocontent":304===e?x="notmodified":(x=w.state,g=w.data,y=w.error,l=!y)):(y=x,(e||!x)&&(x="error",0>e&&(e=0))),E.status=e,E.statusText=(t||x)+"",l?d.resolveWith(h,[g,x,E]):d.rejectWith(h,[E,x,y]),E.statusCode(m),m=void 0,f&&p.trigger(l?"ajaxSuccess":"ajaxError",[E,c,l?g:y]),v.fireWith(h,[E,x]),f&&(p.trigger("ajaxComplete",[E,c]),--n.active||n.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=void 0),t=t||{};var r,i,s,o,u,a,f,l,c=n.ajaxSetup({},t),h=c.context||c,p=c.context&&(h.nodeType||h.jquery)?n(h):n.event,d=n.Deferred(),v=n.Callbacks("once memory"),m=c.statusCode||{},g={},y={},b=0,w="canceled",E={readyState:0,getResponseHeader:function(e){var t;if(2===b){if(!o){o={};while(t=Db.exec(s))o[t[1].toLowerCase()]=t[2]}t=o[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===b?s:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return b||(e=y[n]=y[n]||e,g[e]=t),this},overrideMimeType:function(e){return b||(c.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>b)for(t in e)m[t]=[m[t],e[t]];else E.always(e[E.status]);return this},abort:function(e){var t=e||w;return r&&r.abort(t),x(0,t),this}};if(d.promise(E).complete=v.add,E.success=E.done,E.error=E.fail,c.url=((e||c.url||Ab)+"").replace(Bb,"").replace(Gb,zb[1]+"//"),c.type=t.method||t.type||c.method||c.type,c.dataTypes=n.trim(c.dataType||"*").toLowerCase().match(H)||[""],null==c.crossDomain&&(a=Hb.exec(c.url.toLowerCase()),c.crossDomain=!(!a||a[1]===zb[1]&&a[2]===zb[2]&&(a[3]||("http:"===a[1]?"80":"443"))===(zb[3]||("http:"===zb[1]?"80":"443")))),c.data&&c.processData&&"string"!=typeof c.data&&(c.data=n.param(c.data,c.traditional)),Nb(Ib,c,t,E),2===b)return E;f=c.global,f&&0===n.active++&&n.event.trigger("ajaxStart"),c.type=c.type.toUpperCase(),c.hasContent=!Fb.test(c.type),i=c.url,c.hasContent||(c.data&&(i=c.url+=(yb.test(i)?"&":"?")+c.data,delete c.data),c.cache===!1&&(c.url=Cb.test(i)?i.replace(Cb,"$1_="+xb++):i+(yb.test(i)?"&":"?")+"_="+xb++)),c.ifModified&&(n.lastModified[i]&&E.setRequestHeader("If-Modified-Since",n.lastModified[i]),n.etag[i]&&E.setRequestHeader("If-None-Match",n.etag[i])),(c.data&&c.hasContent&&c.contentType!==!1||t.contentType)&&E.setRequestHeader("Content-Type",c.contentType),E.setRequestHeader("Accept",c.dataTypes[0]&&c.accepts[c.dataTypes[0]]?c.accepts[c.dataTypes[0]]+("*"!==c.dataTypes[0]?", "+Kb+"; q=0.01":""):c.accepts["*"]);for(l in c.headers)E.setRequestHeader(l,c.headers[l]);if(!c.beforeSend||c.beforeSend.call(h,E,c)!==!1&&2!==b){w="abort";for(l in{success:1,error:1,complete:1})E[l](c[l]);if(r=Nb(Jb,c,t,E)){E.readyState=1,f&&p.trigger("ajaxSend",[E,c]),c.async&&c.timeout>0&&(u=setTimeout(function(){E.abort("timeout")},c.timeout));try{b=1,r.send(g,x)}catch(S){if(!(2>b))throw S;x(-1,S)}}else x(-1,"No Transport");return E}return E.abort()},getJSON:function(e,t,r){return n.get(e,t,r,"json")},getScript:function(e,t){return n.get(e,void 0,t,"script")}}),n.each(["get","post"],function(e,t){n[t]=function(e,r,i,s){return n.isFunction(r)&&(s=s||i,i=r,r=void 0),n.ajax({url:e,type:t,dataType:s,data:r,success:i})}}),n.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){n.fn[t]=function(e){return this.on(t,e)}}),n._evalUrl=function(e){return n.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})};var Rb=/%20/g,Sb=/\[\]$/,Tb=/\r?\n/g,Ub=/^(?:submit|button|image|reset|file)$/i,Vb=/^(?:input|select|textarea|keygen)/i;n.param=function(e,t){var r,i=[],s=function(e,t){t=n.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=n.ajaxSettings&&n.ajaxSettings.traditional),n.isArray(e)||e.jquery&&!n.isPlainObject(e))n.each(e,function(){s(this.name,this.value)});else for(r in e)Wb(r,e[r],t,s);return i.join("&").replace(Rb,"+")},n.fn.extend({serialize:function(){return n.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=n.prop(this,"elements");return e?n.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!n(this).is(":disabled")&&Vb.test(this.nodeName)&&!Ub.test(e)&&(this.checked||!U.test(e))}).map(function(e,t){var r=n(this).val();return null==r?null:n.isArray(r)?n.map(r,function(e){return{name:t.name,value:e.replace(Tb,"\r\n")}}):{name:t.name,value:r.replace(Tb,"\r\n")}}).get()}}),n.ajaxSettings.xhr=function(){try{return new XMLHttpRequest}catch(e){}};var Xb=0,Yb={},Zb={0:200,1223:204},$b=n.ajaxSettings.xhr();a.ActiveXObject&&n(a).on("unload",function(){for(var e in Yb)Yb[e]()}),k.cors=!!$b&&"withCredentials"in $b,k.ajax=$b=!!$b,n.ajaxTransport(function(e){var t;return k.cors||$b&&!e.crossDomain?{send:function(n,r){var i,s=e.xhr(),o=++Xb;if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(i in e.xhrFields)s[i]=e.xhrFields[i];e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(i in n)s.setRequestHeader(i,n[i]);t=function(e){return function(){t&&(delete Yb[o],t=s.onload=s.onerror=null,"abort"===e?s.abort():"error"===e?r(s.status,s.statusText):r(Zb[s.status]||s.status,s.statusText,"string"==typeof s.responseText?{text:s.responseText}:void 0,s.getAllResponseHeaders()))}},s.onload=t(),s.onerror=t("error"),t=Yb[o]=t("abort");try{s.send(e.hasContent&&e.data||null)}catch(u){if(t)throw u}},abort:function(){t&&t()}}:void 0}),n.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return n.globalEval(e),e}}}),n.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),n.ajaxTransport("script",function(e){if(e.crossDomain){var t,r;return{send:function(i,s){t=n("<script>").prop({async:!0,charset:e.scriptCharset,src:e.url}).on("load error",r=function(e){t.remove(),r=null,e&&s("error"===e.type?404:200,e.type)}),l.head.appendChild(t[0])},abort:function(){r&&r()}}}});var _b=[],ac=/(=)\?(?=&|$)|\?\?/;n.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=_b.pop()||n.expando+"_"+xb++;return this[e]=!0,e}}),n.ajaxPrefilter("json jsonp",function(e,t,r){var i,s,o,u=e.jsonp!==!1&&(ac.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&ac.test(e.data)&&"data");return u||"jsonp"===e.dataTypes[0]?(i=e.jsonpCallback=n.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,u?e[u]=e[u].replace(ac,"$1"+i):e.jsonp!==!1&&(e.url+=(yb.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return o||n.error(i+" was not called"),o[0]},e.dataTypes[0]="json",s=a[i],a[i]=function(){o=arguments},r.always(function(){a[i]=s,e[i]&&(e.jsonpCallback=t.jsonpCallback,_b.push(i)),o&&n.isFunction(s)&&s(o[0]),o=s=void 0}),"script"):void 0}),n.parseHTML=function(e,t,r){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(r=t,t=!1),t=t||l;var i=y.exec(e),s=!r&&[];return i?[t.createElement(i[1])]:(i=n.buildFragment([e],t,s),s&&s.length&&n(s).remove(),n.merge([],i.childNodes))};var bc=n.fn.load;n.fn.load=function(e,t,r){if("string"!=typeof e&&bc)return bc.apply(this,arguments);var i,s,o,u=this,a=e.indexOf(" ");return a>=0&&(i=n.trim(e.slice(a)),e=e.slice(0,a)),n.isFunction(t)?(r=t,t=void 0):t&&"object"==typeof t&&(s="POST"),u.length>0&&n.ajax({url:e,type:s,dataType:"html",data:t}).done(function(e){o=arguments,u.html(i?n("<div>").append(n.parseHTML(e)).find(i):e)}).complete(r&&function(e,t){u.each(r,o||[e.responseText,t,e])}),this},"function"==typeof define&&define.amd&&define("jquery",[],function(){return n});var cc=a.jQuery,dc=a.$;return n.noConflict=function(e){return a.$===n&&(a.$=dc),e&&a.jQuery===n&&(a.jQuery=cc),n},typeof b===V&&(a.jQuery=a.$=n),n}),function(e,t){if(typeof define=="function"&&define.amd)define("backbone",["underscore","jquery","exports"],function(n,r,i){e.Backbone=e.Exoskeleton=t(e,i,n,r)});else if(typeof exports!="undefined"){var n,r;try{n=require("underscore")}catch(i){}try{r=require("jquery")}catch(i){}t(e,exports,n,r)}else e.Backbone=e.Exoskeleton=t(e,{},e._,e.jQuery||e.Zepto||e.ender||e.$)}(this,function(e,t,n,r){"use strict";var i=e.Backbone,s=e.Exoskeleton,o=t.utils=n=n||{};t.$=r;var u=[],a=u.push,f=u.slice,l={}.toString;t.noConflict=function(){return e.Backbone=i,e.Exoskeleton=s,this},t.emulateHTTP=!1,t.emulateJSON=!1,t.extend=function(e,t){var r=this,i;e&&hasOwnProperty.call(e,"constructor")?i=e.constructor:i=function(){return r.apply(this,arguments)},n.extend(i,r,t);var s=function(){this.constructor=i};return s.prototype=r.prototype,i.prototype=new s,e&&n.extend(i.prototype,e),i.__super__=r.prototype,i};var c=function(){throw new Error('A "url" property or function must be specified')},h=function(e,t){var n=t.error;t.error=function(r){n&&n(e,r,t),e.trigger("error",e,r,t)}},p=function(e){return typeof n[e]=="function"};o.result=function(t,n){var r=t?t[n]:undefined;return typeof r=="function"?t[n]():r},o.defaults=function(t){return f.call(arguments,1).forEach(function(e){for(var n in e)t[n]===undefined&&(t[n]=e[n])}),t},o.extend=function(t){return f.call(arguments,1).forEach(function(e){for(var n in e)t[n]=e[n]}),t};var d={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};o.escape=function(t){return t==null?"":String(t).replace(/[&<>"']/g,function(e){return d[e]})},o.sortBy=function(e,t,n){var r=typeof t=="function"?t:function(e){return e[t]};return e.map(function(e,t,i){return{value:e,index:t,criteria:r.call(n,e,t,i)}}).sort(function(e,t){var n=e.criteria,r=t.criteria;if(n!==r){if(n>r||n===void 0)return 1;if(n<r||r===void 0)return-1}return e.index-t.index}).map(function(e){return e.value})};var v=0;o.uniqueId=function(t){var n=++v+"";return t?t+n:n};var m=function(e,t,n,r){if(e===t)return e!==0||1/e==1/t;if(e==null||t==null)return e===t;var i=l.call(e);if(i!=l.call(t))return!1;switch(i){case"[object String]":return e==String(t);case"[object Number]":return e!==+e?t!==+t:e===0?1/e===1/t:e===+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object RegExp]":return e.source==t.source&&e.global==t.global&&e.multiline==t.multiline&&e.ignoreCase==t.ignoreCase}if(typeof e!="object"||typeof t!="object")return!1;var s=n.length;while(s--)if(n[s]==e)return r[s]==t;var o=e.constructor,u=t.constructor;if(o===u||typeof o=="function"&&o instanceof o&&typeof u=="function"&&u instanceof u){n.push(e),r.push(t);var a=0,f=!0;if(i==="[object Array]"){a=e.length,f=a===t.length;if(f)while(a--)if(!(f=m(e[a],t[a],n,r)))break}else{for(var c in e)if(hasOwnProperty.call(e,c)){a++;if(!(f=hasOwnProperty.call(t,c)&&m(e[c],t[c],n,r)))break}if(f){for(c in t)if(hasOwnProperty.call(t,c)&&!(a--))break;f=!a}}return n.pop(),r.pop(),f}return!1};o.isEqual=function(e,t){return m(e,t,[],[])},o.matchesSelector=function(){if(typeof document=="undefined")return;var e="MatchesSelector",t=document.createElement("div"),n;["matches","webkit"+e,"moz"+e,"ms"+e].some(function(e){var r=e in t;return n=e,r});if(!n)throw new Error("Element#matches is not supported");return function(e,t){return e[n](t)}}(),o.delegate=function(e,t,n,r){typeof n=="function"&&(r=n,n=null);if(typeof r!="function")throw new TypeError("View#delegate expects callback function");var i=e.el,s=r.bind(e),u=n?function(e){for(var t=e.target;t&&t!==i;t=t.parentNode)if(o.matchesSelector(t,n))return e.delegateTarget=t,s(e)}:s;return i.addEventListener(t,u,!1),e._handlers.push({eventName:t,selector:n,callback:r,handler:u}),u},o.undelegate=function(e,t,n,r){typeof n=="function"&&(r=n,n=null);var i=e._handlers,s=function(t){e.el.removeEventListener(t.eventName,t.handler,!1)};!t&&!n&&!r?(i.forEach(s),e._handlers=[]):i.filter(function(e){return e.eventName===t&&(r?e.callback===r:!0)&&(n?e.selector===n:!0)}).forEach(function(e){s(e),i.splice(i.indexOf(e),1)})},o.ajax=function(){var e=/^(?:application|text)\/xml/,n=/^application\/json/,r=function(t,r){return t==null&&(t=r.getResponseHeader("content-type")),e.test(t)?r.responseXML:n.test(t)?JSON.parse(r.responseText):r.responseText},i=function(e){return e.status>=200&&e.status<300||e.status===304||e.status===0&&window.location.protocol==="file:"},s=function(e,t,n){return function(){if(e.readyState!==4)return;var s=e.status,o=r(t.headers&&t.headers.Accept,e);if(i(e))t.success&&t.success(o),n&&n.resolve(o);else{var u=new Error("Server responded with a status of "+s);t.error&&t.error(e,s,u),n&&n.reject(e)}}};return function(e){if(e==null)throw new Error("You must provide options");e.type==null&&(e.type="GET");var n=new XMLHttpRequest,r=t.Deferred&&t.Deferred();e.contentType&&(e.headers==null&&(e.headers={}),e.headers["Content-Type"]=e.contentType);if(e.type==="GET"&&typeof e.data=="object"){var i="",o=function(e,t){return t==null?"":"&"+encodeURIComponent(e)+"="+encodeURIComponent(t)};for(var u in e.data)i+=o(u,e.data[u]);if(i){var a=e.url.indexOf("?")===-1?"?":"&";e.url+=a+i.substring(1)}}e.credentials&&(e.withCredentials=!0),n.addEventListener("readystatechange",s(n,e,r)),n.open(e.type,e.url,!0);if(e.headers)for(var u in e.headers)n.setRequestHeader(u,e.headers[u]);return e.beforeSend&&e.beforeSend(n),n.send(e.data),r?r.promise:undefined}}();var g=t.Events={on:function(e,t,n){if(!b(this,"on",e,[t,n])||!t)return this;this._events||(this._events={});var r=this._events[e]||(this._events[e]=[]);return r.push({callback:t,context:n,ctx:n||this}),this},once:function(e,t,n){if(!b(this,"once",e,[t,n])||!t)return this;var r=this,i,s=function(){if(i)return;i=!0,r.off(e,s),t.apply(this,arguments)};return s._callback=t,this.on(e,s,n)},off:function(e,t,n){var r,i,s,o,u,a,f,l;if(!this._events||!b(this,"off",e,[t,n]))return this;if(!e&&!t&&!n)return this._events=undefined,this;o=e?[e]:Object.keys(this._events);for(u=0,a=o.length;u<a;u++){e=o[u];if(s=this._events[e]){this._events[e]=r=[];if(t||n)for(f=0,l=s.length;f<l;f++)i=s[f],(t&&t!==i.callback&&t!==i.callback._callback||n&&n!==i.context)&&r.push(i);r.length||delete this._events[e]}}return this},trigger:function(e){if(!this._events)return this;var t=f.call(arguments,1);if(!b(this,"trigger",e,t))return this;var n=this._events[e],r=this._events.all;return n&&w(n,t),r&&w(r,arguments),this},stopListening:function(e,t,n){var r=this._listeningTo;if(!r)return this;var i=!t&&!n;!n&&typeof t=="object"&&(n=this),e&&((r={})[e._listenId]=e);for(var s in r)e=r[s],e.off(t,n,this),(i||!Object.keys(e._events).length)&&delete this._listeningTo[s];return this}},y=/\s+/,b=function(e,t,n,r){if(!n)return!0;var i;if(typeof n=="object"){for(var s in n)i=[s,n[s]],a.apply(i,r),e[t].apply(e,i);return!1}if(y.test(n)){var o=n.split(y);for(var u=0,f=o.length;u<f;u++)i=[o[u]],a.apply(i,r),e[t].apply(e,i);return!1}return!0},w=function(e,t){var n,r=-1,i=e.length,s=t[0],o=t[1],u=t[2];switch(t.length){case 0:while(++r<i)(n=e[r]).callback.call(n.ctx);return;case 1:while(++r<i)(n=e[r]).callback.call(n.ctx,s);return;case 2:while(++r<i)(n=e[r]).callback.call(n.ctx,s,o);return;case 3:while(++r<i)(n=e[r]).callback.call(n.ctx,s,o,u);return;default:while(++r<i)(n=e[r]).callback.apply(n.ctx,t)}},E={listenTo:"on",listenToOnce:"once"};Object.keys(E).forEach(function(e){var t=E[e];g[e]=function(e,r,i){var s=this._listeningTo||(this._listeningTo={}),o=e._listenId||(e._listenId=n.uniqueId("l"));return s[o]=e,!i&&typeof r=="object"&&(i=this),e[t](r,i,this),this}}),g.bind=g.on,g.unbind=g.off;var S=t.Model=function(e,t){var r=e||{};t||(t={}),this.cid=n.uniqueId("c"),this.attributes=Object.create(null),t.collection&&(this.collection=t.collection),t.parse&&(r=this.parse(r,t)||{}),r=n.defaults({},r,n.result(this,"defaults")),this.set(r,t),this.changed=Object.create(null),this.initialize.apply(this,arguments)};n.extend(S.prototype,g,{changed:null,validationError:null,idAttribute:"id",initialize:function(){},toJSON:function(e){return n.extend(Object.create(null),this.attributes)},sync:function(){return t.sync.apply(this,arguments)},get:function(e){return this.attributes[e]},escape:function(e){return n.escape(this.get(e))},has:function(e){return this.get(e)!=null},set:function(e,t,r){var i,s,o,u,a,f,l,c;if(e==null)return this;typeof e=="object"?(s=e,r=t):(s={})[e]=t,r||(r={});if(!this._validate(s,r))return!1;o=r.unset,a=r.silent,u=[],f=this._changing,this._changing=!0,f||(this._previousAttributes=n.extend(Object.create(null),this.attributes),this.changed={}),c=this.attributes,l=this._previousAttributes,this.idAttribute in s&&(this.id=s[this.idAttribute]);for(i in s)t=s[i],n.isEqual(c[i],t)||u.push(i),n.isEqual(l[i],t)?delete this.changed[i]:this.changed[i]=t,o?delete c[i]:c[i]=t;if(!a){u.length&&(this._pending=!0);for(var h=0,p=u.length;h<p;h++)this.trigger("change:"+u[h],this,c[u[h]],r)}if(f)return this;if(!a)while(this._pending)this._pending=!1,this.trigger("change",this,r);return this._pending=!1,this._changing=!1,this},unset:function(e,t){return this.set(e,void 0,n.extend({},t,{unset:!0}))},clear:function(e){var t={};for(var r in this.attributes)t[r]=void 0;return this.set(t,n.extend({},e,{unset:!0}))},hasChanged:function(e){return e==null?!!Object.keys(this.changed).length:hasOwnProperty.call(this.changed,e)},changedAttributes:function(e){if(!e)return this.hasChanged()?n.extend(Object.create(null),this.changed):!1;var t,r=!1,i=this._changing?this._previousAttributes:this.attributes;for(var s in e){if(n.isEqual(i[s],t=e[s]))continue;(r||(r={}))[s]=t}return r},previous:function(e){return e==null||!this._previousAttributes?null:this._previousAttributes[e]},previousAttributes:function(){return n.extend(Object.create(null),this._previousAttributes)},fetch:function(e){e=e?n.extend({},e):{},e.parse===void 0&&(e.parse=!0);var t=this,r=e.success;return e.success=function(n){if(!t.set(t.parse(n,e),e))return!1;r&&r(t,n,e),t.trigger("sync",t,n,e)},h(this,e),this.sync("read",this,e)},save:function(e,t,r){var i,s,o,u=this.attributes;e==null||typeof e=="object"?(i=e,r=t):(i={})[e]=t,r=n.extend({validate:!0},r);if(i&&!r.wait){if(!this.set(i,r))return!1}else if(!this._validate(i,r))return!1;i&&r.wait&&(this.attributes=n.extend(Object.create(null),u,i)),r.parse===void 0&&(r.parse=!0);var a=this,f=r.success;return r.success=function(e){a.attributes=u;var t=a.parse(e,r);r.wait&&(t=n.extend(i||{},t));if(t&&typeof t=="object"&&!a.set(t,r))return!1;f&&f(a,e,r),a.trigger("sync",a,e,r)},h(this,r),s=this.isNew()?"create":r.patch?"patch":"update",s==="patch"&&(r.attrs=i),o=this.sync(s,this,r),i&&r.wait&&(this.attributes=u),o},destroy:function(e){e=e?n.extend({},e):{};var t=this,r=e.success,i=function(){t.trigger("destroy",t,t.collection,e)};e.success=function(n){(e.wait||t.isNew())&&i(),r&&r(t,n,e),t.isNew()||t.trigger("sync",t,n,e)};if(this.isNew())return e.success(),!1;h(this,e);var s=this.sync("delete",this,e);return e.wait||i(),s},url:function(){var e=n.result(this,"urlRoot")||n.result(this.collection,"url")||c();return this.isNew()?e:e+(e.charAt(e.length-1)==="/"?"":"/")+encodeURIComponent(this.id)},parse:function(e,t){return e},clone:function(){return new this.constructor(this.attributes)},isNew:function(){return this.id==null},isValid:function(e){return this._validate({},n.extend(e||{},{validate:!0}))},_validate:function(e,t){if(!t.validate||!this.validate)return!0;e=n.extend(Object.create(null),this.attributes,e);var r=this.validationError=this.validate(e,t)||null;return r?(this.trigger("invalid",this,r,n.extend(t,{validationError:r})),!1):!0}});if(n.keys){var x=["keys","values","pairs","invert","pick","omit"];x.filter(p).forEach(function(e){S.prototype[e]=function(){var t=f.call(arguments);return t.unshift(this.attributes),n[e].apply(n,t)}})}var T=t.Collection=function(e,t){t||(t={}),t.model&&(this.model=t.model),t.comparator!==void 0&&(this.comparator=t.comparator),this._reset(),this.initialize.apply(this,arguments),e&&this.reset(e,n.extend({silent:!0},t))},N={add:!0,remove:!0,merge:!0},C={add:!0,remove:!1};n.extend(T.prototype,g,{model:typeof S=="undefined"?null:S,initialize:function(){},toJSON:function(e){return this.map(function(t){return t.toJSON(e)})},sync:function(){return t.sync.apply(this,arguments)},add:function(e,t){return this.set(e,n.extend({merge:!1},t,C))},remove:function(e,t){var n=!Array.isArray(e);e=n?[e]:e.slice(),t||(t={});var r,i,s,o;for(r=0,i=e.length;r<i;r++){o=e[r]=this.get(e[r]);if(!o)continue;delete this._byId[o.id],delete this._byId[o.cid],s=this.indexOf(o),this.models.splice(s,1),this.length--,t.silent||(t.index=s,o.trigger("remove",o,this,t)),this._removeReference(o)}return n?e[0]:e},set:function(e,t){t=n.defaults({},t,N),t.parse&&(e=this.parse(e,t));var r=!Array.isArray(e);e=r?e?[e]:[]:e.slice();var i,s,o,u,a,f,l,c=t.at,h=this.model,p=this.comparator&&c==null&&t.sort!==!1,d=typeof this.comparator=="string"?this.comparator:null,v=[],m=[],g={},y=t.add,b=t.merge,w=t.remove,E=!p&&y&&w?[]:!1;for(i=0,s=e.length;i<s;i++){a=e[i],a instanceof S?o=u=a:o=a[h.prototype.idAttribute];if(f=this.get(o))w&&(g[f.cid]=!0),b&&(a=a===u?u.attributes:a,t.parse&&(a=f.parse(a,t)),f.set(a,t),p&&!l&&f.hasChanged(d)&&(l=!0)),e[i]=f;else if(y){u=e[i]=this._prepareModel(a,t);if(!u)continue;v.push(u),u.on("all",this._onModelEvent,this),this._byId[u.cid]=u,u.id!=null&&(this._byId[u.id]=u)}E&&E.push(f||u)}if(w){for(i=0,s=this.length;i<s;++i)g[(u=this.models[i]).cid]||m.push(u);m.length&&this.remove(m,t)}if(v.length||E&&E.length){p&&(l=!0),this.length+=v.length;if(c!=null)for(i=0,s=v.length;i<s;i++)this.models.splice(c+i,0,v[i]);else{E&&(this.models.length=0);var x=E||v;for(i=0,s=x.length;i<s;i++)this.models.push(x[i])}}l&&this.sort({silent:!0});if(!t.silent){for(i=0,s=v.length;i<s;i++)(u=v[i]).trigger("add",u,this,t);(l||E&&E.length)&&this.trigger("sort",this,t)}return r?e[0]:e},reset:function(e,t){t||(t={});for(var r=0,i=this.models.length;r<i;r++)this._removeReference(this.models[r]);return t.previousModels=this.models,this._reset(),e=this.add(e,n.extend({silent:!0},t)),t.silent||this.trigger("reset",this,t),e},push:function(e,t){return this.add(e,n.extend({at:this.length},t))},pop:function(e){var t=this.at(this.length-1);return this.remove(t,e),t},unshift:function(e,t){return this.add(e,n.extend({at:0},t))},shift:function(e){var t=this.at(0);return this.remove(t,e),t},slice:function(){return f.apply(this.models,arguments)},get:function(e){return e==null?void 0:this._byId[e.id]||this._byId[e.cid]||this._byId[e]},at:function(e){return this.models[e]},where:function(e,t){return!e||!Object.keys(e).length?t?void 0:[]:this[t?"find":"filter"](function(t){for(var n in e)if(e[n]!==t.get(n))return!1;return!0})},findWhere:function(e){return this.where(e,!0)},sort:function(e){if(!this.comparator)throw new Error("Cannot sort a set without a comparator");return e||(e={}),typeof this.comparator=="string"||this.comparator.length===1?this.models=this.sortBy(this.comparator,this):this.models.sort(this.comparator.bind(this)),e.silent||this.trigger("sort",this,e),this},pluck:function(e){return this.models.map(function(t){return t.get(e)})},fetch:function(e){e=e?n.extend({},e):{},e.parse===void 0&&(e.parse=!0);var t=e.success,r=this;return e.success=function(n){var i=e.reset?"reset":"set";r[i](n,e),t&&t(r,n,e),r.trigger("sync",r,n,e)},h(this,e),this.sync("read",this,e)},create:function(e,t){t=t?n.extend({},t):{};if(!(e=this._prepareModel(e,t)))return!1;t.wait||this.add(e,t);var r=this,i=t.success;return t.success=function(e,t,n){n.wait&&r.add(e,n),i&&i(e,t,n)},e.save(null,t),e},parse:function(e,t){return e},clone:function(){return new this.constructor(this.models)},_reset:function(){this.length=0,this.models=[],this._byId=Object.create(null)},_prepareModel:function(e,t){if(e instanceof T.prototype.model)return e.collection||(e.collection=this),e;t=t?n.extend({},t):{},t.collection=this;var r=new this.model(e,t);return r.validationError?(this.trigger("invalid",this,r.validationError,t),!1):r},_removeReference:function(e){this===e.collection&&delete e.collection,e.off("all",this._onModelEvent,this)},_onModelEvent:function(e,t,n,r){if((e==="add"||e==="remove")&&n!==this)return;e==="destroy"&&this.remove(t,r),t&&e==="change:"+t.idAttribute&&(delete this._byId[t.previous(t.idAttribute)],t.id!=null&&(this._byId[t.id]=t)),this.trigger.apply(this,arguments)}});if(p("each")){var k=["forEach","each","map","collect","reduce","foldl","inject","reduceRight","foldr","find","detect","filter","select","reject","every","all","some","any","include","contains","invoke","max","min","toArray","size","first","head","take","initial","rest","tail","drop","last","without","difference","indexOf","shuffle","lastIndexOf","isEmpty","chain"];k.filter(p).forEach(function(e){T.prototype[e]=function(){var t=f.call(arguments);return t.unshift(this.models),n[e].apply(n,t)}});var L=["groupBy","countBy","sortBy"];L.filter(p).forEach(function(e){T.prototype[e]=function(t,r){var i=typeof t=="function"?t:function(e){return e.get(t)};return n[e](this.models,i,r)}})}else["forEach","map","filter","some","every","reduce","reduceRight","indexOf","lastIndexOf"].forEach(function(e){T.prototype[e]=function(t,n){return this.models[e](t,n)}}),T.prototype.find=function(e,t){var n;return this.some(function(r,i,s){if(e.call(t,r,i,s))return n=r,!0}),n},["sortBy"].forEach(function(e){T.prototype[e]=function(t,r){var i=typeof t=="function"?t:function(e){return e.get(t)};return n[e](this.models,i,r)}});var A=/^(\S+)\s*(.*)$/,O=["model","collection","el","id","attributes","className","tagName","events"],M=t.View=function(e){this.cid=n.uniqueId("view"),e&&Object.keys(e).forEach(function(t){O.indexOf(t)!==-1&&(this[t]=e[t])},this),this._handlers=[],this._ensureElement(),this.initialize.apply(this,arguments),this.delegateEvents()};n.extend(M.prototype,g,{useNative:!1,tagName:"div",$:function(e){return t.$&&!this.useNative?this.$el.find(e):this.findAll(e)},find:function(e){return this.el.querySelector(e)},findAll:function(e){return f.call(this.el.querySelectorAll(e))},initialize:function(){},render:function(){return this},remove:function(){var e;return t.$&&!this.useNative?this.$el.remove():(e=this.el.parentNode)&&e.removeChild(this.el),this.stopListening(),this},setElement:function(e,n){return t.$&&!this.useNative?(this.$el&&this.undelegateEvents(),this.$el=e instanceof t.$?e:t.$(e),this.el=this.$el[0]):(this.el&&this.undelegateEvents(),this.el=typeof e=="string"?document.querySelector(e):e),n!==!1&&this.delegateEvents(),this},delegateEvents:function(e,r){if(!e&&!(e=n.result(this,"events")))return this;r||this.undelegateEvents();for(var i in e){var s=e[i];typeof s!="function"&&(s=this[e[i]]);var u=i.match(A),a=u[1],f=u[2];t.$&&!this.useNative?(a+=".delegateEvents"+this.cid,s=s.bind(this),this.$el.on(a,f?f:null,s)):o.delegate(this,a,f,s)}return this},undelegateEvents:function(){return t.$&&!this.useNative?this.$el.off(".delegateEvents"+this.cid):o.undelegate(this),this},_ensureElement:function(){if(!this.el){var e=n.extend({},n.result(this,"attributes"));this.id&&(e.id=n.result(this,"id")),this.className&&(e.className=n.result(this,"className")),e["class"]&&(e.className=e["class"]);var t=n.extend(document.createElement(n.result(this,"tagName")),e);this.setElement(t,!1)}else this.setElement(n.result(this,"el"),!1)}}),t.sync=function(e,r,i){var s=_[e];n.defaults(i||(i={}),{emulateHTTP:t.emulateHTTP,emulateJSON:t.emulateJSON});var o={type:s,dataType:"json"};i.url||(o.url=n.result(r,"url")||c()),i.data==null&&r&&(e==="create"||e==="update"||e==="patch")&&(o.contentType="application/json",o.data=JSON.stringify(i.attrs||r.toJSON(i))),i.emulateJSON&&(o.contentType="application/x-www-form-urlencoded",o.data=o.data?{model:o.data}:{});if(i.emulateHTTP&&(s==="PUT"||s==="DELETE"||s==="PATCH")){o.type="POST",i.emulateJSON&&(o.data._method=s);var u=i.beforeSend;i.beforeSend=function(e){e.setRequestHeader("X-HTTP-Method-Override",s);if(u)return u.apply(this,arguments)}}o.type!=="GET"&&!i.emulateJSON&&(o.processData=!1);var a=i.xhr=t.ajax(n.extend(o,i));return r.trigger("request",r,a,i),a};var _={create:"POST",update:"PUT",patch:"PATCH","delete":"DELETE",read:"GET"};t.ajax=t.$?function(){return t.$.ajax.apply(t.$,arguments)}:o.ajax,t.$&&(t.Deferred=function(){return new t.$.Deferred});var D=t.Router=function(e){e||(e={}),e.routes&&(this.routes=e.routes),this._bindRoutes(),this.initialize.apply(this,arguments)},P=/\((.*?)\)/g,H=/(\(\?)?:\w+/g,B=/\*\w+/g,j=/[\-{}\[\]+?.,\\\^$|#\s]/g,F=function(e){return e?typeof e=="object"&&l.call(e)==="[object RegExp]":!1};n.extend(D.prototype,g,{initialize:function(){},route:function(e,n,r){F(e)||(e=this._routeToRegExp(e)),typeof n=="function"&&(r=n,n=""),r||(r=this[n]);var i=this;return t.history.route(e,function(s){var o=i._extractParameters(e,s);r&&r.apply(i,o),i.trigger.apply(i,["route:"+n].concat(o)),i.trigger("route",n,o),t.history.trigger("route",i,n,o)}),this},navigate:function(e,n){return t.history.navigate(e,n),this},_bindRoutes:function(){if(!this.routes)return;this.routes=n.result(this,"routes");var e,t=Object.keys(this.routes);while((e=t.pop())!=null)this.route(e,this.routes[e])},_routeToRegExp:function(e){return e=e.replace(j,"\\$&").replace(P,"(?:$1)?").replace(H,function(e,t){return t?e:"([^/]+)"}).replace(B,"(.*?)"),new RegExp("^"+e+"$")},_extractParameters:function(e,t){var n=e.exec(t).slice(1);return n.map(function(e){return e?decodeURIComponent(e):null})}});var I=t.History=function(){this.handlers=[],this.checkUrl=this.checkUrl.bind(this),typeof window!="undefined"&&(this.location=window.location,this.history=window.history)},q=/^[#\/]|\s+$/g,R=/^\/+|\/+$/g,U=/\/$/,z=/[#].*$/;return I.started=!1,n.extend(I.prototype,g,{getHash:function(e){var t=(e||this).location.href.match(/#(.*)$/);return t?t[1]:""},getFragment:function(e){if(e==null)if(this._wantsPushState||!this._wantsHashChange){e=this.location.pathname+this.location.search;var t=this.root.replace(U,"");e.indexOf(t)||(e=e.slice(t.length))}else e=this.getHash();return e.replace(q,"")},start:function(e){if(I.started)throw new Error("Backbone.history has already been started");I.started=!0,this.options=n.extend({root:"/"},this.options,e),this.root=this.options.root,this._wantsHashChange=this.options.hashChange!==!1,this._wantsPushState=!!this.options.pushState;var t=this.getFragment();this.root=("/"+this.root+"/").replace(R,"/"),this._wantsPushState?window.addEventListener("popstate",this.checkUrl,!1):this._wantsHashChange&&window.addEventListener("hashchange",this.checkUrl,!1),this.fragment=t;var r=this.location,i=r.pathname.replace(/[^\/]$/,"$&/")===this.root;this._wantsHashChange&&this._wantsPushState&&i&&r.hash&&(this.fragment=this.getHash().replace(q,""),this.history.replaceState({},document.title,this.root+this.fragment));if(!this.options.silent)return this.loadUrl()},stop:function(){window.removeEventListener("popstate",this.checkUrl),window.removeEventListener("hashchange",this.checkUrl),I.started=!1},route:function(e,t){this.handlers.unshift({route:e,callback:t})},checkUrl:function(){var e=this.getFragment();if(e===this.fragment)return!1;this.loadUrl()},loadUrl:function(e){return e=this.fragment=this.getFragment(e),this.handlers.some(function(t){if(t.route.test(e))return t.callback(e),!0})},navigate:function(e,t){if(!I.started)return!1;if(!t||t===!0)t={trigger:!!t};var n=this.root+(e=this.getFragment(e||""));e=e.replace(z,"");if(this.fragment===e)return;this.fragment=e,e===""&&n!=="/"&&(n=n.slice(0,-1));if(this._wantsPushState)this.history[t.replace?"replaceState":"pushState"]({},document.title,n);else{if(!this._wantsHashChange)return this.location.assign(n);this._updateHash(this.location,e,t.replace)}if(t.trigger)return this.loadUrl(e)},_updateHash:function(e,t,n){if(n){var r=e.href.replace(/(javascript:|#).*$/,"");e.replace(r+"#"+t)}else e.hash="#"+t}}),["Model","Collection","Router","View","History"].forEach(function(e){var n=t[e];n&&(n.extend=t.extend)}),n.extend(t,g),t.history=new I,t}),function(e,t){if(typeof define=="function"&&define.amd)define("superhero",["backbone","underscore"],function(n,r){return e.Superhero=t(e,n,r)});else if(typeof exports!="undefined"){var n=require("backbone"),r=require("underscore");module.exports=t(e,n,r)}else e.Superhero=t(e,e.Backbone,e._)}(this,function(e,t,n){"use strict";var r={};r.Events=t.Events,r.ajax=t.ajax;var i=function(e,t){var r=(":"+e).replace(/:([a-z])/g,function(e){return e[1].toUpperCase()}),i="on"+r;n.isFunction(this[i])&&this[i](),this.trigger(e,t)};r.createNameSpace=function(e){var t=e.split("."),n=window;for(var r=0,i=t.length;r<i;r++)n=n[t[r]]=n[t[r]]||{};return n},r.Module=function(e){this.options=e||{},n.isFunction(this.initialize)&&this.initialize(this.options)},r.Module.extend=t.View.extend,r.Module.prototype._triggerCallback=i,n.extend(r.Module.prototype,r.Events,{close:function(){this._triggerCallback("close"),this.stopListening(),this.off()}}),r.Router=t.Router.extend({execute:function(e,n){this.trigger("before:route"),t.Router.prototype.execute.apply(this,arguments)}}),r.history=t.history;var s="state:default",o="state:initialized",u="state:closed",a="state:removed";r.BaseView=t.View.extend({useNative:!0,constructor:function(){this._currentState=s,t.View.apply(this,arguments),n.bindAll(this,"_triggerCallback","_resumeInitializationQueue"),this._appendToInitializationQueue("_initializeUI"),this._resumeInitializationQueue()},setState:function(e){this._currentState=e},getState:function(){return this._currentState},hasState:function(e){return this._currentState===e},enforceState:function(e){var t=Array.prototype.slice.call(arguments);if(t.indexOf(this._currentState)===-1)throw new Error("Superhero.View cannot not change state from "+this._currentState)},close:function(){this.enforceState(o,s),this._triggerCallback("close"),this._disposeUI(),this.undelegateEvents(),this.stopListening(),this.off(),this.el=null,this.$el=null,this.setState(u)},remove:function(){this.enforceState(o,u);var e=this.el;return this.hasState(o)&&this.close(),this._triggerCallback("before:remove"),e.parentNode&&e.parentNode.removeChild(e),this._triggerCallback("remove"),this.setState(a),this},_appendToInitializationQueue:function(e,t){this._initializationQueue=this._initializationQueue||[],!t&&t!==0&&(t=this._initializationQueue.length),this._initializationQueue.splice(t,0,{status:"new",methodName:e})},_setInitializationComplete:function(e){if(!this.hasState(s))return;for(var t=0,n=this._initializationQueue.length;t<n;t++)this._initializationQueue[t].methodName===e&&(this._initializationQueue[t].status="completed");this._resumeInitializationQueue()},_resumeInitializationQueue:function(){if(!this.hasState(s))return;for(var e=0,t=this._initializationQueue.length;e<t;e++){var n=this._initializationQueue[e];if(n.status==="new"){var r=this[n.methodName].call(this);n.status="running",r&&this._setInitializationComplete(n.methodName);return}if(n.status==="running")return}this.setState(o),this._triggerCallback("initialized")},_initializeUI:function(){this._uiBindings=n.clone(this.ui),this.ui={};for(var e in this._uiBindings)if(this._uiBindings.hasOwnProperty(e)){var t=this.el.querySelectorAll(this._uiBindings[e]);this.ui[e]=t.length<=1?t[0]:t}return!0},_disposeUI:function(){for(var e in this.ui)this.ui.hasOwnProperty(e)&&(this.ui[e]=null);this.ui=this._uiBindings,delete this._uiBindings},_triggerCallback:i}),r.Component=r.BaseView.extend({}),r.View=r.BaseView.extend({constructor:function(){var e=arguments[0]||{};e.template&&(this.template=e.template),this.template&&this._appendToInitializationQueue("_initializeTemplate",0),e.region&&(this.region=e.region,this._appendToInitializationQueue("_waitOnAddedToRegion")),this._appendToInitializationQueue("_initializeComponents"),r.BaseView.apply(this,arguments)},close:function(){r.BaseView.prototype.close.apply(this,arguments),this._disposeComponents(),this.region=null},updateTemplate:function(e){this._renderTemplate(e)},_initializeTemplate:function(){if(this.template){if(r.TemplateCache.has(this.template))return this._renderTemplate(),!0;var e=this.template;this.listenTo(r.TemplateCache,"template:retrieved",function(t){t.id===e&&(this._renderTemplate(),this._setInitializationComplete("_initializeTemplate"),this.stopListening(r.TemplateCache,"template:retrieved"))}),r.TemplateCache._retrieveTemplate(this.template)}return!1},_renderTemplate:function(e){this._triggerCallback("before:template:rendered"),e=e||this.model||{},n.isFunction(e.toJSON)&&(e=e.toJSON()),this.el.innerHTML=r.TemplateCache.get(this.template)(e||{}),this._triggerCallback("template:rendered")},_waitOnAddedToRegion:function(){return!1},_addedToRegion:function(){this._setInitializationComplete("_waitOnAddedToRegion"),this._triggerCallback("view:added")},addComponent:function(e,t,r,i){var s=r;n.isString(r)&&(s=this.el.querySelector(r)),i=i||{},i.el=s;var o=new t(i);return this._createdComponentModules.push(o),n.isArray(this.components[e])?this.components[e].push(o):this.components[e]=o,o},_initializeComponents:function(){this._componentBindings=n.clone(this.components),this.components={},this._createdComponentModules=[];for(var e in this._componentBindings)if(this._componentBindings.hasOwnProperty(e)){var t=this._componentBindings[e],r=this.el.querySelectorAll(t.selector);if(r.length===1)this.addComponent(e,t.module||t.type,r[0],t.options);else{this.components[e]=[];for(var i=0,s=r.length;i<s;i++)this.addComponent(e,t.module||t.type,r[i],t.options)}}return!0},_disposeComponents:function(){if(!this._createdComponentModules)return;for(var e=0,t=this._createdComponentModules.length;e<t;e++){var n=this._createdComponentModules[e];n.hasState(o)&&n.close()}this.components=this._componentBindings,delete this._createdComponentModules,delete this._componentBindings}}),r.LayoutView=r.View.extend({constructor:function(){this._appendToInitializationQueue("_initializeRegions"),r.View.apply(this,arguments)},close:function(){r.View.prototype.close.apply(this,arguments),this._disposeRegions()},_initializeRegions:function(){for(var e in this.regions)this.regions.hasOwnProperty(e)&&r.RegionManager.addRegion(e,this.el.querySelector(this.regions[e]));return!0},_disposeRegions:function(){for(var e in this.regions)this.regions.hasOwnProperty(e)&&r.RegionManager.removeRegion(e)}}),r.Collection=t.Collection.extend({}),r.Model=t.Model.extend({parse:function(e){for(var n in this.model)if(this.model.hasOwnProperty(n)){var r=this.model[n],i=e[n];e[n]=new r(i,{parse:!0}),e[n]instanceof t.Model?e[n].urlRoot=this.url()+"/"+n:e[n].url=this.url()+"/"+n}return e},isValid:function(e,t){var r={};r[e]=t;var i=this._executeValidation(r);return n.isEmpty(i)},validate:function(e){var t=this._executeValidation(e);if(!n.isEmpty(t))return t;this.validationError={},this.trigger("valid",this)},_executeValidation:function(e){var t={};for(var r in e){if(!this.validation||!this.validation[r])continue;var i=this.validation[r];n.isFunction(i)&&i(r,e[r])===!1&&(t[r]="invalid"),n.isRegExp(i)&&(i.lastIndex=0,i.test(e[r])===!1&&(t[r]="invalid"))}return t}});var f=r.Module.extend({_registeredRegions:{},addRegions:function(e){for(var t in e)e.hasOwnProperty(t)&&this.addRegion(t,e[t])},addRegion:function(e,t){this._registeredRegions[e]=new r.Region({name:e,el:t})},removeRegion:function(e){var t=this._registeredRegions[e];t.close(),delete this._registeredRegions[e]},get:function(e){return this._registeredRegions[e]}});r.RegionManager=new f,r.Region=r.Module.extend({initialize:function(e){this.name=e.name,this.el=e.el,this._activeViewClass=null,this._activeView=null,this._allowSameView=!1},show:function(e,t){if(e!==this._activeViewClass||this._allowSameView)t=t||{},t.region=this,this._pendingView=new e(t),this._needsTransition=this._activeView&&this._activeView!==null||!1,this._needsTransition&&this._detachView(this._activeView),this._triggerCallback("before:view:added",e),this._activeView=this._pendingView,this._activeViewClass=e,this._attachView(this._activeView),this._pendingView=null,this._allowSameView=!1;return this._activeView},allowSameView:function(){this._allowSameView=!0},clear:function(){this._activeView&&this._detachView(this._activeView,!0)},close:function(){if(!this._activeView)return;this._activeView.remove(),this._activeView=null,this._activeViewClass=null,this.name=null,this.el=null},_attachView:function(e){var t=e.render().el;this._activeView=e,this.el.insertBefore(t,this.el.firstChild),e._addedToRegion(),n.isFunction(e.immediateTransitionIn)&&e.immediateTransitionIn(),!this._needsTransition&&n.isFunction(e.transitionIn)&&e.transitionIn(),this._triggerCallback("view:added")},_detachView:function(e,t){this._activeView=null,this._activeViewClass=null,n.isFunction(e.transitionOut)?e.transitionOut(function(){this._removeView(e,t)}.bind(this)):this._removeView(e,t)},_removeView:function(e,t){e.remove(),this._triggerCallback("view:removed");var r=this._activeView||this._pendingView;r&&n.isFunction(r.transitionIn)&&!t&&(r.hasState(o)?r.transitionIn():this.listenToOnce(r,"initialized",function(){r.transitionIn()}))},_triggerCallback:i});var l=r.Module.extend({_basePath:"/",_templateCache:{},initialize:function(){this._initializeTemplates()},has:function(e){return this._templateCache[e]!==undefined},get:function(e){return this._templateCache[e]},set:function(e,t){this._compileTemplate(e,t)},setBasePath:function(e){this._basePath=e},retrieveTemplate:function(e){this._retrieveTemplate(e)},remove:function(e){this._templateCache[e]=null,delete this._templateCache[e]},_initializeTemplates:function(){var e=document.querySelectorAll('[type="text/template"]');for(var t=0,n=e.length;t<n;t++){var r=e[t],i=r.textContent,s=r.id;r.parentNode.removeChild(r),this._compileTemplate(s,i)}},_retrieveTemplate:function(e,n){var r={url:this._basePath+e,success:function(t){this._compileTemplate(e,t),n&&n.apply(),this._triggerCallback("template:retrieved",{id:e})}.bind(this)};t.ajax(r)},_compileTemplate:function(e,t){this._templateCache[e]=n.template(t)},_triggerCallback:i});return r.TemplateCache=new l,r});var _gsScope="undefined"!=typeof module&&module.exports&&"undefined"!=typeof global?global:this||window;(_gsScope._gsQueue||(_gsScope._gsQueue=[])).push(function(){"use strict";_gsScope._gsDefine("TweenMax",["core.Animation","core.SimpleTimeline","TweenLite"],function(e,t,n){var r=function(e){var t,n=[],r=e.length;for(t=0;t!==r;n.push(e[t++]));return n},i=function(e,t,r){n.call(this,e,t,r),this._cycle=0,this._yoyo=this.vars.yoyo===!0,this._repeat=this.vars.repeat||0,this._repeatDelay=this.vars.repeatDelay||0,this._dirty=!0,this.render=i.prototype.render},s=1e-10,o=n._internals,u=o.isSelector,a=o.isArray,f=i.prototype=n.to({},.1,{}),l=[];i.version="1.16.1",f.constructor=i,f.kill()._gc=!1,i.killTweensOf=i.killDelayedCallsTo=n.killTweensOf,i.getTweensOf=n.getTweensOf,i.lagSmoothing=n.lagSmoothing,i.ticker=n.ticker,i.render=n.render,f.invalidate=function(){return this._yoyo=this.vars.yoyo===!0,this._repeat=this.vars.repeat||0,this._repeatDelay=this.vars.repeatDelay||0,this._uncache(!0),n.prototype.invalidate.call(this)},f.updateTo=function(e,t){var r,i=this.ratio,s=this.vars.immediateRender||e.immediateRender;t&&this._startTime<this._timeline._time&&(this._startTime=this._timeline._time,this._uncache(!1),this._gc?this._enabled(!0,!1):this._timeline.insert(this,this._startTime-this._delay));for(r in e)this.vars[r]=e[r];if(this._initted||s)if(t)this._initted=!1,s&&this.render(0,!0,!0);else if(this._gc&&this._enabled(!0,!1),this._notifyPluginsOfEnabled&&this._firstPT&&n._onPluginEvent("_onDisable",this),this._time/this._duration>.998){var o=this._time;this.render(0,!0,!1),this._initted=!1,this.render(o,!0,!1)}else if(this._time>0||s){this._initted=!1,this._init();for(var u,a=1/(1-i),f=this._firstPT;f;)u=f.s+f.c,f.c*=a,f.s=u-f.c,f=f._next}return this},f.render=function(e,t,n){this._initted||0===this._duration&&this.vars.repeat&&this.invalidate();var r,i,u,a,f,c,h,p,d=this._dirty?this.totalDuration():this._totalDuration,v=this._time,m=this._totalTime,g=this._cycle,y=this._duration,b=this._rawPrevTime;if(e>=d?(this._totalTime=d,this._cycle=this._repeat,this._yoyo&&0!==(1&this._cycle)?(this._time=0,this.ratio=this._ease._calcEnd?this._ease.getRatio(0):0):(this._time=y,this.ratio=this._ease._calcEnd?this._ease.getRatio(1):1),this._reversed||(r=!0,i="onComplete",n=n||this._timeline.autoRemoveChildren),0===y&&(this._initted||!this.vars.lazy||n)&&(this._startTime===this._timeline._duration&&(e=0),(0===e||0>b||b===s)&&b!==e&&(n=!0,b>s&&(i="onReverseComplete")),this._rawPrevTime=p=!t||e||b===e?e:s)):1e-7>e?(this._totalTime=this._time=this._cycle=0,this.ratio=this._ease._calcEnd?this._ease.getRatio(0):0,(0!==m||0===y&&b>0)&&(i="onReverseComplete",r=this._reversed),0>e&&(this._active=!1,0===y&&(this._initted||!this.vars.lazy||n)&&(b>=0&&(n=!0),this._rawPrevTime=p=!t||e||b===e?e:s)),this._initted||(n=!0)):(this._totalTime=this._time=e,0!==this._repeat&&(a=y+this._repeatDelay,this._cycle=this._totalTime/a>>0,0!==this._cycle&&this._cycle===this._totalTime/a&&this._cycle--,this._time=this._totalTime-this._cycle*a,this._yoyo&&0!==(1&this._cycle)&&(this._time=y-this._time),this._time>y?this._time=y:0>this._time&&(this._time=0)),this._easeType?(f=this._time/y,c=this._easeType,h=this._easePower,(1===c||3===c&&f>=.5)&&(f=1-f),3===c&&(f*=2),1===h?f*=f:2===h?f*=f*f:3===h?f*=f*f*f:4===h&&(f*=f*f*f*f),this.ratio=1===c?1-f:2===c?f:.5>this._time/y?f/2:1-f/2):this.ratio=this._ease.getRatio(this._time/y)),v===this._time&&!n&&g===this._cycle)return m!==this._totalTime&&this._onUpdate&&(t||this._onUpdate.apply(this.vars.onUpdateScope||this,this.vars.onUpdateParams||l)),void 0;if(!this._initted){if(this._init(),!this._initted||this._gc)return;if(!n&&this._firstPT&&(this.vars.lazy!==!1&&this._duration||this.vars.lazy&&!this._duration))return this._time=v,this._totalTime=m,this._rawPrevTime=b,this._cycle=g,o.lazyTweens.push(this),this._lazy=[e,t],void 0;this._time&&!r?this.ratio=this._ease.getRatio(this._time/y):r&&this._ease._calcEnd&&(this.ratio=this._ease.getRatio(0===this._time?0:1))}for(this._lazy!==!1&&(this._lazy=!1),this._active||!this._paused&&this._time!==v&&e>=0&&(this._active=!0),0===m&&(2===this._initted&&e>0&&this._init(),this._startAt&&(e>=0?this._startAt.render(e,t,n):i||(i="_dummyGS")),this.vars.onStart&&(0!==this._totalTime||0===y)&&(t||this.vars.onStart.apply(this.vars.onStartScope||this,this.vars.onStartParams||l))),u=this._firstPT;u;)u.f?u.t[u.p](u.c*this.ratio+u.s):u.t[u.p]=u.c*this.ratio+u.s,u=u._next;this._onUpdate&&(0>e&&this._startAt&&this._startTime&&this._startAt.render(e,t,n),t||(this._totalTime!==m||r)&&this._onUpdate.apply(this.vars.onUpdateScope||this,this.vars.onUpdateParams||l)),this._cycle!==g&&(t||this._gc||this.vars.onRepeat&&this.vars.onRepeat.apply(this.vars.onRepeatScope||this,this.vars.onRepeatParams||l)),i&&(!this._gc||n)&&(0>e&&this._startAt&&!this._onUpdate&&this._startTime&&this._startAt.render(e,t,n),r&&(this._timeline.autoRemoveChildren&&this._enabled(!1,!1),this._active=!1),!t&&this.vars[i]&&this.vars[i].apply(this.vars[i+"Scope"]||this,this.vars[i+"Params"]||l),0===y&&this._rawPrevTime===s&&p!==s&&(this._rawPrevTime=0))},i.to=function(e,t,n){return new i(e,t,n)},i.from=function(e,t,n){return n.runBackwards=!0,n.immediateRender=0!=n.immediateRender,new i(e,t,n)},i.fromTo=function(e,t,n,r){return r.startAt=n,r.immediateRender=0!=r.immediateRender&&0!=n.immediateRender,new i(e,t,r)},i.staggerTo=i.allTo=function(e,t,s,o,f,c,h){o=o||0;var p,d,v,m,g=s.delay||0,y=[],b=function(){s.onComplete&&s.onComplete.apply(s.onCompleteScope||this,arguments),f.apply(h||this,c||l)};for(a(e)||("string"==typeof e&&(e=n.selector(e)||e),u(e)&&(e=r(e))),e=e||[],0>o&&(e=r(e),e.reverse(),o*=-1),p=e.length-1,v=0;p>=v;v++){d={};for(m in s)d[m]=s[m];d.delay=g,v===p&&f&&(d.onComplete=b),y[v]=new i(e[v],t,d),g+=o}return y},i.staggerFrom=i.allFrom=function(e,t,n,r,s,o,u){return n.runBackwards=!0,n.immediateRender=0!=n.immediateRender,i.staggerTo(e,t,n,r,s,o,u)},i.staggerFromTo=i.allFromTo=function(e,t,n,r,s,o,u,a){return r.startAt=n,r.immediateRender=0!=r.immediateRender&&0!=n.immediateRender,i.staggerTo(e,t,r,s,o,u,a)},i.delayedCall=function(e,t,n,r,s){return new i(t,0,{delay:e,onComplete:t,onCompleteParams:n,onCompleteScope:r,onReverseComplete:t,onReverseCompleteParams:n,onReverseCompleteScope:r,immediateRender:!1,useFrames:s,overwrite:0})},i.set=function(e,t){return new i(e,0,t)},i.isTweening=function(e){return n.getTweensOf(e,!0).length>0};var c=function(e,t){for(var r=[],i=0,s=e._first;s;)s instanceof n?r[i++]=s:(t&&(r[i++]=s),r=r.concat(c(s,t)),i=r.length),s=s._next;return r},h=i.getAllTweens=function(t){return c(e._rootTimeline,t).concat(c(e._rootFramesTimeline,t))};i.killAll=function(e,n,r,i){null==n&&(n=!0),null==r&&(r=!0);var s,o,u,a=h(0!=i),f=a.length,l=n&&r&&i;for(u=0;f>u;u++)o=a[u],(l||o instanceof t||(s=o.target===o.vars.onComplete)&&r||n&&!s)&&(e?o.totalTime(o._reversed?0:o.totalDuration()):o._enabled(!1,!1))},i.killChildTweensOf=function(e,t){if(null!=e){var s,f,l,c,h,p=o.tweenLookup;if("string"==typeof e&&(e=n.selector(e)||e),u(e)&&(e=r(e)),a(e))for(c=e.length;--c>-1;)i.killChildTweensOf(e[c],t);else{s=[];for(l in p)for(f=p[l].target.parentNode;f;)f===e&&(s=s.concat(p[l].tweens)),f=f.parentNode;for(h=s.length,c=0;h>c;c++)t&&s[c].totalTime(s[c].totalDuration()),s[c]._enabled(!1,!1)}}};var p=function(e,n,r,i){n=n!==!1,r=r!==!1,i=i!==!1;for(var s,o,u=h(i),a=n&&r&&i,f=u.length;--f>-1;)o=u[f],(a||o instanceof t||(s=o.target===o.vars.onComplete)&&r||n&&!s)&&o.paused(e)};return i.pauseAll=function(e,t,n){p(!0,e,t,n)},i.resumeAll=function(e,t,n){p(!1,e,t,n)},i.globalTimeScale=function(t){var r=e._rootTimeline,i=n.ticker.time;return arguments.length?(t=t||s,r._startTime=i-(i-r._startTime)*r._timeScale/t,r=e._rootFramesTimeline,i=n.ticker.frame,r._startTime=i-(i-r._startTime)*r._timeScale/t,r._timeScale=e._rootTimeline._timeScale=t,t):r._timeScale},f.progress=function(e){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&0!==(1&this._cycle)?1-e:e)+this._cycle*(this._duration+this._repeatDelay),!1):this._time/this.duration()},f.totalProgress=function(e){return arguments.length?this.totalTime(this.totalDuration()*e,!1):this._totalTime/this.totalDuration()},f.time=function(e,t){return arguments.length?(this._dirty&&this.totalDuration(),e>this._duration&&(e=this._duration),this._yoyo&&0!==(1&this._cycle)?e=this._duration-e+this._cycle*(this._duration+this._repeatDelay):0!==this._repeat&&(e+=this._cycle*(this._duration+this._repeatDelay)),this.totalTime(e,t)):this._time},f.duration=function(t){return arguments.length?e.prototype.duration.call(this,t):this._duration},f.totalDuration=function(e){return arguments.length?-1===this._repeat?this:this.duration((e-this._repeat*this._repeatDelay)/(this._repeat+1)):(this._dirty&&(this._totalDuration=-1===this._repeat?999999999999:this._duration*(this._repeat+1)+this._repeatDelay*this._repeat,this._dirty=!1),this._totalDuration)},f.repeat=function(e){return arguments.length?(this._repeat=e,this._uncache(!0)):this._repeat},f.repeatDelay=function(e){return arguments.length?(this._repeatDelay=e,this._uncache(!0)):this._repeatDelay},f.yoyo=function(e){return arguments.length?(this._yoyo=e,this):this._yoyo},i},!0),_gsScope._gsDefine("TimelineLite",["core.Animation","core.SimpleTimeline","TweenLite"],function(e,t,n){var r=function(e){t.call(this,e),this._labels={},this.autoRemoveChildren=this.vars.autoRemoveChildren===!0,this.smoothChildTiming=this.vars.smoothChildTiming===!0,this._sortChildren=!0,this._onUpdate=this.vars.onUpdate;var n,r,i=this.vars;for(r in i)n=i[r],a(n)&&-1!==n.join("").indexOf("{self}")&&(i[r]=this._swapSelfInParams(n));a(i.tweens)&&this.add(i.tweens,0,i.align,i.stagger)},i=1e-10,s=n._internals,o=r._internals={},u=s.isSelector,a=s.isArray,f=s.lazyTweens,l=s.lazyRender,c=[],h=_gsScope._gsDefine.globals,p=function(e){var t,n={};for(t in e)n[t]=e[t];return n},d=o.pauseCallback=function(e,t,n,r){var s,o=e._timeline,u=o._totalTime,a=e._startTime,f=0>e._rawPrevTime||0===e._rawPrevTime&&o._reversed,l=f?0:i,h=f?i:0;if(t||!this._forcingPlayhead){for(o.pause(a),s=e._prev;s&&s._startTime===a;)s._rawPrevTime=h,s=s._prev;for(s=e._next;s&&s._startTime===a;)s._rawPrevTime=l,s=s._next;t&&t.apply(r||o,n||c),(this._forcingPlayhead||!o._paused)&&o.seek(u)}},v=function(e){var t,n=[],r=e.length;for(t=0;t!==r;n.push(e[t++]));return n},m=r.prototype=new t;return r.version="1.16.1",m.constructor=r,m.kill()._gc=m._forcingPlayhead=!1,m.to=function(e,t,r,i){var s=r.repeat&&h.TweenMax||n;return t?this.add(new s(e,t,r),i):this.set(e,r,i)},m.from=function(e,t,r,i){return this.add((r.repeat&&h.TweenMax||n).from(e,t,r),i)},m.fromTo=function(e,t,r,i,s){var o=i.repeat&&h.TweenMax||n;return t?this.add(o.fromTo(e,t,r,i),s):this.set(e,i,s)},m.staggerTo=function(e,t,i,s,o,a,f,l){var c,h=new r({onComplete:a,onCompleteParams:f,onCompleteScope:l,smoothChildTiming:this.smoothChildTiming});for("string"==typeof e&&(e=n.selector(e)||e),e=e||[],u(e)&&(e=v(e)),s=s||0,0>s&&(e=v(e),e.reverse(),s*=-1),c=0;e.length>c;c++)i.startAt&&(i.startAt=p(i.startAt)),h.to(e[c],t,p(i),c*s);return this.add(h,o)},m.staggerFrom=function(e,t,n,r,i,s,o,u){return n.immediateRender=0!=n.immediateRender,n.runBackwards=!0,this.staggerTo(e,t,n,r,i,s,o,u)},m.staggerFromTo=function(e,t,n,r,i,s,o,u,a){return r.startAt=n,r.immediateRender=0!=r.immediateRender&&0!=n.immediateRender,this.staggerTo(e,t,r,i,s,o,u,a)},m.call=function(e,t,r,i){return this.add(n.delayedCall(0,e,t,r),i)},m.set=function(e,t,r){return r=this._parseTimeOrLabel(r,0,!0),null==t.immediateRender&&(t.immediateRender=r===this._time&&!this._paused),this.add(new n(e,0,t),r)},r.exportRoot=function(e,t){e=e||{},null==e.smoothChildTiming&&(e.smoothChildTiming=!0);var i,s,o=new r(e),u=o._timeline;for(null==t&&(t=!0),u._remove(o,!0),o._startTime=0,o._rawPrevTime=o._time=o._totalTime=u._time,i=u._first;i;)s=i._next,t&&i instanceof n&&i.target===i.vars.onComplete||o.add(i,i._startTime-i._delay),i=s;return u.add(o,0),o},m.add=function(i,s,o,u){var f,l,c,h,p,d;if("number"!=typeof s&&(s=this._parseTimeOrLabel(s,0,!0,i)),!(i instanceof e)){if(i instanceof Array||i&&i.push&&a(i)){for(o=o||"normal",u=u||0,f=s,l=i.length,c=0;l>c;c++)a(h=i[c])&&(h=new r({tweens:h})),this.add(h,f),"string"!=typeof h&&"function"!=typeof h&&("sequence"===o?f=h._startTime+h.totalDuration()/h._timeScale:"start"===o&&(h._startTime-=h.delay())),f+=u;return this._uncache(!0)}if("string"==typeof i)return this.addLabel(i,s);if("function"!=typeof i)throw"Cannot add "+i+" into the timeline; it is not a tween, timeline, function, or string.";i=n.delayedCall(0,i)}if(t.prototype.add.call(this,i,s),(this._gc||this._time===this._duration)&&!this._paused&&this._duration<this.duration())for(p=this,d=p.rawTime()>i._startTime;p._timeline;)d&&p._timeline.smoothChildTiming?p.totalTime(p._totalTime,!0):p._gc&&p._enabled(!0,!1),p=p._timeline;return this},m.remove=function(t){if(t instanceof e)return this._remove(t,!1);if(t instanceof Array||t&&t.push&&a(t)){for(var n=t.length;--n>-1;)this.remove(t[n]);return this}return"string"==typeof t?this.removeLabel(t):this.kill(null,t)},m._remove=function(e,n){t.prototype._remove.call(this,e,n);var r=this._last;return r?this._time>r._startTime+r._totalDuration/r._timeScale&&(this._time=this.duration(),this._totalTime=this._totalDuration):this._time=this._totalTime=this._duration=this._totalDuration=0,this},m.append=function(e,t){return this.add(e,this._parseTimeOrLabel(null,t,!0,e))},m.insert=m.insertMultiple=function(e,t,n,r){return this.add(e,t||0,n,r)},m.appendMultiple=function(e,t,n,r){return this.add(e,this._parseTimeOrLabel(null,t,!0,e),n,r)},m.addLabel=function(e,t){return this._labels[e]=this._parseTimeOrLabel(t),this},m.addPause=function(e,t,r,i){var s=n.delayedCall(0,d,["{self}",t,r,i],this);return s.data="isPause",this.add(s,e)},m.removeLabel=function(e){return delete this._labels[e],this},m.getLabelTime=function(e){return null!=this._labels[e]?this._labels[e]:-1},m._parseTimeOrLabel=function(t,n,r,i){var s;if(i instanceof e&&i.timeline===this)this.remove(i);else if(i&&(i instanceof Array||i.push&&a(i)))for(s=i.length;--s>-1;)i[s]instanceof e&&i[s].timeline===this&&this.remove(i[s]);if("string"==typeof n)return this._parseTimeOrLabel(n,r&&"number"==typeof t&&null==this._labels[n]?t-this.duration():0,r);if(n=n||0,"string"!=typeof t||!isNaN(t)&&null==this._labels[t])null==t&&(t=this.duration());else{if(s=t.indexOf("="),-1===s)return null==this._labels[t]?r?this._labels[t]=this.duration()+n:n:this._labels[t]+n;n=parseInt(t.charAt(s-1)+"1",10)*Number(t.substr(s+1)),t=s>1?this._parseTimeOrLabel(t.substr(0,s-1),0,r):this.duration()}return Number(t)+n},m.seek=function(e,t){return this.totalTime("number"==typeof e?e:this._parseTimeOrLabel(e),t!==!1)},m.stop=function(){return this.paused(!0)},m.gotoAndPlay=function(e,t){return this.play(e,t)},m.gotoAndStop=function(e,t){return this.pause(e,t)},m.render=function(e,t,n){this._gc&&this._enabled(!0,!1);var r,s,o,u,a,h=this._dirty?this.totalDuration():this._totalDuration,p=this._time,d=this._startTime,v=this._timeScale,m=this._paused;if(e>=h)this._totalTime=this._time=h,this._reversed||this._hasPausedChild()||(s=!0,u="onComplete",a=!!this._timeline.autoRemoveChildren,0===this._duration&&(0===e||0>this._rawPrevTime||this._rawPrevTime===i)&&this._rawPrevTime!==e&&this._first&&(a=!0,this._rawPrevTime>i&&(u="onReverseComplete"))),this._rawPrevTime=this._duration||!t||e||this._rawPrevTime===e?e:i,e=h+1e-4;else if(1e-7>e)if(this._totalTime=this._time=0,(0!==p||0===this._duration&&this._rawPrevTime!==i&&(this._rawPrevTime>0||0>e&&this._rawPrevTime>=0))&&(u="onReverseComplete",s=this._reversed),0>e)this._active=!1,this._timeline.autoRemoveChildren&&this._reversed?(a=s=!0,u="onReverseComplete"):this._rawPrevTime>=0&&this._first&&(a=!0),this._rawPrevTime=e;else{if(this._rawPrevTime=this._duration||!t||e||this._rawPrevTime===e?e:i,0===e&&s)for(r=this._first;r&&0===r._startTime;)r._duration||(s=!1),r=r._next;e=0,this._initted||(a=!0)}else this._totalTime=this._time=this._rawPrevTime=e;if(this._time!==p&&this._first||n||a){if(this._initted||(this._initted=!0),this._active||!this._paused&&this._time!==p&&e>0&&(this._active=!0),0===p&&this.vars.onStart&&0!==this._time&&(t||this.vars.onStart.apply(this.vars.onStartScope||this,this.vars.onStartParams||c)),this._time>=p)for(r=this._first;r&&(o=r._next,!this._paused||m);)(r._active||r._startTime<=this._time&&!r._paused&&!r._gc)&&(r._reversed?r.render((r._dirty?r.totalDuration():r._totalDuration)-(e-r._startTime)*r._timeScale,t,n):r.render((e-r._startTime)*r._timeScale,t,n)),r=o;else for(r=this._last;r&&(o=r._prev,!this._paused||m);)(r._active||p>=r._startTime&&!r._paused&&!r._gc)&&(r._reversed?r.render((r._dirty?r.totalDuration():r._totalDuration)-(e-r._startTime)*r._timeScale,t,n):r.render((e-r._startTime)*r._timeScale,t,n)),r=o;this._onUpdate&&(t||(f.length&&l(),this._onUpdate.apply(this.vars.onUpdateScope||this,this.vars.onUpdateParams||c))),u&&(this._gc||(d===this._startTime||v!==this._timeScale)&&(0===this._time||h>=this.totalDuration())&&(s&&(f.length&&l(),this._timeline.autoRemoveChildren&&this._enabled(!1,!1),this._active=!1),!t&&this.vars[u]&&this.vars[u].apply(this.vars[u+"Scope"]||this,this.vars[u+"Params"]||c)))}},m._hasPausedChild=function(){for(var e=this._first;e;){if(e._paused||e instanceof r&&e._hasPausedChild())return!0;e=e._next}return!1},m.getChildren=function(e,t,r,i){i=i||-9999999999;for(var s=[],o=this._first,u=0;o;)i>o._startTime||(o instanceof n?t!==!1&&(s[u++]=o):(r!==!1&&(s[u++]=o),e!==!1&&(s=s.concat(o.getChildren(!0,t,r)),u=s.length))),o=o._next;return s},m.getTweensOf=function(e,t){var r,i,s=this._gc,o=[],u=0;for(s&&this._enabled(!0,!0),r=n.getTweensOf(e),i=r.length;--i>-1;)(r[i].timeline===this||t&&this._contains(r[i]))&&(o[u++]=r[i]);return s&&this._enabled(!1,!0),o},m.recent=function(){return this._recent},m._contains=function(e){for(var t=e.timeline;t;){if(t===this)return!0;t=t.timeline}return!1},m.shiftChildren=function(e,t,n){n=n||0;for(var r,i=this._first,s=this._labels;i;)i._startTime>=n&&(i._startTime+=e),i=i._next;if(t)for(r in s)s[r]>=n&&(s[r]+=e);return this._uncache(!0)},m._kill=function(e,t){if(!e&&!t)return this._enabled(!1,!1);for(var n=t?this.getTweensOf(t):this.getChildren(!0,!0,!1),r=n.length,i=!1;--r>-1;)n[r]._kill(e,t)&&(i=!0);return i},m.clear=function(e){var t=this.getChildren(!1,!0,!0),n=t.length;for(this._time=this._totalTime=0;--n>-1;)t[n]._enabled(!1,!1);return e!==!1&&(this._labels={}),this._uncache(!0)},m.invalidate=function(){for(var t=this._first;t;)t.invalidate(),t=t._next;return e.prototype.invalidate.call(this)},m._enabled=function(e,n){if(e===this._gc)for(var r=this._first;r;)r._enabled(e,!0),r=r._next;return t.prototype._enabled.call(this,e,n)},m.totalTime=function(){this._forcingPlayhead=!0;var t=e.prototype.totalTime.apply(this,arguments);return this._forcingPlayhead=!1,t},m.duration=function(e){return arguments.length?(0!==this.duration()&&0!==e&&this.timeScale(this._duration/e),this):(this._dirty&&this.totalDuration(),this._duration)},m.totalDuration=function(e){if(!arguments.length){if(this._dirty){for(var t,n,r=0,i=this._last,s=999999999999;i;)t=i._prev,i._dirty&&i.totalDuration(),i._startTime>s&&this._sortChildren&&!i._paused?this.add(i,i._startTime-i._delay):s=i._startTime,0>i._startTime&&!i._paused&&(r-=i._startTime,this._timeline.smoothChildTiming&&(this._startTime+=i._startTime/this._timeScale),this.shiftChildren(-i._startTime,!1,-9999999999),s=0),n=i._startTime+i._totalDuration/i._timeScale,n>r&&(r=n),i=t;this._duration=this._totalDuration=r,this._dirty=!1}return this._totalDuration}return 0!==this.totalDuration()&&0!==e&&this.timeScale(this._totalDuration/e),this},m.paused=function(t){if(!t)for(var n=this._first,r=this._time;n;)n._startTime===r&&"isPause"===n.data&&(n._rawPrevTime=0),n=n._next;return e.prototype.paused.apply(this,arguments)},m.usesFrames=function(){for(var t=this._timeline;t._timeline;)t=t._timeline;return t===e._rootFramesTimeline},m.rawTime=function(){return this._paused?this._totalTime:(this._timeline.rawTime()-this._startTime)*this._timeScale},r},!0),_gsScope._gsDefine("TimelineMax",["TimelineLite","TweenLite","easing.Ease"],function(e,t,n){var r=function(t){e.call(this,t),this._repeat=this.vars.repeat||0,this._repeatDelay=this.vars.repeatDelay||0,this._cycle=0,this._yoyo=this.vars.yoyo===!0,this._dirty=!0},i=1e-10,s=[],o=t._internals,u=o.lazyTweens,a=o.lazyRender,f=new n(null,null,1,0),l=r.prototype=new e;return l.constructor=r,l.kill()._gc=!1,r.version="1.16.1",l.invalidate=function(){return this._yoyo=this.vars.yoyo===!0,this._repeat=this.vars.repeat||0,this._repeatDelay=this.vars.repeatDelay||0,this._uncache(!0),e.prototype.invalidate.call(this)},l.addCallback=function(e,n,r,i){return this.add(t.delayedCall(0,e,r,i),n)},l.removeCallback=function(e,t){if(e)if(null==t)this._kill(null,e);else for(var n=this.getTweensOf(e,!1),r=n.length,i=this._parseTimeOrLabel(t);--r>-1;)n[r]._startTime===i&&n[r]._enabled(!1,!1);return this},l.removePause=function(t){return this.removeCallback(e._internals.pauseCallback,t)},l.tweenTo=function(e,n){n=n||{};var r,i,o,u={ease:f,useFrames:this.usesFrames(),immediateRender:!1};for(i in n)u[i]=n[i];return u.time=this._parseTimeOrLabel(e),r=Math.abs(Number(u.time)-this._time)/this._timeScale||.001,o=new t(this,r,u),u.onStart=function(){o.target.paused(!0),o.vars.time!==o.target.time()&&r===o.duration()&&o.duration(Math.abs(o.vars.time-o.target.time())/o.target._timeScale),n.onStart&&n.onStart.apply(n.onStartScope||o,n.onStartParams||s)},o},l.tweenFromTo=function(e,t,n){n=n||{},e=this._parseTimeOrLabel(e),n.startAt={onComplete:this.seek,onCompleteParams:[e],onCompleteScope:this},n.immediateRender=n.immediateRender!==!1;var r=this.tweenTo(t,n);return r.duration(Math.abs(r.vars.time-e)/this._timeScale||.001)},l.render=function(e,t,n){this._gc&&this._enabled(!0,!1);var r,o,f,l,c,p,d=this._dirty?this.totalDuration():this._totalDuration,v=this._duration,m=this._time,g=this._totalTime,y=this._startTime,b=this._timeScale,w=this._rawPrevTime,E=this._paused,S=this._cycle;if(e>=d)this._locked||(this._totalTime=d,this._cycle=this._repeat),this._reversed||this._hasPausedChild()||(o=!0,l="onComplete",c=!!this._timeline.autoRemoveChildren,0===this._duration&&(0===e||0>w||w===i)&&w!==e&&this._first&&(c=!0,w>i&&(l="onReverseComplete"))),this._rawPrevTime=this._duration||!t||e||this._rawPrevTime===e?e:i,this._yoyo&&0!==(1&this._cycle)?this._time=e=0:(this._time=v,e=v+1e-4);else if(1e-7>e)if(this._locked||(this._totalTime=this._cycle=0),this._time=0,(0!==m||0===v&&w!==i&&(w>0||0>e&&w>=0)&&!this._locked)&&(l="onReverseComplete",o=this._reversed),0>e)this._active=!1,this._timeline.autoRemoveChildren&&this._reversed?(c=o=!0,l="onReverseComplete"):w>=0&&this._first&&(c=!0),this._rawPrevTime=e;else{if(this._rawPrevTime=v||!t||e||this._rawPrevTime===e?e:i,0===e&&o)for(r=this._first;r&&0===r._startTime;)r._duration||(o=!1),r=r._next;e=0,this._initted||(c=!0)}else 0===v&&0>w&&(c=!0),this._time=this._rawPrevTime=e,this._locked||(this._totalTime=e,0!==this._repeat&&(p=v+this._repeatDelay,this._cycle=this._totalTime/p>>0,0!==this._cycle&&this._cycle===this._totalTime/p&&this._cycle--,this._time=this._totalTime-this._cycle*p,this._yoyo&&0!==(1&this._cycle)&&(this._time=v-this._time),this._time>v?(this._time=v,e=v+1e-4):0>this._time?this._time=e=0:e=this._time));if(this._cycle!==S&&!this._locked){var x=this._yoyo&&0!==(1&S),T=x===(this._yoyo&&0!==(1&this._cycle)),N=this._totalTime,C=this._cycle,k=this._rawPrevTime,L=this._time;if(this._totalTime=S*v,S>this._cycle?x=!x:this._totalTime+=v,this._time=m,this._rawPrevTime=0===v?w-1e-4:w,this._cycle=S,this._locked=!0,m=x?0:v,this.render(m,t,0===v),t||this._gc||this.vars.onRepeat&&this.vars.onRepeat.apply(this.vars.onRepeatScope||this,this.vars.onRepeatParams||s),T&&(m=x?v+1e-4:-0.0001,this.render(m,!0,!1)),this._locked=!1,this._paused&&!E)return;this._time=L,this._totalTime=N,this._cycle=C,this._rawPrevTime=k}if(!(this._time!==m&&this._first||n||c))return g!==this._totalTime&&this._onUpdate&&(t||this._onUpdate.apply(this.vars.onUpdateScope||this,this.vars.onUpdateParams||s)),void 0;if(this._initted||(this._initted=!0),this._active||!this._paused&&this._totalTime!==g&&e>0&&(this._active=!0),0===g&&this.vars.onStart&&0!==this._totalTime&&(t||this.vars.onStart.apply(this.vars.onStartScope||this,this.vars.onStartParams||s)),this._time>=m)for(r=this._first;r&&(f=r._next,!this._paused||E);)(r._active||r._startTime<=this._time&&!r._paused&&!r._gc)&&(r._reversed?r.render((r._dirty?r.totalDuration():r._totalDuration)-(e-r._startTime)*r._timeScale,t,n):r.render((e-r._startTime)*r._timeScale,t,n)),r=f;else for(r=this._last;r&&(f=r._prev,!this._paused||E);)(r._active||m>=r._startTime&&!r._paused&&!r._gc)&&(r._reversed?r.render((r._dirty?r.totalDuration():r._totalDuration)-(e-r._startTime)*r._timeScale,t,n):r.render((e-r._startTime)*r._timeScale,t,n)),r=f;this._onUpdate&&(t||(u.length&&a(),this._onUpdate.apply(this.vars.onUpdateScope||this,this.vars.onUpdateParams||s))),l&&(this._locked||this._gc||(y===this._startTime||b!==this._timeScale)&&(0===this._time||d>=this.totalDuration())&&(o&&(u.length&&a(),this._timeline.autoRemoveChildren&&this._enabled(!1,!1),this._active=!1),!t&&this.vars[l]&&this.vars[l].apply(this.vars[l+"Scope"]||this,this.vars[l+"Params"]||s)))},l.getActive=function(e,t,n){null==e&&(e=!0),null==t&&(t=!0),null==n&&(n=!1);var r,i,s=[],o=this.getChildren(e,t,n),u=0,a=o.length;for(r=0;a>r;r++)i=o[r],i.isActive()&&(s[u++]=i);return s},l.getLabelAfter=function(e){e||0!==e&&(e=this._time);var t,n=this.getLabelsArray(),r=n.length;for(t=0;r>t;t++)if(n[t].time>e)return n[t].name;return null},l.getLabelBefore=function(e){null==e&&(e=this._time);for(var t=this.getLabelsArray(),n=t.length;--n>-1;)if(e>t[n].time)return t[n].name;return null},l.getLabelsArray=function(){var e,t=[],n=0;for(e in this._labels)t[n++]={time:this._labels[e],name:e};return t.sort(function(e,t){return e.time-t.time}),t},l.progress=function(e,t){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&0!==(1&this._cycle)?1-e:e)+this._cycle*(this._duration+this._repeatDelay),t):this._time/this.duration()},l.totalProgress=function(e,t){return arguments.length?this.totalTime(this.totalDuration()*e,t):this._totalTime/this.totalDuration()},l.totalDuration=function(t){return arguments.length?-1===this._repeat?this:this.duration((t-this._repeat*this._repeatDelay)/(this._repeat+1)):(this._dirty&&(e.prototype.totalDuration.call(this),this._totalDuration=-1===this._repeat?999999999999:this._duration*(this._repeat+1)+this._repeatDelay*this._repeat),this._totalDuration)},l.time=function(e,t){return arguments.length?(this._dirty&&this.totalDuration(),e>this._duration&&(e=this._duration),this._yoyo&&0!==(1&this._cycle)?e=this._duration-e+this._cycle*(this._duration+this._repeatDelay):0!==this._repeat&&(e+=this._cycle*(this._duration+this._repeatDelay)),this.totalTime(e,t)):this._time},l.repeat=function(e){return arguments.length?(this._repeat=e,this._uncache(!0)):this._repeat},l.repeatDelay=function(e){return arguments.length?(this._repeatDelay=e,this._uncache(!0)):this._repeatDelay},l.yoyo=function(e){return arguments.length?(this._yoyo=e,this):this._yoyo},l.currentLabel=function(e){return arguments.length?this.seek(e,!0):this.getLabelBefore(this._time+1e-8)},r},!0),function(){var e=180/Math.PI,t=[],n=[],r=[],i={},s=_gsScope._gsDefine.globals,o=function(e,t,n,r){this.a=e,this.b=t,this.c=n,this.d=r,this.da=r-e,this.ca=n-e,this.ba=t-e},u=",x,y,z,left,top,right,bottom,marginTop,marginLeft,marginRight,marginBottom,paddingLeft,paddingTop,paddingRight,paddingBottom,backgroundPosition,backgroundPosition_y,",a=function(e,t,n,r){var i={a:e},s={},o={},u={c:r},a=(e+t)/2,f=(t+n)/2,l=(n+r)/2,c=(a+f)/2,h=(f+l)/2,p=(h-c)/8;return i.b=a+(e-a)/4,s.b=c+p,i.c=s.a=(i.b+s.b)/2,s.c=o.a=(c+h)/2,o.b=h-p,u.b=l+(r-l)/4,o.c=u.a=(o.b+u.b)/2,[i,s,o,u]},f=function(e,i,s,o,u){var f,l,c,h,p,d,v,m,g,y,b,w,E,S=e.length-1,x=0,T=e[0].a;for(f=0;S>f;f++)p=e[x],l=p.a,c=p.d,h=e[x+1].d,u?(b=t[f],w=n[f],E=.25*(w+b)*i/(o?.5:r[f]||.5),d=c-(c-l)*(o?.5*i:0!==b?E/b:0),v=c+(h-c)*(o?.5*i:0!==w?E/w:0),m=c-(d+((v-d)*(3*b/(b+w)+.5)/4||0))):(d=c-.5*(c-l)*i,v=c+.5*(h-c)*i,m=c-(d+v)/2),d+=m,v+=m,p.c=g=d,p.b=0!==f?T:T=p.a+.6*(p.c-p.a),p.da=c-l,p.ca=g-l,p.ba=T-l,s?(y=a(l,T,g,c),e.splice(x,1,y[0],y[1],y[2],y[3]),x+=4):x++,T=v;p=e[x],p.b=T,p.c=T+.4*(p.d-T),p.da=p.d-p.a,p.ca=p.c-p.a,p.ba=T-p.a,s&&(y=a(p.a,T,p.c,p.d),e.splice(x,1,y[0],y[1],y[2],y[3]))},l=function(e,r,i,s){var u,a,f,l,c,h,p=[];if(s)for(e=[s].concat(e),a=e.length;--a>-1;)"string"==typeof (h=e[a][r])&&"="===h.charAt(1)&&(e[a][r]=s[r]+Number(h.charAt(0)+h.substr(2)));if(u=e.length-2,0>u)return p[0]=new o(e[0][r],0,0,e[-1>u?0:1][r]),p;for(a=0;u>a;a++)f=e[a][r],l=e[a+1][r],p[a]=new o(f,0,0,l),i&&(c=e[a+2][r],t[a]=(t[a]||0)+(l-f)*(l-f),n[a]=(n[a]||0)+(c-l)*(c-l));return p[a]=new o(e[a][r],0,0,e[a+1][r]),p},c=function(e,s,o,a,c,h){var p,d,v,m,g,y,b,w,E={},S=[],x=h||e[0];c="string"==typeof c?","+c+",":u,null==s&&(s=1);for(d in e[0])S.push(d);if(e.length>1){for(w=e[e.length-1],b=!0,p=S.length;--p>-1;)if(d=S[p],Math.abs(x[d]-w[d])>.05){b=!1;break}b&&(e=e.concat(),h&&e.unshift(h),e.push(e[1]),h=e[e.length-3])}for(t.length=n.length=r.length=0,p=S.length;--p>-1;)d=S[p],i[d]=-1!==c.indexOf(","+d+","),E[d]=l(e,d,i[d],h);for(p=t.length;--p>-1;)t[p]=Math.sqrt(t[p]),n[p]=Math.sqrt(n[p]);if(!a){for(p=S.length;--p>-1;)if(i[d])for(v=E[S[p]],y=v.length-1,m=0;y>m;m++)g=v[m+1].da/n[m]+v[m].da/t[m],r[m]=(r[m]||0)+g*g;for(p=r.length;--p>-1;)r[p]=Math.sqrt(r[p])}for(p=S.length,m=o?4:1;--p>-1;)d=S[p],v=E[d],f(v,s,o,a,i[d]),b&&(v.splice(0,m),v.splice(v.length-m,m));return E},h=function(e,t,n){t=t||"soft";var r,i,s,u,a,f,l,c,h,p,d,v={},m="cubic"===t?3:2,g="soft"===t,y=[];if(g&&n&&(e=[n].concat(e)),null==e||m+1>e.length)throw"invalid Bezier data";for(h in e[0])y.push(h);for(f=y.length;--f>-1;){for(h=y[f],v[h]=a=[],p=0,c=e.length,l=0;c>l;l++)r=null==n?e[l][h]:"string"==typeof (d=e[l][h])&&"="===d.charAt(1)?n[h]+Number(d.charAt(0)+d.substr(2)):Number(d),g&&l>1&&c-1>l&&(a[p++]=(r+a[p-2])/2),a[p++]=r;for(c=p-m+1,p=0,l=0;c>l;l+=m)r=a[l],i=a[l+1],s=a[l+2],u=2===m?0:a[l+3],a[p++]=d=3===m?new o(r,i,s,u):new o(r,(2*i+r)/3,(2*i+s)/3,s);a.length=p}return v},p=function(e,t,n){for(var r,i,s,o,u,a,f,l,c,h,p,d=1/n,v=e.length;--v>-1;)for(h=e[v],s=h.a,o=h.d-s,u=h.c-s,a=h.b-s,r=i=0,l=1;n>=l;l++)f=d*l,c=1-f,r=i-(i=(f*f*o+3*c*(f*u+c*a))*f),p=v*n+l-1,t[p]=(t[p]||0)+r*r},d=function(e,t){t=t>>0||6;var n,r,i,s,o=[],u=[],a=0,f=0,l=t-1,c=[],h=[];for(n in e)p(e[n],o,t);for(i=o.length,r=0;i>r;r++)a+=Math.sqrt(o[r]),s=r%t,h[s]=a,s===l&&(f+=a,s=r/t>>0,c[s]=h,u[s]=f,a=0,h=[]);return{length:f,lengths:u,segments:c}},v=_gsScope._gsDefine.plugin({propName:"bezier",priority:-1,version:"1.3.4",API:2,global:!0,init:function(e,t,n){this._target=e,t instanceof Array&&(t={values:t}),this._func={},this._round={},this._props=[],this._timeRes=null==t.timeResolution?6:parseInt(t.timeResolution,10);var r,i,s,o,u,a=t.values||[],f={},l=a[0],p=t.autoRotate||n.vars.orientToBezier;this._autoRotate=p?p instanceof Array?p:[["x","y","rotation",p===!0?0:Number(p)||0]]:null;for(r in l)this._props.push(r);for(s=this._props.length;--s>-1;)r=this._props[s],this._overwriteProps.push(r),i=this._func[r]="function"==typeof e[r],f[r]=i?e[r.indexOf("set")||"function"!=typeof e["get"+r.substr(3)]?r:"get"+r.substr(3)]():parseFloat(e[r]),u||f[r]!==a[0][r]&&(u=f);if(this._beziers="cubic"!==t.type&&"quadratic"!==t.type&&"soft"!==t.type?c(a,isNaN(t.curviness)?1:t.curviness,!1,"thruBasic"===t.type,t.correlate,u):h(a,t.type,f),this._segCount=this._beziers[r].length,this._timeRes){var v=d(this._beziers,this._timeRes);this._length=v.length,this._lengths=v.lengths,this._segments=v.segments,this._l1=this._li=this._s1=this._si=0,this._l2=this._lengths[0],this._curSeg=this._segments[0],this._s2=this._curSeg[0],this._prec=1/this._curSeg.length}if(p=this._autoRotate)for(this._initialRotations=[],p[0]instanceof Array||(this._autoRotate=p=[p]),s=p.length;--s>-1;){for(o=0;3>o;o++)r=p[s][o],this._func[r]="function"==typeof e[r]?e[r.indexOf("set")||"function"!=typeof e["get"+r.substr(3)]?r:"get"+r.substr(3)]:!1;r=p[s][2],this._initialRotations[s]=this._func[r]?this._func[r].call(this._target):this._target[r]}return this._startRatio=n.vars.runBackwards?1:0,!0},set:function(t){var n,r,i,s,o,u,a,f,l,c,h=this._segCount,p=this._func,d=this._target,v=t!==this._startRatio;if(this._timeRes){if(l=this._lengths,c=this._curSeg,t*=this._length,i=this._li,t>this._l2&&h-1>i){for(f=h-1;f>i&&t>=(this._l2=l[++i]););this._l1=l[i-1],this._li=i,this._curSeg=c=this._segments[i],this._s2=c[this._s1=this._si=0]}else if(this._l1>t&&i>0){for(;i>0&&(this._l1=l[--i])>=t;);0===i&&this._l1>t?this._l1=0:i++,this._l2=l[i],this._li=i,this._curSeg=c=this._segments[i],this._s1=c[(this._si=c.length-1)-1]||0,this._s2=c[this._si]}if(n=i,t-=this._l1,i=this._si,t>this._s2&&c.length-1>i){for(f=c.length-1;f>i&&t>=(this._s2=c[++i]););this._s1=c[i-1],this._si=i}else if(this._s1>t&&i>0){for(;i>0&&(this._s1=c[--i])>=t;);0===i&&this._s1>t?this._s1=0:i++,this._s2=c[i],this._si=i}u=(i+(t-this._s1)/(this._s2-this._s1))*this._prec}else n=0>t?0:t>=1?h-1:h*t>>0,u=(t-n*(1/h))*h;for(r=1-u,i=this._props.length;--i>-1;)s=this._props[i],o=this._beziers[s][n],a=(u*u*o.da+3*r*(u*o.ca+r*o.ba))*u+o.a,this._round[s]&&(a=Math.round(a)),p[s]?d[s](a):d[s]=a;if(this._autoRotate){var m,g,y,b,w,E,S,x=this._autoRotate;for(i=x.length;--i>-1;)s=x[i][2],E=x[i][3]||0,S=x[i][4]===!0?1:e,o=this._beziers[x[i][0]],m=this._beziers[x[i][1]],o&&m&&(o=o[n],m=m[n],g=o.a+(o.b-o.a)*u,b=o.b+(o.c-o.b)*u,g+=(b-g)*u,b+=(o.c+(o.d-o.c)*u-b)*u,y=m.a+(m.b-m.a)*u,w=m.b+(m.c-m.b)*u,y+=(w-y)*u,w+=(m.c+(m.d-m.c)*u-w)*u,a=v?Math.atan2(w-y,b-g)*S+E:this._initialRotations[i],p[s]?d[s](a):d[s]=a)}}}),m=v.prototype;v.bezierThrough=c,v.cubicToQuadratic=a,v._autoCSS=!0,v.quadraticToCubic=function(e,t,n){return new o(e,(2*t+e)/3,(2*t+n)/3,n)},v._cssRegister=function(){var e=s.CSSPlugin;if(e){var t=e._internals,n=t._parseToProxy,r=t._setPluginRatio,i=t.CSSPropTween;t._registerComplexSpecialProp("bezier",{parser:function(e,t,s,o,u,a){t instanceof Array&&(t={values:t}),a=new v;var f,l,c,h=t.values,p=h.length-1,d=[],m={};if(0>p)return u;for(f=0;p>=f;f++)c=n(e,h[f],o,u,a,p!==f),d[f]=c.end;for(l in t)m[l]=t[l];return m.values=d,u=new i(e,"bezier",0,0,c.pt,2),u.data=c,u.plugin=a,u.setRatio=r,0===m.autoRotate&&(m.autoRotate=!0),!m.autoRotate||m.autoRotate instanceof Array||(f=m.autoRotate===!0?0:Number(m.autoRotate),m.autoRotate=null!=c.end.left?[["left","top","rotation",f,!1]]:null!=c.end.x?[["x","y","rotation",f,!1]]:!1),m.autoRotate&&(o._transform||o._enableTransforms(!1),c.autoRotate=o._target._gsTransform),a._onInitTween(c.proxy,m,o._tween),u}})}},m._roundProps=function(e,t){for(var n=this._overwriteProps,r=n.length;--r>-1;)(e[n[r]]||e.bezier||e.bezierThrough)&&(this._round[n[r]]=t)},m._kill=function(e){var t,n,r=this._props;for(t in this._beziers)if(t in e)for(delete this._beziers[t],delete this._func[t],n=r.length;--n>-1;)r[n]===t&&r.splice(n,1);return this._super._kill.call(this,e)}}(),_gsScope._gsDefine("plugins.CSSPlugin",["plugins.TweenPlugin","TweenLite"],function(e,t){var n,r,i,s,o=function(){e.call(this,"css"),this._overwriteProps.length=0,this.setRatio=o.prototype.setRatio},u=_gsScope._gsDefine.globals,a={},f=o.prototype=new e("css");f.constructor=o,o.version="1.16.1",o.API=2,o.defaultTransformPerspective=0,o.defaultSkewType="compensated",f="px",o.suffixMap={top:f,right:f,bottom:f,left:f,width:f,height:f,fontSize:f,padding:f,margin:f,perspective:f,lineHeight:""};var l,c,h,p,d,v,m=/(?:\d|\-\d|\.\d|\-\.\d)+/g,g=/(?:\d|\-\d|\.\d|\-\.\d|\+=\d|\-=\d|\+=.\d|\-=\.\d)+/g,y=/(?:\+=|\-=|\-|\b)[\d\-\.]+[a-zA-Z0-9]*(?:%|\b)/gi,b=/(?![+-]?\d*\.?\d+|[+-]|e[+-]\d+)[^0-9]/g,w=/(?:\d|\-|\+|=|#|\.)*/g,E=/opacity *= *([^)]*)/i,S=/opacity:([^;]*)/i,x=/alpha\(opacity *=.+?\)/i,T=/^(rgb|hsl)/,N=/([A-Z])/g,C=/-([a-z])/gi,k=/(^(?:url\(\"|url\())|(?:(\"\))$|\)$)/gi,L=function(e,t){return t.toUpperCase()},A=/(?:Left|Right|Width)/i,O=/(M11|M12|M21|M22)=[\d\-\.e]+/gi,M=/progid\:DXImageTransform\.Microsoft\.Matrix\(.+?\)/i,_=/,(?=[^\)]*(?:\(|$))/gi,D=Math.PI/180,P=180/Math.PI,H={},B=document,j=function(e){return B.createElementNS?B.createElementNS("http://www.w3.org/1999/xhtml",e):B.createElement(e)},F=j("div"),I=j("img"),q=o._internals={_specialProps:a},R=navigator.userAgent,U=function(){var e=R.indexOf("Android"),t=j("a");return h=-1!==R.indexOf("Safari")&&-1===R.indexOf("Chrome")&&(-1===e||Number(R.substr(e+8,1))>3),d=h&&6>Number(R.substr(R.indexOf("Version/")+8,1)),p=-1!==R.indexOf("Firefox"),(/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(R)||/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(R))&&(v=parseFloat(RegExp.$1)),t?(t.style.cssText="top:1px;opacity:.55;",/^0.55/.test(t.style.opacity)):!1}(),z=function(e){return E.test("string"==typeof e?e:(e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?parseFloat(RegExp.$1)/100:1},W=function(e){window.console&&0},X="",V="",$=function(e,t){t=t||F;var n,r,i=t.style;if(void 0!==i[e])return e;for(e=e.charAt(0).toUpperCase()+e.substr(1),n=["O","Moz","ms","Ms","Webkit"],r=5;--r>-1&&void 0===i[n[r]+e];);return r>=0?(V=3===r?"ms":n[r],X="-"+V.toLowerCase()+"-",V+e):null},J=B.defaultView?B.defaultView.getComputedStyle:function(){},K=o.getStyle=function(e,t,n,r,i){var s;return U||"opacity"!==t?(!r&&e.style[t]?s=e.style[t]:(n=n||J(e))?s=n[t]||n.getPropertyValue(t)||n.getPropertyValue(t.replace(N,"-$1").toLowerCase()):e.currentStyle&&(s=e.currentStyle[t]),null==i||s&&"none"!==s&&"auto"!==s&&"auto auto"!==s?s:i):z(e)},Q=q.convertToPixels=function(e,n,r,i,s){if("px"===i||!i)return r;if("auto"===i||!r)return 0;var u,a,f,l=A.test(n),c=e,h=F.style,p=0>r;if(p&&(r=-r),"%"===i&&-1!==n.indexOf("border"))u=r/100*(l?e.clientWidth:e.clientHeight);else{if(h.cssText="border:0 solid red;position:"+K(e,"position")+";line-height:0;","%"!==i&&c.appendChild)h[l?"borderLeftWidth":"borderTopWidth"]=r+i;else{if(c=e.parentNode||B.body,a=c._gsCache,f=t.ticker.frame,a&&l&&a.time===f)return a.width*r/100;h[l?"width":"height"]=r+i}c.appendChild(F),u=parseFloat(F[l?"offsetWidth":"offsetHeight"]),c.removeChild(F),l&&"%"===i&&o.cacheWidths!==!1&&(a=c._gsCache=c._gsCache||{},a.time=f,a.width=100*(u/r)),0!==u||s||(u=Q(e,n,r,i,!0))}return p?-u:u},G=q.calculateOffset=function(e,t,n){if("absolute"!==K(e,"position",n))return 0;var r="left"===t?"Left":"Top",i=K(e,"margin"+r,n);return e["offset"+r]-(Q(e,t,parseFloat(i),i.replace(w,""))||0)},Y=function(e,t){var n,r,i,s={};if(t=t||J(e,null))if(n=t.length)for(;--n>-1;)i=t[n],(-1===i.indexOf("-transform")||xt===i)&&(s[i.replace(C,L)]=t.getPropertyValue(i));else for(n in t)(-1===n.indexOf("Transform")||St===n)&&(s[n]=t[n]);else if(t=e.currentStyle||e.style)for(n in t)"string"==typeof n&&void 0===s[n]&&(s[n.replace(C,L)]=t[n]);return U||(s.opacity=z(e)),r=_t(e,t,!1),s.rotation=r.rotation,s.skewX=r.skewX,s.scaleX=r.scaleX,s.scaleY=r.scaleY,s.x=r.x,s.y=r.y,Nt&&(s.z=r.z,s.rotationX=r.rotationX,s.rotationY=r.rotationY,s.scaleZ=r.scaleZ),s.filters&&delete s.filters,s},Z=function(e,t,n,r,i){var s,o,u,a={},f=e.style;for(o in n)"cssText"!==o&&"length"!==o&&isNaN(o)&&(t[o]!==(s=n[o])||i&&i[o])&&-1===o.indexOf("Origin")&&("number"==typeof s||"string"==typeof s)&&(a[o]="auto"!==s||"left"!==o&&"top"!==o?""!==s&&"auto"!==s&&"none"!==s||"string"!=typeof t[o]||""===t[o].replace(b,"")?s:0:G(e,o),void 0!==f[o]&&(u=new pt(f,o,f[o],u)));if(r)for(o in r)"className"!==o&&(a[o]=r[o]);return{difs:a,firstMPT:u}},et={width:["Left","Right"],height:["Top","Bottom"]},tt=["marginLeft","marginRight","marginTop","marginBottom"],nt=function(e,t,n){var r=parseFloat("width"===t?e.offsetWidth:e.offsetHeight),i=et[t],s=i.length;for(n=n||J(e,null);--s>-1;)r-=parseFloat(K(e,"padding"+i[s],n,!0))||0,r-=parseFloat(K(e,"border"+i[s]+"Width",n,!0))||0;return r},rt=function(e,t){(null==e||""===e||"auto"===e||"auto auto"===e)&&(e="0 0");var n=e.split(" "),r=-1!==e.indexOf("left")?"0%":-1!==e.indexOf("right")?"100%":n[0],i=-1!==e.indexOf("top")?"0%":-1!==e.indexOf("bottom")?"100%":n[1];return null==i?i="center"===r?"50%":"0":"center"===i&&(i="50%"),("center"===r||isNaN(parseFloat(r))&&-1===(r+"").indexOf("="))&&(r="50%"),e=r+" "+i+(n.length>2?" "+n[2]:""),t&&(t.oxp=-1!==r.indexOf("%"),t.oyp=-1!==i.indexOf("%"),t.oxr="="===r.charAt(1),t.oyr="="===i.charAt(1),t.ox=parseFloat(r.replace(b,"")),t.oy=parseFloat(i.replace(b,"")),t.v=e),t||e},it=function(e,t){return"string"==typeof e&&"="===e.charAt(1)?parseInt(e.charAt(0)+"1",10)*parseFloat(e.substr(2)):parseFloat(e)-parseFloat(t)},st=function(e,t){return null==e?t:"string"==typeof e&&"="===e.charAt(1)?parseInt(e.charAt(0)+"1",10)*parseFloat(e.substr(2))+t:parseFloat(e)},ot=function(e,t,n,r){var i,s,o,u,a,f=1e-6;return null==e?u=t:"number"==typeof e?u=e:(i=360,s=e.split("_"),a="="===e.charAt(1),o=(a?parseInt(e.charAt(0)+"1",10)*parseFloat(s[0].substr(2)):parseFloat(s[0]))*(-1===e.indexOf("rad")?1:P)-(a?0:t),s.length&&(r&&(r[n]=t+o),-1!==e.indexOf("short")&&(o%=i,o!==o%(i/2)&&(o=0>o?o+i:o-i)),-1!==e.indexOf("_cw")&&0>o?o=(o+9999999999*i)%i-(0|o/i)*i:-1!==e.indexOf("ccw")&&o>0&&(o=(o-9999999999*i)%i-(0|o/i)*i)),u=t+o),f>u&&u>-f&&(u=0),u},ut={aqua:[0,255,255],lime:[0,255,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,255],navy:[0,0,128],white:[255,255,255],fuchsia:[255,0,255],olive:[128,128,0],yellow:[255,255,0],orange:[255,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[255,0,0],pink:[255,192,203],cyan:[0,255,255],transparent:[255,255,255,0]},at=function(e,t,n){return e=0>e?e+1:e>1?e-1:e,0|255*(1>6*e?t+6*(n-t)*e:.5>e?n:2>3*e?t+6*(n-t)*(2/3-e):t)+.5},ft=o.parseColor=function(e){var t,n,r,i,s,o;return e&&""!==e?"number"==typeof e?[e>>16,255&e>>8,255&e]:(","===e.charAt(e.length-1)&&(e=e.substr(0,e.length-1)),ut[e]?ut[e]:"#"===e.charAt(0)?(4===e.length&&(t=e.charAt(1),n=e.charAt(2),r=e.charAt(3),e="#"+t+t+n+n+r+r),e=parseInt(e.substr(1),16),[e>>16,255&e>>8,255&e]):"hsl"===e.substr(0,3)?(e=e.match(m),i=Number(e[0])%360/360,s=Number(e[1])/100,o=Number(e[2])/100,n=.5>=o?o*(s+1):o+s-o*s,t=2*o-n,e.length>3&&(e[3]=Number(e[3])),e[0]=at(i+1/3,t,n),e[1]=at(i,t,n),e[2]=at(i-1/3,t,n),e):(e=e.match(m)||ut.transparent,e[0]=Number(e[0]),e[1]=Number(e[1]),e[2]=Number(e[2]),e.length>3&&(e[3]=Number(e[3])),e)):ut.black},lt="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#.+?\\b";for(f in ut)lt+="|"+f+"\\b";lt=RegExp(lt+")","gi");var ct=function(e,t,n,r){if(null==e)return function(e){return e};var i,s=t?(e.match(lt)||[""])[0]:"",o=e.split(s).join("").match(y)||[],u=e.substr(0,e.indexOf(o[0])),a=")"===e.charAt(e.length-1)?")":"",f=-1!==e.indexOf(" ")?" ":",",l=o.length,c=l>0?o[0].replace(m,""):"";return l?i=t?function(e){var t,h,p,d;if("number"==typeof e)e+=c;else if(r&&_.test(e)){for(d=e.replace(_,"|").split("|"),p=0;d.length>p;p++)d[p]=i(d[p]);return d.join(",")}if(t=(e.match(lt)||[s])[0],h=e.split(t).join("").match(y)||[],p=h.length,l>p--)for(;l>++p;)h[p]=n?h[0|(p-1)/2]:o[p];return u+h.join(f)+f+t+a+(-1!==e.indexOf("inset")?" inset":"")}:function(e){var t,s,h;if("number"==typeof e)e+=c;else if(r&&_.test(e)){for(s=e.replace(_,"|").split("|"),h=0;s.length>h;h++)s[h]=i(s[h]);return s.join(",")}if(t=e.match(y)||[],h=t.length,l>h--)for(;l>++h;)t[h]=n?t[0|(h-1)/2]:o[h];return u+t.join(f)+a}:function(e){return e}},ht=function(e){return e=e.split(","),function(t,n,r,i,s,o,u){var a,f=(n+"").split(" ");for(u={},a=0;4>a;a++)u[e[a]]=f[a]=f[a]||f[(a-1)/2>>0];return i.parse(t,u,s,o)}},pt=(q._setPluginRatio=function(e){this.plugin.setRatio(e);for(var t,n,r,i,s=this.data,o=s.proxy,u=s.firstMPT,a=1e-6;u;)t=o[u.v],u.r?t=Math.round(t):a>t&&t>-a&&(t=0),u.t[u.p]=t,u=u._next;if(s.autoRotate&&(s.autoRotate.rotation=o.rotation),1===e)for(u=s.firstMPT;u;){if(n=u.t,n.type){if(1===n.type){for(i=n.xs0+n.s+n.xs1,r=1;n.l>r;r++)i+=n["xn"+r]+n["xs"+(r+1)];n.e=i}}else n.e=n.s+n.xs0;u=u._next}},function(e,t,n,r,i){this.t=e,this.p=t,this.v=n,this.r=i,r&&(r._prev=this,this._next=r)}),dt=(q._parseToProxy=function(e,t,n,r,i,s){var o,u,a,f,l,c=r,h={},p={},d=n._transform,v=H;for(n._transform=null,H=t,r=l=n.parse(e,t,r,i),H=v,s&&(n._transform=d,c&&(c._prev=null,c._prev&&(c._prev._next=null)));r&&r!==c;){if(1>=r.type&&(u=r.p,p[u]=r.s+r.c,h[u]=r.s,s||(f=new pt(r,"s",u,f,r.r),r.c=0),1===r.type))for(o=r.l;--o>0;)a="xn"+o,u=r.p+"_"+a,p[u]=r.data[a],h[u]=r[a],s||(f=new pt(r,a,u,f,r.rxp[a]));r=r._next}return{proxy:h,end:p,firstMPT:f,pt:l}},q.CSSPropTween=function(e,t,r,i,o,u,a,f,l,c,h){this.t=e,this.p=t,this.s=r,this.c=i,this.n=a||t,e instanceof dt||s.push(this.n),this.r=f,this.type=u||0,l&&(this.pr=l,n=!0),this.b=void 0===c?r:c,this.e=void 0===h?r+i:h,o&&(this._next=o,o._prev=this)}),vt=o.parseComplex=function(e,t,n,r,i,s,o,u,a,f){n=n||s||"",o=new dt(e,t,0,0,o,f?2:1,null,!1,u,n,r),r+="";var c,h,p,d,v,y,b,w,E,S,x,N,C=n.split(", ").join(",").split(" "),k=r.split(", ").join(",").split(" "),L=C.length,A=l!==!1;for((-1!==r.indexOf(",")||-1!==n.indexOf(","))&&(C=C.join(" ").replace(_,", ").split(" "),k=k.join(" ").replace(_,", ").split(" "),L=C.length),L!==k.length&&(C=(s||"").split(" "),L=C.length),o.plugin=a,o.setRatio=f,c=0;L>c;c++)if(d=C[c],v=k[c],w=parseFloat(d),w||0===w)o.appendXtra("",w,it(v,w),v.replace(g,""),A&&-1!==v.indexOf("px"),!0);else if(i&&("#"===d.charAt(0)||ut[d]||T.test(d)))N=","===v.charAt(v.length-1)?"),":")",d=ft(d),v=ft(v),E=d.length+v.length>6,E&&!U&&0===v[3]?(o["xs"+o.l]+=o.l?" transparent":"transparent",o.e=o.e.split(k[c]).join("transparent")):(U||(E=!1),o.appendXtra(E?"rgba(":"rgb(",d[0],v[0]-d[0],",",!0,!0).appendXtra("",d[1],v[1]-d[1],",",!0).appendXtra("",d[2],v[2]-d[2],E?",":N,!0),E&&(d=4>d.length?1:d[3],o.appendXtra("",d,(4>v.length?1:v[3])-d,N,!1)));else if(y=d.match(m)){if(b=v.match(g),!b||b.length!==y.length)return o;for(p=0,h=0;y.length>h;h++)x=y[h],S=d.indexOf(x,p),o.appendXtra(d.substr(p,S-p),Number(x),it(b[h],x),"",A&&"px"===d.substr(S+x.length,2),0===h),p=S+x.length;o["xs"+o.l]+=d.substr(p)}else o["xs"+o.l]+=o.l?" "+d:d;if(-1!==r.indexOf("=")&&o.data){for(N=o.xs0+o.data.s,c=1;o.l>c;c++)N+=o["xs"+c]+o.data["xn"+c];o.e=N+o["xs"+c]}return o.l||(o.type=-1,o.xs0=o.e),o.xfirst||o},mt=9;for(f=dt.prototype,f.l=f.pr=0;--mt>0;)f["xn"+mt]=0,f["xs"+mt]="";f.xs0="",f._next=f._prev=f.xfirst=f.data=f.plugin=f.setRatio=f.rxp=null,f.appendXtra=function(e,t,n,r,i,s){var o=this,u=o.l;return o["xs"+u]+=s&&u?" "+e:e||"",n||0===u||o.plugin?(o.l++,o.type=o.setRatio?2:1,o["xs"+o.l]=r||"",u>0?(o.data["xn"+u]=t+n,o.rxp["xn"+u]=i,o["xn"+u]=t,o.plugin||(o.xfirst=new dt(o,"xn"+u,t,n,o.xfirst||o,0,o.n,i,o.pr),o.xfirst.xs0=0),o):(o.data={s:t+n},o.rxp={},o.s=t,o.c=n,o.r=i,o)):(o["xs"+u]+=t+(r||""),o)};var gt=function(e,t){t=t||{},this.p=t.prefix?$(e)||e:e,a[e]=a[this.p]=this,this.format=t.formatter||ct(t.defaultValue,t.color,t.collapsible,t.multi),t.parser&&(this.parse=t.parser),this.clrs=t.color,this.multi=t.multi,this.keyword=t.keyword,this.dflt=t.defaultValue,this.pr=t.priority||0},yt=q._registerComplexSpecialProp=function(e,t,n){"object"!=typeof t&&(t={parser:n});var r,i,s=e.split(","),o=t.defaultValue;for(n=n||[o],r=0;s.length>r;r++)t.prefix=0===r&&t.prefix,t.defaultValue=n[r]||o,i=new gt(s[r],t)},bt=function(e){if(!a[e]){var t=e.charAt(0).toUpperCase()+e.substr(1)+"Plugin";yt(e,{parser:function(e,n,r,i,s,o,f){var l=u.com.greensock.plugins[t];return l?(l._cssRegister(),a[r].parse(e,n,r,i,s,o,f)):(W("Error: "+t+" js file not loaded."),s)}})}};f=gt.prototype,f.parseComplex=function(e,t,n,r,i,s){var o,u,a,f,l,c,h=this.keyword;if(this.multi&&(_.test(n)||_.test(t)?(u=t.replace(_,"|").split("|"),a=n.replace(_,"|").split("|")):h&&(u=[t],a=[n])),a){for(f=a.length>u.length?a.length:u.length,o=0;f>o;o++)t=u[o]=u[o]||this.dflt,n=a[o]=a[o]||this.dflt,h&&(l=t.indexOf(h),c=n.indexOf(h),l!==c&&(-1===c?u[o]=u[o].split(h).join(""):-1===l&&(u[o]+=" "+h)));t=u.join(", "),n=a.join(", ")}return vt(e,this.p,t,n,this.clrs,this.dflt,r,this.pr,i,s)},f.parse=function(e,t,n,r,s,o){return this.parseComplex(e.style,this.format(K(e,this.p,i,!1,this.dflt)),this.format(t),s,o)},o.registerSpecialProp=function(e,t,n){yt(e,{parser:function(e,r,i,s,o,u){var a=new dt(e,i,0,0,o,2,i,!1,n);return a.plugin=u,a.setRatio=t(e,r,s._tween,i),a},priority:n})},o.useSVGTransformAttr=h;var wt,Et="scaleX,scaleY,scaleZ,x,y,z,skewX,skewY,rotation,rotationX,rotationY,perspective,xPercent,yPercent".split(","),St=$("transform"),xt=X+"transform",Tt=$("transformOrigin"),Nt=null!==$("perspective"),Ct=q.Transform=function(){this.perspective=parseFloat(o.defaultTransformPerspective)||0,this.force3D=o.defaultForce3D!==!1&&Nt?o.defaultForce3D||"auto":!1},kt=window.SVGElement,Lt=function(e,t,n){var r,i=B.createElementNS("http://www.w3.org/2000/svg",e),s=/([a-z])([A-Z])/g;for(r in n)i.setAttributeNS(null,r.replace(s,"$1-$2").toLowerCase(),n[r]);return t.appendChild(i),i},At=B.documentElement,Ot=function(){var e,t,n,r=v||/Android/i.test(R)&&!window.chrome;return B.createElementNS&&!r&&(e=Lt("svg",At),t=Lt("rect",e,{width:100,height:50,x:100}),n=t.getBoundingClientRect().width,t.style[Tt]="50% 50%",t.style[St]="scaleX(0.5)",r=n===t.getBoundingClientRect().width&&(!p||!Nt),At.removeChild(e)),r}(),Mt=function(e,t,n,r){var i,s;r&&(s=r.split(" ")).length||(i=e.getBBox(),t=rt(t).split(" "),s=[(-1!==t[0].indexOf("%")?parseFloat(t[0])/100*i.width:parseFloat(t[0]))+i.x,(-1!==t[1].indexOf("%")?parseFloat(t[1])/100*i.height:parseFloat(t[1]))+i.y]),n.xOrigin=parseFloat(s[0]),n.yOrigin=parseFloat(s[1]),e.setAttribute("data-svg-origin",s.join(" "))},_t=q.getTransform=function(e,t,n,r){if(e._gsTransform&&n&&!r)return e._gsTransform;var s,u,a,f,l,c,h,p,d,v,m=n?e._gsTransform||new Ct:new Ct,g=0>m.scaleX,y=2e-5,b=1e5,w=Nt?parseFloat(K(e,Tt,t,!1,"0 0 0").split(" ")[2])||m.zOrigin||0:0,E=parseFloat(o.defaultTransformPerspective)||0;if(St?u=K(e,xt,t,!0):e.currentStyle&&(u=e.currentStyle.filter.match(O),u=u&&4===u.length?[u[0].substr(4),Number(u[2].substr(4)),Number(u[1].substr(4)),u[3].substr(4),m.x||0,m.y||0].join(","):""),s=!u||"none"===u||"matrix(1, 0, 0, 1, 0, 0)"===u,m.svg=!!(kt&&"function"==typeof e.getBBox&&e.getCTM&&(!e.parentNode||e.parentNode.getBBox&&e.parentNode.getCTM)),m.svg&&(s&&-1!==(e.style[St]+"").indexOf("matrix")&&(u=e.style[St],s=!1),Mt(e,K(e,Tt,i,!1,"50% 50%")+"",m,e.getAttribute("data-svg-origin")),wt=o.useSVGTransformAttr||Ot,a=e.getAttribute("transform"),s&&a&&-1!==a.indexOf("matrix")&&(u=a,s=0)),!s){for(a=(u||"").match(/(?:\-|\b)[\d\-\.e]+\b/gi)||[],f=a.length;--f>-1;)l=Number(a[f]),a[f]=(c=l-(l|=0))?(0|c*b+(0>c?-0.5:.5))/b+l:l;if(16===a.length){var S,x,T,N,C,k=a[0],L=a[1],A=a[2],M=a[3],_=a[4],D=a[5],H=a[6],B=a[7],j=a[8],F=a[9],I=a[10],q=a[12],R=a[13],U=a[14],z=a[11],W=Math.atan2(H,I);m.zOrigin&&(U=-m.zOrigin,q=j*U-a[12],R=F*U-a[13],U=I*U+m.zOrigin-a[14]),m.rotationX=W*P,W&&(N=Math.cos(-W),C=Math.sin(-W),S=_*N+j*C,x=D*N+F*C,T=H*N+I*C,j=_*-C+j*N,F=D*-C+F*N,I=H*-C+I*N,z=B*-C+z*N,_=S,D=x,H=T),W=Math.atan2(j,I),m.rotationY=W*P,W&&(N=Math.cos(-W),C=Math.sin(-W),S=k*N-j*C,x=L*N-F*C,T=A*N-I*C,F=L*C+F*N,I=A*C+I*N,z=M*C+z*N,k=S,L=x,A=T),W=Math.atan2(L,k),m.rotation=W*P,W&&(N=Math.cos(-W),C=Math.sin(-W),k=k*N+_*C,x=L*N+D*C,D=L*-C+D*N,H=A*-C+H*N,L=x),m.rotationX&&Math.abs(m.rotationX)+Math.abs(m.rotation)>359.9&&(m.rotationX=m.rotation=0,m.rotationY+=180),m.scaleX=(0|Math.sqrt(k*k+L*L)*b+.5)/b,m.scaleY=(0|Math.sqrt(D*D+F*F)*b+.5)/b,m.scaleZ=(0|Math.sqrt(H*H+I*I)*b+.5)/b,m.skewX=0,m.perspective=z?1/(0>z?-z:z):0,m.x=q,m.y=R,m.z=U,m.svg&&(m.x-=m.xOrigin-(m.xOrigin*k-m.yOrigin*_),m.y-=m.yOrigin-(m.yOrigin*L-m.xOrigin*D))}else if(!(Nt&&!r&&a.length&&m.x===a[4]&&m.y===a[5]&&(m.rotationX||m.rotationY)||void 0!==m.x&&"none"===K(e,"display",t))){var X=a.length>=6,V=X?a[0]:1,$=a[1]||0,J=a[2]||0,Q=X?a[3]:1;m.x=a[4]||0,m.y=a[5]||0,h=Math.sqrt(V*V+$*$),p=Math.sqrt(Q*Q+J*J),d=V||$?Math.atan2($,V)*P:m.rotation||0,v=J||Q?Math.atan2(J,Q)*P+d:m.skewX||0,Math.abs(v)>90&&270>Math.abs(v)&&(g?(h*=-1,v+=0>=d?180:-180,d+=0>=d?180:-180):(p*=-1,v+=0>=v?180:-180)),m.scaleX=h,m.scaleY=p,m.rotation=d,m.skewX=v,Nt&&(m.rotationX=m.rotationY=m.z=0,m.perspective=E,m.scaleZ=1),m.svg&&(m.x-=m.xOrigin-(m.xOrigin*V-m.yOrigin*$),m.y-=m.yOrigin-(m.yOrigin*Q-m.xOrigin*J))}m.zOrigin=w;for(f in m)y>m[f]&&m[f]>-y&&(m[f]=0)}return n&&(e._gsTransform=m,m.svg&&(wt&&e.style[St]?Bt(e.style,St):!wt&&e.getAttribute("transform")&&e.removeAttribute("transform"))),m},Dt=function(e){var t,n,r=this.data,i=-r.rotation*D,s=i+r.skewX*D,o=1e5,u=(0|Math.cos(i)*r.scaleX*o)/o,a=(0|Math.sin(i)*r.scaleX*o)/o,f=(0|Math.sin(s)*-r.scaleY*o)/o,l=(0|Math.cos(s)*r.scaleY*o)/o,c=this.t.style,h=this.t.currentStyle;if(h){n=a,a=-f,f=-n,t=h.filter,c.filter="";var p,d,m=this.t.offsetWidth,g=this.t.offsetHeight,y="absolute"!==h.position,b="progid:DXImageTransform.Microsoft.Matrix(M11="+u+", M12="+a+", M21="+f+", M22="+l,S=r.x+m*r.xPercent/100,x=r.y+g*r.yPercent/100;if(null!=r.ox&&(p=(r.oxp?.01*m*r.ox:r.ox)-m/2,d=(r.oyp?.01*g*r.oy:r.oy)-g/2,S+=p-(p*u+d*a),x+=d-(p*f+d*l)),y?(p=m/2,d=g/2,b+=", Dx="+(p-(p*u+d*a)+S)+", Dy="+(d-(p*f+d*l)+x)+")"):b+=", sizingMethod='auto expand')",c.filter=-1!==t.indexOf("DXImageTransform.Microsoft.Matrix(")?t.replace(M,b):b+" "+t,(0===e||1===e)&&1===u&&0===a&&0===f&&1===l&&(y&&-1===b.indexOf("Dx=0, Dy=0")||E.test(t)&&100!==parseFloat(RegExp.$1)||-1===t.indexOf(t.indexOf("Alpha"))&&c.removeAttribute("filter")),!y){var T,N,C,k=8>v?1:-1;for(p=r.ieOffsetX||0,d=r.ieOffsetY||0,r.ieOffsetX=Math.round((m-((0>u?-u:u)*m+(0>a?-a:a)*g))/2+S),r.ieOffsetY=Math.round((g-((0>l?-l:l)*g+(0>f?-f:f)*m))/2+x),mt=0;4>mt;mt++)N=tt[mt],T=h[N],n=-1!==T.indexOf("px")?parseFloat(T):Q(this.t,N,parseFloat(T),T.replace(w,""))||0,C=n!==r[N]?2>mt?-r.ieOffsetX:-r.ieOffsetY:2>mt?p-r.ieOffsetX:d-r.ieOffsetY,c[N]=(r[N]=Math.round(n-C*(0===mt||2===mt?1:k)))+"px"}}},Pt=q.set3DTransformRatio=q.setTransformRatio=function(e){var t,n,r,i,s,o,u,a,f,l,c,h,d,v,m,g,y,b,w,E,S,x,T,N=this.data,C=this.t.style,k=N.rotation,L=N.rotationX,A=N.rotationY,O=N.scaleX,M=N.scaleY,_=N.scaleZ,P=N.x,H=N.y,B=N.z,j=N.svg,F=N.perspective,I=N.force3D;if(!((1!==e&&0!==e||"auto"!==I||this.tween._totalTime!==this.tween._totalDuration&&this.tween._totalTime)&&I||B||F||A||L)||!!wt&&!!j||!Nt)return k||N.skewX||j?(k*=D,x=N.skewX*D,T=1e5,t=Math.cos(k)*O,i=Math.sin(k)*O,n=Math.sin(k-x)*-M,s=Math.cos(k-x)*M,x&&"simple"===N.skewType&&(y=Math.tan(x),y=Math.sqrt(1+y*y),n*=y,s*=y,N.skewY&&(t*=y,i*=y)),j&&(P+=N.xOrigin-(N.xOrigin*t+N.yOrigin*n),H+=N.yOrigin-(N.xOrigin*i+N.yOrigin*s),v=1e-6,v>P&&P>-v&&(P=0),v>H&&H>-v&&(H=0)),w=(0|t*T)/T+","+(0|i*T)/T+","+(0|n*T)/T+","+(0|s*T)/T+","+P+","+H+")",j&&wt?this.t.setAttribute("transform","matrix("+w):C[St]=(N.xPercent||N.yPercent?"translate("+N.xPercent+"%,"+N.yPercent+"%) matrix(":"matrix(")+w):C[St]=(N.xPercent||N.yPercent?"translate("+N.xPercent+"%,"+N.yPercent+"%) matrix(":"matrix(")+O+",0,0,"+M+","+P+","+H+")",void 0;if(p&&(v=1e-4,v>O&&O>-v&&(O=_=2e-5),v>M&&M>-v&&(M=_=2e-5),!F||N.z||N.rotationX||N.rotationY||(F=0)),k||N.skewX)k*=D,m=t=Math.cos(k),g=i=Math.sin(k),N.skewX&&(k-=N.skewX*D,m=Math.cos(k),g=Math.sin(k),"simple"===N.skewType&&(y=Math.tan(N.skewX*D),y=Math.sqrt(1+y*y),m*=y,g*=y,N.skewY&&(t*=y,i*=y))),n=-g,s=m;else{if(!(A||L||1!==_||F||j))return C[St]=(N.xPercent||N.yPercent?"translate("+N.xPercent+"%,"+N.yPercent+"%) translate3d(":"translate3d(")+P+"px,"+H+"px,"+B+"px)"+(1!==O||1!==M?" scale("+O+","+M+")":""),void 0;t=s=1,n=i=0}f=1,r=o=u=a=l=c=0,h=F?-1/F:0,d=N.zOrigin,v=1e-6,E=",",S="0",k=A*D,k&&(m=Math.cos(k),g=Math.sin(k),u=-g,l=h*-g,r=t*g,o=i*g,f=m,h*=m,t*=m,i*=m),k=L*D,k&&(m=Math.cos(k),g=Math.sin(k),y=n*m+r*g,b=s*m+o*g,a=f*g,c=h*g,r=n*-g+r*m,o=s*-g+o*m,f*=m,h*=m,n=y,s=b),1!==_&&(r*=_,o*=_,f*=_,h*=_),1!==M&&(n*=M,s*=M,a*=M,c*=M),1!==O&&(t*=O,i*=O,u*=O,l*=O),(d||j)&&(d&&(P+=r*-d,H+=o*-d,B+=f*-d+d),j&&(P+=N.xOrigin-(N.xOrigin*t+N.yOrigin*n),H+=N.yOrigin-(N.xOrigin*i+N.yOrigin*s)),v>P&&P>-v&&(P=S),v>H&&H>-v&&(H=S),v>B&&B>-v&&(B=0)),w=N.xPercent||N.yPercent?"translate("+N.xPercent+"%,"+N.yPercent+"%) matrix3d(":"matrix3d(",w+=(v>t&&t>-v?S:t)+E+(v>i&&i>-v?S:i)+E+(v>u&&u>-v?S:u),w+=E+(v>l&&l>-v?S:l)+E+(v>n&&n>-v?S:n)+E+(v>s&&s>-v?S:s),L||A?(w+=E+(v>a&&a>-v?S:a)+E+(v>c&&c>-v?S:c)+E+(v>r&&r>-v?S:r),w+=E+(v>o&&o>-v?S:o)+E+(v>f&&f>-v?S:f)+E+(v>h&&h>-v?S:h)+E):w+=",0,0,0,0,1,0,",w+=P+E+H+E+B+E+(F?1+ -B/F:1)+")",C[St]=w};f=Ct.prototype,f.x=f.y=f.z=f.skewX=f.skewY=f.rotation=f.rotationX=f.rotationY=f.zOrigin=f.xPercent=f.yPercent=0,f.scaleX=f.scaleY=f.scaleZ=1,yt("transform,scale,scaleX,scaleY,scaleZ,x,y,z,rotation,rotationX,rotationY,rotationZ,skewX,skewY,shortRotation,shortRotationX,shortRotationY,shortRotationZ,transformOrigin,svgOrigin,transformPerspective,directionalRotation,parseTransform,force3D,skewType,xPercent,yPercent",{parser:function(e,t,n,r,s,u,a){if(r._lastParsedTransform===a)return s;r._lastParsedTransform=a;var f,l,c,h,p,d,v,m=r._transform=_t(e,i,!0,a.parseTransform),g=e.style,y=1e-6,b=Et.length,w=a,E={};if("string"==typeof w.transform&&St)c=F.style,c[St]=w.transform,c.display="block",c.position="absolute",B.body.appendChild(F),f=_t(F,null,!1),B.body.removeChild(F);else if("object"==typeof w){if(f={scaleX:st(null!=w.scaleX?w.scaleX:w.scale,m.scaleX),scaleY:st(null!=w.scaleY?w.scaleY:w.scale,m.scaleY),scaleZ:st(w.scaleZ,m.scaleZ),x:st(w.x,m.x),y:st(w.y,m.y),z:st(w.z,m.z),xPercent:st(w.xPercent,m.xPercent),yPercent:st(w.yPercent,m.yPercent),perspective:st(w.transformPerspective,m.perspective)},v=w.directionalRotation,null!=v)if("object"==typeof v)for(c in v)w[c]=v[c];else w.rotation=v;"string"==typeof w.x&&-1!==w.x.indexOf("%")&&(f.x=0,f.xPercent=st(w.x,m.xPercent)),"string"==typeof w.y&&-1!==w.y.indexOf("%")&&(f.y=0,f.yPercent=st(w.y,m.yPercent)),f.rotation=ot("rotation"in w?w.rotation:"shortRotation"in w?w.shortRotation+"_short":"rotationZ"in w?w.rotationZ:m.rotation,m.rotation,"rotation",E),Nt&&(f.rotationX=ot("rotationX"in w?w.rotationX:"shortRotationX"in w?w.shortRotationX+"_short":m.rotationX||0,m.rotationX,"rotationX",E),f.rotationY=ot("rotationY"in w?w.rotationY:"shortRotationY"in w?w.shortRotationY+"_short":m.rotationY||0,m.rotationY,"rotationY",E)),f.skewX=null==w.skewX?m.skewX:ot(w.skewX,m.skewX),f.skewY=null==w.skewY?m.skewY:ot(w.skewY,m.skewY),(l=f.skewY-m.skewY)&&(f.skewX+=l,f.rotation+=l)}for(Nt&&null!=w.force3D&&(m.force3D=w.force3D,d=!0),m.skewType=w.skewType||m.skewType||o.defaultSkewType,p=m.force3D||m.z||m.rotationX||m.rotationY||f.z||f.rotationX||f.rotationY||f.perspective,p||null==w.scale||(f.scaleZ=1);--b>-1;)n=Et[b],h=f[n]-m[n],(h>y||-y>h||null!=w[n]||null!=H[n])&&(d=!0,s=new dt(m,n,m[n],h,s),n in E&&(s.e=E[n]),s.xs0=0,s.plugin=u,r._overwriteProps.push(s.n));return h=w.transformOrigin,m.svg&&(h||w.svgOrigin)&&(Mt(e,rt(h),f,w.svgOrigin),s=new dt(m,"xOrigin",m.xOrigin,f.xOrigin-m.xOrigin,s,-1,"transformOrigin"),s.b=m.xOrigin,s.e=s.xs0=f.xOrigin,s=new dt(m,"yOrigin",m.yOrigin,f.yOrigin-m.yOrigin,s,-1,"transformOrigin"),s.b=m.yOrigin,s.e=s.xs0=f.yOrigin,h=wt?null:"0px 0px"),(h||Nt&&p&&m.zOrigin)&&(St?(d=!0,n=Tt,h=(h||K(e,n,i,!1,"50% 50%"))+"",s=new dt(g,n,0,0,s,-1,"transformOrigin"),s.b=g[n],s.plugin=u,Nt?(c=m.zOrigin,h=h.split(" "),m.zOrigin=(h.length>2&&(0===c||"0px"!==h[2])?parseFloat(h[2]):c)||0,s.xs0=s.e=h[0]+" "+(h[1]||"50%")+" 0px",s=new dt(m,"zOrigin",0,0,s,-1,s.n),s.b=c,s.xs0=s.e=m.zOrigin):s.xs0=s.e=h):rt(h+"",m)),d&&(r._transformType=m.svg&&wt||!p&&3!==this._transformType?2:3),s},prefix:!0}),yt("boxShadow",{defaultValue:"0px 0px 0px 0px #999",prefix:!0,color:!0,multi:!0,keyword:"inset"}),yt("borderRadius",{defaultValue:"0px",parser:function(e,t,n,s,o){t=this.format(t);var u,a,f,l,c,h,p,d,v,m,g,y,b,w,E,S,x=["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"],T=e.style;for(v=parseFloat(e.offsetWidth),m=parseFloat(e.offsetHeight),u=t.split(" "),a=0;x.length>a;a++)this.p.indexOf("border")&&(x[a]=$(x[a])),c=l=K(e,x[a],i,!1,"0px"),-1!==c.indexOf(" ")&&(l=c.split(" "),c=l[0],l=l[1]),h=f=u[a],p=parseFloat(c),y=c.substr((p+"").length),b="="===h.charAt(1),b?(d=parseInt(h.charAt(0)+"1",10),h=h.substr(2),d*=parseFloat(h),g=h.substr((d+"").length-(0>d?1:0))||""):(d=parseFloat(h),g=h.substr((d+"").length)),""===g&&(g=r[n]||y),g!==y&&(w=Q(e,"borderLeft",p,y),E=Q(e,"borderTop",p,y),"%"===g?(c=100*(w/v)+"%",l=100*(E/m)+"%"):"em"===g?(S=Q(e,"borderLeft",1,"em"),c=w/S+"em",l=E/S+"em"):(c=w+"px",l=E+"px"),b&&(h=parseFloat(c)+d+g,f=parseFloat(l)+d+g)),o=vt(T,x[a],c+" "+l,h+" "+f,!1,"0px",o);return o},prefix:!0,formatter:ct("0px 0px 0px 0px",!1,!0)}),yt("backgroundPosition",{defaultValue:"0 0",parser:function(e,t,n,r,s,o){var u,a,f,l,c,h,p="background-position",d=i||J(e,null),m=this.format((d?v?d.getPropertyValue(p+"-x")+" "+d.getPropertyValue(p+"-y"):d.getPropertyValue(p):e.currentStyle.backgroundPositionX+" "+e.currentStyle.backgroundPositionY)||"0 0"),g=this.format(t);if(-1!==m.indexOf("%")!=(-1!==g.indexOf("%"))&&(h=K(e,"backgroundImage").replace(k,""),h&&"none"!==h)){for(u=m.split(" "),a=g.split(" "),I.setAttribute("src",h),f=2;--f>-1;)m=u[f],l=-1!==m.indexOf("%"),l!==(-1!==a[f].indexOf("%"))&&(c=0===f?e.offsetWidth-I.width:e.offsetHeight-I.height,u[f]=l?parseFloat(m)/100*c+"px":100*(parseFloat(m)/c)+"%");m=u.join(" ")}return this.parseComplex(e.style,m,g,s,o)},formatter:rt}),yt("backgroundSize",{defaultValue:"0 0",formatter:rt}),yt("perspective",{defaultValue:"0px",prefix:!0}),yt("perspectiveOrigin",{defaultValue:"50% 50%",prefix:!0}),yt("transformStyle",{prefix:!0}),yt("backfaceVisibility",{prefix:!0}),yt("userSelect",{prefix:!0}),yt("margin",{parser:ht("marginTop,marginRight,marginBottom,marginLeft")}),yt("padding",{parser:ht("paddingTop,paddingRight,paddingBottom,paddingLeft")}),yt("clip",{defaultValue:"rect(0px,0px,0px,0px)",parser:function(e,t,n,r,s,o){var u,a,f;return 9>v?(a=e.currentStyle,f=8>v?" ":",",u="rect("+a.clipTop+f+a.clipRight+f+a.clipBottom+f+a.clipLeft+")",t=this.format(t).split(",").join(f)):(u=this.format(K(e,this.p,i,!1,this.dflt)),t=this.format(t)),this.parseComplex(e.style,u,t,s,o)}}),yt("textShadow",{defaultValue:"0px 0px 0px #999",color:!0,multi:!0}),yt("autoRound,strictUnits",{parser:function(e,t,n,r,i){return i}}),yt("border",{defaultValue:"0px solid #000",parser:function(e,t,n,r,s,o){return this.parseComplex(e.style,this.format(K(e,"borderTopWidth",i,!1,"0px")+" "+K(e,"borderTopStyle",i,!1,"solid")+" "+K(e,"borderTopColor",i,!1,"#000")),this.format(t),s,o)},color:!0,formatter:function(e){var t=e.split(" ");return t[0]+" "+(t[1]||"solid")+" "+(e.match(lt)||["#000"])[0]}}),yt("borderWidth",{parser:ht("borderTopWidth,borderRightWidth,borderBottomWidth,borderLeftWidth")}),yt("float,cssFloat,styleFloat",{parser:function(e,t,n,r,i){var s=e.style,o="cssFloat"in s?"cssFloat":"styleFloat";return new dt(s,o,0,0,i,-1,n,!1,0,s[o],t)}});var Ht=function(e){var t,n=this.t,r=n.filter||K(this.data,"filter")||"",i=0|this.s+this.c*e;100===i&&(-1===r.indexOf("atrix(")&&-1===r.indexOf("radient(")&&-1===r.indexOf("oader(")?(n.removeAttribute("filter"),t=!K(this.data,"filter")):(n.filter=r.replace(x,""),t=!0)),t||(this.xn1&&(n.filter=r=r||"alpha(opacity="+i+")"),-1===r.indexOf("pacity")?0===i&&this.xn1||(n.filter=r+" alpha(opacity="+i+")"):n.filter=r.replace(E,"opacity="+i))};yt("opacity,alpha,autoAlpha",{defaultValue:"1",parser:function(e,t,n,r,s,o){var u=parseFloat(K(e,"opacity",i,!1,"1")),a=e.style,f="autoAlpha"===n;return"string"==typeof t&&"="===t.charAt(1)&&(t=("-"===t.charAt(0)?-1:1)*parseFloat(t.substr(2))+u),f&&1===u&&"hidden"===K(e,"visibility",i)&&0!==t&&(u=0),U?s=new dt(a,"opacity",u,t-u,s):(s=new dt(a,"opacity",100*u,100*(t-u),s),s.xn1=f?1:0,a.zoom=1,s.type=2,s.b="alpha(opacity="+s.s+")",s.e="alpha(opacity="+(s.s+s.c)+")",s.data=e,s.plugin=o,s.setRatio=Ht),f&&(s=new dt(a,"visibility",0,0,s,-1,null,!1,0,0!==u?"inherit":"hidden",0===t?"hidden":"inherit"),s.xs0="inherit",r._overwriteProps.push(s.n),r._overwriteProps.push(n)),s}});var Bt=function(e,t){t&&(e.removeProperty?(("ms"===t.substr(0,2)||"webkit"===t.substr(0,6))&&(t="-"+t),e.removeProperty(t.replace(N,"-$1").toLowerCase())):e.removeAttribute(t))},jt=function(e){if(this.t._gsClassPT=this,1===e||0===e){this.t.setAttribute("class",0===e?this.b:this.e);for(var t=this.data,n=this.t.style;t;)t.v?n[t.p]=t.v:Bt(n,t.p),t=t._next;1===e&&this.t._gsClassPT===this&&(this.t._gsClassPT=null)}else this.t.getAttribute("class")!==this.e&&this.t.setAttribute("class",this.e)};yt("className",{parser:function(e,t,r,s,o,u,a){var f,l,c,h,p,d=e.getAttribute("class")||"",v=e.style.cssText;if(o=s._classNamePT=new dt(e,r,0,0,o,2),o.setRatio=jt,o.pr=-11,n=!0,o.b=d,l=Y(e,i),c=e._gsClassPT){for(h={},p=c.data;p;)h[p.p]=1,p=p._next;c.setRatio(1)}return e._gsClassPT=o,o.e="="!==t.charAt(1)?t:d.replace(RegExp("\\s*\\b"+t.substr(2)+"\\b"),"")+("+"===t.charAt(0)?" "+t.substr(2):""),e.setAttribute("class",o.e),f=Z(e,l,Y(e),a,h),e.setAttribute("class",d),o.data=f.firstMPT,e.style.cssText=v,o=o.xfirst=s.parse(e,f.difs,o,u)}});var Ft=function(e){if((1===e||0===e)&&this.data._totalTime===this.data._totalDuration&&"isFromStart"!==this.data.data){var t,n,r,i,s,o=this.t.style,u=a.transform.parse;if("all"===this.e)o.cssText="",i=!0;else for(t=this.e.split(" ").join("").split(","),r=t.length;--r>-1;)n=t[r],a[n]&&(a[n].parse===u?i=!0:n="transformOrigin"===n?Tt:a[n].p),Bt(o,n);i&&(Bt(o,St),s=this.t._gsTransform,s&&(s.svg&&this.t.removeAttribute("data-svg-origin"),delete this.t._gsTransform))}};for(yt("clearProps",{parser:function(e,t,r,i,s){return s=new dt(e,r,0,0,s,2),s.setRatio=Ft,s.e=t,s.pr=-10,s.data=i._tween,n=!0,s}}),f="bezier,throwProps,physicsProps,physics2D".split(","),mt=f.length;mt--;)bt(f[mt]);f=o.prototype,f._firstPT=f._lastParsedTransform=f._transform=null,f._onInitTween=function(e,t,u){if(!e.nodeType)return!1;this._target=e,this._tween=u,this._vars=t,l=t.autoRound,n=!1,r=t.suffixMap||o.suffixMap,i=J(e,""),s=this._overwriteProps;var f,p,v,m,g,y,b,w,E,x=e.style;if(c&&""===x.zIndex&&(f=K(e,"zIndex",i),("auto"===f||""===f)&&this._addLazySet(x,"zIndex",0)),"string"==typeof t&&(m=x.cssText,f=Y(e,i),x.cssText=m+";"+t,f=Z(e,f,Y(e)).difs,!U&&S.test(t)&&(f.opacity=parseFloat(RegExp.$1)),t=f,x.cssText=m),this._firstPT=p=t.className?a.className.parse(e,t.className,"className",this,null,null,t):this.parse(e,t,null),this._transformType){for(E=3===this._transformType,St?h&&(c=!0,""===x.zIndex&&(b=K(e,"zIndex",i),("auto"===b||""===b)&&this._addLazySet(x,"zIndex",0)),d&&this._addLazySet(x,"WebkitBackfaceVisibility",this._vars.WebkitBackfaceVisibility||(E?"visible":"hidden"))):x.zoom=1,v=p;v&&v._next;)v=v._next;w=new dt(e,"transform",0,0,null,2),this._linkCSSP(w,null,v),w.setRatio=St?Pt:Dt,w.data=this._transform||_t(e,i,!0),w.tween=u,w.pr=-1,s.pop()}if(n){for(;p;){for(y=p._next,v=m;v&&v.pr>p.pr;)v=v._next;(p._prev=v?v._prev:g)?p._prev._next=p:m=p,(p._next=v)?v._prev=p:g=p,p=y}this._firstPT=m}return!0},f.parse=function(e,t,n,s){var o,u,f,c,h,p,d,v,m,g,y=e.style;for(o in t)p=t[o],u=a[o],u?n=u.parse(e,p,o,this,n,s,t):(h=K(e,o,i)+"",m="string"==typeof p,"color"===o||"fill"===o||"stroke"===o||-1!==o.indexOf("Color")||m&&T.test(p)?(m||(p=ft(p),p=(p.length>3?"rgba(":"rgb(")+p.join(",")+")"),n=vt(y,o,h,p,!0,"transparent",n,0,s)):!m||-1===p.indexOf(" ")&&-1===p.indexOf(",")?(f=parseFloat(h),d=f||0===f?h.substr((f+"").length):"",(""===h||"auto"===h)&&("width"===o||"height"===o?(f=nt(e,o,i),d="px"):"left"===o||"top"===o?(f=G(e,o,i),d="px"):(f="opacity"!==o?0:1,d="")),g=m&&"="===p.charAt(1),g?(c=parseInt(p.charAt(0)+"1",10),p=p.substr(2),c*=parseFloat(p),v=p.replace(w,"")):(c=parseFloat(p),v=m?p.replace(w,""):""),""===v&&(v=o in r?r[o]:d),p=c||0===c?(g?c+f:c)+v:t[o],d!==v&&""!==v&&(c||0===c)&&f&&(f=Q(e,o,f,d),"%"===v?(f/=Q(e,o,100,"%")/100,t.strictUnits!==!0&&(h=f+"%")):"em"===v?f/=Q(e,o,1,"em"):"px"!==v&&(c=Q(e,o,c,v),v="px"),g&&(c||0===c)&&(p=c+f+v)),g&&(c+=f),!f&&0!==f||!c&&0!==c?void 0!==y[o]&&(p||"NaN"!=p+""&&null!=p)?(n=new dt(y,o,c||f||0,0,n,-1,o,!1,0,h,p),n.xs0="none"!==p||"display"!==o&&-1===o.indexOf("Style")?p:h):W("invalid "+o+" tween value: "+t[o]):(n=new dt(y,o,f,c-f,n,0,o,l!==!1&&("px"===v||"zIndex"===o),0,h,p),n.xs0=v)):n=vt(y,o,h,p,!0,null,n,0,s)),s&&n&&!n.plugin&&(n.plugin=s);return n},f.setRatio=function(e){var t,n,r,i=this._firstPT,s=1e-6;if(1!==e||this._tween._time!==this._tween._duration&&0!==this._tween._time)if(e||this._tween._time!==this._tween._duration&&0!==this._tween._time||this._tween._rawPrevTime===-0.000001)for(;i;){if(t=i.c*e+i.s,i.r?t=Math.round(t):s>t&&t>-s&&(t=0),i.type)if(1===i.type)if(r=i.l,2===r)i.t[i.p]=i.xs0+t+i.xs1+i.xn1+i.xs2;else if(3===r)i.t[i.p]=i.xs0+t+i.xs1+i.xn1+i.xs2+i.xn2+i.xs3;else if(4===r)i.t[i.p]=i.xs0+t+i.xs1+i.xn1+i.xs2+i.xn2+i.xs3+i.xn3+i.xs4;else if(5===r)i.t[i.p]=i.xs0+t+i.xs1+i.xn1+i.xs2+i.xn2+i.xs3+i.xn3+i.xs4+i.xn4+i.xs5;else{for(n=i.xs0+t+i.xs1,r=1;i.l>r;r++)n+=i["xn"+r]+i["xs"+(r+1)];i.t[i.p]=n}else-1===i.type?i.t[i.p]=i.xs0:i.setRatio&&i.setRatio(e);else i.t[i.p]=t+i.xs0;i=i._next}else for(;i;)2!==i.type?i.t[i.p]=i.b:i.setRatio(e),i=i._next;else for(;i;)2!==i.type?i.t[i.p]=i.e:i.setRatio(e),i=i._next},f._enableTransforms=function(e){this._transform=this._transform||_t(this._target,i,!0),this._transformType=this._transform.svg&&wt||!e&&3!==this._transformType?2:3};var It=function(){this.t[this.p]=this.e,this.data._linkCSSP(this,this._next,null,!0)};f._addLazySet=function(e,t,n){var r=this._firstPT=new dt(e,t,0,0,this._firstPT,2);r.e=n,r.setRatio=It,r.data=this},f._linkCSSP=function(e,t,n,r){return e&&(t&&(t._prev=e),e._next&&(e._next._prev=e._prev),e._prev?e._prev._next=e._next:this._firstPT===e&&(this._firstPT=e._next,r=!0),n?n._next=e:r||null!==this._firstPT||(this._firstPT=e),e._next=t,e._prev=n),e},f._kill=function(t){var n,r,i,s=t;if(t.autoAlpha||t.alpha){s={};for(r in t)s[r]=t[r];s.opacity=1,s.autoAlpha&&(s.visibility=1)}return t.className&&(n=this._classNamePT)&&(i=n.xfirst,i&&i._prev?this._linkCSSP(i._prev,n._next,i._prev._prev):i===this._firstPT&&(this._firstPT=n._next),n._next&&this._linkCSSP(n._next,n._next._next,i._prev),this._classNamePT=null),e.prototype._kill.call(this,s)};var qt=function(e,t,n){var r,i,s,o;if(e.slice)for(i=e.length;--i>-1;)qt(e[i],t,n);else for(r=e.childNodes,i=r.length;--i>-1;)s=r[i],o=s.type,s.style&&(t.push(Y(s)),n&&n.push(s)),1!==o&&9!==o&&11!==o||!s.childNodes.length||qt(s,t,n)};return o.cascadeTo=function(e,n,r){var i,s,o,u,a=t.to(e,n,r),f=[a],l=[],c=[],h=[],p=t._internals.reservedProps;for(e=a._targets||a.target,qt(e,l,h),a.render(n,!0,!0),qt(e,c),a.render(0,!0,!0),a._enabled(!0),i=h.length;--i>-1;)if(s=Z(h[i],l[i],c[i]),s.firstMPT){s=s.difs;for(o in r)p[o]&&(s[o]=r[o]);u={};for(o in s)u[o]=l[i][o];f.push(t.fromTo(h[i],n,u,s))}return f},e.activate([o]),o},!0),function(){var e=_gsScope._gsDefine.plugin({propName:"roundProps",priority:-1,API:2,init:function(e,t,n){return this._tween=n,!0}}),t=e.prototype;t._onInitAllProps=function(){for(var e,t,n,r=this._tween,i=r.vars.roundProps instanceof Array?r.vars.roundProps:r.vars.roundProps.split(","),s=i.length,o={},u=r._propLookup.roundProps;--s>-1;)o[i[s]]=1;for(s=i.length;--s>-1;)for(e=i[s],t=r._firstPT;t;)n=t._next,t.pg?t.t._roundProps(o,!0):t.n===e&&(this._add(t.t,e,t.s,t.c),n&&(n._prev=t._prev),t._prev?t._prev._next=n:r._firstPT===t&&(r._firstPT=n),t._next=t._prev=null,r._propLookup[e]=u),t=n;return!1},t._add=function(e,t,n,r){this._addTween(e,t,n,n+r,t,!0),this._overwriteProps.push(t)}}(),_gsScope._gsDefine.plugin({propName:"attr",API:2,version:"0.3.3",init:function(e,t){var n,r,i;if("function"!=typeof e.setAttribute)return!1;this._target=e,this._proxy={},this._start={},this._end={};for(n in t)this._start[n]=this._proxy[n]=r=e.getAttribute(n),i=this._addTween(this._proxy,n,parseFloat(r),t[n],n),this._end[n]=i?i.s+i.c:t[n],this._overwriteProps.push(n);return!0},set:function(e){this._super.setRatio.call(this,e);for(var t,n=this._overwriteProps,r=n.length,i=1===e?this._end:e?this._proxy:this._start;--r>-1;)t=n[r],this._target.setAttribute(t,i[t]+"")}}),_gsScope._gsDefine.plugin({propName:"directionalRotation",version:"0.2.1",API:2,init:function(e,t){"object"!=typeof t&&(t={rotation:t}),this.finals={};var n,r,i,s,o,u,a=t.useRadians===!0?2*Math.PI:360,f=1e-6;for(n in t)"useRadians"!==n&&(u=(t[n]+"").split("_"),r=u[0],i=parseFloat("function"!=typeof e[n]?e[n]:e[n.indexOf("set")||"function"!=typeof e["get"+n.substr(3)]?n:"get"+n.substr(3)]()),s=this.finals[n]="string"==typeof r&&"="===r.charAt(1)?i+parseInt(r.charAt(0)+"1",10)*Number(r.substr(2)):Number(r)||0,o=s-i,u.length&&(r=u.join("_"),-1!==r.indexOf("short")&&(o%=a,o!==o%(a/2)&&(o=0>o?o+a:o-a)),-1!==r.indexOf("_cw")&&0>o?o=(o+9999999999*a)%a-(0|o/a)*a:-1!==r.indexOf("ccw")&&o>0&&(o=(o-9999999999*a)%a-(0|o/a)*a)),(o>f||-f>o)&&(this._addTween(e,n,i,i+o,n),this._overwriteProps.push(n)));return!0},set:function(e){var t;if(1!==e)this._super.setRatio.call(this,e);else for(t=this._firstPT;t;)t.f?t.t[t.p](this.finals[t.p]):t.t[t.p]=this.finals[t.p],t=t._next}})._autoCSS=!0,_gsScope._gsDefine("easing.Back",["easing.Ease"],function(e){var t,n,r,i=_gsScope.GreenSockGlobals||_gsScope,s=i.com.greensock,o=2*Math.PI,u=Math.PI/2,a=s._class,f=function(t,n){var r=a("easing."+t,function(){},!0),i=r.prototype=new e;return i.constructor=r,i.getRatio=n,r},l=e.register||function(){},c=function(e,t,n,r){var i=a("easing."+e,{easeOut:new t,easeIn:new n,easeInOut:new r},!0);return l(i,e),i},h=function(e,t,n){this.t=e,this.v=t,n&&(this.next=n,n.prev=this,this.c=n.v-t,this.gap=n.t-e)},p=function(t,n){var r=a("easing."+t,function(e){this._p1=e||0===e?e:1.70158,this._p2=1.525*this._p1},!0),i=r.prototype=new e;return i.constructor=r,i.getRatio=n,i.config=function(e){return new r(e)},r},d=c("Back",p("BackOut",function(e){return(e-=1)*e*((this._p1+1)*e+this._p1)+1}),p("BackIn",function(e){return e*e*((this._p1+1)*e-this._p1)}),p("BackInOut",function(e){return 1>(e*=2)?.5*e*e*((this._p2+1)*e-this._p2):.5*((e-=2)*e*((this._p2+1)*e+this._p2)+2)})),v=a("easing.SlowMo",function(e,t,n){t=t||0===t?t:.7,null==e?e=.7:e>1&&(e=1),this._p=1!==e?t:0,this._p1=(1-e)/2,this._p2=e,this._p3=this._p1+this._p2,this._calcEnd=n===!0},!0),m=v.prototype=new e;return m.constructor=v,m.getRatio=function(e){var t=e+(.5-e)*this._p;return this._p1>e?this._calcEnd?1-(e=1-e/this._p1)*e:t-(e=1-e/this._p1)*e*e*e*t:e>this._p3?this._calcEnd?1-(e=(e-this._p3)/this._p1)*e:t+(e-t)*(e=(e-this._p3)/this._p1)*e*e*e:this._calcEnd?1:t},v.ease=new v(.7,.7),m.config=v.config=function(e,t,n){return new v(e,t,n)},t=a("easing.SteppedEase",function(e){e=e||1,this._p1=1/e,this._p2=e+1},!0),m=t.prototype=new e,m.constructor=t,m.getRatio=function(e){return 0>e?e=0:e>=1&&(e=.999999999),(this._p2*e>>0)*this._p1},m.config=t.config=function(e){return new t(e)},n=a("easing.RoughEase",function(t){t=t||{};for(var n,r,i,s,o,u,a=t.taper||"none",f=[],l=0,c=0|(t.points||20),p=c,d=t.randomize!==!1,v=t.clamp===!0,m=t.template instanceof e?t.template:null,g="number"==typeof t.strength?.4*t.strength:.4;--p>-1;)n=d?Math.random():1/c*p,r=m?m.getRatio(n):n,"none"===a?i=g:"out"===a?(s=1-n,i=s*s*g):"in"===a?i=n*n*g:.5>n?(s=2*n,i=.5*s*s*g):(s=2*(1-n),i=.5*s*s*g),d?r+=Math.random()*i-.5*i:p%2?r+=.5*i:r-=.5*i,v&&(r>1?r=1:0>r&&(r=0)),f[l++]={x:n,y:r};for(f.sort(function(e,t){return e.x-t.x}),u=new h(1,1,null),p=c;--p>-1;)o=f[p],u=new h(o.x,o.y,u);this._prev=new h(0,0,0!==u.t?u:u.next)},!0),m=n.prototype=new e,m.constructor=n,m.getRatio=function(e){var t=this._prev;if(e>t.t){for(;t.next&&e>=t.t;)t=t.next;t=t.prev}else for(;t.prev&&t.t>=e;)t=t.prev;return this._prev=t,t.v+(e-t.t)/t.gap*t.c},m.config=function(e){return new n(e)},n.ease=new n,c("Bounce",f("BounceOut",function(e){return 1/2.75>e?7.5625*e*e:2/2.75>e?7.5625*(e-=1.5/2.75)*e+.75:2.5/2.75>e?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375}),f("BounceIn",function(e){return 1/2.75>(e=1-e)?1-7.5625*e*e:2/2.75>e?1-(7.5625*(e-=1.5/2.75)*e+.75):2.5/2.75>e?1-(7.5625*(e-=2.25/2.75)*e+.9375):1-(7.5625*(e-=2.625/2.75)*e+.984375)}),f("BounceInOut",function(e){var t=.5>e;return e=t?1-2*e:2*e-1,e=1/2.75>e?7.5625*e*e:2/2.75>e?7.5625*(e-=1.5/2.75)*e+.75:2.5/2.75>e?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375,t?.5*(1-e):.5*e+.5})),c("Circ",f("CircOut",function(e){return Math.sqrt(1-(e-=1)*e)}),f("CircIn",function(e){return-(Math.sqrt(1-e*e)-1)}),f("CircInOut",function(e){return 1>(e*=2)?-0.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1)})),r=function(t,n,r){var i=a("easing."+t,function(e,t){this._p1=e>=1?e:1,this._p2=(t||r)/(1>e?e:1),this._p3=this._p2/o*(Math.asin(1/this._p1)||0),this._p2=o/this._p2},!0),s=i.prototype=new e;return s.constructor=i,s.getRatio=n,s.config=function(e,t){return new i(e,t)},i},c("Elastic",r("ElasticOut",function(e){return this._p1*Math.pow(2,-10*e)*Math.sin((e-this._p3)*this._p2)+1},.3),r("ElasticIn",function(e){return-(this._p1*Math.pow(2,10*(e-=1))*Math.sin((e-this._p3)*this._p2))},.3),r("ElasticInOut",function(e){return 1>(e*=2)?-0.5*this._p1*Math.pow(2,10*(e-=1))*Math.sin((e-this._p3)*this._p2):.5*this._p1*Math.pow(2,-10*(e-=1))*Math.sin((e-this._p3)*this._p2)+1},.45)),c("Expo",f("ExpoOut",function(e){return 1-Math.pow(2,-10*e)}),f("ExpoIn",function(e){return Math.pow(2,10*(e-1))-.001}),f("ExpoInOut",function(e){return 1>(e*=2)?.5*Math.pow(2,10*(e-1)):.5*(2-Math.pow(2,-10*(e-1)))})),c("Sine",f("SineOut",function(e){return Math.sin(e*u)}),f("SineIn",function(e){return-Math.cos(e*u)+1}),f("SineInOut",function(e){return-0.5*(Math.cos(Math.PI*e)-1)})),a("easing.EaseLookup",{find:function(t){return e.map[t]}},!0),l(i.SlowMo,"SlowMo","ease,"),l(n,"RoughEase","ease,"),l(t,"SteppedEase","ease,"),d},!0)}),_gsScope._gsDefine&&_gsScope._gsQueue.pop()(),function(e,t){"use strict";var n=e.GreenSockGlobals=e.GreenSockGlobals||e;if(!n.TweenLite){var r,i,s,o,u,a=function(e){var t,r=e.split("."),i=n;for(t=0;r.length>t;t++)i[r[t]]=i=i[r[t]]||{};return i},f=a("com.greensock"),l=1e-10,c=function(e){var t,n=[],r=e.length;for(t=0;t!==r;n.push(e[t++]));return n},h=function(){},p=function(){var e=Object.prototype.toString,t=e.call([]);return function(n){return null!=n&&(n instanceof Array||"object"==typeof n&&!!n.push&&e.call(n)===t)}}(),d={},v=function(r,i,s,o){this.sc=d[r]?d[r].sc:[],d[r]=this,this.gsClass=null,this.func=s;var u=[];this.check=function(f){for(var l,c,h,p,m=i.length,g=m;--m>-1;)(l=d[i[m]]||new v(i[m],[])).gsClass?(u[m]=l.gsClass,g--):f&&l.sc.push(this);if(0===g&&s)for(c=("com.greensock."+r).split("."),h=c.pop(),p=a(c.join("."))[h]=this.gsClass=s.apply(s,u),o&&(n[h]=p,"function"==typeof define&&define.amd?define((e.GreenSockAMDPath?e.GreenSockAMDPath+"/":"")+r.split(".").pop(),[],function(){return p}):r===t&&"undefined"!=typeof module&&module.exports&&(module.exports=p)),m=0;this.sc.length>m;m++)this.sc[m].check()},this.check(!0)},m=e._gsDefine=function(e,t,n,r){return new v(e,t,n,r)},g=f._class=function(e,t,n){return t=t||function(){},m(e,[],function(){return t},n),t};m.globals=n;var y=[0,0,1,1],b=[],w=g("easing.Ease",function(e,t,n,r){this._func=e,this._type=n||0,this._power=r||0,this._params=t?y.concat(t):y},!0),E=w.map={},S=w.register=function(e,t,n,r){for(var i,s,o,u,a=t.split(","),l=a.length,c=(n||"easeIn,easeOut,easeInOut").split(",");--l>-1;)for(s=a[l],i=r?g("easing."+s,null,!0):f.easing[s]||{},o=c.length;--o>-1;)u=c[o],E[s+"."+u]=E[u+s]=i[u]=e.getRatio?e:e[u]||new e};for(s=w.prototype,s._calcEnd=!1,s.getRatio=function(e){if(this._func)return this._params[0]=e,this._func.apply(null,this._params);var t=this._type,n=this._power,r=1===t?1-e:2===t?e:.5>e?2*e:2*(1-e);return 1===n?r*=r:2===n?r*=r*r:3===n?r*=r*r*r:4===n&&(r*=r*r*r*r),1===t?1-r:2===t?r:.5>e?r/2:1-r/2},r=["Linear","Quad","Cubic","Quart","Quint,Strong"],i=r.length;--i>-1;)s=r[i]+",Power"+i,S(new w(null,null,1,i),s,"easeOut",!0),S(new w(null,null,2,i),s,"easeIn"+(0===i?",easeNone":"")),S(new w(null,null,3,i),s,"easeInOut");E.linear=f.easing.Linear.easeIn,E.swing=f.easing.Quad.easeInOut;var x=g("events.EventDispatcher",function(e){this._listeners={},this._eventTarget=e||this});s=x.prototype,s.addEventListener=function(e,t,n,r,i){i=i||0;var s,a,f=this._listeners[e],l=0;for(null==f&&(this._listeners[e]=f=[]),a=f.length;--a>-1;)s=f[a],s.c===t&&s.s===n?f.splice(a,1):0===l&&i>s.pr&&(l=a+1);f.splice(l,0,{c:t,s:n,up:r,pr:i}),this!==o||u||o.wake()},s.removeEventListener=function(e,t){var n,r=this._listeners[e];if(r)for(n=r.length;--n>-1;)if(r[n].c===t)return r.splice(n,1),void 0},s.dispatchEvent=function(e){var t,n,r,i=this._listeners[e];if(i)for(t=i.length,n=this._eventTarget;--t>-1;)r=i[t],r&&(r.up?r.c.call(r.s||n,{type:e,target:n}):r.c.call(r.s||n))};var T=e.requestAnimationFrame,N=e.cancelAnimationFrame,C=Date.now||function(){return(new Date).getTime()},k=C();for(r=["ms","moz","webkit","o"],i=r.length;--i>-1&&!T;)T=e[r[i]+"RequestAnimationFrame"],N=e[r[i]+"CancelAnimationFrame"]||e[r[i]+"CancelRequestAnimationFrame"];g("Ticker",function(e,t){var n,r,i,s,a,f=this,c=C(),p=t!==!1&&T,d=500,v=33,m="tick",g=function(e){var t,o,u=C()-k;u>d&&(c+=u-v),k+=u,f.time=(k-c)/1e3,t=f.time-a,(!n||t>0||e===!0)&&(f.frame++,a+=t+(t>=s?.004:s-t),o=!0),e!==!0&&(i=r(g)),o&&f.dispatchEvent(m)};x.call(f),f.time=f.frame=0,f.tick=function(){g(!0)},f.lagSmoothing=function(e,t){d=e||1/l,v=Math.min(t,d,0)},f.sleep=function(){null!=i&&(p&&N?N(i):clearTimeout(i),r=h,i=null,f===o&&(u=!1))},f.wake=function(){null!==i?f.sleep():f.frame>10&&(k=C()-d+5),r=0===n?h:p&&T?T:function(e){return setTimeout(e,0|1e3*(a-f.time)+1)},f===o&&(u=!0),g(2)},f.fps=function(e){return arguments.length?(n=e,s=1/(n||60),a=this.time+s,f.wake(),void 0):n},f.useRAF=function(e){return arguments.length?(f.sleep(),p=e,f.fps(n),void 0):p},f.fps(e),setTimeout(function(){p&&5>f.frame&&f.useRAF(!1)},1500)}),s=f.Ticker.prototype=new f.events.EventDispatcher,s.constructor=f.Ticker;var L=g("core.Animation",function(e,t){if(this.vars=t=t||{},this._duration=this._totalDuration=e||0,this._delay=Number(t.delay)||0,this._timeScale=1,this._active=t.immediateRender===!0,this.data=t.data,this._reversed=t.reversed===!0,z){u||o.wake();var n=this.vars.useFrames?U:z;n.add(this,n._time),this.vars.paused&&this.paused(!0)}});o=L.ticker=new f.Ticker,s=L.prototype,s._dirty=s._gc=s._initted=s._paused=!1,s._totalTime=s._time=0,s._rawPrevTime=-1,s._next=s._last=s._onUpdate=s._timeline=s.timeline=null,s._paused=!1;var A=function(){u&&C()-k>2e3&&o.wake(),setTimeout(A,2e3)};A(),s.play=function(e,t){return null!=e&&this.seek(e,t),this.reversed(!1).paused(!1)},s.pause=function(e,t){return null!=e&&this.seek(e,t),this.paused(!0)},s.resume=function(e,t){return null!=e&&this.seek(e,t),this.paused(!1)},s.seek=function(e,t){return this.totalTime(Number(e),t!==!1)},s.restart=function(e,t){return this.reversed(!1).paused(!1).totalTime(e?-this._delay:0,t!==!1,!0)},s.reverse=function(e,t){return null!=e&&this.seek(e||this.totalDuration(),t),this.reversed(!0).paused(!1)},s.render=function(){},s.invalidate=function(){return this._time=this._totalTime=0,this._initted=this._gc=!1,this._rawPrevTime=-1,(this._gc||!this.timeline)&&this._enabled(!0),this},s.isActive=function(){var e,t=this._timeline,n=this._startTime;return!t||!this._gc&&!this._paused&&t.isActive()&&(e=t.rawTime())>=n&&n+this.totalDuration()/this._timeScale>e},s._enabled=function(e,t){return u||o.wake(),this._gc=!e,this._active=this.isActive(),t!==!0&&(e&&!this.timeline?this._timeline.add(this,this._startTime-this._delay):!e&&this.timeline&&this._timeline._remove(this,!0)),!1},s._kill=function(){return this._enabled(!1,!1)},s.kill=function(e,t){return this._kill(e,t),this},s._uncache=function(e){for(var t=e?this:this.timeline;t;)t._dirty=!0,t=t.timeline;return this},s._swapSelfInParams=function(e){for(var t=e.length,n=e.concat();--t>-1;)"{self}"===e[t]&&(n[t]=this);return n},s.eventCallback=function(e,t,n,r){if("on"===(e||"").substr(0,2)){var i=this.vars;if(1===arguments.length)return i[e];null==t?delete i[e]:(i[e]=t,i[e+"Params"]=p(n)&&-1!==n.join("").indexOf("{self}")?this._swapSelfInParams(n):n,i[e+"Scope"]=r),"onUpdate"===e&&(this._onUpdate=t)}return this},s.delay=function(e){return arguments.length?(this._timeline.smoothChildTiming&&this.startTime(this._startTime+e-this._delay),this._delay=e,this):this._delay},s.duration=function(e){return arguments.length?(this._duration=this._totalDuration=e,this._uncache(!0),this._timeline.smoothChildTiming&&this._time>0&&this._time<this._duration&&0!==e&&this.totalTime(this._totalTime*(e/this._duration),!0),this):(this._dirty=!1,this._duration)},s.totalDuration=function(e){return this._dirty=!1,arguments.length?this.duration(e):this._totalDuration},s.time=function(e,t){return arguments.length?(this._dirty&&this.totalDuration(),this.totalTime(e>this._duration?this._duration:e,t)):this._time},s.totalTime=function(e,t,n){if(u||o.wake(),!arguments.length)return this._totalTime;if(this._timeline){if(0>e&&!n&&(e+=this.totalDuration()),this._timeline.smoothChildTiming){this._dirty&&this.totalDuration();var r=this._totalDuration,i=this._timeline;if(e>r&&!n&&(e=r),this._startTime=(this._paused?this._pauseTime:i._time)-(this._reversed?r-e:e)/this._timeScale,i._dirty||this._uncache(!1),i._timeline)for(;i._timeline;)i._timeline._time!==(i._startTime+i._totalTime)/i._timeScale&&i.totalTime(i._totalTime,!0),i=i._timeline}this._gc&&this._enabled(!0,!1),(this._totalTime!==e||0===this._duration)&&(this.render(e,t,!1),P.length&&X())}return this},s.progress=s.totalProgress=function(e,t){return arguments.length?this.totalTime(this.duration()*e,t):this._time/this.duration()},s.startTime=function(e){return arguments.length?(e!==this._startTime&&(this._startTime=e,this.timeline&&this.timeline._sortChildren&&this.timeline.add(this,e-this._delay)),this):this._startTime},s.endTime=function(e){return this._startTime+(0!=e?this.totalDuration():this.duration())/this._timeScale},s.timeScale=function(e){if(!arguments.length)return this._timeScale;if(e=e||l,this._timeline&&this._timeline.smoothChildTiming){var t=this._pauseTime,n=t||0===t?t:this._timeline.totalTime();this._startTime=n-(n-this._startTime)*this._timeScale/e}return this._timeScale=e,this._uncache(!1)},s.reversed=function(e){return arguments.length?(e!=this._reversed&&(this._reversed=e,this.totalTime(this._timeline&&!this._timeline.smoothChildTiming?this.totalDuration()-this._totalTime:this._totalTime,!0)),this):this._reversed},s.paused=function(e){if(!arguments.length)return this._paused;var t,n,r=this._timeline;return e!=this._paused&&r&&(u||e||o.wake(),t=r.rawTime(),n=t-this._pauseTime,!e&&r.smoothChildTiming&&(this._startTime+=n,this._uncache(!1)),this._pauseTime=e?t:null,this._paused=e,this._active=this.isActive(),!e&&0!==n&&this._initted&&this.duration()&&this.render(r.smoothChildTiming?this._totalTime:(t-this._startTime)/this._timeScale,!0,!0)),this._gc&&!e&&this._enabled(!0,!1),this};var O=g("core.SimpleTimeline",function(e){L.call(this,0,e),this.autoRemoveChildren=this.smoothChildTiming=!0});s=O.prototype=new L,s.constructor=O,s.kill()._gc=!1,s._first=s._last=s._recent=null,s._sortChildren=!1,s.add=s.insert=function(e,t){var n,r;if(e._startTime=Number(t||0)+e._delay,e._paused&&this!==e._timeline&&(e._pauseTime=e._startTime+(this.rawTime()-e._startTime)/e._timeScale),e.timeline&&e.timeline._remove(e,!0),e.timeline=e._timeline=this,e._gc&&e._enabled(!0,!0),n=this._last,this._sortChildren)for(r=e._startTime;n&&n._startTime>r;)n=n._prev;return n?(e._next=n._next,n._next=e):(e._next=this._first,this._first=e),e._next?e._next._prev=e:this._last=e,e._prev=n,this._recent=e,this._timeline&&this._uncache(!0),this},s._remove=function(e,t){return e.timeline===this&&(t||e._enabled(!1,!0),e._prev?e._prev._next=e._next:this._first===e&&(this._first=e._next),e._next?e._next._prev=e._prev:this._last===e&&(this._last=e._prev),e._next=e._prev=e.timeline=null,e===this._recent&&(this._recent=this._last),this._timeline&&this._uncache(!0)),this},s.render=function(e,t,n){var r,i=this._first;for(this._totalTime=this._time=this._rawPrevTime=e;i;)r=i._next,(i._active||e>=i._startTime&&!i._paused)&&(i._reversed?i.render((i._dirty?i.totalDuration():i._totalDuration)-(e-i._startTime)*i._timeScale,t,n):i.render((e-i._startTime)*i._timeScale,t,n)),i=r},s.rawTime=function(){return u||o.wake(),this._totalTime};var M=g("TweenLite",function(t,n,r){if(L.call(this,n,r),this.render=M.prototype.render,null==t)throw"Cannot tween a null target.";this.target=t="string"!=typeof t?t:M.selector(t)||t;var i,s,o,u=t.jquery||t.length&&t!==e&&t[0]&&(t[0]===e||t[0].nodeType&&t[0].style&&!t.nodeType),a=this.vars.overwrite;if(this._overwrite=a=null==a?R[M.defaultOverwrite]:"number"==typeof a?a>>0:R[a],(u||t instanceof Array||t.push&&p(t))&&"number"!=typeof t[0])for(this._targets=o=c(t),this._propLookup=[],this._siblings=[],i=0;o.length>i;i++)s=o[i],s?"string"!=typeof s?s.length&&s!==e&&s[0]&&(s[0]===e||s[0].nodeType&&s[0].style&&!s.nodeType)?(o.splice(i--,1),this._targets=o=o.concat(c(s))):(this._siblings[i]=V(s,this,!1),1===a&&this._siblings[i].length>1&&J(s,this,null,1,this._siblings[i])):(s=o[i--]=M.selector(s),"string"==typeof s&&o.splice(i+1,1)):o.splice(i--,1);else this._propLookup={},this._siblings=V(t,this,!1),1===a&&this._siblings.length>1&&J(t,this,null,1,this._siblings);(this.vars.immediateRender||0===n&&0===this._delay&&this.vars.immediateRender!==!1)&&(this._time=-l,this.render(-this._delay))},!0),_=function(t){return t&&t.length&&t!==e&&t[0]&&(t[0]===e||t[0].nodeType&&t[0].style&&!t.nodeType)},D=function(e,t){var n,r={};for(n in e)q[n]||n in t&&"transform"!==n&&"x"!==n&&"y"!==n&&"width"!==n&&"height"!==n&&"className"!==n&&"border"!==n||!(!j[n]||j[n]&&j[n]._autoCSS)||(r[n]=e[n],delete e[n]);e.css=r};s=M.prototype=new L,s.constructor=M,s.kill()._gc=!1,s.ratio=0,s._firstPT=s._targets=s._overwrittenProps=s._startAt=null,s._notifyPluginsOfEnabled=s._lazy=!1,M.version="1.16.1",M.defaultEase=s._ease=new w(null,null,1,1),M.defaultOverwrite="auto",M.ticker=o,M.autoSleep=120,M.lagSmoothing=function(e,t){o.lagSmoothing(e,t)},M.selector=e.$||e.jQuery||function(t){var n=e.$||e.jQuery;return n?(M.selector=n,n(t)):"undefined"==typeof document?t:document.querySelectorAll?document.querySelectorAll(t):document.getElementById("#"===t.charAt(0)?t.substr(1):t)};var P=[],H={},B=M._internals={isArray:p,isSelector:_,lazyTweens:P},j=M._plugins={},F=B.tweenLookup={},I=0,q=B.reservedProps={ease:1,delay:1,overwrite:1,onComplete:1,onCompleteParams:1,onCompleteScope:1,useFrames:1,runBackwards:1,startAt:1,onUpdate:1,onUpdateParams:1,onUpdateScope:1,onStart:1,onStartParams:1,onStartScope:1,onReverseComplete:1,onReverseCompleteParams:1,onReverseCompleteScope:1,onRepeat:1,onRepeatParams:1,onRepeatScope:1,easeParams:1,yoyo:1,immediateRender:1,repeat:1,repeatDelay:1,data:1,paused:1,reversed:1,autoCSS:1,lazy:1,onOverwrite:1},R={none:0,all:1,auto:2,concurrent:3,allOnStart:4,preexisting:5,"true":1,"false":0},U=L._rootFramesTimeline=new O,z=L._rootTimeline=new O,W=30,X=B.lazyRender=function(){var e,t=P.length;for(H={};--t>-1;)e=P[t],e&&e._lazy!==!1&&(e.render(e._lazy[0],e._lazy[1],!0),e._lazy=!1);P.length=0};z._startTime=o.time,U._startTime=o.frame,z._active=U._active=!0,setTimeout(X,1),L._updateRoot=M.render=function(){var e,t,n;if(P.length&&X(),z.render((o.time-z._startTime)*z._timeScale,!1,!1),U.render((o.frame-U._startTime)*U._timeScale,!1,!1),P.length&&X(),o.frame>=W){W=o.frame+(parseInt(M.autoSleep,10)||120);for(n in F){for(t=F[n].tweens,e=t.length;--e>-1;)t[e]._gc&&t.splice(e,1);0===t.length&&delete F[n]}if(n=z._first,(!n||n._paused)&&M.autoSleep&&!U._first&&1===o._listeners.tick.length){for(;n&&n._paused;)n=n._next;n||o.sleep()}}},o.addEventListener("tick",L._updateRoot);var V=function(e,t,n){var r,i,s=e._gsTweenID;if(F[s||(e._gsTweenID=s="t"+I++)]||(F[s]={target:e,tweens:[]}),t&&(r=F[s].tweens,r[i=r.length]=t,n))for(;--i>-1;)r[i]===t&&r.splice(i,1);return F[s].tweens},$=function(e,t,n,r){var i,s,o=e.vars.onOverwrite;return o&&(i=o(e,t,n,r)),o=M.onOverwrite,o&&(s=o(e,t,n,r)),i!==!1&&s!==!1},J=function(e,t,n,r,i){var s,o,u,a;if(1===r||r>=4){for(a=i.length,s=0;a>s;s++)if((u=i[s])!==t)u._gc||$(u,t)&&u._enabled(!1,!1)&&(o=!0);else if(5===r)break;return o}var f,c=t._startTime+l,h=[],p=0,d=0===t._duration;for(s=i.length;--s>-1;)(u=i[s])===t||u._gc||u._paused||(u._timeline!==t._timeline?(f=f||K(t,0,d),0===K(u,f,d)&&(h[p++]=u)):c>=u._startTime&&u._startTime+u.totalDuration()/u._timeScale>c&&((d||!u._initted)&&2e-10>=c-u._startTime||(h[p++]=u)));for(s=p;--s>-1;)if(u=h[s],2===r&&u._kill(n,e,t)&&(o=!0),2!==r||!u._firstPT&&u._initted){if(2!==r&&!$(u,t))continue;u._enabled(!1,!1)&&(o=!0)}return o},K=function(e,t,n){for(var r=e._timeline,i=r._timeScale,s=e._startTime;r._timeline;){if(s+=r._startTime,i*=r._timeScale,r._paused)return-100;r=r._timeline}return s/=i,s>t?s-t:n&&s===t||!e._initted&&2*l>s-t?l:(s+=e.totalDuration()/e._timeScale/i)>t+l?0:s-t-l};s._init=function(){var e,t,n,r,i,s=this.vars,o=this._overwrittenProps,u=this._duration,a=!!s.immediateRender,f=s.ease;if(s.startAt){this._startAt&&(this._startAt.render(-1,!0),this._startAt.kill()),i={};for(r in s.startAt)i[r]=s.startAt[r];if(i.overwrite=!1,i.immediateRender=!0,i.lazy=a&&s.lazy!==!1,i.startAt=i.delay=null,this._startAt=M.to(this.target,0,i),a)if(this._time>0)this._startAt=null;else if(0!==u)return}else if(s.runBackwards&&0!==u)if(this._startAt)this._startAt.render(-1,!0),this._startAt.kill(),this._startAt=null;else{0!==this._time&&(a=!1),n={};for(r in s)q[r]&&"autoCSS"!==r||(n[r]=s[r]);if(n.overwrite=0,n.data="isFromStart",n.lazy=a&&s.lazy!==!1,n.immediateRender=a,this._startAt=M.to(this.target,0,n),a){if(0===this._time)return}else this._startAt._init(),this._startAt._enabled(!1),this.vars.immediateRender&&(this._startAt=null)}if(this._ease=f=f?f instanceof w?f:"function"==typeof f?new w(f,s.easeParams):E[f]||M.defaultEase:M.defaultEase,s.easeParams instanceof Array&&f.config&&(this._ease=f.config.apply(f,s.easeParams)),this._easeType=this._ease._type,this._easePower=this._ease._power,this._firstPT=null,this._targets)for(e=this._targets.length;--e>-1;)this._initProps(this._targets[e],this._propLookup[e]={},this._siblings[e],o?o[e]:null)&&(t=!0);else t=this._initProps(this.target,this._propLookup,this._siblings,o);if(t&&M._onPluginEvent("_onInitAllProps",this),o&&(this._firstPT||"function"!=typeof this.target&&this._enabled(!1,!1)),s.runBackwards)for(n=this._firstPT;n;)n.s+=n.c,n.c=-n.c,n=n._next;this._onUpdate=s.onUpdate,this._initted=!0},s._initProps=function(t,n,r,i){var s,o,u,a,f,l;if(null==t)return!1;H[t._gsTweenID]&&X(),this.vars.css||t.style&&t!==e&&t.nodeType&&j.css&&this.vars.autoCSS!==!1&&D(this.vars,t);for(s in this.vars){if(l=this.vars[s],q[s])l&&(l instanceof Array||l.push&&p(l))&&-1!==l.join("").indexOf("{self}")&&(this.vars[s]=l=this._swapSelfInParams(l,this));else if(j[s]&&(a=new j[s])._onInitTween(t,this.vars[s],this)){for(this._firstPT=f={_next:this._firstPT,t:a,p:"setRatio",s:0,c:1,f:!0,n:s,pg:!0,pr:a._priority},o=a._overwriteProps.length;--o>-1;)n[a._overwriteProps[o]]=this._firstPT;(a._priority||a._onInitAllProps)&&(u=!0),(a._onDisable||a._onEnable)&&(this._notifyPluginsOfEnabled=!0)}else this._firstPT=n[s]=f={_next:this._firstPT,t:t,p:s,f:"function"==typeof t[s],n:s,pg:!1,pr:0},f.s=f.f?t[s.indexOf("set")||"function"!=typeof t["get"+s.substr(3)]?s:"get"+s.substr(3)]():parseFloat(t[s]),f.c="string"==typeof l&&"="===l.charAt(1)?parseInt(l.charAt(0)+"1",10)*Number(l.substr(2)):Number(l)-f.s||0;f&&f._next&&(f._next._prev=f)}return i&&this._kill(i,t)?this._initProps(t,n,r,i):this._overwrite>1&&this._firstPT&&r.length>1&&J(t,this,n,this._overwrite,r)?(this._kill(n,t),this._initProps(t,n,r,i)):(this._firstPT&&(this.vars.lazy!==!1&&this._duration||this.vars.lazy&&!this._duration)&&(H[t._gsTweenID]=!0),u)},s.render=function(e,t,n){var r,i,s,o,u=this._time,a=this._duration,f=this._rawPrevTime;if(e>=a)this._totalTime=this._time=a,this.ratio=this._ease._calcEnd?this._ease.getRatio(1):1,this._reversed||(r=!0,i="onComplete",n=n||this._timeline.autoRemoveChildren),0===a&&(this._initted||!this.vars.lazy||n)&&(this._startTime===this._timeline._duration&&(e=0),(0===e||0>f||f===l&&"isPause"!==this.data)&&f!==e&&(n=!0,f>l&&(i="onReverseComplete")),this._rawPrevTime=o=!t||e||f===e?e:l);else if(1e-7>e)this._totalTime=this._time=0,this.ratio=this._ease._calcEnd?this._ease.getRatio(0):0,(0!==u||0===a&&f>0)&&(i="onReverseComplete",r=this._reversed),0>e&&(this._active=!1,0===a&&(this._initted||!this.vars.lazy||n)&&(f>=0&&(f!==l||"isPause"!==this.data)&&(n=!0),this._rawPrevTime=o=!t||e||f===e?e:l)),this._initted||(n=!0);else if(this._totalTime=this._time=e,this._easeType){var c=e/a,h=this._easeType,p=this._easePower;(1===h||3===h&&c>=.5)&&(c=1-c),3===h&&(c*=2),1===p?c*=c:2===p?c*=c*c:3===p?c*=c*c*c:4===p&&(c*=c*c*c*c),this.ratio=1===h?1-c:2===h?c:.5>e/a?c/2:1-c/2}else this.ratio=this._ease.getRatio(e/a);if(this._time!==u||n){if(!this._initted){if(this._init(),!this._initted||this._gc)return;if(!n&&this._firstPT&&(this.vars.lazy!==!1&&this._duration||this.vars.lazy&&!this._duration))return this._time=this._totalTime=u,this._rawPrevTime=f,P.push(this),this._lazy=[e,t],void 0;this._time&&!r?this.ratio=this._ease.getRatio(this._time/a):r&&this._ease._calcEnd&&(this.ratio=this._ease.getRatio(0===this._time?0:1))}for(this._lazy!==!1&&(this._lazy=!1),this._active||!this._paused&&this._time!==u&&e>=0&&(this._active=!0),0===u&&(this._startAt&&(e>=0?this._startAt.render(e,t,n):i||(i="_dummyGS")),this.vars.onStart&&(0!==this._time||0===a)&&(t||this.vars.onStart.apply(this.vars.onStartScope||this,this.vars.onStartParams||b))),s=this._firstPT;s;)s.f?s.t[s.p](s.c*this.ratio+s.s):s.t[s.p]=s.c*this.ratio+s.s,s=s._next;this._onUpdate&&(0>e&&this._startAt&&e!==-0.0001&&this._startAt.render(e,t,n),t||(this._time!==u||r)&&this._onUpdate.apply(this.vars.onUpdateScope||this,this.vars.onUpdateParams||b)),i&&(!this._gc||n)&&(0>e&&this._startAt&&!this._onUpdate&&e!==-0.0001&&this._startAt.render(e,t,n),r&&(this._timeline.autoRemoveChildren&&this._enabled(!1,!1),this._active=!1),!t&&this.vars[i]&&this.vars[i].apply(this.vars[i+"Scope"]||this,this.vars[i+"Params"]||b),0===a&&this._rawPrevTime===l&&o!==l&&(this._rawPrevTime=0))}},s._kill=function(e,t,n){if("all"===e&&(e=null),null!=e||null!=t&&t!==this.target){t="string"!=typeof t?t||this._targets||this.target:M.selector(t)||t;var r,i,s,o,u,a,f,l,c;if((p(t)||_(t))&&"number"!=typeof t[0])for(r=t.length;--r>-1;)this._kill(e,t[r])&&(a=!0);else{if(this._targets){for(r=this._targets.length;--r>-1;)if(t===this._targets[r]){u=this._propLookup[r]||{},this._overwrittenProps=this._overwrittenProps||[],i=this._overwrittenProps[r]=e?this._overwrittenProps[r]||{}:"all";break}}else{if(t!==this.target)return!1;u=this._propLookup,i=this._overwrittenProps=e?this._overwrittenProps||{}:"all"}if(u){if(f=e||u,l=e!==i&&"all"!==i&&e!==u&&("object"!=typeof e||!e._tempKill),n&&(M.onOverwrite||this.vars.onOverwrite)){for(s in f)u[s]&&(c||(c=[]),c.push(s));if(!$(this,n,t,c))return!1}for(s in f)(o=u[s])&&(o.pg&&o.t._kill(f)&&(a=!0),o.pg&&0!==o.t._overwriteProps.length||(o._prev?o._prev._next=o._next:o===this._firstPT&&(this._firstPT=o._next),o._next&&(o._next._prev=o._prev),o._next=o._prev=null),delete u[s]),l&&(i[s]=1);!this._firstPT&&this._initted&&this._enabled(!1,!1)}}return a}return this._lazy=!1,this._enabled(!1,!1)},s.invalidate=function(){return this._notifyPluginsOfEnabled&&M._onPluginEvent("_onDisable",this),this._firstPT=this._overwrittenProps=this._startAt=this._onUpdate=null,this._notifyPluginsOfEnabled=this._active=this._lazy=!1,this._propLookup=this._targets?{}:[],L.prototype.invalidate.call(this),this.vars.immediateRender&&(this._time=-l,this.render(-this._delay)),this},s._enabled=function(e,t){if(u||o.wake(),e&&this._gc){var n,r=this._targets;if(r)for(n=r.length;--n>-1;)this._siblings[n]=V(r[n],this,!0);else this._siblings=V(this.target,this,!0)}return L.prototype._enabled.call(this,e,t),this._notifyPluginsOfEnabled&&this._firstPT?M._onPluginEvent(e?"_onEnable":"_onDisable",this):!1},M.to=function(e,t,n){return new M(e,t,n)},M.from=function(e,t,n){return n.runBackwards=!0,n.immediateRender=0!=n.immediateRender,new M(e,t,n)},M.fromTo=function(e,t,n,r){return r.startAt=n,r.immediateRender=0!=r.immediateRender&&0!=n.immediateRender,new M(e,t,r)},M.delayedCall=function(e,t,n,r,i){return new M(t,0,{delay:e,onComplete:t,onCompleteParams:n,onCompleteScope:r,onReverseComplete:t,onReverseCompleteParams:n,onReverseCompleteScope:r,immediateRender:!1,lazy:!1,useFrames:i,overwrite:0})},M.set=function(e,t){return new M(e,0,t)},M.getTweensOf=function(e,t){if(null==e)return[];e="string"!=typeof e?e:M.selector(e)||e;var n,r,i,s;if((p(e)||_(e))&&"number"!=typeof e[0]){for(n=e.length,r=[];--n>-1;)r=r.concat(M.getTweensOf(e[n],t));for(n=r.length;--n>-1;)for(s=r[n],i=n;--i>-1;)s===r[i]&&r.splice(n,1)}else for(r=V(e).concat(),n=r.length;--n>-1;)(r[n]._gc||t&&!r[n].isActive())&&r.splice(n,1);return r},M.killTweensOf=M.killDelayedCallsTo=function(e,t,n){"object"==typeof t&&(n=t,t=!1);for(var r=M.getTweensOf(e,t),i=r.length;--i>-1;)r[i]._kill(n,e)};var Q=g("plugins.TweenPlugin",function(e,t){this._overwriteProps=(e||"").split(","),this._propName=this._overwriteProps[0],this._priority=t||0,this._super=Q.prototype},!0);if(s=Q.prototype,Q.version="1.10.1",Q.API=2,s._firstPT=null,s._addTween=function(e,t,n,r,i,s){var o,u;return null!=r&&(o="number"==typeof r||"="!==r.charAt(1)?Number(r)-n:parseInt(r.charAt(0)+"1",10)*Number(r.substr(2)))?(this._firstPT=u={_next:this._firstPT,t:e,p:t,s:n,c:o,f:"function"==typeof e[t],n:i||t,r:s},u._next&&(u._next._prev=u),u):void 0},s.setRatio=function(e){for(var t,n=this._firstPT,r=1e-6;n;)t=n.c*e+n.s,n.r?t=Math.round(t):r>t&&t>-r&&(t=0),n.f?n.t[n.p](t):n.t[n.p]=t,n=n._next},s._kill=function(e){var t,n=this._overwriteProps,r=this._firstPT;if(null!=e[this._propName])this._overwriteProps=[];else for(t=n.length;--t>-1;)null!=e[n[t]]&&n.splice(t,1);for(;r;)null!=e[r.n]&&(r._next&&(r._next._prev=r._prev),r._prev?(r._prev._next=r._next,r._prev=null):this._firstPT===r&&(this._firstPT=r._next)),r=r._next;return!1},s._roundProps=function(e,t){for(var n=this._firstPT;n;)(e[this._propName]||null!=n.n&&e[n.n.split(this._propName+"_").join("")])&&(n.r=t),n=n._next},M._onPluginEvent=function(e,t){var n,r,i,s,o,u=t._firstPT;if("_onInitAllProps"===e){for(;u;){for(o=u._next,r=i;r&&r.pr>u.pr;)r=r._next;(u._prev=r?r._prev:s)?u._prev._next=u:i=u,(u._next=r)?r._prev=u:s=u,u=o}u=t._firstPT=i}for(;u;)u.pg&&"function"==typeof u.t[e]&&u.t[e]()&&(n=!0),u=u._next;return n},Q.activate=function(e){for(var t=e.length;--t>-1;)e[t].API===Q.API&&(j[(new e[t])._propName]=e[t]);return!0},m.plugin=function(e){if(!(e&&e.propName&&e.init&&e.API))throw"illegal plugin definition.";var t,n=e.propName,r=e.priority||0,i=e.overwriteProps,s={init:"_onInitTween",set:"setRatio",kill:"_kill",round:"_roundProps",initAll:"_onInitAllProps"},o=g("plugins."+n.charAt(0).toUpperCase()+n.substr(1)+"Plugin",function(){Q.call(this,n,r),this._overwriteProps=i||[]},e.global===!0),u=o.prototype=new Q(n);u.constructor=o,o.API=e.API;for(t in s)"function"==typeof e[t]&&(u[s[t]]=e[t]);return o.version=e.version,Q.activate([o]),o},r=e._gsQueue){for(i=0;r.length>i;i++)r[i]();for(s in d)d[s].func||e.console.log("GSAP encountered missing dependency: com.greensock."+s)}u=!1}}("undefined"!=typeof module&&module.exports&&"undefined"!=typeof global?global:this||window,"TweenMax"),define("tweenmax",function(){}),window.Modernizr=function(e,t,n){function r(e){d.cssText=e}function i(e,t){return r(prefixes.join(e+";")+(t||""))}function s(e,t){return typeof e===t}function o(e,t){return!!~(""+e).indexOf(t)}function u(e,t,r){for(var i in e){var o=t[e[i]];if(o!==n)return r===!1?e[i]:s(o,"function")?o.bind(r||t):o}return!1}var a="2.8.3",f={},l=!0,c=t.documentElement,h="modernizr",p=t.createElement(h),d=p.style,v,m={}.toString,g={},y={},b={},w=[],E=w.slice,S,x={}.hasOwnProperty,T;!s(x,"undefined")&&!s(x.call,"undefined")?T=function(e,t){return x.call(e,t)}:T=function(e,t){return t in e&&s(e.constructor.prototype[t],"undefined")},Function.prototype.bind||(Function.prototype.bind=function(e){var t=this;if(typeof t!="function")throw new TypeError;var n=E.call(arguments,1),r=function(){if(this instanceof r){var i=function(){};i.prototype=t.prototype;var s=new i,o=t.apply(s,n.concat(E.call(arguments)));return Object(o)===o?o:s}return t.apply(e,n.concat(E.call(arguments)))};return r}),g.history=function(){return!!e.history&&!!history.pushState};for(var N in g)T(g,N)&&(S=N.toLowerCase(),f[S]=g[N](),w.push((f[S]?"":"no-")+S));return f.addTest=function(e,t){if(typeof e=="object")for(var r in e)T(e,r)&&f.addTest(r,e[r]);else{e=e.toLowerCase();if(f[e]!==n)return f;t=typeof t=="function"?t():t,typeof l!="undefined"&&l&&(c.className+=" "+(t?"":"no-")+e),f[e]=t}return f},r(""),p=v=null,function(e,t){function n(e,t){var n=e.createElement("p"),r=e.getElementsByTagName("head")[0]||e.documentElement;return n.innerHTML="x<style>"+t+"</style>",r.insertBefore(n.lastChild,r.firstChild)}function r(){var e=y.elements;return typeof e=="string"?e.split(" "):e}function i(e){var t=m[e[d]];return t||(t={},v++,e[d]=v,m[v]=t),t}function s(e,n,r){n||(n=t);if(g)return n.createElement(e);r||(r=i(n));var s;return r.cache[e]?s=r.cache[e].cloneNode():h.test(e)?s=(r.cache[e]=r.createElem(e)).cloneNode():s=r.createElem(e),s.canHaveChildren&&!c.test(e)&&!s.tagUrn?r.frag.appendChild(s):s}function o(e,n){e||(e=t);if(g)return e.createDocumentFragment();n=n||i(e);var s=n.frag.cloneNode(),o=0,u=r(),a=u.length;for(;o<a;o++)s.createElement(u[o]);return s}function u(e,t){t.cache||(t.cache={},t.createElem=e.createElement,t.createFrag=e.createDocumentFragment,t.frag=t.createFrag()),e.createElement=function(n){return y.shivMethods?s(n,e,t):t.createElem(n)},e.createDocumentFragment=Function("h,f","return function(){var n=f.cloneNode(),c=n.createElement;h.shivMethods&&("+r().join().replace(/[\w\-]+/g,function(e){return t.createElem(e),t.frag.createElement(e),'c("'+e+'")'})+");return n}")(y,t.frag)}function a(e){e||(e=t);var r=i(e);return y.shivCSS&&!p&&!r.hasCSS&&(r.hasCSS=!!n(e,"article,aside,dialog,figcaption,figure,footer,header,hgroup,main,nav,section{display:block}mark{background:#FF0;color:#000}template{display:none}")),g||u(e,r),e}var f="3.7.0",l=e.html5||{},c=/^<|^(?:button|map|select|textarea|object|iframe|option|optgroup)$/i,h=/^(?:a|b|code|div|fieldset|h1|h2|h3|h4|h5|h6|i|label|li|ol|p|q|span|strong|style|table|tbody|td|th|tr|ul)$/i,p,d="_html5shiv",v=0,m={},g;(function(){try{var e=t.createElement("a");e.innerHTML="<xyz></xyz>",p="hidden"in e,g=e.childNodes.length==1||function(){t.createElement("a");var e=t.createDocumentFragment();return typeof e.cloneNode=="undefined"||typeof e.createDocumentFragment=="undefined"||typeof e.createElement=="undefined"}()}catch(n){p=!0,g=!0}})();var y={elements:l.elements||"abbr article aside audio bdi canvas data datalist details dialog figcaption figure footer header hgroup main mark meter nav output progress section summary template time video",version:f,shivCSS:l.shivCSS!==!1,supportsUnknownElements:g,shivMethods:l.shivMethods!==!1,type:"default",shivDocument:a,createElement:s,createDocumentFragment:o};e.html5=y,a(t)}(this,t),f._version=a,c.className=c.className.replace(/(^|\s)no-js(\s|$)/,"$1$2")+(l?" js "+w.join(" "):""),f}(this,this.document),function(e,t,n){function r(e){return"[object Function]"==d.call(e)}function i(e){return"string"==typeof e}function s(){}function o(e){return!e||"loaded"==e||"complete"==e||"uninitialized"==e}function u(){var e=v.shift();m=1,e?e.t?h(function(){("c"==e.t?k.injectCss:k.injectJs)(e.s,0,e.a,e.x,e.e,1)},0):(e(),u()):m=0}function a(e,n,r,i,s,a,f){function l(t){if(!d&&o(c.readyState)&&(w.r=d=1,!m&&u(),c.onload=c.onreadystatechange=null,t)){"img"!=e&&h(function(){b.removeChild(c)},50);for(var r in T[n])T[n].hasOwnProperty(r)&&T[n][r].onload()}}var f=f||k.errorTimeout,c=t.createElement(e),d=0,g=0,w={t:r,s:n,e:s,a:a,x:f};1===T[n]&&(g=1,T[n]=[]),"object"==e?c.data=n:(c.src=n,c.type=e),c.width=c.height="0",c.onerror=c.onload=c.onreadystatechange=function(){l.call(this,g)},v.splice(i,0,w),"img"!=e&&(g||2===T[n]?(b.insertBefore(c,y?null:p),h(l,f)):T[n].push(c))}function f(e,t,n,r,s){return m=0,t=t||"j",i(e)?a("c"==t?E:w,e,t,this.i++,n,r,s):(v.splice(this.i++,0,e),1==v.length&&u()),this}function l(){var e=k;return e.loader={load:f,i:0},e}var c=t.documentElement,h=e.setTimeout,p=t.getElementsByTagName("script")[0],d={}.toString,v=[],m=0,g="MozAppearance"in c.style,y=g&&!!t.createRange().compareNode,b=y?c:p.parentNode,c=e.opera&&"[object Opera]"==d.call(e.opera),c=!!t.attachEvent&&!c,w=g?"object":c?"script":"img",E=c?"script":w,S=Array.isArray||function(e){return"[object Array]"==d.call(e)},x=[],T={},N={timeout:function(e,t){return t.length&&(e.timeout=t[0]),e}},C,k;k=function(e){function t(e){var e=e.split("!"),t=x.length,n=e.pop(),r=e.length,n={url:n,origUrl:n,prefixes:e},i,s,o;for(s=0;s<r;s++)o=e[s].split("="),(i=N[o.shift()])&&(n=i(n,o));for(s=0;s<t;s++)n=x[s](n);return n}function o(e,i,s,o,u){var a=t(e),f=a.autoCallback;a.url.split(".").pop().split("?").shift(),a.bypass||(i&&(i=r(i)?i:i[e]||i[o]||i[e.split("/").pop().split("?")[0]]),a.instead?a.instead(e,i,s,o,u):(T[a.url]?a.noexec=!0:T[a.url]=1,s.load(a.url,a.forceCSS||!a.forceJS&&"css"==a.url.split(".").pop().split("?").shift()?"c":n,a.noexec,a.attrs,a.timeout),(r(i)||r(f))&&s.load(function(){l(),i&&i(a.origUrl,u,o),f&&f(a.origUrl,u,o),T[a.url]=2})))}function u(e,t){function n(e,n){if(e){if(i(e))n||(f=function(){var e=[].slice.call(arguments);l.apply(this,e),c()}),o(e,f,t,0,u);else if(Object(e)===e)for(p in h=function(){var t=0,n;for(n in e)e.hasOwnProperty(n)&&t++;return t}(),e)e.hasOwnProperty(p)&&(!n&&!--h&&(r(f)?f=function(){var e=[].slice.call(arguments);l.apply(this,e),c()}:f[p]=function(e){return function(){var t=[].slice.call(arguments);e&&e.apply(this,t),c()}}(l[p])),o(e[p],f,t,p,u))}else!n&&c()}var u=!!e.test,a=e.load||e.both,f=e.callback||s,l=f,c=e.complete||s,h,p;n(u?e.yep:e.nope,!!a),a&&n(a)}var a,f,c=this.yepnope.loader;if(i(e))o(e,0,c,0);else if(S(e))for(a=0;a<e.length;a++)f=e[a],i(f)?o(f,0,c,0):S(f)?k(f):Object(f)===f&&u(f,c);else Object(e)===e&&u(e,c)},k.addPrefix=function(e,t){N[e]=t},k.addFilter=function(e){x.push(e)},k.errorTimeout=1e4,null==t.readyState&&t.addEventListener&&(t.readyState="loading",t.addEventListener("DOMContentLoaded",C=function(){t.removeEventListener("DOMContentLoaded",C,0),t.readyState="complete"},0)),e.yepnope=l(),e.yepnope.executeStack=u,e.yepnope.injectJs=function(e,n,r,i,a,f){var l=t.createElement("script"),c,d,i=i||k.errorTimeout;l.src=e;for(d in r)l.setAttribute(d,r[d]);n=f?u:n||s,l.onreadystatechange=l.onload=function(){!c&&o(l.readyState)&&(c=1,n(),l.onload=l.onreadystatechange=null)},h(function(){c||(c=1,n(1))},i),a?l.onload():p.parentNode.insertBefore(l,p)},e.yepnope.injectCss=function(e,n,r,i,o,a){var i=t.createElement("link"),f,n=a?u:n||s;i.href=e,i.rel="stylesheet",i.type="text/css";for(f in r)i.setAttribute(f,r[f]);o||(p.parentNode.insertBefore(i,p),h(n,0))}}(this,document),Modernizr.load=function(){yepnope.apply(window,[].slice.call(arguments,0))},define("modernizr",function(){}),function(e){var t="0.4.2",n="hasOwnProperty",r=/[\.\/]/,i=/\s*,\s*/,s="*",o=function(){},u=function(e,t){return e-t},a,f,l={n:{}},c=function(){for(var e=0,t=this.length;e<t;e++)if(typeof this[e]!="undefined")return this[e]},h=function(){var e=this.length;while(--e)if(typeof this[e]!="undefined")return this[e]},p=function(e,t){e=String(e);var n=l,r=f,i=Array.prototype.slice.call(arguments,2),s=p.listeners(e),o=0,d=!1,v,m=[],g={},y=[],b=a,w=[];y.firstDefined=c,y.lastDefined=h,a=e,f=0;for(var E=0,S=s.length;E<S;E++)"zIndex"in s[E]&&(m.push(s[E].zIndex),s[E].zIndex<0&&(g[s[E].zIndex]=s[E]));m.sort(u);while(m[o]<0){v=g[m[o++]],y.push(v.apply(t,i));if(f)return f=r,y}for(E=0;E<S;E++){v=s[E];if("zIndex"in v)if(v.zIndex==m[o]){y.push(v.apply(t,i));if(f)break;do{o++,v=g[m[o]],v&&y.push(v.apply(t,i));if(f)break}while(v)}else g[v.zIndex]=v;else{y.push(v.apply(t,i));if(f)break}}return f=r,a=b,y};p._events=l,p.listeners=function(e){var t=e.split(r),n=l,i,o,u,a,f,c,h,p,d=[n],v=[];for(a=0,f=t.length;a<f;a++){p=[];for(c=0,h=d.length;c<h;c++){n=d[c].n,o=[n[t[a]],n[s]],u=2;while(u--)i=o[u],i&&(p.push(i),v=v.concat(i.f||[]))}d=p}return v},p.on=function(e,t){e=String(e);if(typeof t!="function")return function(){};var n=e.split(i);for(var s=0,o=n.length;s<o;s++)(function(e){var n=e.split(r),i=l,s;for(var o=0,u=n.length;o<u;o++)i=i.n,i=i.hasOwnProperty(n[o])&&i[n[o]]||(i[n[o]]={n:{}});i.f=i.f||[];for(o=0,u=i.f.length;o<u;o++)if(i.f[o]==t){s=!0;break}!s&&i.f.push(t)})(n[s]);return function(e){+e==+e&&(t.zIndex=+e)}},p.f=function(e){var t=[].slice.call(arguments,1);return function(){p.apply(null,[e,null].concat(t).concat([].slice.call(arguments,0)))}},p.stop=function(){f=1},p.nt=function(e){return e?(new RegExp("(?:\\.|\\/|^)"+e+"(?:\\.|\\/|$)")).test(a):a},p.nts=function(){return a.split(r)},p.off=p.unbind=function(e,t){if(!e){p._events=l={n:{}};return}var o=e.split(i);if(o.length>1){for(var u=0,a=o.length;u<a;u++)p.off(o[u],t);return}o=e.split(r);var f,c,h,u,a,d,v,m=[l];for(u=0,a=o.length;u<a;u++)for(d=0;d<m.length;d+=h.length-2){h=[d,1],f=m[d].n;if(o[u]!=s)f[o[u]]&&h.push(f[o[u]]);else for(c in f)f[n](c)&&h.push(f[c]);m.splice.apply(m,h)}for(u=0,a=m.length;u<a;u++){f=m[u];while(f.n){if(t){if(f.f){for(d=0,v=f.f.length;d<v;d++)if(f.f[d]==t){f.f.splice(d,1);break}!f.f.length&&delete f.f}for(c in f.n)if(f.n[n](c)&&f.n[c].f){var g=f.n[c].f;for(d=0,v=g.length;d<v;d++)if(g[d]==t){g.splice(d,1);break}!g.length&&delete f.n[c].f}}else{delete f.f;for(c in f.n)f.n[n](c)&&f.n[c].f&&delete f.n[c].f}f=f.n}}},p.once=function(e,t){var n=function(){return p.unbind(e,n),t.apply(this,arguments)};return p.on(e,n)},p.version=t,p.toString=function(){return"You are running Eve "+t},typeof module!="undefined"&&module.exports?module.exports=p:typeof define=="function"&&define.amd?define("eve",[],function(){return p}):e.eve=p}(this),function(e,t){if(typeof define=="function"&&define.amd)define("snap",["eve"],function(n){return t(e,n)});else if(typeof exports!="undefined"){var n=require("eve");module.exports=t(e,n)}else t(e,e.eve)}(window||this,function(e,t){var n=function(t){var n={},r=e.requestAnimationFrame||e.webkitRequestAnimationFrame||e.mozRequestAnimationFrame||e.oRequestAnimationFrame||e.msRequestAnimationFrame||function(e){setTimeout(e,16)},i=Array.isArray||function(e){return e instanceof Array||Object.prototype.toString.call(e)=="[object Array]"},s=0,o="M"+(+(new Date)).toString(36),u=function(){return o+(s++).toString(36)},a=function(e,t,n,r){if(i(e)){res=[];for(var s=0,o=e.length;s<o;s++)res[s]=a(e[s],t,n[s],r);return res}var u=(n-e)/(r-t);return function(n){return e+u*(n-t)}},f=Date.now||function(){return+(new Date)},l=function(e){var t=this;if(e==null)return t.s;var n=t.s-e;t.b+=t.dur*n,t.B+=t.dur*n,t.s=e},c=function(e){var t=this;if(e==null)return t.spd;t.spd=e},h=function(e){var t=this;if(e==null)return t.dur;t.s=t.s*e/t.dur,t.dur=e},p=function(){var e=this;delete n[e.id],e.update(),t("mina.stop."+e.id,e)},d=function(){var e=this;if(e.pdif)return;delete n[e.id],e.update(),e.pdif=e.get()-e.b},v=function(){var e=this;if(!e.pdif)return;e.b=e.get()-e.pdif,delete e.pdif,n[e.id]=e},m=function(){var e=this,t;if(i(e.start)){t=[];for(var n=0,r=e.start.length;n<r;n++)t[n]=+e.start[n]+(e.end[n]-e.start[n])*e.easing(e.s)}else t=+e.start+(e.end-e.start)*e.easing(e.s);e.set(t)},g=function(){var e=0;for(var i in n)if(n.hasOwnProperty(i)){var s=n[i],o=s.get(),u;e++,s.s=(o-s.b)/(s.dur/s.spd),s.s>=1&&(delete n[i],s.s=1,e--,function(e){setTimeout(function(){t("mina.finish."+e.id,e)})}(s)),s.update()}e&&r(g)},y=function(e,t,i,s,o,a,f){var b={id:u(),start:e,end:t,b:i,s:0,dur:s-i,spd:1,get:o,set:a,easing:f||y.linear,status:l,speed:c,duration:h,stop:p,pause:d,resume:v,update:m};n[b.id]=b;var w=0,E;for(E in n)if(n.hasOwnProperty(E)){w++;if(w==2)break}return w==1&&r(g),b};return y.time=f,y.getById=function(e){return n[e]||null},y.linear=function(e){return e},y.easeout=function(e){return Math.pow(e,1.7)},y.easein=function(e){return Math.pow(e,.48)},y.easeinout=function(e){if(e==1)return 1;if(e==0)return 0;var t=.48-e/1.04,n=Math.sqrt(.1734+t*t),r=n-t,i=Math.pow(Math.abs(r),1/3)*(r<0?-1:1),s=-n-t,o=Math.pow(Math.abs(s),1/3)*(s<0?-1:1),u=i+o+.5;return(1-u)*3*u*u+u*u*u},y.backin=function(e){if(e==1)return 1;var t=1.70158;return e*e*((t+1)*e-t)},y.backout=function(e){if(e==0)return 0;e-=1;var t=1.70158;return e*e*((t+1)*e+t)+1},y.elastic=function(e){return e==!!e?e:Math.pow(2,-10*e)*Math.sin((e-.075)*2*Math.PI/.3)+1},y.bounce=function(e){var t=7.5625,n=2.75,r;return e<1/n?r=t*e*e:e<2/n?(e-=1.5/n,r=t*e*e+.75):e<2.5/n?(e-=2.25/n,r=t*e*e+.9375):(e-=2.625/n,r=t*e*e+.984375),r},e.mina=y,y}(typeof t=="undefined"?function(){}:t),r=function(e){function n(e,t){if(e){if(e.nodeType)return ht(e);if(I(e,"array")&&n.set)return n.set.apply(n,e);if(e instanceof at)return e;if(t==null)return e=r.doc.querySelector(String(e)),ht(e)}return e=e==null?"100%":e,t=t==null?"100%":t,new ct(e,t)}function j(e,t){if(t){e=="#text"&&(e=r.doc.createTextNode(t.text||t["#text"]||"")),e=="#comment"&&(e=r.doc.createComment(t.text||t["#text"]||"")),typeof e=="string"&&(e=j(e));if(typeof t=="string")return e.nodeType==1?t.substring(0,6)=="xlink:"?e.getAttributeNS(D,t.substring(6)):t.substring(0,4)=="xml:"?e.getAttributeNS(P,t.substring(4)):e.getAttribute(t):t=="text"?e.nodeValue:null;if(e.nodeType==1){for(var n in t)if(t[i](n)){var o=s(t[n]);o?n.substring(0,6)=="xlink:"?e.setAttributeNS(D,n.substring(6),o):n.substring(0,4)=="xml:"?e.setAttributeNS(P,n.substring(4),o):e.setAttribute(n,o):e.removeAttribute(n)}}else"text"in t&&(e.nodeValue=t.text)}else e=r.doc.createElementNS(P,e);return e}function F(e){var t=e.attributes,n,r={};for(var i=0;i<t.length;i++)t[i].namespaceURI==D?n="xlink:":n="",n+=t[i].name,r[n]=t[i].textContent;return r}function I(e,t){return t=s.prototype.toLowerCase.call(t),t=="finite"?isFinite(e):t=="array"&&(e instanceof Array||Array.isArray&&Array.isArray(e))?!0:t=="null"&&e===null||t==typeof e&&e!==null||t=="object"&&e===Object(e)||y.call(e).slice(8,-1).toLowerCase()==t}function q(e){if(typeof e=="function"||Object(e)!==e)return e;var t=new e.constructor;for(var n in e)e[i](n)&&(t[n]=q(e[n]));return t}function R(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return e.push(e.splice(n,1)[0])}function U(e,t,n){function r(){var s=Array.prototype.slice.call(arguments,0),o=s.join("␀"),u=r.cache=r.cache||{},a=r.count=r.count||[];return u[i](o)?(R(a,o),n?n(u[o]):u[o]):(a.length>=1e3&&delete u[a.shift()],a.push(o),u[o]=e.apply(t,s),n?n(u[o]):u[o])}return r}function z(e,t,n,r,i,s){if(i==null){var o=e-n,u=t-r;return!o&&!u?0:(180+a.atan2(-u,-o)*180/p+360)%360}return z(e,t,i,s)-z(n,r,i,s)}function W(e){return e%360*p/180}function X(e){return e*180/p%360}function V(){return this.x+m+this.y}function $(){return this.x+m+this.y+m+this.width+" × "+this.height}function tt(e){var t=[];return e=e.replace(/(?:^|\s)(\w+)\(([^)]+)\)/g,function(e,n,r){return r=r.split(/\s*,\s*|\s+/),n=="rotate"&&r.length==1&&r.push(0,0),n=="scale"&&(r.length>2?r=r.slice(0,2):r.length==2&&r.push(0,0),r.length==1&&r.push(r[0],0,0)),n=="skewX"?t.push(["m",1,0,a.tan(W(r[0])),1,0,0]):n=="skewY"?t.push(["m",1,a.tan(W(r[0])),0,1,0,0]):t.push([n.charAt(0)].concat(r)),e}),t}function nt(e,t){var r=et(e),i=new n.Matrix;if(r)for(var o=0,u=r.length;o<u;o++){var a=r[o],f=a.length,l=s(a[0]).toLowerCase(),c=a[0]!=l,h=c?i.invert():0,p,d,v,m,g;l=="t"&&f==2?i.translate(a[1],0):l=="t"&&f==3?c?(p=h.x(0,0),d=h.y(0,0),v=h.x(a[1],a[2]),m=h.y(a[1],a[2]),i.translate(v-p,m-d)):i.translate(a[1],a[2]):l=="r"?f==2?(g=g||t,i.rotate(a[1],g.x+g.width/2,g.y+g.height/2)):f==4&&(c?(v=h.x(a[2],a[3]),m=h.y(a[2],a[3]),i.rotate(a[1],v,m)):i.rotate(a[1],a[2],a[3])):l=="s"?f==2||f==3?(g=g||t,i.scale(a[1],a[f-1],g.x+g.width/2,g.y+g.height/2)):f==4?c?(v=h.x(a[2],a[3]),m=h.y(a[2],a[3]),i.scale(a[1],a[1],v,m)):i.scale(a[1],a[1],a[2],a[3]):f==5&&(c?(v=h.x(a[3],a[4]),m=h.y(a[3],a[4]),i.scale(a[1],a[2],v,m)):i.scale(a[1],a[2],a[3],a[4])):l=="m"&&f==7&&i.add(a[1],a[2],a[3],a[4],a[5],a[6])}return i}function it(e){var t=e.node.ownerSVGElement&&ht(e.node.ownerSVGElement)||e.node.parentNode&&ht(e.node.parentNode)||n.select("svg")||n(0,0),r=t.select("defs"),i=r==null?!1:r.node;return i||(i=lt("defs",t.node).node),i}function st(e){return e.node.ownerSVGElement&&ht(e.node.ownerSVGElement)||n.select("svg")}function ot(e,t,n){function o(e){if(e==null)return v;if(e==+e)return e;j(s,{width:e});try{return s.getBBox().width}catch(t){return 0}}function u(e){if(e==null)return v;if(e==+e)return e;j(s,{height:e});try{return s.getBBox().height}catch(t){return 0}}function a(r,s){t==null?i[r]=s(e.attr(r)||0):r==t&&(i=s(n==null?e.attr(r)||0:n))}var r=st(e).node,i={},s=r.querySelector(".svg---mgr");s||(s=j("rect"),j(s,{x:-9e9,y:-9e9,width:10,height:10,"class":"svg---mgr",fill:"none"}),r.appendChild(s));switch(e.type){case"rect":a("rx",o),a("ry",u);case"image":a("width",o),a("height",u);case"text":a("x",o),a("y",u);break;case"circle":a("cx",o),a("cy",u),a("r",o);break;case"ellipse":a("cx",o),a("cy",u),a("rx",o),a("ry",u);break;case"line":a("x1",o),a("x2",o),a("y1",u),a("y2",u);break;case"marker":a("refX",o),a("markerWidth",o),a("refY",u),a("markerHeight",u);break;case"radialGradient":a("fx",o),a("fy",u);break;case"tspan":a("dx",o),a("dy",u);break;default:a(t,o)}return r.removeChild(s),i}function ut(e){I(e,"array")||(e=Array.prototype.slice.call(arguments,0));var t=0,n=0,r=this.node;while(this[t])delete this[t++];for(t=0;t<e.length;t++)e[t].type=="set"?e[t].forEach(function(e){r.appendChild(e.node)}):r.appendChild(e[t].node);var i=r.childNodes;for(t=0;t<i.length;t++)this[n++]=ht(i[t]);return this}function at(e){if(e.snap in H)return H[e.snap];var t;try{t=e.ownerSVGElement}catch(n){}this.node=e,t&&(this.paper=new ct(t)),this.type=e.tagName||e.nodeName;var r=this.id=_(this);this.anims={},this._={transform:[]},e.snap=r,H[r]=this,this.type=="g"&&(this.add=ut);if(this.type in{g:1,mask:1,pattern:1,symbol:1})for(var s in ct.prototype)ct.prototype[i](s)&&(this[s]=ct.prototype[s])}function ft(e){this.node=e}function lt(e,t){var n=j(e);t.appendChild(n);var r=ht(n);return r}function ct(e,t){var n,s,o,u=ct.prototype;if(e&&e.tagName=="svg"){if(e.snap in H)return H[e.snap];var a=e.ownerDocument;n=new at(e),s=e.getElementsByTagName("desc")[0],o=e.getElementsByTagName("defs")[0],s||(s=j("desc"),s.appendChild(a.createTextNode("Created with Snap")),n.node.appendChild(s)),o||(o=j("defs"),n.node.appendChild(o)),n.defs=o;for(var f in u)u[i](f)&&(n[f]=u[f]);n.paper=n.root=n}else n=lt("svg",r.doc.body),j(n.node,{height:t,version:1.1,width:e,xmlns:P});return n}function ht(e){return e?e instanceof at||e instanceof ft?e:e.tagName&&e.tagName.toLowerCase()=="svg"?new ct(e):e.tagName&&e.tagName.toLowerCase()=="object"&&e.type=="image/svg+xml"?new ct(e.contentDocument.getElementsByTagName("svg")[0]):new at(e):e}function pt(e,t){for(var n=0,r=e.length;n<r;n++){var i={type:e[n].type,attr:e[n].attr()},s=e[n].children();t.push(i),s.length&&pt(s,i.childNodes=[])}}n.version="0.4.0",n.toString=function(){return"Snap v"+this.version},n._={};var r={win:e.window,doc:e.window.document};n._.glob=r;var i="hasOwnProperty",s=String,o=parseFloat,u=parseInt,a=Math,f=a.max,l=a.min,c=a.abs,h=a.pow,p=a.PI,d=a.round,v="",m=" ",y=Object.prototype.toString,b=/^url\(['"]?([^\)]+?)['"]?\)$/i,w=/^\s*((#[a-f\d]{6})|(#[a-f\d]{3})|rgba?\(\s*([\d\.]+%?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+%?(?:\s*,\s*[\d\.]+%?)?)\s*\)|hsba?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?%?)\s*\)|hsla?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?%?)\s*\))\s*$/i,E=/^(?:cubic-)?bezier\(([^,]+),([^,]+),([^,]+),([^\)]+)\)/,S=/^url\(#?([^)]+)\)$/,x=n._.separator=/[,\s]+/,T=/[\s]/g,N=/[\s]*,[\s]*/,C={hs:1,rg:1},k=/([a-z])[\s,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\s]*,?[\s]*)+)/ig,L=/([rstm])[\s,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\s]*,?[\s]*)+)/ig,A=/(-?\d*\.?\d*(?:e[\-+]?\\d+)?)[\s]*,?[\s]*/ig,O=0,M="S"+(+(new Date)).toString(36),_=function(e){return(e&&e.type?e.type:v)+M+(O++).toString(36)},D="http://www.w3.org/1999/xlink",P="http://www.w3.org/2000/svg",H={},B=n.url=function(e){return"url('#"+e+"')"};n._.$=j,n._.id=_,n.format=function(){var e=/\{([^\}]+)\}/g,t=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g,n=function(e,n,r){var i=r;return n.replace(t,function(e,t,n,r,s){t=t||r,i&&(t in i&&(i=i[t]),typeof i=="function"&&s&&(i=i()))}),i=(i==null||i==r?e:i)+"",i};return function(t,r){return s(t).replace(e,function(e,t){return n(e,t,r)})}}(),n._.clone=q,n._.cacher=U,n.rad=W,n.deg=X,n.sin=function(e){return a.sin(n.rad(e))},n.tan=function(e){return a.tan(n.rad(e))},n.cos=function(e){return a.cos(n.rad(e))},n.asin=function(e){return n.deg(a.asin(e))},n.acos=function(e){return n.deg(a.acos(e))},n.atan=function(e){return n.deg(a.atan(e))},n.atan2=function(e){return n.deg(a.atan2(e))},n.angle=z,n.len=function(e,t,r,i){return Math.sqrt(n.len2(e,t,r,i))},n.len2=function(e,t,n,r){return(e-n)*(e-n)+(t-r)*(t-r)},n.closestPoint=function(e,t,n){function r(e){var r=e.x-t,i=e.y-n;return r*r+i*i}var i=e.node,s=i.getTotalLength(),o=s/i.pathSegList.numberOfItems*.125,u,a,f=Infinity;for(var l,c=0,h;c<=s;c+=o)(h=r(l=i.getPointAtLength(c)))<f&&(u=l,a=c,f=h);o*=.5;while(o>.5){var p,d,v,m,g,y;(v=a-o)>=0&&(g=r(p=i.getPointAtLength(v)))<f?(u=p,a=v,f=g):(m=a+o)<=s&&(y=r(d=i.getPointAtLength(m)))<f?(u=d,a=m,f=y):o*=.5}return u={x:u.x,y:u.y,length:a,distance:Math.sqrt(f)},u},n.is=I,n.snapTo=function(e,t,n){n=I(n,"finite")?n:10;if(I(e,"array")){var r=e.length;while(r--)if(c(e[r]-t)<=n)return e[r]}else{e=+e;var i=t%e;if(i<n)return t-i;if(i>e-n)return t-i+e}return t},n.getRGB=U(function(e){if(!e||!!((e=s(e)).indexOf("-")+1))return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:G};if(e=="none")return{r:-1,g:-1,b:-1,hex:"none",toString:G};!C[i](e.toLowerCase().substring(0,2))&&e.charAt()!="#"&&(e=J(e));if(!e)return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:G};var t,r,c,h,p,d,v,m=e.match(w);return m?(m[2]&&(h=u(m[2].substring(5),16),c=u(m[2].substring(3,5),16),r=u(m[2].substring(1,3),16)),m[3]&&(h=u((d=m[3].charAt(3))+d,16),c=u((d=m[3].charAt(2))+d,16),r=u((d=m[3].charAt(1))+d,16)),m[4]&&(v=m[4].split(N),r=o(v[0]),v[0].slice(-1)=="%"&&(r*=2.55),c=o(v[1]),v[1].slice(-1)=="%"&&(c*=2.55),h=o(v[2]),v[2].slice(-1)=="%"&&(h*=2.55),m[1].toLowerCase().slice(0,4)=="rgba"&&(p=o(v[3])),v[3]&&v[3].slice(-1)=="%"&&(p/=100)),m[5]?(v=m[5].split(N),r=o(v[0]),v[0].slice(-1)=="%"&&(r/=100),c=o(v[1]),v[1].slice(-1)=="%"&&(c/=100),h=o(v[2]),v[2].slice(-1)=="%"&&(h/=100),(v[0].slice(-3)=="deg"||v[0].slice(-1)=="°")&&(r/=360),m[1].toLowerCase().slice(0,4)=="hsba"&&(p=o(v[3])),v[3]&&v[3].slice(-1)=="%"&&(p/=100),n.hsb2rgb(r,c,h,p)):m[6]?(v=m[6].split(N),r=o(v[0]),v[0].slice(-1)=="%"&&(r/=100),c=o(v[1]),v[1].slice(-1)=="%"&&(c/=100),h=o(v[2]),v[2].slice(-1)=="%"&&(h/=100),(v[0].slice(-3)=="deg"||v[0].slice(-1)=="°")&&(r/=360),m[1].toLowerCase().slice(0,4)=="hsla"&&(p=o(v[3])),v[3]&&v[3].slice(-1)=="%"&&(p/=100),n.hsl2rgb(r,c,h,p)):(r=l(a.round(r),255),c=l(a.round(c),255),h=l(a.round(h),255),p=l(f(p,0),1),m={r:r,g:c,b:h,toString:G},m.hex="#"+(16777216|h|c<<8|r<<16).toString(16).slice(1),m.opacity=I(p,"finite")?p:1,m)):{r:-1,g:-1,b:-1,hex:"none",error:1,toString:G}},n),n.hsb=U(function(e,t,r){return n.hsb2rgb(e,t,r).hex}),n.hsl=U(function(e,t,r){return n.hsl2rgb(e,t,r).hex}),n.rgb=U(function(e,t,n,r){if(I(r,"finite")){var i=a.round;return"rgba("+[i(e),i(t),i(n),+r.toFixed(2)]+")"}return"#"+(16777216|n|t<<8|e<<16).toString(16).slice(1)});var J=function(e){var t=r.doc.getElementsByTagName("head")[0]||r.doc.getElementsByTagName("svg")[0],n="rgb(255, 0, 0)";return J=U(function(e){if(e.toLowerCase()=="red")return n;t.style.color=n,t.style.color=e;var i=r.doc.defaultView.getComputedStyle(t,v).getPropertyValue("color");return i==n?null:i}),J(e)},K=function(){return"hsb("+[this.h,this.s,this.b]+")"},Q=function(){return"hsl("+[this.h,this.s,this.l]+")"},G=function(){return this.opacity==1||this.opacity==null?this.hex:"rgba("+[this.r,this.g,this.b,this.opacity]+")"},Y=function(e,t,r){t==null&&I(e,"object")&&"r"in e&&"g"in e&&"b"in e&&(r=e.b,t=e.g,e=e.r);if(t==null&&I(e,string)){var i=n.getRGB(e);e=i.r,t=i.g,r=i.b}if(e>1||t>1||r>1)e/=255,t/=255,r/=255;return[e,t,r]},Z=function(e,t,r,i){e=a.round(e*255),t=a.round(t*255),r=a.round(r*255);var s={r:e,g:t,b:r,opacity:I(i,"finite")?i:1,hex:n.rgb(e,t,r),toString:G};return I(i,"finite")&&(s.opacity=i),s};n.color=function(e){var t;return I(e,"object")&&"h"in e&&"s"in e&&"b"in e?(t=n.hsb2rgb(e),e.r=t.r,e.g=t.g,e.b=t.b,e.opacity=1,e.hex=t.hex):I(e,"object")&&"h"in e&&"s"in e&&"l"in e?(t=n.hsl2rgb(e),e.r=t.r,e.g=t.g,e.b=t.b,e.opacity=1,e.hex=t.hex):(I(e,"string")&&(e=n.getRGB(e)),I(e,"object")&&"r"in e&&"g"in e&&"b"in e&&!("error"in e)?(t=n.rgb2hsl(e),e.h=t.h,e.s=t.s,e.l=t.l,t=n.rgb2hsb(e),e.v=t.b):(e={hex:"none"},e.r=e.g=e.b=e.h=e.s=e.v=e.l=-1,e.error=1)),e.toString=G,e},n.hsb2rgb=function(e,t,n,r){I(e,"object")&&"h"in e&&"s"in e&&"b"in e&&(n=e.b,t=e.s,r=e.o,e=e.h),e*=360;var i,s,o,u,a;return e=e%360/60,a=n*t,u=a*(1-c(e%2-1)),i=s=o=n-a,e=~~e,i+=[a,u,0,0,u,a][e],s+=[u,a,a,u,0,0][e],o+=[0,0,u,a,a,u][e],Z(i,s,o,r)},n.hsl2rgb=function(e,t,n,r){I(e,"object")&&"h"in e&&"s"in e&&"l"in e&&(n=e.l,t=e.s,e=e.h);if(e>1||t>1||n>1)e/=360,t/=100,n/=100;e*=360;var i,s,o,u,a;return e=e%360/60,a=2*t*(n<.5?n:1-n),u=a*(1-c(e%2-1)),i=s=o=n-a/2,e=~~e,i+=[a,u,0,0,u,a][e],s+=[u,a,a,u,0,0][e],o+=[0,0,u,a,a,u][e],Z(i,s,o,r)},n.rgb2hsb=function(e,t,n){n=Y(e,t,n),e=n[0],t=n[1],n=n[2];var r,i,s,o;return s=f(e,t,n),o=s-l(e,t,n),r=o==0?null:s==e?(t-n)/o:s==t?(n-e)/o+2:(e-t)/o+4,r=(r+360)%6*60/360,i=o==0?0:o/s,{h:r,s:i,b:s,toString:K}},n.rgb2hsl=function(e,t,n){n=Y(e,t,n),e=n[0],t=n[1],n=n[2];var r,i,s,o,u,a;return o=f(e,t,n),u=l(e,t,n),a=o-u,r=a==0?null:o==e?(t-n)/a:o==t?(n-e)/a+2:(e-t)/a+4,r=(r+360)%6*60/360,s=(o+u)/2,i=a==0?0:s<.5?a/(2*s):a/(2-2*s),{h:r,s:i,l:s,toString:Q}},n.parsePathString=function(e){if(!e)return null;var t=n.path(e);if(t.arr)return n.path.clone(t.arr);var r={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},i=[];return I(e,"array")&&I(e[0],"array")&&(i=n.path.clone(e)),i.length||s(e).replace(k,function(e,t,n){var s=[],o=t.toLowerCase();n.replace(A,function(e,t){t&&s.push(+t)}),o=="m"&&s.length>2&&(i.push([t].concat(s.splice(0,2))),o="l",t=t=="m"?"l":"L"),o=="o"&&s.length==1&&i.push([t,s[0]]);if(o=="r")i.push([t].concat(s));else while(s.length>=r[o]){i.push([t].concat(s.splice(0,r[o])));if(!r[o])break}}),i.toString=n.path.toString,t.arr=n.path.clone(i),i};var et=n.parseTransformString=function(e){if(!e)return null;var t={r:3,s:4,t:2,m:6},r=[];return I(e,"array")&&I(e[0],"array")&&(r=n.path.clone(e)),r.length||s(e).replace(L,function(e,t,n){var i=[],s=t.toLowerCase();n.replace(A,function(e,t){t&&i.push(+t)}),r.push([t].concat(i))}),r.toString=n.path.toString,r};n._.svgTransform2string=tt,n._.rgTransform=/^[a-z][\s]*-?\.?\d/i,n._.transform2matrix=nt,n._unit2px=ot;var rt=r.doc.contains||r.doc.compareDocumentPosition?function(e,t){var n=e.nodeType==9?e.documentElement:e,r=t&&t.parentNode;return e==r||!!r&&r.nodeType==1&&!!(n.contains?n.contains(r):e.compareDocumentPosition&&e.compareDocumentPosition(r)&16)}:function(e,t){if(t)while(t){t=t.parentNode;if(t==e)return!0}return!1};n._.getSomeDefs=it,n._.getSomeSVG=st,n.select=function(e){return e=s(e).replace(/([^\\]):/g,"$1\\:"),ht(r.doc.querySelector(e))},n.selectAll=function(e){var t=r.doc.querySelectorAll(e),i=(n.set||Array)();for(var s=0;s<t.length;s++)i.push(ht(t[s]));return i},setInterval(function(){for(var e in H)if(H[i](e)){var t=H[e],n=t.node;(t.type!="svg"&&!n.ownerSVGElement||t.type=="svg"&&(!n.parentNode||"ownerSVGElement"in n.parentNode&&!n.ownerSVGElement))&&delete H[e]}},1e4),at.prototype.attr=function(e,n){var r=this,s=r.node;if(!e){if(s.nodeType!=1)return{text:s.nodeValue};var o=s.attributes,u={};for(var a=0,f=o.length;a<f;a++)u[o[a].nodeName]=o[a].nodeValue;return u}if(I(e,"string")){if(!(arguments.length>1))return t("snap.util.getattr."+e,r).firstDefined();var l={};l[e]=n,e=l}for(var c in e)e[i](c)&&t("snap.util.attr."+c,r,e[c]);return r},n.parse=function(e){var t=r.doc.createDocumentFragment(),n=!0,i=r.doc.createElement("div");e=s(e),e.match(/^\s*<\s*svg(?:\s|>)/)||(e="<svg>"+e+"</svg>",n=!1),i.innerHTML=e,e=i.getElementsByTagName("svg")[0];if(e)if(n)t=e;else while(e.firstChild)t.appendChild(e.firstChild);return new ft(t)},n.fragment=function(){var e=Array.prototype.slice.call(arguments,0),t=r.doc.createDocumentFragment();for(var i=0,s=e.length;i<s;i++){var o=e[i];o.node&&o.node.nodeType&&t.appendChild(o.node),o.nodeType&&t.appendChild(o),typeof o=="string"&&t.appendChild(n.parse(o).node)}return new ft(t)},n._.make=lt,n._.wrap=ht,ct.prototype.el=function(e,t){var n=lt(e,this.node);return t&&n.attr(t),n},at.prototype.children=function(){var e=[],t=this.node.childNodes;for(var r=0,i=t.length;r<i;r++)e[r]=n(t[r]);return e},at.prototype.toJSON=function(){var e=[];return pt([this],e),e[0]},t.on("snap.util.getattr",function(){var e=t.nt();e=e.substring(e.lastIndexOf(".")+1);var n=e.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()});return dt[i](n)?this.node.ownerDocument.defaultView.getComputedStyle(this.node,null).getPropertyValue(n):j(this.node,e)});var dt={"alignment-baseline":0,"baseline-shift":0,clip:0,"clip-path":0,"clip-rule":0,color:0,"color-interpolation":0,"color-interpolation-filters":0,"color-profile":0,"color-rendering":0,cursor:0,direction:0,display:0,"dominant-baseline":0,"enable-background":0,fill:0,"fill-opacity":0,"fill-rule":0,filter:0,"flood-color":0,"flood-opacity":0,font:0,"font-family":0,"font-size":0,"font-size-adjust":0,"font-stretch":0,"font-style":0,"font-variant":0,"font-weight":0,"glyph-orientation-horizontal":0,"glyph-orientation-vertical":0,"image-rendering":0,kerning:0,"letter-spacing":0,"lighting-color":0,marker:0,"marker-end":0,"marker-mid":0,"marker-start":0,mask:0,opacity:0,overflow:0,"pointer-events":0,"shape-rendering":0,"stop-color":0,"stop-opacity":0,stroke:0,"stroke-dasharray":0,"stroke-dashoffset":0,"stroke-linecap":0,"stroke-linejoin":0,"stroke-miterlimit":0,"stroke-opacity":0,"stroke-width":0,"text-anchor":0,"text-decoration":0,"text-rendering":0,"unicode-bidi":0,visibility:0,"word-spacing":0,"writing-mode":0};t.on("snap.util.attr",function(e){var n=t.nt(),r={};n=n.substring(n.lastIndexOf(".")+1),r[n]=e;var s=n.replace(/-(\w)/gi,function(e,t){return t.toUpperCase()}),o=n.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()});dt[i](o)?this.node.style[s]=e==null?v:e:j(this.node,r)}),function(e){}(ct.prototype),n.ajax=function(e,n,r,i){var s=new XMLHttpRequest,o=_();if(s){if(I(n,"function"))i=r,r=n,n=null;else if(I(n,"object")){var u=[];for(var a in n)n.hasOwnProperty(a)&&u.push(encodeURIComponent(a)+"="+encodeURIComponent(n[a]));n=u.join("&")}return s.open(n?"POST":"GET",e,!0),n&&(s.setRequestHeader("X-Requested-With","XMLHttpRequest"),s.setRequestHeader("Content-type","application/x-www-form-urlencoded")),r&&(t.once("snap.ajax."+o+".0",r),t.once("snap.ajax."+o+".200",r),t.once("snap.ajax."+o+".304",r)),s.onreadystatechange=function(){if(s.readyState!=4)return;t("snap.ajax."+o+"."+s.status,i,s)},s.readyState==4?s:(s.send(n),s)}},n.load=function(e,t,r){n.ajax(e,function(e){var i=n.parse(e.responseText);r?t.call(r,i):t(i)})};var vt=function(e){var t=e.getBoundingClientRect(),n=e.ownerDocument,r=n.body,i=n.documentElement,s=i.clientTop||r.clientTop||0,o=i.clientLeft||r.clientLeft||0,u=t.top+(g.win.pageYOffset||i.scrollTop||r.scrollTop)-s,a=t.left+(g.win.pageXOffset||i.scrollLeft||r.scrollLeft)-o;return{y:u,x:a}};return n.getElementByPoint=function(e,t){var n=this,i=n.canvas,s=r.doc.elementFromPoint(e,t);if(r.win.opera&&s.tagName=="svg"){var o=vt(s),u=s.createSVGRect();u.x=e-o.x,u.y=t-o.y,u.width=u.height=1;var a=s.getIntersectionList(u,null);a.length&&(s=a[a.length-1])}return s?ht(s):null},n.plugin=function(e){e(n,at,ct,r,ft)},r.win.Snap=n,n}(e||this);return r.plugin(function(r,i,s,o,u){function y(e,t){if(t==null){var n=!0;e.type=="linearGradient"||e.type=="radialGradient"?t=e.node.getAttribute("gradientTransform"):e.type=="pattern"?t=e.node.getAttribute("patternTransform"):t=e.node.getAttribute("transform");if(!t)return new r.Matrix;t=r._.svgTransform2string(t)}else r._.rgTransform.test(t)?t=l(t).replace(/\.{3}|\u2026/g,e._.transform||""):t=r._.svgTransform2string(t),f(t,"array")&&(t=r.path?r.path.toString.call(t):l(t)),e._.transform=t;var i=r._.transform2matrix(t,e.getBBox(1));if(n)return i;e.matrix=i}function b(e){function o(e,t){var n=h(e.node,t);n=n&&n.match(r),n=n&&n[2];if(!n||n.charAt()!="#")return;n=n.substring(1),n&&(s[n]=(s[n]||[]).concat(function(n){var r={};r[t]=URL(n),h(e.node,r)}))}function u(e){var t=h(e.node,"xlink:href");if(!t||t.charAt()!="#")return;t=t.substring(1),t&&(s[t]=(s[t]||[]).concat(function(t){e.attr("xlink:href","#"+t)}))}var t=e.selectAll("*"),n,r=/^\s*url\(("|'|)(.*)\1\)\s*$/,i=[],s={};for(var a=0,f=t.length;a<f;a++){n=t[a],o(n,"fill"),o(n,"stroke"),o(n,"filter"),o(n,"mask"),o(n,"clip-path"),u(n);var l=h(n.node,"id");l&&(h(n.node,{id:n.id}),i.push({old:l,id:n.id}))}for(a=0,f=i.length;a<f;a++){var c=s[i[a].old];if(c)for(var p=0,d=c.length;p<d;p++)c[p](i[a].id)}}function w(e,t,n){return function(r){var i=r.slice(e,t);return i.length==1&&(i=i[0]),n?n(i):i}}function x(e){return function(){var t=e?"<"+this.type:"",n=this.node.attributes,r=this.node.childNodes;if(e)for(var i=0,s=n.length;i<s;i++)t+=" "+n[i].name+'="'+n[i].value.replace(/"/g,'\\"')+'"';if(r.length){e&&(t+=">");for(i=0,s=r.length;i<s;i++)r[i].nodeType==3?t+=r[i].nodeValue:r[i].nodeType==1&&(t+=m(r[i]).toString());e&&(t+="</"+this.type+">")}else e&&(t+="/>");return t}}var a=i.prototype,f=r.is,l=String,c=r._unit2px,h=r._.$,p=r._.make,d=r._.getSomeDefs,v="hasOwnProperty",m=r._.wrap;a.getBBox=function(e){if(!r.Matrix||!r.path)return this.node.getBBox();var t=this,n=new r.Matrix;if(t.removed)return r._.box();while(t.type=="use"){e||(n=n.add(t.transform().localMatrix.translate(t.attr("x")||0,t.attr("y")||0)));if(t.original)t=t.original;else{var i=t.attr("xlink:href");t=t.original=t.node.ownerDocument.getElementById(i.substring(i.indexOf("#")+1))}}var s=t._,o=r.path.get[t.type]||r.path.get.deflt;try{return e?(s.bboxwt=o?r.path.getBBox(t.realPath=o(t)):r._.box(t.node.getBBox()),r._.box(s.bboxwt)):(t.realPath=o(t),t.matrix=t.transform().localMatrix,s.bbox=r.path.getBBox(r.path.map(t.realPath,n.add(t.matrix))),r._.box(s.bbox))}catch(u){return r._.box()}};var g=function(){return this.string};a.transform=function(e){var t=this._;if(e==null){var n=this,i=new r.Matrix(this.node.getCTM()),s=y(this),o=[s],u=new r.Matrix,a,f=s.toTransformString(),c=l(s)==l(this.matrix)?l(t.transform):f;while(n.type!="svg"&&(n=n.parent()))o.push(y(n));a=o.length;while(a--)u.add(o[a]);return{string:c,globalMatrix:i,totalMatrix:u,localMatrix:s,diffMatrix:i.clone().add(s.invert()),global:i.toTransformString(),total:u.toTransformString(),local:f,toString:g}}return e instanceof r.Matrix?(this.matrix=e,this._.transform=e.toTransformString()):y(this,e),this.node&&(this.type=="linearGradient"||this.type=="radialGradient"?h(this.node,{gradientTransform:this.matrix}):this.type=="pattern"?h(this.node,{patternTransform:this.matrix}):h(this.node,{transform:this.matrix})),this},a.parent=function(){return m(this.node.parentNode)},a.append=a.add=function(e){if(e){if(e.type=="set"){var t=this;return e.forEach(function(e){t.add(e)}),this}e=m(e),this.node.appendChild(e.node),e.paper=this.paper}return this},a.appendTo=function(e){return e&&(e=m(e),e.append(this)),this},a.prepend=function(e){if(e){if(e.type=="set"){var t=this,n;return e.forEach(function(e){n?n.after(e):t.prepend(e),n=e}),this}e=m(e);var r=e.parent();this.node.insertBefore(e.node,this.node.firstChild),this.add&&this.add(),e.paper=this.paper,this.parent()&&this.parent().add(),r&&r.add()}return this},a.prependTo=function(e){return e=m(e),e.prepend(this),this},a.before=function(e){if(e.type=="set"){var t=this;return e.forEach(function(e){var n=e.parent();t.node.parentNode.insertBefore(e.node,t.node),n&&n.add()}),this.parent().add(),this}e=m(e);var n=e.parent();return this.node.parentNode.insertBefore(e.node,this.node),this.parent()&&this.parent().add(),n&&n.add(),e.paper=this.paper,this},a.after=function(e){e=m(e);var t=e.parent();return this.node.nextSibling?this.node.parentNode.insertBefore(e.node,this.node.nextSibling):this.node.parentNode.appendChild(e.node),this.parent()&&this.parent().add(),t&&t.add(),e.paper=this.paper,this},a.insertBefore=function(e){e=m(e);var t=this.parent();return e.node.parentNode.insertBefore(this.node,e.node),this.paper=e.paper,t&&t.add(),e.parent()&&e.parent().add(),this},a.insertAfter=function(e){e=m(e);var t=this.parent();return e.node.parentNode.insertBefore(this.node,e.node.nextSibling),this.paper=e.paper,t&&t.add(),e.parent()&&e.parent().add(),this},a.remove=function(){var e=this.parent();return this.node.parentNode&&this.node.parentNode.removeChild(this.node),delete this.paper,this.removed=!0,e&&e.add(),this},a.select=function(e){return m(this.node.querySelector(e))},a.selectAll=function(e){var t=this.node.querySelectorAll(e),n=(r.set||Array)();for(var i=0;i<t.length;i++)n.push(m(t[i]));return n},a.asPX=function(e,t){return t==null&&(t=this.attr(e)),+c(this,e,t)},a.use=function(){var e,t=this.node.id;return t||(t=this.id,h(this.node,{id:t})),this.type=="linearGradient"||this.type=="radialGradient"||this.type=="pattern"?e=p(this.type,this.node.parentNode):e=p("use",this.node.parentNode),h(e.node,{"xlink:href":"#"+t}),e.original=this,e},a.clone=function(){var e=m(this.node.cloneNode(!0));return h(e.node,"id")&&h(e.node,{id:e.id}),b(e),e.insertAfter(this),e},a.toDefs=function(){var e=d(this);return e.appendChild(this.node),this},a.pattern=a.toPattern=function(e,t,n,r){var i=p("pattern",d(this));return e==null&&(e=this.getBBox()),f(e,"object")&&"x"in e&&(t=e.y,n=e.width,r=e.height,e=e.x),h(i.node,{x:e,y:t,width:n,height:r,patternUnits:"userSpaceOnUse",id:i.id,viewBox:[e,t,n,r].join(" ")}),i.node.appendChild(this.node),i},a.marker=function(e,t,n,r,i,s){var o=p("marker",d(this));return e==null&&(e=this.getBBox()),f(e,"object")&&"x"in e&&(t=e.y,n=e.width,r=e.height,i=e.refX||e.cx,s=e.refY||e.cy,e=e.x),h(o.node,{viewBox:[e,t,n,r].join(" "),markerWidth:n,markerHeight:r,orient:"auto",refX:i||0,refY:s||0,id:o.id}),o.node.appendChild(this.node),o};var E=function(e,t,r,i){typeof r=="function"&&!r.length&&(i=r,r=n.linear),this.attr=e,this.dur=t,r&&(this.easing=r),i&&(this.callback=i)};r._.Animation=E,r.animation=function(e,t,n,r){return new E(e,t,n,r)},a.inAnim=function(){var e=this,t=[];for(var n in e.anims)e.anims[v](n)&&function(e){t.push({anim:new E(e._attrs,e.dur,e.easing,e._callback),mina:e,curStatus:e.status(),status:function(t){return e.status(t)},stop:function(){e.stop()}})}(e.anims[n]);return t},r.animate=function(e,r,i,s,o,u){typeof o=="function"&&!o.length&&(u=o,o=n.linear);var a=n.time(),f=n(e,r,a,a+s,n.time,i,o);return u&&t.once("mina.finish."+f.id,u),f},a.stop=function(){var e=this.inAnim();for(var t=0,n=e.length;t<n;t++)e[t].stop();return this},a.animate=function(e,r,i,s){typeof i=="function"&&!i.length&&(s=i,i=n.linear),e instanceof E&&(s=e.callback,i=e.easing,r=e.dur,e=e.attr);var o=[],u=[],a={},c,h,p,d,m=this;for(var g in e)if(e[v](g)){m.equal?(d=m.equal(g,l(e[g])),c=d.from,h=d.to,p=d.f):(c=+m.attr(g),h=+e[g]);var y=f(c,"array")?c.length:1;a[g]=w(o.length,o.length+y,p),o=o.concat(c),u=u.concat(h)}var b=n.time(),S=n(o,u,b,b+r,n.time,function(e){var t={};for(var n in a)a[v](n)&&(t[n]=a[n](e));m.attr(t)},i);return m.anims[S.id]=S,S._attrs=e,S._callback=s,t("snap.animcreated."+m.id,S),t.once("mina.finish."+S.id,function(){delete m.anims[S.id],s&&s.call(m)}),t.once("mina.stop."+S.id,function(){delete m.anims[S.id]}),m};var S={};a.data=function(e,n){var i=S[this.id]=S[this.id]||{};if(arguments.length==0)return t("snap.data.get."+this.id,this,i,null),i;if(arguments.length==1){if(r.is(e,"object")){for(var s in e)e[v](s)&&this.data(s,e[s]);return this}return t("snap.data.get."+this.id,this,i[e],e),i[e]}return i[e]=n,t("snap.data.set."+this.id,this,n,e),this},a.removeData=function(e){return e==null?S[this.id]={}:S[this.id]&&delete S[this.id][e],this},a.outerSVG=a.toString=x(1),a.innerSVG=x(),a.toDataURL=function(){if(e&&e.btoa){var t=this.getBBox(),n=r.format('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="{width}" height="{height}" viewBox="{x} {y} {width} {height}">{contents}</svg>',{x:+t.x.toFixed(3),y:+t.y.toFixed(3),width:+t.width.toFixed(3),height:+t.height.toFixed(3),contents:this.outerSVG()});return"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(n)))}},u.prototype.select=a.select,u.prototype.selectAll=a.selectAll}),r.plugin(function(e,t,n,r,i){function f(e,t,n,r,i,o){if(t==null&&s.call(e)=="[object SVGMatrix]"){this.a=e.a,this.b=e.b,this.c=e.c,this.d=e.d,this.e=e.e,this.f=e.f;return}e!=null?(this.a=+e,this.b=+t,this.c=+n,this.d=+r,this.e=+i,this.f=+o):(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0)}var s=Object.prototype.toString,o=String,u=Math,a="";(function(t){function n(e){return e[0]*e[0]+e[1]*e[1]}function r(e){var t=u.sqrt(n(e));e[0]&&(e[0]/=t),e[1]&&(e[1]/=t)}t.add=function(e,t,n,r,i,s){var o=[[],[],[]],u=[[this.a,this.c,this.e],[this.b,this.d,this.f],[0,0,1]],a=[[e,n,i],[t,r,s],[0,0,1]],l,c,h,p;e&&e instanceof f&&(a=[[e.a,e.c,e.e],[e.b,e.d,e.f],[0,0,1]]);for(l=0;l<3;l++)for(c=0;c<3;c++){p=0;for(h=0;h<3;h++)p+=u[l][h]*a[h][c];o[l][c]=p}return this.a=o[0][0],this.b=o[1][0],this.c=o[0][1],this.d=o[1][1],this.e=o[0][2],this.f=o[1][2],this},t.invert=function(){var e=this,t=e.a*e.d-e.b*e.c;return new f(e.d/t,-e.b/t,-e.c/t,e.a/t,(e.c*e.f-e.d*e.e)/t,(e.b*e.e-e.a*e.f)/t)},t.clone=function(){return new f(this.a,this.b,this.c,this.d,this.e,this.f)},t.translate=function(e,t){return this.add(1,0,0,1,e,t)},t.scale=function(e,t,n,r){return t==null&&(t=e),(n||r)&&this.add(1,0,0,1,n,r),this.add(e,0,0,t,0,0),(n||r)&&this.add(1,0,0,1,-n,-r),this},t.rotate=function(t,n,r){t=e.rad(t),n=n||0,r=r||0;var i=+u.cos(t).toFixed(9),s=+u.sin(t).toFixed(9);return this.add(i,s,-s,i,n,r),this.add(1,0,0,1,-n,-r)},t.x=function(e,t){return e*this.a+t*this.c+this.e},t.y=function(e,t){return e*this.b+t*this.d+this.f},t.get=function(e){return+this[o.fromCharCode(97+e)].toFixed(4)},t.toString=function(){return"matrix("+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)].join()+")"},t.offset=function(){return[this.e.toFixed(4),this.f.toFixed(4)]},t.determinant=function(){return this.a*this.d-this.b*this.c},t.split=function(){var t={};t.dx=this.e,t.dy=this.f;var i=[[this.a,this.c],[this.b,this.d]];t.scalex=u.sqrt(n(i[0])),r(i[0]),t.shear=i[0][0]*i[1][0]+i[0][1]*i[1][1],i[1]=[i[1][0]-i[0][0]*t.shear,i[1][1]-i[0][1]*t.shear],t.scaley=u.sqrt(n(i[1])),r(i[1]),t.shear/=t.scaley,this.determinant()<0&&(t.scalex=-t.scalex);var s=-i[0][1],o=i[1][1];return o<0?(t.rotate=e.deg(u.acos(o)),s<0&&(t.rotate=360-t.rotate)):t.rotate=e.deg(u.asin(s)),t.isSimple=!+t.shear.toFixed(9)&&(t.scalex.toFixed(9)==t.scaley.toFixed(9)||!t.rotate),t.isSuperSimple=!+t.shear.toFixed(9)&&t.scalex.toFixed(9)==t.scaley.toFixed(9)&&!t.rotate,t.noRotation=!+t.shear.toFixed(9)&&!t.rotate,t},t.toTransformString=function(e){var t=e||this.split();return+t.shear.toFixed(9)?"m"+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)]:(t.scalex=+t.scalex.toFixed(4),t.scaley=+t.scaley.toFixed(4),t.rotate=+t.rotate.toFixed(4),(t.dx||t.dy?"t"+[+t.dx.toFixed(4),+t.dy.toFixed(4)]:a)+(t.scalex!=1||t.scaley!=1?"s"+[t.scalex,t.scaley,0,0]:a)+(t.rotate?"r"+[+t.rotate.toFixed(4),0,0]:a))}})(f.prototype),e.Matrix=f,e.matrix=function(e,t,n,r,i,s){return new f(e,t,n,r,i,s)}}),r.plugin(function(e,n,r,i,s){function g(r){return function(i){t.stop(),i instanceof s&&i.node.childNodes.length==1&&(i.node.firstChild.tagName=="radialGradient"||i.node.firstChild.tagName=="linearGradient"||i.node.firstChild.tagName=="pattern")&&(i=i.node.firstChild,l(this).appendChild(i),i=a(i));if(i instanceof n)if(i.type=="radialGradient"||i.type=="linearGradient"||i.type=="pattern"){i.node.id||h(i.node,{id:i.id});var o=p(i.node.id)}else o=i.attr(r);else{o=e.color(i);if(o.error){var u=e(l(this).ownerSVGElement).gradient(i);u?(u.node.id||h(u.node,{id:u.id}),o=p(u.node.id)):o=i}else o=d(o)}var f={};f[r]=o,h(this.node,f),this.node.style[r]=m}}function b(e){t.stop(),e==+e&&(e+="px"),this.node.style.fontSize=e}function w(e){var t=[],n=e.childNodes;for(var r=0,i=n.length;r<i;r++){var s=n[r];s.nodeType==3&&t.push(s.nodeValue),s.tagName=="tspan"&&(s.childNodes.length==1&&s.firstChild.nodeType==3?t.push(s.firstChild.nodeValue):t.push(w(s)))}return t}function E(){return t.stop(),this.node.style.fontSize}var o="hasOwnProperty",u=e._.make,a=e._.wrap,f=e.is,l=e._.getSomeDefs,c=/^url\(#?([^)]+)\)$/,h=e._.$,p=e.url,d=String,v=e._.separator,m="";t.on("snap.util.attr.mask",function(e){if(e instanceof n||e instanceof s){t.stop(),e instanceof s&&e.node.childNodes.length==1&&(e=e.node.firstChild,l(this).appendChild(e),e=a(e));if(e.type=="mask")var r=e;else r=u("mask",l(this)),r.node.appendChild(e.node);!r.node.id&&h(r.node,{id:r.id}),h(this.node,{mask:p(r.id)})}}),function(e){t.on("snap.util.attr.clip",e),t.on("snap.util.attr.clip-path",e),t.on("snap.util.attr.clipPath",e)}(function(e){if(e instanceof n||e instanceof s){t.stop();if(e.type=="clipPath")var r=e;else r=u("clipPath",l(this)),r.node.appendChild(e.node),!r.node.id&&h(r.node,{id:r.id});h(this.node,{"clip-path":p(r.node.id||r.id)})}}),t.on("snap.util.attr.fill",g("fill")),t.on("snap.util.attr.stroke",g("stroke"));var y=/^([lr])(?:\(([^)]*)\))?(.*)$/i;t.on("snap.util.grad.parse",function(t){t=d(t);var n=t.match(y);if(!n)return null;var r=n[1],i=n[2],s=n[3];return i=i.split(/\s*,\s*/).map(function(e){return+e==e?+e:e}),i.length==1&&i[0]==0&&(i=[]),s=s.split("-"),s=s.map(function(e){e=e.split(":");var t={color:e[0]};return e[1]&&(t.offset=parseFloat(e[1])),t}),{type:r,params:i,stops:s}}),t.on("snap.util.attr.d",function(n){t.stop(),f(n,"array")&&f(n[0],"array")&&(n=e.path.toString.call(n)),n=d(n),n.match(/[ruo]/i)&&(n=e.path.toAbsolute(n)),h(this.node,{d:n})})(-1),t.on("snap.util.attr.#text",function(e){t.stop(),e=d(e);var n=i.doc.createTextNode(e);while(this.node.firstChild)this.node.removeChild(this.node.firstChild);this.node.appendChild(n)})(-1),t.on("snap.util.attr.path",function(e){t.stop(),this.attr({d:e})})(-1),t.on("snap.util.attr.class",function(e){t.stop(),this.node.className.baseVal=e})(-1),t.on("snap.util.attr.viewBox",function(e){var n;f(e,"object")&&"x"in e?n=[e.x,e.y,e.width,e.height].join(" "):f(e,"array")?n=e.join(" "):n=e,h(this.node,{viewBox:n}),t.stop()})(-1),t.on("snap.util.attr.transform",function(e){this.transform(e),t.stop()})(-1),t.on("snap.util.attr.r",function(e){this.type=="rect"&&(t.stop(),h(this.node,{rx:e,ry:e}))})(-1),t.on("snap.util.attr.textpath",function(e){t.stop();if(this.type=="text"){var r,i,s;if(!e&&this.textPath){i=this.textPath;while(i.node.firstChild)this.node.appendChild(i.node.firstChild);i.remove(),delete this.textPath;return}if(f(e,"string")){var o=l(this),u=a(o.parentNode).path(e);o.appendChild(u.node),r=u.id,u.attr({id:r})}else e=a(e),e instanceof n&&(r=e.attr("id"),r||(r=e.id,e.attr({id:r})));if(r){i=this.textPath,s=this.node;if(i)i.attr({"xlink:href":"#"+r});else{i=h("textPath",{"xlink:href":"#"+r});while(s.firstChild)i.appendChild(s.firstChild);s.appendChild(i),this.textPath=a(i)}}}})(-1),t.on("snap.util.attr.text",function(e){if(this.type=="text"){var n=0,r=this.node,s=function(e){var t=h("tspan");if(f(e,"array"))for(var n=0;n<e.length;n++)t.appendChild(s(e[n]));else t.appendChild(i.doc.createTextNode(e));return t.normalize&&t.normalize(),t};while(r.firstChild)r.removeChild(r.firstChild);var o=s(e);while(o.firstChild)r.appendChild(o.firstChild)}t.stop()})(-1),t.on("snap.util.attr.fontSize",b)(-1),t.on("snap.util.attr.font-size",b)(-1),t.on("snap.util.getattr.transform",function(){return t.stop(),this.transform()})(-1),t.on("snap.util.getattr.textpath",function(){return t.stop(),this.textPath})(-1),function(){function n(n){return function(){t.stop();var r=i.doc.defaultView.getComputedStyle(this.node,null).getPropertyValue("marker-"+n);return r=="none"?r:e(i.doc.getElementById(r.match(c)[1]))}}function r(e){return function(n){t.stop();var r="marker"+e.charAt(0).toUpperCase()+e.substring(1);if(n==""||!n){this.node.style[r]="none";return}if(n.type=="marker"){var i=n.node.id;i||h(n.node,{id:n.id}),this.node.style[r]=p(i);return}}}t.on("snap.util.getattr.marker-end",n("end"))(-1),t.on("snap.util.getattr.markerEnd",n("end"))(-1),t.on("snap.util.getattr.marker-start",n("start"))(-1),t.on("snap.util.getattr.markerStart",n("start"))(-1),t.on("snap.util.getattr.marker-mid",n("mid"))(-1),t.on("snap.util.getattr.markerMid",n("mid"))(-1),t.on("snap.util.attr.marker-end",r("end"))(-1),t.on("snap.util.attr.markerEnd",r("end"))(-1),t.on("snap.util.attr.marker-start",r("start"))(-1),t.on("snap.util.attr.markerStart",r("start"))(-1),t.on("snap.util.attr.marker-mid",r("mid"))(-1),t.on("snap.util.attr.markerMid",r("mid"))(-1)}(),t.on("snap.util.getattr.r",function(){if(this.type=="rect"&&h(this.node,"rx")==h(this.node,"ry"))return t.stop(),h(this.node,"rx")})(-1),t.on("snap.util.getattr.text",function(){if(this.type=="text"||this.type=="tspan"){t.stop();var e=w(this.node);return e.length==1?e[0]:e}})(-1),t.on("snap.util.getattr.#text",function(){return this.node.textContent})(-1),t.on("snap.util.getattr.viewBox",function(){t.stop();var n=h(this.node,"viewBox");if(n)return n=n.split(v),e._.box(+n[0],+n[1],+n[2],+n[3]);return})(-1),t.on("snap.util.getattr.points",function(){var e=h(this.node,"points");t.stop();if(e)return e.split(v);return})(-1),t.on("snap.util.getattr.path",function(){var e=h(this.node,"d");return t.stop(),e})(-1),t.on("snap.util.getattr.class",function(){return this.node.className.baseVal})(-1),t.on("snap.util.getattr.fontSize",E)(-1),t.on("snap.util.getattr.font-size",E)(-1)}),r.plugin(function(e,t,n,r,i){var s=/\S+/g,o=/[\t\r\n\f]/g,u=/(^\s+|\s+$)/g,a=String,f=t.prototype;f.addClass=function(e){var t=a(e||"").match(s)||[],n=this.node,r=n.className.baseVal,i=r.match(s)||[],o,u,f,l;if(t.length){o=0;while(f=t[o++])u=i.indexOf(f),~u||i.push(f);l=i.join(" "),r!=l&&(n.className.baseVal=l)}return this},f.removeClass=function(e){var t=a(e||"").match(s)||[],n=this.node,r=n.className.baseVal,i=r.match(s)||[],o,u,f,l;if(i.length){o=0;while(f=t[o++])u=i.indexOf(f),~u&&i.splice(u,1);l=i.join(" "),r!=l&&(n.className.baseVal=l)}return this},f.hasClass=function(e){var t=this.node,n=t.className.baseVal,r=n.match(s)||[];return!!~r.indexOf(e)},f.toggleClass=function(e,t){if(t!=null)return t?this.addClass(e):this.removeClass(e);var n=(e||"").match(s)||[],r=this.node,i=r.className.baseVal,o=i.match(s)||[],u,a,f,l;u=0;while(f=n[u++])a=o.indexOf(f),~a?o.splice(a,1):o.push(f);return l=o.join(" "),i!=l&&(r.className.baseVal=l),this}}),r.plugin(function(e,n,r,i,s){function l(e){return e}function c(e){return function(t){return+t.toFixed(3)+e}}var o={"+":function(e,t){return e+t},"-":function(e,t){return e-t},"/":function(e,t){return e/t},"*":function(e,t){return e*t}},u=String,a=/[a-z]+$/i,f=/^\s*([+\-\/*])\s*=\s*([\d.eE+\-]+)\s*([^\d\s]+)?\s*$/;t.on("snap.util.attr",function(e){var n=u(e).match(f);if(n){var r=t.nt(),i=r.substring(r.lastIndexOf(".")+1),s=this.attr(i),l={};t.stop();var c=n[3]||"",h=s.match(a),p=o[n[1]];h&&h==c?e=p(parseFloat(s),+n[2]):(s=this.asPX(i),e=p(this.asPX(i),this.asPX(i,n[2]+c)));if(isNaN(s)||isNaN(e))return;l[i]=e,this.attr(l)}})(-10),t.on("snap.util.equal",function(e,n){var r,i,s=u(this.attr(e)||""),h=this,p=u(n).match(f);if(p){t.stop();var d=p[3]||"",v=s.match(a),m=o[p[1]];return v&&v==d?{from:parseFloat(s),to:m(parseFloat(s),+p[2]),f:c(v)}:(s=this.asPX(e),{from:s,to:m(s,this.asPX(e,p[2]+d)),f:l})}})(-10)}),r.plugin(function(n,r,i,s,o){var u=i.prototype,a=n.is;u.rect=function(e,t,n,r,i,s){var o;return s==null&&(s=i),a(e,"object")&&e=="[object Object]"?o=e:e!=null&&(o={x:e,y:t,width:n,height:r},i!=null&&(o.rx=i,o.ry=s)),this.el("rect",o)},u.circle=function(e,t,n){var r;return a(e,"object")&&e=="[object Object]"?r=e:e!=null&&(r={cx:e,cy:t,r:n}),this.el("circle",r)};var f=function(){function e(){this.parentNode.removeChild(this)}return function(t,n){var r=s.doc.createElement("img"),i=s.doc.body;r.style.cssText="position:absolute;left:-9999em;top:-9999em",r.onload=function(){n.call(r),r.onload=r.onerror=null,i.removeChild(r)},r.onerror=e,i.appendChild(r),r.src=t}}();u.image=function(e,t,r,i,s){var o=this.el("image");if(a(e,"object")&&"src"in e)o.attr(e);else if(e!=null){var u={"xlink:href":e,preserveAspectRatio:"none"};t!=null&&r!=null&&(u.x=t,u.y=r),i!=null&&s!=null?(u.width=i,u.height=s):f(e,function(){n._.$(o.node,{width:this.offsetWidth,height:this.offsetHeight})}),n._.$(o.node,u)}return o},u.ellipse=function(e,t,n,r){var i;return a(e,"object")&&e=="[object Object]"?i=e:e!=null&&(i={cx:e,cy:t,rx:n,ry:r}),this.el("ellipse",i)},u.path=function(e){var t;return a(e,"object")&&!a(e,"array")?t=e:e&&(t={d:e}),this.el("path",t)},u.group=u.g=function(e){var t,n=this.el("g");return arguments.length==1&&e&&!e.type?n.attr(e):arguments.length&&n.add(Array.prototype.slice.call(arguments,0)),n},u.svg=function(e,t,n,r,i,s,o,u){var f={};return a(e,"object")&&t==null?f=e:(e!=null&&(f.x=e),t!=null&&(f.y=t),n!=null&&(f.width=n),r!=null&&(f.height=r),i!=null&&s!=null&&o!=null&&u!=null&&(f.viewBox=[i,s,o,u])),this.el("svg",f)},u.mask=function(e){var t,n=this.el("mask");return arguments.length==1&&e&&!e.type?n.attr(e):arguments.length&&n.add(Array.prototype.slice.call(arguments,0)),n},u.ptrn=function(e,t,n,r,i,s,o,u){if(a(e,"object"))var f=e;else f={patternUnits:"userSpaceOnUse"},e&&(f.x=e),t&&(f.y=t),n!=null&&(f.width=n),r!=null&&(f.height=r),i!=null&&s!=null&&o!=null&&u!=null?f.viewBox=[i,s,o,u]:f.viewBox=[e||0,t||0,n||0,r||0];return this.el("pattern",f)},u.use=function(e){return e!=null?(e instanceof r&&(e.attr("id")||e.attr({id:n._.id(e)}),e=e.attr("id")),String(e).charAt()=="#"&&(e=e.substring(1)),this.el("use",{"xlink:href":"#"+e})):r.prototype.use.call(this)},u.symbol=function(e,t,n,r){var i={};return e!=null&&t!=null&&n!=null&&r!=null&&(i.viewBox=[e,t,n,r]),this.el("symbol",i)},u.text=function(e,t,n){var r={};return a(e,"object")?r=e:e!=null&&(r={x:e,y:t,text:n||""}),this.el("text",r)},u.line=function(e,t,n,r){var i={};return a(e,"object")?i=e:e!=null&&(i={x1:e,x2:n,y1:t,y2:r}),this.el("line",i)},u.polyline=function(e){arguments.length>1&&(e=Array.prototype.slice.call(arguments,0));var t={};return a(e,"object")&&!a(e,"array")?t=e:e!=null&&(t={points:e}),this.el("polyline",t)},u.polygon=function(e){arguments.length>1&&(e=Array.prototype.slice.call(arguments,0));var t={};return a(e,"object")&&!a(e,"array")?t=e:e!=null&&(t={points:e}),this.el("polygon",t)},function(){function i(){return this.selectAll("stop")}function s(e,t){var i=r("stop"),s={offset:+t+"%"};return e=n.color(e),s["stop-color"]=e.hex,e.opacity<1&&(s["stop-opacity"]=e.opacity),r(i,s),this.node.appendChild(i),this}function o(){if(this.type=="linearGradient"){var e=r(this.node,"x1")||0,t=r(this.node,"x2")||1,i=r(this.node,"y1")||0,s=r(this.node,"y2")||0;return n._.box(e,i,math.abs(t-e),math.abs(s-i))}var o=this.node.cx||.5,u=this.node.cy||.5,a=this.node.r||0;return n._.box(o-a,u-a,a*2,a*2)}function a(e,n){function h(e,t){var n=(t-a)/(e-c);for(var r=c;r<e;r++)o[r].offset=+(+a+n*(r-c)).toFixed(2);c=e,a=t}var i=t("snap.util.grad.parse",null,n).firstDefined(),s;if(!i)return null;i.params.unshift(e),i.type.toLowerCase()=="l"?s=f.apply(0,i.params):s=l.apply(0,i.params),i.type!=i.type.toLowerCase()&&r(s.node,{gradientUnits:"userSpaceOnUse"});var o=i.stops,u=o.length,a=0,c=0;u--;for(var p=0;p<u;p++)"offset"in o[p]&&h(p,o[p].offset);o[u].offset=o[u].offset||100,h(u,o[u].offset);for(p=0;p<=u;p++){var d=o[p];s.addStop(d.color,d.offset)}return s}function f(e,t,u,a,f){var l=n._.make("linearGradient",e);return l.stops=i,l.addStop=s,l.getBBox=o,t!=null&&r(l.node,{x1:t,y1:u,x2:a,y2:f}),l}function l(e,t,u,a,f,l){var c=n._.make("radialGradient",e);return c.stops=i,c.addStop=s,c.getBBox=o,t!=null&&r(c.node,{cx:t,cy:u,r:a}),f!=null&&l!=null&&r(c.node,{fx:f,fy:l}),c}var r=n._.$;u.gradient=function(e){return a(this.defs,e)},u.gradientLinear=function(e,t,n,r){return f(this.defs,e,t,n,r)},u.gradientRadial=function(e,t,n,r,i){return l(this.defs,e,t,n,r,i)},u.toString=function(){var e=this.node.ownerDocument,t=e.createDocumentFragment(),r=e.createElement("div"),i=this.node.cloneNode(!0),s;return t.appendChild(r),r.appendChild(i),n._.$(i,{xmlns:"http://www.w3.org/2000/svg"}),s=r.innerHTML,t.removeChild(t.firstChild),s},u.toDataURL=function(){if(e&&e.btoa)return"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(this)))},u.clear=function(){var e=this.node.firstChild,t;while(e)t=e.nextSibling,e.tagName!="defs"?e.parentNode.removeChild(e):u.clear.call({node:e}),e=t}}()}),r.plugin(function(e,t,n,r){function m(e){var t=m.ps=m.ps||{};return t[e]?t[e].sleep=100:t[e]={sleep:100},setTimeout(function(){for(var n in t)t[u](n)&&n!=e&&(t[n].sleep--,!t[n].sleep&&delete t[n])}),t[e]}function g(e,t,n,r){return e==null&&(e=t=n=r=0),t==null&&(t=e.y,n=e.width,r=e.height,e=e.x),{x:e,y:t,width:n,w:n,height:r,h:r,x2:e+n,y2:t+r,cx:e+n/2,cy:t+r/2,r1:l.min(n,r)/2,r2:l.max(n,r)/2,r0:l.sqrt(n*n+r*r)/2,path:R(e,t,n,r),vb:[e,t,n,r].join(" ")}}function y(){return this.join(",").replace(a,"$1")}function b(e){var t=o(e);return t.toString=y,t}function w(e,t,n,r,i,s,o,u,a){return a==null?O(e,t,n,r,i,s,o,u):N(e,t,n,r,i,s,o,u,M(e,t,n,r,i,s,o,u,a))}function E(n,r){function i(e){return+(+e).toFixed(3)}return e._.cacher(function(e,s,o){e instanceof t&&(e=e.attr("d")),e=Y(e);var u,a,f,l,c="",h={},p,d=0;for(var v=0,m=e.length;v<m;v++){f=e[v];if(f[0]=="M")u=+f[1],a=+f[2];else{l=w(u,a,f[1],f[2],f[3],f[4],f[5],f[6]);if(d+l>s){if(r&&!h.start){p=w(u,a,f[1],f[2],f[3],f[4],f[5],f[6],s-d),c+=["C"+i(p.start.x),i(p.start.y),i(p.m.x),i(p.m.y),i(p.x),i(p.y)];if(o)return c;h.start=c,c=["M"+i(p.x),i(p.y)+"C"+i(p.n.x),i(p.n.y),i(p.end.x),i(p.end.y),i(f[5]),i(f[6])].join(),d+=l,u=+f[5],a=+f[6];continue}if(!n&&!r)return p=w(u,a,f[1],f[2],f[3],f[4],f[5],f[6],s-d),p}d+=l,u=+f[5],a=+f[6]}c+=f.shift()+f}return h.end=c,p=n?d:r?h:N(u,a,f[0],f[1],f[2],f[3],f[4],f[5],1),p},null,e._.clone)}function N(e,t,n,r,i,s,o,u,a){var f=1-a,h=d(f,3),p=d(f,2),v=a*a,m=v*a,g=h*e+p*3*a*n+f*3*a*a*i+m*o,y=h*t+p*3*a*r+f*3*a*a*s+m*u,b=e+2*a*(n-e)+v*(i-2*n+e),w=t+2*a*(r-t)+v*(s-2*r+t),E=n+2*a*(i-n)+v*(o-2*i+n),S=r+2*a*(s-r)+v*(u-2*s+r),x=f*e+a*n,T=f*t+a*r,N=f*i+a*o,C=f*s+a*u,k=90-l.atan2(b-E,w-S)*180/c;return{x:g,y:y,m:{x:b,y:w},n:{x:E,y:S},start:{x:x,y:T},end:{x:N,y:C},alpha:k}}function C(t,n,r,i,s,o,u,a){e.is(t,"array")||(t=[t,n,r,i,s,o,u,a]);var f=G.apply(null,t);return g(f.min.x,f.min.y,f.max.x-f.min.x,f.max.y-f.min.y)}function k(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}function L(e,t){return e=g(e),t=g(t),k(t,e.x,e.y)||k(t,e.x2,e.y)||k(t,e.x,e.y2)||k(t,e.x2,e.y2)||k(e,t.x,t.y)||k(e,t.x2,t.y)||k(e,t.x,t.y2)||k(e,t.x2,t.y2)||(e.x<t.x2&&e.x>t.x||t.x<e.x2&&t.x>e.x)&&(e.y<t.y2&&e.y>t.y||t.y<e.y2&&t.y>e.y)}function A(e,t,n,r,i){var s=-3*t+9*n-9*r+3*i,o=e*s+6*t-12*n+6*r;return e*o-3*t+3*n}function O(e,t,n,r,i,s,o,u,a){a==null&&(a=1),a=a>1?1:a<0?0:a;var f=a/2,c=12,h=[-0.1252,.1252,-0.3678,.3678,-0.5873,.5873,-0.7699,.7699,-0.9041,.9041,-0.9816,.9816],p=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],d=0;for(var v=0;v<c;v++){var m=f*h[v]+f,g=A(m,e,n,i,o),y=A(m,t,r,s,u),b=g*g+y*y;d+=p[v]*l.sqrt(b)}return f*d}function M(e,t,n,r,i,s,o,u,a){if(a<0||O(e,t,n,r,i,s,o,u)<a)return;var f=1,l=f/2,c=f-l,h,p=.01;h=O(e,t,n,r,i,s,o,u,c);while(v(h-a)>p)l/=2,c+=(h<a?1:-1)*l,h=O(e,t,n,r,i,s,o,u,c);return c}function _(e,t,n,r,i,s,o,u){if(p(e,n)<h(i,o)||h(e,n)>p(i,o)||p(t,r)<h(s,u)||h(t,r)>p(s,u))return;var a=(e*r-t*n)*(i-o)-(e-n)*(i*u-s*o),f=(e*r-t*n)*(s-u)-(t-r)*(i*u-s*o),l=(e-n)*(s-u)-(t-r)*(i-o);if(!l)return;var c=a/l,d=f/l,v=+c.toFixed(2),m=+d.toFixed(2);if(v<+h(e,n).toFixed(2)||v>+p(e,n).toFixed(2)||v<+h(i,o).toFixed(2)||v>+p(i,o).toFixed(2)||m<+h(t,r).toFixed(2)||m>+p(t,r).toFixed(2)||m<+h(s,u).toFixed(2)||m>+p(s,u).toFixed(2))return;return{x:c,y:d}}function D(e,t){return H(e,t)}function P(e,t){return H(e,t,1)}function H(e,t,n){var r=C(e),i=C(t);if(!L(r,i))return n?0:[];var s=O.apply(0,e),o=O.apply(0,t),u=~~(s/8),a=~~(o/8),f=[],l=[],c={},h=n?0:[];for(var p=0;p<u+1;p++){var d=N.apply(0,e.concat(p/u));f.push({x:d.x,y:d.y,t:p/u})}for(p=0;p<a+1;p++)d=N.apply(0,t.concat(p/a)),l.push({x:d.x,y:d.y,t:p/a});for(p=0;p<u;p++)for(var m=0;m<a;m++){var g=f[p],y=f[p+1],b=l[m],w=l[m+1],E=v(y.x-g.x)<.001?"y":"x",S=v(w.x-b.x)<.001?"y":"x",x=_(g.x,g.y,y.x,y.y,b.x,b.y,w.x,w.y);if(x){if(c[x.x.toFixed(4)]==x.y.toFixed(4))continue;c[x.x.toFixed(4)]=x.y.toFixed(4);var T=g.t+v((x[E]-g[E])/(y[E]-g[E]))*(y.t-g.t),k=b.t+v((x[S]-b[S])/(w[S]-b[S]))*(w.t-b.t);T>=0&&T<=1&&k>=0&&k<=1&&(n?h++:h.push({x:x.x,y:x.y,t1:T,t2:k}))}}return h}function B(e,t){return F(e,t)}function j(e,t){return F(e,t,1)}function F(e,t,n){e=Y(e),t=Y(t);var r,i,s,o,u,a,f,l,c,h,p=n?0:[];for(var d=0,v=e.length;d<v;d++){var m=e[d];if(m[0]=="M")r=u=m[1],i=a=m[2];else{m[0]=="C"?(c=[r,i].concat(m.slice(1)),r=c[6],i=c[7]):(c=[r,i,r,i,u,a,u,a],r=u,i=a);for(var g=0,y=t.length;g<y;g++){var b=t[g];if(b[0]=="M")s=f=b[1],o=l=b[2];else{b[0]=="C"?(h=[s,o].concat(b.slice(1)),s=h[6],o=h[7]):(h=[s,o,s,o,f,l,f,l],s=f,o=l);var w=H(c,h,n);if(n)p+=w;else{for(var E=0,S=w.length;E<S;E++)w[E].segment1=d,w[E].segment2=g,w[E].bez1=c,w[E].bez2=h;p=p.concat(w)}}}}}return p}function I(e,t,n){var r=q(e);return k(r,t,n)&&F(e,[["M",t,n],["H",r.x2+10]],1)%2==1}function q(e){var t=m(e);if(t.bbox)return o(t.bbox);if(!e)return g();e=Y(e);var n=0,r=0,i=[],s=[],u;for(var a=0,f=e.length;a<f;a++){u=e[a];if(u[0]=="M")n=u[1],r=u[2],i.push(n),s.push(r);else{var l=G(n,r,u[1],u[2],u[3],u[4],u[5],u[6]);i=i.concat(l.min.x,l.max.x),s=s.concat(l.min.y,l.max.y),n=u[5],r=u[6]}}var c=h.apply(0,i),d=h.apply(0,s),v=p.apply(0,i),y=p.apply(0,s),b=g(c,d,v-c,y-d);return t.bbox=o(b),b}function R(e,t,n,r,i){if(i)return[["M",+e+ +i,t],["l",n-i*2,0],["a",i,i,0,0,1,i,i],["l",0,r-i*2],["a",i,i,0,0,1,-i,i],["l",i*2-n,0],["a",i,i,0,0,1,-i,-i],["l",0,i*2-r],["a",i,i,0,0,1,i,-i],["z"]];var s=[["M",e,t],["l",n,0],["l",0,r],["l",-n,0],["z"]];return s.toString=y,s}function U(e,t,n,r,i){i==null&&r==null&&(r=n),e=+e,t=+t,n=+n,r=+r;if(i!=null)var s=Math.PI/180,o=e+n*Math.cos(-r*s),u=e+n*Math.cos(-i*s),a=t+n*Math.sin(-r*s),f=t+n*Math.sin(-i*s),l=[["M",o,a],["A",n,n,0,+(i-r>180),0,u,f]];else l=[["M",e,t],["m",0,-r],["a",n,r,0,1,1,0,2*r],["a",n,r,0,1,1,0,-2*r],["z"]];return l.toString=y,l}function X(t){var n=m(t),r=String.prototype.toLowerCase;if(n.rel)return b(n.rel);if(!e.is(t,"array")||!e.is(t&&t[0],"array"))t=e.parsePathString(t);var i=[],s=0,o=0,u=0,a=0,f=0;t[0][0]=="M"&&(s=t[0][1],o=t[0][2],u=s,a=o,f++,i.push(["M",s,o]));for(var l=f,c=t.length;l<c;l++){var h=i[l]=[],p=t[l];if(p[0]!=r.call(p[0])){h[0]=r.call(p[0]);switch(h[0]){case"a":h[1]=p[1],h[2]=p[2],h[3]=p[3],h[4]=p[4],h[5]=p[5],h[6]=+(p[6]-s).toFixed(3),h[7]=+(p[7]-o).toFixed(3);break;case"v":h[1]=+(p[1]-o).toFixed(3);break;case"m":u=p[1],a=p[2];default:for(var d=1,v=p.length;d<v;d++)h[d]=+(p[d]-(d%2?s:o)).toFixed(3)}}else{h=i[l]=[],p[0]=="m"&&(u=p[1]+s,a=p[2]+o);for(var g=0,w=p.length;g<w;g++)i[l][g]=p[g]}var E=i[l].length;switch(i[l][0]){case"z":s=u,o=a;break;case"h":s+=+i[l][E-1];break;case"v":o+=+i[l][E-1];break;default:s+=+i[l][E-2],o+=+i[l][E-1]}}return i.toString=y,n.rel=b(i),i}function V(t){var n=m(t);if(n.abs)return b(n.abs);if(!s(t,"array")||!s(t&&t[0],"array"))t=e.parsePathString(t);if(!t||!t.length)return[["M",0,0]];var r=[],i=0,o=0,u=0,a=0,f=0,l;t[0][0]=="M"&&(i=+t[0][1],o=+t[0][2],u=i,a=o,f++,r[0]=["M",i,o]);var c=t.length==3&&t[0][0]=="M"&&t[1][0].toUpperCase()=="R"&&t[2][0].toUpperCase()=="Z";for(var h,p,d=f,v=t.length;d<v;d++){r.push(h=[]),p=t[d],l=p[0];if(l!=l.toUpperCase()){h[0]=l.toUpperCase();switch(h[0]){case"A":h[1]=p[1],h[2]=p[2],h[3]=p[3],h[4]=p[4],h[5]=p[5],h[6]=+p[6]+i,h[7]=+p[7]+o;break;case"V":h[1]=+p[1]+o;break;case"H":h[1]=+p[1]+i;break;case"R":var g=[i,o].concat(p.slice(1));for(var w=2,E=g.length;w<E;w++)g[w]=+g[w]+i,g[++w]=+g[w]+o;r.pop(),r=r.concat(et(g,c));break;case"O":r.pop(),g=U(i,o,p[1],p[2]),g.push(g[0]),r=r.concat(g);break;case"U":r.pop(),r=r.concat(U(i,o,p[1],p[2],p[3])),h=["U"].concat(r[r.length-1].slice(-2));break;case"M":u=+p[1]+i,a=+p[2]+o;default:for(w=1,E=p.length;w<E;w++)h[w]=+p[w]+(w%2?i:o)}}else if(l=="R")g=[i,o].concat(p.slice(1)),r.pop(),r=r.concat(et(g,c)),h=["R"].concat(p.slice(-2));else if(l=="O")r.pop(),g=U(i,o,p[1],p[2]),g.push(g[0]),r=r.concat(g);else if(l=="U")r.pop(),r=r.concat(U(i,o,p[1],p[2],p[3])),h=["U"].concat(r[r.length-1].slice(-2));else for(var S=0,x=p.length;S<x;S++)h[S]=p[S];l=l.toUpperCase();if(l!="O")switch(h[0]){case"Z":i=+u,o=+a;break;case"H":i=h[1];break;case"V":o=h[1];break;case"M":u=h[h.length-2],a=h[h.length-1];default:i=h[h.length-2],o=h[h.length-1]}}return r.toString=y,n.abs=b(r),r}function $(e,t,n,r){return[e,t,n,r,n,r]}function J(e,t,n,r,i,s){var o=1/3,u=2/3;return[o*e+u*n,o*t+u*r,o*i+u*n,o*s+u*r,i,s]}function K(t,n,r,i,s,o,u,a,f,h){var p=c*120/180,d=c/180*(+s||0),m=[],g,y=e._.cacher(function(e,t,n){var r=e*l.cos(n)-t*l.sin(n),i=e*l.sin(n)+t*l.cos(n);return{x:r,y:i}});if(!h){g=y(t,n,-d),t=g.x,n=g.y,g=y(a,f,-d),a=g.x,f=g.y;var b=l.cos(c/180*s),w=l.sin(c/180*s),E=(t-a)/2,S=(n-f)/2,x=E*E/(r*r)+S*S/(i*i);x>1&&(x=l.sqrt(x),r=x*r,i=x*i);var T=r*r,N=i*i,C=(o==u?-1:1)*l.sqrt(v((T*N-T*S*S-N*E*E)/(T*S*S+N*E*E))),k=C*r*S/i+(t+a)/2,L=C*-i*E/r+(n+f)/2,A=l.asin(((n-L)/i).toFixed(9)),O=l.asin(((f-L)/i).toFixed(9));A=t<k?c-A:A,O=a<k?c-O:O,A<0&&(A=c*2+A),O<0&&(O=c*2+O),u&&A>O&&(A-=c*2),!u&&O>A&&(O-=c*2)}else A=h[0],O=h[1],k=h[2],L=h[3];var M=O-A;if(v(M)>p){var _=O,D=a,P=f;O=A+p*(u&&O>A?1:-1),a=k+r*l.cos(O),f=L+i*l.sin(O),m=K(a,f,r,i,s,0,u,D,P,[O,_,k,L])}M=O-A;var H=l.cos(A),B=l.sin(A),j=l.cos(O),F=l.sin(O),I=l.tan(M/4),q=4/3*r*I,R=4/3*i*I,U=[t,n],z=[t+q*B,n-R*H],W=[a+q*F,f-R*j],X=[a,f];z[0]=2*U[0]-z[0],z[1]=2*U[1]-z[1];if(h)return[z,W,X].concat(m);m=[z,W,X].concat(m).join().split(",");var V=[];for(var $=0,J=m.length;$<J;$++)V[$]=$%2?y(m[$-1],m[$],d).y:y(m[$],m[$+1],d).x;return V}function Q(e,t,n,r,i,s,o,u,a){var f=1-a;return{x:d(f,3)*e+d(f,2)*3*a*n+f*3*a*a*i+d(a,3)*o,y:d(f,3)*t+d(f,2)*3*a*r+f*3*a*a*s+d(a,3)*u}}function G(e,t,n,r,i,s,o,u){var a=[],f=[[],[]],c,d,m,g,y,b,w,E;for(var S=0;S<2;++S){S==0?(d=6*e-12*n+6*i,c=-3*e+9*n-9*i+3*o,m=3*n-3*e):(d=6*t-12*r+6*s,c=-3*t+9*r-9*s+3*u,m=3*r-3*t);if(v(c)<1e-12){if(v(d)<1e-12)continue;g=-m/d,0<g&&g<1&&a.push(g);continue}w=d*d-4*m*c,E=l.sqrt(w);if(w<0)continue;y=(-d+E)/(2*c),0<y&&y<1&&a.push(y),b=(-d-E)/(2*c),0<b&&b<1&&a.push(b)}var x,T,N=a.length,C=N,k;while(N--)g=a[N],k=1-g,f[0][N]=k*k*k*e+3*k*k*g*n+3*k*g*g*i+g*g*g*o,f[1][N]=k*k*k*t+3*k*k*g*r+3*k*g*g*s+g*g*g*u;return f[0][C]=e,f[1][C]=t,f[0][C+1]=o,f[1][C+1]=u,f[0].length=f[1].length=C+2,{min:{x:h.apply(0,f[0]),y:h.apply(0,f[1])},max:{x:p.apply(0,f[0]),y:p.apply(0,f[1])}}}function Y(e,t){var n=!t&&m(e);if(!t&&n.curve)return b(n.curve);var r=V(e),i=t&&V(t),s={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},o={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},u=function(e,t,n){var r,i;if(!e)return["C",t.x,t.y,t.x,t.y,t.x,t.y];!(e[0]in{T:1,Q:1})&&(t.qx=t.qy=null);switch(e[0]){case"M":t.X=e[1],t.Y=e[2];break;case"A":e=["C"].concat(K.apply(0,[t.x,t.y].concat(e.slice(1))));break;case"S":n=="C"||n=="S"?(r=t.x*2-t.bx,i=t.y*2-t.by):(r=t.x,i=t.y),e=["C",r,i].concat(e.slice(1));break;case"T":n=="Q"||n=="T"?(t.qx=t.x*2-t.qx,t.qy=t.y*2-t.qy):(t.qx=t.x,t.qy=t.y),e=["C"].concat(J(t.x,t.y,t.qx,t.qy,e[1],e[2]));break;case"Q":t.qx=e[1],t.qy=e[2],e=["C"].concat(J(t.x,t.y,e[1],e[2],e[3],e[4]));break;case"L":e=["C"].concat($(t.x,t.y,e[1],e[2]));break;case"H":e=["C"].concat($(t.x,t.y,e[1],t.y));break;case"V":e=["C"].concat($(t.x,t.y,t.x,e[1]));break;case"Z":e=["C"].concat($(t.x,t.y,t.X,t.Y))}return e},a=function(e,t){if(e[t].length>7){e[t].shift();var n=e[t];while(n.length)c[t]="A",i&&(h[t]="A"),e.splice(t++,0,["C"].concat(n.splice(0,6)));e.splice(t,1),y=p(r.length,i&&i.length||0)}},l=function(e,t,n,s,o){e&&t&&e[o][0]=="M"&&t[o][0]!="M"&&(t.splice(o,0,["M",s.x,s.y]),n.bx=0,n.by=0,n.x=e[o][1],n.y=e[o][2],y=p(r.length,i&&i.length||0))},c=[],h=[],d="",v="";for(var g=0,y=p(r.length,i&&i.length||0);g<y;g++){r[g]&&(d=r[g][0]),d!="C"&&(c[g]=d,g&&(v=c[g-1])),r[g]=u(r[g],s,v),c[g]!="A"&&d=="C"&&(c[g]="C"),a(r,g),i&&(i[g]&&(d=i[g][0]),d!="C"&&(h[g]=d,g&&(v=h[g-1])),i[g]=u(i[g],o,v),h[g]!="A"&&d=="C"&&(h[g]="C"),a(i,g)),l(r,i,s,o,g),l(i,r,o,s,g);var w=r[g],E=i&&i[g],S=w.length,x=i&&E.length;s.x=w[S-2],s.y=w[S-1],s.bx=f(w[S-4])||s.x,s.by=f(w[S-3])||s.y,o.bx=i&&(f(E[x-4])||o.x),o.by=i&&(f(E[x-3])||o.y),o.x=i&&E[x-2],o.y=i&&E[x-1]}return i||(n.curve=b(r)),i?[r,i]:r}function Z(e,t){if(!t)return e;var n,r,i,s,o,u,a;e=Y(e);for(i=0,o=e.length;i<o;i++){a=e[i];for(s=1,u=a.length;s<u;s+=2)n=t.x(a[s],a[s+1]),r=t.y(a[s],a[s+1]),a[s]=n,a[s+1]=r}return e}function et(e,t){var n=[];for(var r=0,i=e.length;i-2*!t>r;r+=2){var s=[{x:+e[r-2],y:+e[r-1]},{x:+e[r],y:+e[r+1]},{x:+e[r+2],y:+e[r+3]},{x:+e[r+4],y:+e[r+5]}];t?r?i-4==r?s[3]={x:+e[0],y:+e[1]}:i-2==r&&(s[2]={x:+e[0],y:+e[1]},s[3]={x:+e[2],y:+e[3]}):s[0]={x:+e[i-2],y:+e[i-1]}:i-4==r?s[3]=s[2]:r||(s[0]={x:+e[r],y:+e[r+1]}),n.push(["C",(-s[0].x+6*s[1].x+s[2].x)/6,(-s[0].y+6*s[1].y+s[2].y)/6,(s[1].x+6*s[2].x-s[3].x)/6,(s[1].y+6*s[2].y-s[3].y)/6,s[2].x,s[2].y])}return n}var i=t.prototype,s=e.is,o=e._.clone,u="hasOwnProperty",a=/,?([a-z]),?/gi,f=parseFloat,l=Math,c=l.PI,h=l.min,p=l.max,d=l.pow,v=l.abs,S=E(1),x=E(),T=E(0,1),z=e._unit2px,W={path:function(e){return e.attr("path")},circle:function(e){var t=z(e);return U(t.cx,t.cy,t.r)},ellipse:function(e){var t=z(e);return U(t.cx||0,t.cy||0,t.rx,t.ry)},rect:function(e){var t=z(e);return R(t.x||0,t.y||0,t.width,t.height,t.rx,t.ry)},image:function(e){var t=z(e);return R(t.x||0,t.y||0,t.width,t.height)},line:function(e){return"M"+[e.attr("x1")||0,e.attr("y1")||0,e.attr("x2"),e.attr("y2")]},polyline:function(e){return"M"+e.attr("points")},polygon:function(e){return"M"+e.attr("points")+"z"},deflt:function(e){var t=e.node.getBBox();return R(t.x,t.y,t.width,t.height)}};e.path=m,e.path.getTotalLength=S,e.path.getPointAtLength=x,e.path.getSubpath=function(e,t,n){if(this.getTotalLength(e)-n<1e-6)return T(e,t).end;var r=T(e,n,1);return t?T(r,t).end:r},i.getTotalLength=function(){if(this.node.getTotalLength)return this.node.getTotalLength()},i.getPointAtLength=function(e){return x(this.attr("d"),e)},i.getSubpath=function(t,n){return e.path.getSubpath(this.attr("d"),t,n)},e._.box=g,e.path.findDotsAtSegment=N,e.path.bezierBBox=C,e.path.isPointInsideBBox=k,e.closest=function(t,n,r,i){var s=100,o=g(t-s/2,n-s/2,s,s),u=[],a=r[0].hasOwnProperty("x")?function(e){return{x:r[e].x,y:r[e].y}}:function(e){return{x:r[e],y:i[e]}},f=0;while(s<=1e6&&!f){for(var l=0,c=r.length;l<c;l++){var h=a(l);if(k(o,h.x,h.y)){f++,u.push(h);break}}f||(s*=2,o=g(t-s/2,n-s/2,s,s))}if(s==1e6)return;var p=Infinity,d;for(l=0,c=u.length;l<c;l++){var v=e.len(t,n,u[l].x,u[l].y);p>v&&(p=v,u[l].len=v,d=u[l])}return d},e.path.isBBoxIntersect=L,e.path.intersection=B,e.path.intersectionNumber=j,e.path.isPointInside=I,e.path.getBBox=q,e.path.get=W,e.path.toRelative=X,e.path.toAbsolute=V,e.path.toCubic=Y,e.path.map=Z,e.path.toString=y,e.path.clone=b}),r.plugin(function(e,r,i,s){var o=Math.max,u=Math.min,a=function(e){this.items=[],this.bindings={},this.length=0,this.type="set";if(e)for(var t=0,n=e.length;t<n;t++)e[t]&&(this[this.items.length]=this.items[this.items.length]=e[t],this.length++)},f=a.prototype;f.push=function(){var e,t;for(var n=0,r=arguments.length;n<r;n++)e=arguments[n],e&&(t=this.items.length,this[t]=this.items[t]=e,this.length++);return this},f.pop=function(){return this.length&&delete this[this.length--],this.items.pop()},f.forEach=function(e,t){for(var n=0,r=this.items.length;n<r;n++)if(e.call(t,this.items[n],n)===!1)return this;return this},f.animate=function(r,i,s,o){typeof s=="function"&&!s.length&&(o=s,s=n.linear),r instanceof e._.Animation&&(o=r.callback,s=r.easing,i=s.dur,r=r.attr);var u=arguments;if(e.is(r,"array")&&e.is(u[u.length-1],"array"))var a=!0;var f,l=function(){f?this.b=f:f=this.b},c=0,h=this,p=o&&function(){++c==h.length&&o.call(this)};return this.forEach(function(e,n){t.once("snap.animcreated."+e.id,l),a?u[n]&&e.animate.apply(e,u[n]):e.animate(r,i,s,p)})},f.remove=function(){while(this.length)this.pop().remove();return this},f.bind=function(e,t,n){var r={};if(typeof t=="function")this.bindings[e]=t;else{var i=n||e;this.bindings[e]=function(e){r[i]=e,t.attr(r)}}return this},f.attr=function(e){var t={};for(var n in e)this.bindings[n]?this.bindings[n](e[n]):t[n]=e[n];for(var r=0,i=this.items.length;r<i;r++)this.items[r].attr(t);return this},f.clear=function(){while(this.length)this.pop()},f.splice=function(e,t,n){e=e<0?o(this.length+e,0):e,t=o(0,u(this.length-e,t));var r=[],i=[],s=[],f;for(f=2;f<arguments.length;f++)s.push(arguments[f]);for(f=0;f<t;f++)i.push(this[e+f]);for(;f<this.length-e;f++)r.push(this[e+f]);var l=s.length;for(f=0;f<l+r.length;f++)this.items[e+f]=this[e+f]=f<l?s[f]:r[f-l];f=this.items.length=this.length-=t-l;while(this[f])delete this[f++];return new a(i)},f.exclude=function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]==e)return this.splice(t,1),!0;return!1},f.insertAfter=function(e){var t=this.items.length;while(t--)this.items[t].insertAfter(e);return this},f.getBBox=function(){var e=[],t=[],n=[],r=[];for(var i=this.items.length;i--;)if(!this.items[i].removed){var s=this.items[i].getBBox();e.push(s.x),t.push(s.y),n.push(s.x+s.width),r.push(s.y+s.height)}return e=u.apply(0,e),t=u.apply(0,t),n=o.apply(0,n),r=o.apply(0,r),{x:e,y:t,x2:n,y2:r,width:n-e,height:r-t,cx:e+(n-e)/2,cy:t+(r-t)/2}},f.clone=function(e){e=new a;for(var t=0,n=this.items.length;t<n;t++)e.push(this.items[t].clone());return e},f.toString=function(){return"Snap‘s set"},f.type="set",e.Set=a,e.set=function(){var e=new a;return arguments.length&&e.push.apply(e,Array.prototype.slice.call(arguments,0)),e}}),r.plugin(function(e,n,r,i){function a(e){var t=e[0];switch(t.toLowerCase()){case"t":return[t,0,0];case"m":return[t,1,0,0,1,0,0];case"r":return e.length==4?[t,0,e[2],e[3]]:[t,0];case"s":return e.length==5?[t,1,1,e[3],e[4]]:e.length==3?[t,1,1]:[t,1]}}function f(t,n,r){n=u(n).replace(/\.{3}|\u2026/g,t),t=e.parseTransformString(t)||[],n=e.parseTransformString(n)||[];var i=Math.max(t.length,n.length),s=[],o=[],f=0,l,c,h,p;for(;f<i;f++){h=t[f]||a(n[f]),p=n[f]||a(h);if(h[0]!=p[0]||h[0].toLowerCase()=="r"&&(h[2]!=p[2]||h[3]!=p[3])||h[0].toLowerCase()=="s"&&(h[3]!=p[3]||h[4]!=p[4])){t=e._.transform2matrix(t,r()),n=e._.transform2matrix(n,r()),s=[["m",t.a,t.b,t.c,t.d,t.e,t.f]],o=[["m",n.a,n.b,n.c,n.d,n.e,n.f]];break}s[f]=[],o[f]=[];for(l=0,c=Math.max(h.length,p.length);l<c;l++)l in h&&(s[f][l]=h[l]),l in p&&(o[f][l]=p[l])}return{from:v(s),to:v(o),f:d(s)}}function l(e){return e}function c(e){return function(t){return+t.toFixed(3)+e}}function h(e){return e.join(" ")}function p(t){return e.rgb(t[0],t[1],t[2])}function d(e){var t=0,n,r,i,s,o,u,a=[];for(n=0,r=e.length;n<r;n++){o="[",u=['"'+e[n][0]+'"'];for(i=1,s=e[n].length;i<s;i++)u[i]="val["+t++ +"]";o+=u+"]",a[n]=o}return Function("val","return Snap.path.toString.call(["+a+"])")}function v(e){var t=[];for(var n=0,r=e.length;n<r;n++)for(var i=1,s=e[n].length;i<s;i++)t.push(e[n][i]);return t}function m(e){return isFinite(parseFloat(e))}function g(t,n){return!e.is(t,"array")||!e.is(n,"array")?!1:t.toString()==n.toString()}var s={},o=/[a-z]+$/i,u=String;s.stroke=s.fill="colour",n.prototype.equal=function(e,n){return t("snap.util.equal",this,e,n).firstDefined()},t.on("snap.util.equal",function(t,n){var r,i,a=u(this.attr(t)||""),y=this;if(m(a)&&m(n))return{from:parseFloat(a),to:parseFloat(n),f:l};if(s[t]=="colour")return r=e.color(a),i=e.color(n),{from:[r.r,r.g,r.b,r.opacity],to:[i.r,i.g,i.b,i.opacity],f:p};if(t=="viewBox")return r=this.attr(t).vb.split(" ").map(Number),i=n.split(" ").map(Number),{from:r,to:i,f:h};if(t=="transform"||t=="gradientTransform"||t=="patternTransform")return n instanceof e.Matrix&&(n=n.toTransformString()),e._.rgTransform.test(n)||(n=e._.svgTransform2string(n)),f(a,n,function(){return y.getBBox(1)});if(t=="d"||t=="path")return r=e.path.toCubic(a,n),{from:v(r[0]),to:v(r[1]),f:d(r[0])};if(t=="points")return r=u(a).split(e._.separator),i=u(n).split(e._.separator),{from:r,to:i,f:function(e){return e}};var b=a.match(o),w=u(n).match(o);return b&&g(b,w)?{from:parseFloat(a),to:parseFloat(n),f:c(b)}:{from:this.asPX(t),to:this.asPX(t,n),f:l}})}),r.plugin(function(e,n,r,i){var s=n.prototype,o="hasOwnProperty",u="createTouch"in i.doc,a=["click","dblclick","mousedown","mousemove","mouseout","mouseover","mouseup","touchstart","touchmove","touchend","touchcancel"],f={mousedown:"touchstart",mousemove:"touchmove",mouseup:"touchend"},l=function(e,t){var n=e=="y"?"scrollTop":"scrollLeft",r=t&&t.node?t.node.ownerDocument:i.doc;return r[n in r.documentElement?"documentElement":"body"][n]},c=function(){this.returnValue=!1},h=function(){return this.originalEvent.preventDefault()},p=function(){this.cancelBubble=!0},d=function(){return this.originalEvent.stopPropagation()},v=function(e,t,n,r){var i=u&&f[t]?f[t]:t,s=function(i){var s=l("y",r),a=l("x",r);if(u&&f[o](t))for(var c=0,p=i.targetTouches&&i.targetTouches.length;c<p;c++)if(i.targetTouches[c].target==e||e.contains(i.targetTouches[c].target)){var v=i;i=i.targetTouches[c],i.originalEvent=v,i.preventDefault=h,i.stopPropagation=d;break}var m=i.clientX+a,g=i.clientY+s;return n.call(r,i,m,g)};return t!==i&&e.addEventListener(t,s,!1),e.addEventListener(i,s,!1),function(){return t!==i&&e.removeEventListener(t,s,!1),e.removeEventListener(i,s,!1),!0}},m=[],g=function(e){var n=e.clientX,r=e.clientY,i=l("y"),s=l("x"),o,a=m.length;while(a--){o=m[a];if(u){var f=e.touches&&e.touches.length,c;while(f--){c=e.touches[f];if(c.identifier==o.el._drag.id||o.el.node.contains(c.target)){n=c.clientX,r=c.clientY,(e.originalEvent?e.originalEvent:e).preventDefault();break}}}else e.preventDefault();var h=o.el.node,p,d=h.nextSibling,v=h.parentNode,g=h.style.display;n+=s,r+=i,t("snap.drag.move."+o.el.id,o.move_scope||o.el,n-o.el._drag.x,r-o.el._drag.y,n,r,e)}},y=function(n){e.unmousemove(g).unmouseup(y);var r=m.length,i;while(r--)i=m[r],i.el._drag={},t("snap.drag.end."+i.el.id,i.end_scope||i.start_scope||i.move_scope||i.el,n),t.off("snap.drag.*."+i.el.id);m=[]};for(var b=a.length;b--;)(function(t){e[t]=s[t]=function(n,r){if(e.is(n,"function"))this.events=this.events||[],this.events.push({name:t,f:n,unbind:v(this.node||document,t,n,r||this)});else for(var i=0,s=this.events.length;i<s;i++)if(this.events[i].name==t)try{this.events[i].f.call(this)}catch(o){}return this},e["un"+t]=s["un"+t]=function(e){var n=this.events||[],r=n.length;while(r--)if(n[r].name==t&&(n[r].f==e||!e))return n[r].unbind(),n.splice(r,1),!n.length&&delete this.events,this;return this}})(a[b]);s.hover=function(e,t,n,r){return this.mouseover(e,n).mouseout(t,r||n)},s.unhover=function(e,t){return this.unmouseover(e).unmouseout(t)};var w=[];s.drag=function(n,r,i,s,o,u){function l(f,l,c){(f.originalEvent||f).preventDefault(),a._drag.x=l,a._drag.y=c,a._drag.id=f.identifier,!m.length&&e.mousemove(g).mouseup(y),m.push({el:a,move_scope:s,start_scope:o,end_scope:u}),r&&t.on("snap.drag.start."+a.id,r),n&&t.on("snap.drag.move."+a.id,n),i&&t.on("snap.drag.end."+a.id,i),t("snap.drag.start."+a.id,o||s||a,l,c,f)}function c(e,n,r){t("snap.draginit."+a.id,a,e,n,r)}var a=this;if(!arguments.length){var f;return a.drag(function(e,t){this.attr({transform:f+(f?"T":"t")+[e,t]})},function(){f=this.transform().local})}return t.on("snap.draginit."+a.id,l),a._drag={},w.push({el:a,start:l,init:c}),a.mousedown(c),a},s.undrag=function(){var n=w.length;while(n--)w[n].el==this&&(this.unmousedown(w[n].init),w.splice(n,1),t.unbind("snap.drag.*."+this.id),t.unbind("snap.draginit."+this.id));return!w.length&&e.unmousemove(g).unmouseup(y),this}}),r.plugin(function(e,n,r,i){var s=n.prototype,o=r.prototype,u=/^\s*url\((.+)\)/,a=String,f=e._.$;e.filter={},o.filter=function(t){var r=this;r.type!="svg"&&(r=r.paper);var i=e.parse(a(t)),s=e._.id(),o=r.node.offsetWidth,u=r.node.offsetHeight,l=f("filter");return f(l,{id:s,filterUnits:"userSpaceOnUse"}),l.appendChild(i.node),r.defs.appendChild(l),new n(l)},t.on("snap.util.getattr.filter",function(){t.stop();var n=f(this.node,"filter");if(n){var r=a(n).match(u);return r&&e.select(r[1])}}),t.on("snap.util.attr.filter",function(r){if(r instanceof n&&r.type=="filter"){t.stop();var i=r.node.id;i||(f(r.node,{id:r.id}),i=r.id),f(this.node,{filter:e.url(i)})}if(!r||r=="none")t.stop(),this.node.removeAttribute("filter")}),e.filter.blur=function(t,n){t==null&&(t=2);var r=n==null?t:[t,n];return e.format('<feGaussianBlur stdDeviation="{def}"/>',{def:r})},e.filter.blur.toString=function(){return this()},e.filter.shadow=function(t,n,r,i,s){return typeof r=="string"&&(i=r,s=i,r=4),typeof i!="string"&&(s=i,i="#000"),i=i||"#000",r==null&&(r=4),s==null&&(s=1),t==null&&(t=0,n=2),n==null&&(n=t),i=e.color(i),e.format('<feGaussianBlur in="SourceAlpha" stdDeviation="{blur}"/><feOffset dx="{dx}" dy="{dy}" result="offsetblur"/><feFlood flood-color="{color}"/><feComposite in2="offsetblur" operator="in"/><feComponentTransfer><feFuncA type="linear" slope="{opacity}"/></feComponentTransfer><feMerge><feMergeNode/><feMergeNode in="SourceGraphic"/></feMerge>',{color:i,dx:t,dy:n,blur:r,opacity:s})},e.filter.shadow.toString=function(){return this()},e.filter.grayscale=function(t){return t==null&&(t=1),e.format('<feColorMatrix type="matrix" values="{a} {b} {c} 0 0 {d} {e} {f} 0 0 {g} {b} {h} 0 0 0 0 0 1 0"/>',{a:.2126+.7874*(1-t),b:.7152-.7152*(1-t),c:.0722-.0722*(1-t),d:.2126-.2126*(1-t),e:.7152+.2848*(1-t),f:.0722-.0722*(1-t),g:.2126-.2126*(1-t),h:.0722+.9278*(1-t)})},e.filter.grayscale.toString=function(){return this()},e.filter.sepia=function(t){return t==null&&(t=1),e.format('<feColorMatrix type="matrix" values="{a} {b} {c} 0 0 {d} {e} {f} 0 0 {g} {h} {i} 0 0 0 0 0 1 0"/>',{a:.393+.607*(1-t),b:.769-.769*(1-t),c:.189-.189*(1-t),d:.349-.349*(1-t),e:.686+.314*(1-t),f:.168-.168*(1-t),g:.272-.272*(1-t),h:.534-.534*(1-t),i:.131+.869*(1-t)})},e.filter.sepia.toString=function(){return this()},e.filter.saturate=function(t){return t==null&&(t=1),e.format('<feColorMatrix type="saturate" values="{amount}"/>',{amount:1-t})},e.filter.saturate.toString=function(){return this()},e.filter.hueRotate=function(t){return t=t||0,e.format('<feColorMatrix type="hueRotate" values="{angle}"/>',{angle:t})},e.filter.hueRotate.toString=function(){return this()},e.filter.invert=function(t){return t==null&&(t=1),e.format('<feComponentTransfer><feFuncR type="table" tableValues="{amount} {amount2}"/><feFuncG type="table" tableValues="{amount} {amount2}"/><feFuncB type="table" tableValues="{amount} {amount2}"/></feComponentTransfer>',{amount:t,amount2:1-t})},e.filter.invert.toString=function(){return this()},e.filter.brightness=function(t){return t==null&&(t=1),e.format('<feComponentTransfer><feFuncR type="linear" slope="{amount}"/><feFuncG type="linear" slope="{amount}"/><feFuncB type="linear" slope="{amount}"/></feComponentTransfer>',{amount:t})},e.filter.brightness.toString=function(){return this()},e.filter.contrast=function(t){return t==null&&(t=1),e.format('<feComponentTransfer><feFuncR type="linear" slope="{amount}" intercept="{amount2}"/><feFuncG type="linear" slope="{amount}" intercept="{amount2}"/><feFuncB type="linear" slope="{amount}" intercept="{amount2}"/></feComponentTransfer>',{amount:t,amount2:.5-t/2})},e.filter.contrast.toString=function(){return this()}}),r.plugin(function(e,t,n,r,i){var s=e._.box,o=e.is,u=/^[^a-z]*([tbmlrc])/i,a=function(){return"T"+this.dx+","+this.dy};t.prototype.getAlign=function(e,t){t==null&&o(e,"string")&&(t=e,e=null),e=e||this.paper;var n=e.getBBox?e.getBBox():s(e),r=this.getBBox(),i={};t=t&&t.match(u),t=t?t[1].toLowerCase():"c";switch(t){case"t":i.dx=0,i.dy=n.y-r.y;break;case"b":i.dx=0,i.dy=n.y2-r.y2;break;case"m":i.dx=0,i.dy=n.cy-r.cy;break;case"l":i.dx=n.x-r.x,i.dy=0;break;case"r":i.dx=n.x2-r.x2,i.dy=0;break;default:i.dx=n.cx-r.cx,i.dy=0}return i.toString=a,i},t.prototype.align=function(e,t){return this.transform("..."+this.getAlign(e,t))}}),r}),define("shared/utils/ResizeManager",["underscore","superhero"],function(e,t){var n=t.Module.extend({initialize:function(){e.bindAll(this,"_windowResizeHandler"),this._updateValues(),this.setupEventListeners()},setupEventListeners:function(){var e=Settings.isTablet?"orientationchange":"resize";window.addEventListener(e,this._windowResizeHandler)},viewportWidth:function(){return this._viewportWidth||0},viewportHeight:function(){return this._viewportHeight||0},documentWidth:function(){var e=document.body,t=document.documentElement;return Math.max(e.scrollWidth,e.offsetWidth,t.clientWidth,t.scrollWidth,t.offsetWidth)},documentHeight:function(){var e=document.body,t=document.documentElement;return Math.max(e.scrollHeight,e.offsetHeight,t.clientHeight,t.scrollHeight,t.offsetHeight)},mainWidth:function(){return this._viewportWidth||0},mainHeight:function(){return this._viewportHeight||0},forceResize:function(){this._updateValues(),this.trigger("resize",{target:this})},_updateValues:function(){this._viewportWidth=window.innerWidth,this._viewportHeight=window.innerHeight},_windowResizeHandler:function(e){this._updateValues(),this.trigger("resize",{target:this})}});return new n}),define("shared/utils/GestureManager",["underscore","superhero"],function(e,t){var n=t.Module.extend({_scrollings:[],_mousewheelEvent:/Firefox/i.test(navigator.userAgent)?"DOMMouseScroll":"mousewheel",initialize:function(){e.bindAll(this,"_mouseDownHandler","_mouseMoveHandler","_mouseUpHandler","_touchStartHandler","_touchMoveHandler","_touchEndHandler","_mousewheelHandler"),this._isDisabled={x:!0,y:!1},this._touchStartPosition={x:0,y:0},this._touchPreviousPosition={x:0,y:0},this._touchCurrentPosition={x:0,y:0}},setElement:function(e){this.el&&this._reset(),this.el=e,this._setupEventListeners()},onClose:function(){this._closeEventListeners()},_setupEventListeners:function(){this._touchmoveThrottle=e.throttle(this._touchMoveHandler,30),this._mousemoveThrottle=e.throttle(this._mouseMoveHandler,30),Settings.device.type==="mobile"||Settings.device.type==="tablet"?(this.el.addEventListener("touchstart",this._touchStartHandler),this.el.addEventListener("touchmove",this._touchmoveThrottle),this.el.addEventListener("touchend",this._touchEndHandler)):(this.el.addEventListener("mousedown",this._mouseDownHandler),this.el.addEventListener("mousemove",this._mousemoveThrottle),this.el.addEventListener("mouseup",this._mouseUpHandler)),this.el.attachEvent?this.el.attachEvent("on"+this._mousewheelEvent,this._mousewheelHandler):this.el.addEventListener&&this.el.addEventListener(this._mousewheelEvent,this._mousewheelHandler)},_closeEventListeners:function(){Settings.device.type==="mobile"||Settings.device.type==="tablet"?(this.el.removeEventListener("touchstart",this._touchStartHandler),this.el.removeEventListener("touchmove",this._touchmoveThrottle),this.el.removeEventListener("touchend",this._touchEndHandler)):(this.el.removeEventListener("mousedown",this._mouseDownHandler),this.el.removeEventListener("mousemove",this._mousemoveThrottle),this.el.removeEventListener("mouseup",this._mouseUpHandler)),this.el.attachEvent?this.el.detachEvent("on"+this._mousewheelEvent,this._mousewheelHandler):this.el.addEventListener&&this.el.removeEventListener(this._mousewheelEvent,this._mousewheelHandler)},disableSlide:function(e){e=e||"y",this._isDisabled[e]=!0},enableSlide:function(e){e=e||"y",this._isDisabled[e]=!1},_reset:function(){this._closeEventListeners()},_slideStart:function(e){this._touchPreviousPosition.x=this._touchStartPosition.x,this._touchPreviousPosition.y=this._touchStartPosition.y;if(this._isDisabled.y&&this._isDisabled.x)return;var t=this._closest(e.target,"prevent-gesture",5);if(t)return;this._isActive=!0},_slideMove:function(e){if(!this._isActive)return;if(!this._isDisabled.x){var t=this._touchPreviousPosition.x-this._touchCurrentPosition.x,n=Math.max(-1,Math.min(1,t));t>0?this.trigger("slide:right:moving",{delta:-t,direction:-n}):this.trigger("slide:left:moving",{delta:-t,direction:-n})}if(!this._isDisabled.y){var r=this._touchPreviousPosition.y-this._touchCurrentPosition.y,n=Math.max(-1,Math.min(1,r));r>0?this.trigger("slide:up:moving",{delta:-r,direction:-n}):this.trigger("slide:down:moving",{delta:-r,direction:-n})}this._touchPreviousPosition.x=this._touchCurrentPosition.x,this._touchPreviousPosition.y=this._touchCurrentPosition.y},_slideEnd:function(e){if(!this._isActive)return;if(!this._isDisabled.x){var t=this._touchPreviousPosition.x-this._touchCurrentPosition.x,n=Math.max(-1,Math.min(1,t));t>0?this.trigger("slide:right:end",{delta:-t,direction:-n}):this.trigger("slide:left:end",{delta:-t,direction:-n})}if(!this._isDisabled.y){var r=this._touchPreviousPosition.y-this._touchCurrentPosition.y,n=Math.max(-1,Math.min(1,r));r>0?this.trigger("slide:up:end",{delta:-r,direction:-n}):this.trigger("slide:down:end",{delta:-r,direction:-n})}this._touchPreviousPosition.x=this._touchCurrentPosition.x,this._touchPreviousPosition.y=this._touchCurrentPosition.y,this._isActive=!1},_mouseDownHandler:function(e){this._touchStartPosition.x=e.pageX,this._touchStartPosition.y=e.pageY,this._slideStart(e)},_mouseMoveHandler:function(e){this._touchCurrentPosition.x=e.pageX,this._touchCurrentPosition.y=e.pageY,this._slideMove(e)},_mouseUpHandler:function(e){this._touchCurrentPosition.x=e.pageX,this._touchCurrentPosition.y=e.pageY,this._slideEnd(e)},_touchStartHandler:function(e){this._touchStartPosition.x=e.changedTouches[0].pageX,this._touchStartPosition.y=e.changedTouches[0].pageY,this._slideStart(e)},_touchMoveHandler:function(e){e.preventDefault(),this._touchCurrentPosition.x=e.changedTouches[0].pageX,this._touchCurrentPosition.y=e.changedTouches[0].pageY,this._slideMove(e)},_touchEndHandler:function(e){this._touchCurrentPosition.x=e.changedTouches[0].pageX,this._touchCurrentPosition.y=e.changedTouches[0].pageY,this._slideEnd(e)},_mousewheelHandler:function(e){function l(e,t){var n=0,r=e.slice(Math.max(e.length-t,1));for(var i=0;i<r.length;i++)n+=r[i];return Math.ceil(n/t)}e=window.event||e||e.originalEvent;var t=e.target||e.srcTarget||e.srcElement,n=this._closest(t,"prevent-gesture",5);if(n)return;if(this._isDisabled.y)return;var r=(new Date).getTime(),i=e.wheelDelta?e.wheelDelta/120:-e.detail/3,s=Math.max(-1,Math.min(1,i));this._scrollings.length>149&&this._scrollings.shift(),this._scrollings.push(Math.abs(i));var o=r-this._prevTime;this._prevTime=r,o>200&&(this._scrollings=[]);var u=l(this._scrollings,10),a=l(this._scrollings,70),f=u>=a;return f&&(s<0?this.trigger("scroll:up",{delta:i,direction:-s}):this.trigger("scroll:down",{delta:i,direction:-s})),!1},_closest:function(e,t,n){if(e&&e.classList.contains(t))return e;var r=n;while(e.parentNode){e=e.parentNode;if(e.classList&&e.classList.contains(t))return e;r--;if(r<0)return null}return null}});return new n}),this.createjs=this.createjs||{},function(){"use strict";var e=createjs.PreloadJS=createjs.PreloadJS||{};e.version="0.6.0",e.buildDate="Thu, 11 Dec 2014 23:32:09 GMT"}(),this.createjs=this.createjs||{},createjs.extend=function(e,t){"use strict";function n(){this.constructor=e}return n.prototype=t.prototype,e.prototype=new n},this.createjs=this.createjs||{},createjs.promote=function(e,t){"use strict";var n=e.prototype,r=Object.getPrototypeOf&&Object.getPrototypeOf(n)||n.__proto__;if(r){n[(t+="_")+"constructor"]=r.constructor;for(var i in r)n.hasOwnProperty(i)&&"function"==typeof r[i]&&(n[t+i]=r[i])}return e},this.createjs=this.createjs||{},createjs.indexOf=function(e,t){"use strict";for(var n=0,r=e.length;r>n;n++)if(t===e[n])return n;return-1},this.createjs=this.createjs||{},function(){"use strict";createjs.proxy=function(e,t){var n=Array.prototype.slice.call(arguments,2);return function(){return e.apply(t,Array.prototype.slice.call(arguments,0).concat(n))}}}(),this.createjs=this.createjs||{},function(){"use strict";function e(){throw"BrowserDetect cannot be instantiated"}var t=e.agent=window.navigator.userAgent;e.isWindowPhone=t.indexOf("IEMobile")>-1||t.indexOf("Windows Phone")>-1,e.isFirefox=t.indexOf("Firefox")>-1,e.isOpera=null!=window.opera,e.isChrome=t.indexOf("Chrome")>-1,e.isIOS=(t.indexOf("iPod")>-1||t.indexOf("iPhone")>-1||t.indexOf("iPad")>-1)&&!e.isWindowPhone,e.isAndroid=t.indexOf("Android")>-1&&!e.isWindowPhone,e.isBlackberry=t.indexOf("Blackberry")>-1,createjs.BrowserDetect=e}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t,n){this.type=e,this.target=null,this.currentTarget=null,this.eventPhase=0,this.bubbles=!!t,this.cancelable=!!n,this.timeStamp=(new Date).getTime(),this.defaultPrevented=!1,this.propagationStopped=!1,this.immediatePropagationStopped=!1,this.removed=!1}var t=e.prototype;t.preventDefault=function(){this.defaultPrevented=this.cancelable&&!0},t.stopPropagation=function(){this.propagationStopped=!0},t.stopImmediatePropagation=function(){this.immediatePropagationStopped=this.propagationStopped=!0},t.remove=function(){this.removed=!0},t.clone=function(){return new e(this.type,this.bubbles,this.cancelable)},t.set=function(e){for(var t in e)this[t]=e[t];return this},t.toString=function(){return"[Event (type="+this.type+")]"},createjs.Event=e}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t,n){this.Event_constructor("error"),this.title=e,this.message=t,this.data=n}var t=createjs.extend(e,createjs.Event);t.clone=function(){return new createjs.ErrorEvent(this.title,this.message,this.data)},createjs.ErrorEvent=createjs.promote(e,"Event")}(),this.createjs=this.createjs||{},function(){"use strict";function e(){this._listeners=null,this._captureListeners=null}var t=e.prototype;e.initialize=function(e){e.addEventListener=t.addEventListener,e.on=t.on,e.removeEventListener=e.off=t.removeEventListener,e.removeAllEventListeners=t.removeAllEventListeners,e.hasEventListener=t.hasEventListener,e.dispatchEvent=t.dispatchEvent,e._dispatchEvent=t._dispatchEvent,e.willTrigger=t.willTrigger},t.addEventListener=function(e,t,n){var r;r=n?this._captureListeners=this._captureListeners||{}:this._listeners=this._listeners||{};var i=r[e];return i&&this.removeEventListener(e,t,n),i=r[e],i?i.push(t):r[e]=[t],t},t.on=function(e,t,n,r,i,s){return t.handleEvent&&(n=n||t,t=t.handleEvent),n=n||this,this.addEventListener(e,function(e){t.call(n,e,i),r&&e.remove()},s)},t.removeEventListener=function(e,t,n){var r=n?this._captureListeners:this._listeners;if(r){var i=r[e];if(i)for(var s=0,o=i.length;o>s;s++)if(i[s]==t){1==o?delete r[e]:i.splice(s,1);break}}},t.off=t.removeEventListener,t.removeAllEventListeners=function(e){e?(this._listeners&&delete this._listeners[e],this._captureListeners&&delete this._captureListeners[e]):this._listeners=this._captureListeners=null},t.dispatchEvent=function(e){if("string"==typeof e){var t=this._listeners;if(!t||!t[e])return!1;e=new createjs.Event(e)}else e.target&&e.clone&&(e=e.clone());try{e.target=this}catch(n){}if(e.bubbles&&this.parent){for(var r=this,i=[r];r.parent;)i.push(r=r.parent);var s,o=i.length;for(s=o-1;s>=0&&!e.propagationStopped;s--)i[s]._dispatchEvent(e,1+(0==s));for(s=1;o>s&&!e.propagationStopped;s++)i[s]._dispatchEvent(e,3)}else this._dispatchEvent(e,2);return e.defaultPrevented},t.hasEventListener=function(e){var t=this._listeners,n=this._captureListeners;return!!(t&&t[e]||n&&n[e])},t.willTrigger=function(e){for(var t=this;t;){if(t.hasEventListener(e))return!0;t=t.parent}return!1},t.toString=function(){return"[EventDispatcher]"},t._dispatchEvent=function(e,t){var n,r=1==t?this._captureListeners:this._listeners;if(e&&r){var i=r[e.type];if(!i||!(n=i.length))return;try{e.currentTarget=this}catch(s){}try{e.eventPhase=t}catch(s){}e.removed=!1,i=i.slice();for(var o=0;n>o&&!e.immediatePropagationStopped;o++){var u=i[o];u.handleEvent?u.handleEvent(e):u(e),e.removed&&(this.off(e.type,u,1==t),e.removed=!1)}}},createjs.EventDispatcher=e}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t){this.Event_constructor("progress"),this.loaded=e,this.total=null==t?1:t,this.progress=0==t?0:this.loaded/this.total}var t=createjs.extend(e,createjs.Event);t.clone=function(){return new createjs.ProgressEvent(this.loaded,this.total)},createjs.ProgressEvent=createjs.promote(e,"Event")}(window),function(){function e(t,r){function s(e){if(s[e]!==g)return s[e];var t;if("bug-string-char-index"==e)t="a"!="a"[0];else if("json"==e)t=s("json-stringify")&&s("json-parse");else{var n,i='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if("json-stringify"==e){var a=r.stringify,l="function"==typeof a&&w;if(l){(n=function(){return 1}).toJSON=n;try{l="0"===a(0)&&"0"===a(new o)&&'""'==a(new u)&&a(b)===g&&a(g)===g&&a()===g&&"1"===a(n)&&"[1]"==a([n])&&"[null]"==a([g])&&"null"==a(null)&&"[null,null,null]"==a([g,b,null])&&a({a:[n,!0,!1,null,"\0\b\n\f\r	"]})==i&&"1"===a(null,n)&&"[\n 1,\n 2\n]"==a([1,2],null,1)&&'"-271821-04-20T00:00:00.000Z"'==a(new f(-864e13))&&'"+275760-09-13T00:00:00.000Z"'==a(new f(864e13))&&'"-000001-01-01T00:00:00.000Z"'==a(new f(-621987552e5))&&'"1969-12-31T23:59:59.999Z"'==a(new f(-1))}catch(c){l=!1}}t=l}if("json-parse"==e){var h=r.parse;if("function"==typeof h)try{if(0===h("0")&&!h(!1)){n=h(i);var p=5==n.a.length&&1===n.a[0];if(p){try{p=!h('"	"')}catch(c){}if(p)try{p=1!==h("01")}catch(c){}if(p)try{p=1!==h("1.")}catch(c){}}}}catch(c){p=!1}t=p}}return s[e]=!!t}t||(t=i.Object()),r||(r=i.Object());var o=t.Number||i.Number,u=t.String||i.String,a=t.Object||i.Object,f=t.Date||i.Date,l=t.SyntaxError||i.SyntaxError,h=t.TypeError||i.TypeError,p=t.Math||i.Math,d=t.JSON||i.JSON;"object"==typeof d&&d&&(r.stringify=d.stringify,r.parse=d.parse);var v,m,g,y=a.prototype,b=y.toString,w=new f(-0xc782b5b800cec);try{w=-109252==w.getUTCFullYear()&&0===w.getUTCMonth()&&1===w.getUTCDate()&&10==w.getUTCHours()&&37==w.getUTCMinutes()&&6==w.getUTCSeconds()&&708==w.getUTCMilliseconds()}catch(E){}if(!s("json")){var S="[object Function]",x="[object Date]",T="[object Number]",N="[object String]",C="[object Array]",k="[object Boolean]",L=s("bug-string-char-index");if(!w)var A=p.floor,O=[0,31,59,90,120,151,181,212,243,273,304,334],M=function(e,t){return O[t]+365*(e-1970)+A((e-1969+(t=+(t>1)))/4)-A((e-1901+t)/100)+A((e-1601+t)/400)};if((v=y.hasOwnProperty)||(v=function(e){var t,n={};return(n.__proto__=null,n.__proto__={toString:1},n).toString!=b?v=function(e){var t=this.__proto__,n=e in(this.__proto__=null,this);return this.__proto__=t,n}:(t=n.constructor,v=function(e){var n=(this.constructor||t).prototype;return e in this&&!(e in n&&this[e]===n[e])}),n=null,v.call(this,e)}),m=function(e,t){var r,i,s,o=0;(r=function(){this.valueOf=0}).prototype.valueOf=0,i=new r;for(s in i)v.call(i,s)&&o++;return r=i=null,o?m=2==o?function(e,t){var n,r={},i=b.call(e)==S;for(n in e)i&&"prototype"==n||v.call(r,n)||!(r[n]=1)||!v.call(e,n)||t(n)}:function(e,t){var n,r,i=b.call(e)==S;for(n in e)i&&"prototype"==n||!v.call(e,n)||(r="constructor"===n)||t(n);(r||v.call(e,n="constructor"))&&t(n)}:(i=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],m=function(e,t){var r,s,o=b.call(e)==S,u=!o&&"function"!=typeof e.constructor&&n[typeof e.hasOwnProperty]&&e.hasOwnProperty||v;for(r in e)o&&"prototype"==r||!u.call(e,r)||t(r);for(s=i.length;r=i[--s];u.call(e,r)&&t(r));}),m(e,t)},!s("json-stringify")){var _={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},D="000000",P=function(e,t){return(D+(t||0)).slice(-e)},H="\\u00",B=function(e){for(var t='"',n=0,r=e.length,i=!L||r>10,s=i&&(L?e.split(""):e);r>n;n++){var o=e.charCodeAt(n);switch(o){case 8:case 9:case 10:case 12:case 13:case 34:case 92:t+=_[o];break;default:if(32>o){t+=H+P(2,o.toString(16));break}t+=i?s[n]:e.charAt(n)}}return t+'"'},j=function(e,t,n,r,i,s,o){var u,a,f,l,c,p,d,y,w,E,S,L,O,_,D,H;try{u=t[e]}catch(F){}if("object"==typeof u&&u)if(a=b.call(u),a!=x||v.call(u,"toJSON"))"function"==typeof u.toJSON&&(a!=T&&a!=N&&a!=C||v.call(u,"toJSON"))&&(u=u.toJSON(e));else if(u>-1/0&&1/0>u){if(M){for(c=A(u/864e5),f=A(c/365.2425)+1970-1;M(f+1,0)<=c;f++);for(l=A((c-M(f,0))/30.42);M(f,l+1)<=c;l++);c=1+c-M(f,l),p=(u%864e5+864e5)%864e5,d=A(p/36e5)%24,y=A(p/6e4)%60,w=A(p/1e3)%60,E=p%1e3}else f=u.getUTCFullYear(),l=u.getUTCMonth(),c=u.getUTCDate(),d=u.getUTCHours(),y=u.getUTCMinutes(),w=u.getUTCSeconds(),E=u.getUTCMilliseconds();u=(0>=f||f>=1e4?(0>f?"-":"+")+P(6,0>f?-f:f):P(4,f))+"-"+P(2,l+1)+"-"+P(2,c)+"T"+P(2,d)+":"+P(2,y)+":"+P(2,w)+"."+P(3,E)+"Z"}else u=null;if(n&&(u=n.call(t,e,u)),null===u)return"null";if(a=b.call(u),a==k)return""+u;if(a==T)return u>-1/0&&1/0>u?""+u:"null";if(a==N)return B(""+u);if("object"==typeof u){for(_=o.length;_--;)if(o[_]===u)throw h();if(o.push(u),S=[],D=s,s+=i,a==C){for(O=0,_=u.length;_>O;O++)L=j(O,u,n,r,i,s,o),S.push(L===g?"null":L);H=S.length?i?"[\n"+s+S.join(",\n"+s)+"\n"+D+"]":"["+S.join(",")+"]":"[]"}else m(r||u,function(e){var t=j(e,u,n,r,i,s,o);t!==g&&S.push(B(e)+":"+(i?" ":"")+t)}),H=S.length?i?"{\n"+s+S.join(",\n"+s)+"\n"+D+"}":"{"+S.join(",")+"}":"{}";return o.pop(),H}};r.stringify=function(e,t,r){var i,s,o,u;if(n[typeof t]&&t)if((u=b.call(t))==S)s=t;else if(u==C){o={};for(var a,f=0,l=t.length;l>f;a=t[f++],u=b.call(a),(u==N||u==T)&&(o[a]=1));}if(r)if((u=b.call(r))==T){if((r-=r%1)>0)for(i="",r>10&&(r=10);i.length<r;i+=" ");}else u==N&&(i=r.length<=10?r:r.slice(0,10));return j("",(a={},a[""]=e,a),s,o,i,"",[])}}if(!s("json-parse")){var F,I,q=u.fromCharCode,R={92:"\\",34:'"',47:"/",98:"\b",116:"	",110:"\n",102:"\f",114:"\r"},U=function(){throw F=I=null,l()},z=function(){for(var e,t,n,r,i,s=I,o=s.length;o>F;)switch(i=s.charCodeAt(F)){case 9:case 10:case 13:case 32:F++;break;case 123:case 125:case 91:case 93:case 58:case 44:return e=L?s.charAt(F):s[F],F++,e;case 34:for(e="@",F++;o>F;)if(i=s.charCodeAt(F),32>i)U();else if(92==i)switch(i=s.charCodeAt(++F)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:e+=R[i],F++;break;case 117:for(t=++F,n=F+4;n>F;F++)i=s.charCodeAt(F),i>=48&&57>=i||i>=97&&102>=i||i>=65&&70>=i||U();e+=q("0x"+s.slice(t,F));break;default:U()}else{if(34==i)break;for(i=s.charCodeAt(F),t=F;i>=32&&92!=i&&34!=i;)i=s.charCodeAt(++F);e+=s.slice(t,F)}if(34==s.charCodeAt(F))return F++,e;U();default:if(t=F,45==i&&(r=!0,i=s.charCodeAt(++F)),i>=48&&57>=i){for(48==i&&(i=s.charCodeAt(F+1),i>=48&&57>=i)&&U(),r=!1;o>F&&(i=s.charCodeAt(F),i>=48&&57>=i);F++);if(46==s.charCodeAt(F)){for(n=++F;o>n&&(i=s.charCodeAt(n),i>=48&&57>=i);n++);n==F&&U(),F=n}if(i=s.charCodeAt(F),101==i||69==i){for(i=s.charCodeAt(++F),(43==i||45==i)&&F++,n=F;o>n&&(i=s.charCodeAt(n),i>=48&&57>=i);n++);n==F&&U(),F=n}return+s.slice(t,F)}if(r&&U(),"true"==s.slice(F,F+4))return F+=4,!0;if("false"==s.slice(F,F+5))return F+=5,!1;if("null"==s.slice(F,F+4))return F+=4,null;U()}return"$"},W=function(e){var t,n;if("$"==e&&U(),"string"==typeof e){if("@"==(L?e.charAt(0):e[0]))return e.slice(1);if("["==e){for(t=[];e=z(),"]"!=e;n||(n=!0))n&&(","==e?(e=z(),"]"==e&&U()):U()),","==e&&U(),t.push(W(e));return t}if("{"==e){for(t={};e=z(),"}"!=e;n||(n=!0))n&&(","==e?(e=z(),"}"==e&&U()):U()),(","==e||"string"!=typeof e||"@"!=(L?e.charAt(0):e[0])||":"!=z())&&U(),t[e.slice(1)]=W(z());return t}U()}return e},X=function(e,t,n){var r=V(e,t,n);r===g?delete e[t]:e[t]=r},V=function(e,t,n){var r,i=e[t];if("object"==typeof i&&i)if(b.call(i)==C)for(r=i.length;r--;)X(i,r,n);else m(i,function(e){X(i,e,n)});return n.call(e,t,i)};r.parse=function(e,t){var n,r;return F=0,I=""+e,n=W(z()),"$"!=z()&&U(),F=I=null,t&&b.call(t)==S?V((r={},r[""]=n,r),"",t):n}}}return r.runInContext=e,r}var t="function"==typeof define&&define.amd,n={"function":!0,object:!0},r=n[typeof exports]&&exports&&!exports.nodeType&&exports,i=n[typeof window]&&window||this,s=r&&n[typeof module]&&module&&!module.nodeType&&"object"==typeof global&&global;if(!s||s.global!==s&&s.window!==s&&s.self!==s||(i=s),r&&!t)e(i,r);else{var o=i.JSON,u=i.JSON3,a=!1,f=e(i,i.JSON3={noConflict:function(){return a||(a=!0,i.JSON=o,i.JSON3=u,o=u=null),f}});i.JSON={parse:f.parse,stringify:f.stringify}}t&&define("preloadjs",[],function(){return f})}.call(this),function(){var e={};e.parseXML=function(e,t){var n=null;try{if(window.DOMParser){var r=new DOMParser;n=r.parseFromString(e,t)}else n=new ActiveXObject("Microsoft.XMLDOM"),n.async=!1,n.loadXML(e)}catch(i){}return n},e.parseJSON=function(e){if(null==e)return null;try{return JSON.parse(e)}catch(t){throw t}},createjs.DataUtils=e}(),this.createjs=this.createjs||{},function(){"use strict";function e(){this.src=null,this.type=null,this.id=null,this.maintainOrder=!1,this.callback=null,this.data=null,this.method=createjs.LoadItem.GET,this.values=null,this.headers=null,this.withCredentials=!1,this.mimeType=null,this.crossOrigin=null,this.loadTimeout=8e3}var t=e.prototype={},n=e;n.create=function(t){if("string"==typeof t){var r=new e;return r.src=t,r}if(t instanceof n)return t;if(t instanceof Object)return t;throw new Error("Type not recognized.")},t.set=function(e){for(var t in e)this[t]=e[t];return this},createjs.LoadItem=n}(),function(){var e={};e.ABSOLUTE_PATT=/^(?:\w+:)?\/{2}/i,e.RELATIVE_PATT=/^[./]*?\//i,e.EXTENSION_PATT=/\/?[^/]+\.(\w{1,5})$/i,e.parseURI=function(t){var n={absolute:!1,relative:!1};if(null==t)return n;var r=t.indexOf("?");r>-1&&(t=t.substr(0,r));var i;return e.ABSOLUTE_PATT.test(t)?n.absolute=!0:e.RELATIVE_PATT.test(t)&&(n.relative=!0),(i=t.match(e.EXTENSION_PATT))&&(n.extension=i[1].toLowerCase()),n},e.formatQueryString=function(e,t){if(null==e)throw new Error("You must specify data.");var n=[];for(var r in e)n.push(r+"="+escape(e[r]));return t&&(n=n.concat(t)),n.join("&")},e.buildPath=function(e,t){if(null==t)return e;var n=[],r=e.indexOf("?");if(-1!=r){var i=e.slice(r+1);n=n.concat(i.split("&"))}return-1!=r?e.slice(0,r)+"?"+this._formatQueryString(t,n):e+"?"+this._formatQueryString(t,n)},e.isCrossDomain=function(e){var t=document.createElement("a");t.href=e.src;var n=document.createElement("a");n.href=location.href;var r=""!=t.hostname&&(t.port!=n.port||t.protocol!=n.protocol||t.hostname!=n.hostname);return r},e.isLocal=function(e){var t=document.createElement("a");return t.href=e.src,""==t.hostname&&"file:"==t.protocol},e.isBinary=function(e){switch(e){case createjs.AbstractLoader.IMAGE:case createjs.AbstractLoader.BINARY:return!0;default:return!1}},e.isImageTag=function(e){return e instanceof HTMLImageElement},e.isAudioTag=function(e){return window.HTMLAudioElement?e instanceof HTMLAudioElement:!1},e.isVideoTag=function(e){return window.HTMLVideoElement?e instanceof HTMLVideoElement:void 0},e.isText=function(e){switch(e){case createjs.AbstractLoader.TEXT:case createjs.AbstractLoader.JSON:case createjs.AbstractLoader.MANIFEST:case createjs.AbstractLoader.XML:case createjs.AbstractLoader.CSS:case createjs.AbstractLoader.SVG:case createjs.AbstractLoader.JAVASCRIPT:return!0;default:return!1}},e.getTypeByExtension=function(e){if(null==e)return createjs.AbstractLoader.TEXT;switch(e.toLowerCase()){case"jpeg":case"jpg":case"gif":case"png":case"webp":case"bmp":return createjs.AbstractLoader.IMAGE;case"ogg":case"mp3":case"webm":return createjs.AbstractLoader.SOUND;case"mp4":case"webm":case"ts":return createjs.AbstractLoader.VIDEO;case"json":return createjs.AbstractLoader.JSON;case"xml":return createjs.AbstractLoader.XML;case"css":return createjs.AbstractLoader.CSS;case"js":return createjs.AbstractLoader.JAVASCRIPT;case"svg":return createjs.AbstractLoader.SVG;default:return createjs.AbstractLoader.TEXT}},createjs.RequestUtils=e}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t,n){this.EventDispatcher_constructor(),this.loaded=!1,this.canceled=!1,this.progress=0,this.type=n,this.resultFormatter=null,this._item=e?createjs.LoadItem.create(e):null,this._preferXHR=t,this._result=null,this._rawResult=null,this._loadedItems=null,this._tagSrcAttribute=null,this._tag=null}var t=createjs.extend(e,createjs.EventDispatcher),n=e;n.POST="POST",n.GET="GET",n.BINARY="binary",n.CSS="css",n.IMAGE="image",n.JAVASCRIPT="javascript",n.JSON="json",n.JSONP="jsonp",n.MANIFEST="manifest",n.SOUND="sound",n.VIDEO="video",n.SPRITESHEET="spritesheet",n.SVG="svg",n.TEXT="text",n.XML="xml",t.getItem=function(){return this._item},t.getResult=function(e){return e?this._rawResult:this._result},t.getTag=function(){return this._tag},t.setTag=function(e){this._tag=e},t.load=function(){this._createRequest(),this._request.on("complete",this,this),this._request.on("progress",this,this),this._request.on("loadStart",this,this),this._request.on("abort",this,this),this._request.on("timeout",this,this),this._request.on("error",this,this);var e=new createjs.Event("initialize");e.loader=this._request,this.dispatchEvent(e),this._request.load()},t.cancel=function(){this.canceled=!0,this.destroy()},t.destroy=function(){this._request&&(this._request.removeAllEventListeners(),this._request.destroy()),this._request=null,this._item=null,this._rawResult=null,this._result=null,this._loadItems=null,this.removeAllEventListeners()},t.getLoadedItems=function(){return this._loadedItems},t._createRequest=function(){this._request=this._preferXHR?new createjs.XHRRequest(this._item):new createjs.TagRequest(this._item,this._tag||this._createTag(),this._tagSrcAttribute)},t._createTag=function(){return null},t._sendLoadStart=function(){this._isCanceled()||this.dispatchEvent("loadstart")},t._sendProgress=function(e){if(!this._isCanceled()){var t=null;"number"==typeof e?(this.progress=e,t=new createjs.ProgressEvent(this.progress)):(t=e,this.progress=e.loaded/e.total,t.progress=this.progress,(isNaN(this.progress)||1/0==this.progress)&&(this.progress=0)),this.hasEventListener("progress")&&this.dispatchEvent(t)}},t._sendComplete=function(){if(!this._isCanceled()){this.loaded=!0;var e=new createjs.Event("complete");e.rawResult=this._rawResult,null!=this._result&&(e.result=this._result),this.dispatchEvent(e)}},t._sendError=function(e){!this._isCanceled()&&this.hasEventListener("error")&&(null==e&&(e=new createjs.ErrorEvent("PRELOAD_ERROR_EMPTY")),this.dispatchEvent(e))},t._isCanceled=function(){return null==window.createjs||this.canceled?!0:!1},t.resultFormatter=null,t.handleEvent=function(e){switch(e.type){case"complete":this._rawResult=e.target._response;var t=this.resultFormatter&&this.resultFormatter(this),n=this;t instanceof Function?t(function(e){n._result=e,n._sendComplete()}):(this._result=t||this._rawResult,this._sendComplete());break;case"progress":this._sendProgress(e);break;case"error":this._sendError(e);break;case"loadstart":this._sendLoadStart();break;case"abort":case"timeout":this._isCanceled()||this.dispatchEvent(e.type)}},t.buildPath=function(e,t){return createjs.RequestUtils.buildPath(e,t)},t.toString=function(){return"[PreloadJS AbstractLoader]"},createjs.AbstractLoader=createjs.promote(e,"EventDispatcher")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t,n){this.AbstractLoader_constructor(e,t,n),this.resultFormatter=this._formatResult,this._tagSrcAttribute="src"}var t=createjs.extend(e,createjs.AbstractLoader);t.load=function(){this._tag||(this._tag=this._createTag(this._item.src)),this._tag.preload="auto",this._tag.load(),this.AbstractLoader_load()},t._createTag=function(){},t._createRequest=function(){this._request=this._preferXHR?new createjs.XHRRequest(this._item):new createjs.MediaTagRequest(this._item,this._tag||this._createTag(),this._tagSrcAttribute)},t._formatResult=function(e){return this._tag.removeEventListener&&this._tag.removeEventListener("canplaythrough",this._loadedHandler),this._tag.onstalled=null,this._preferXHR&&(e.getTag().src=e.getResult(!0)),e.getTag()},createjs.AbstractMediaLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";var e=function(e){this._item=e},t=createjs.extend(e,createjs.EventDispatcher);t.load=function(){},t.destroy=function(){},t.cancel=function(){},createjs.AbstractRequest=createjs.promote(e,"EventDispatcher")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t,n){this.AbstractRequest_constructor(e),this._tag=t,this._tagSrcAttribute=n,this._loadedHandler=createjs.proxy(this._handleTagComplete,this),this._addedToDOM=!1,this._startTagVisibility=null}var t=createjs.extend(e,createjs.AbstractRequest);t.load=function(){null==this._tag.parentNode&&(window.document.body.appendChild(this._tag),this._addedToDOM=!0),this._tag.onload=createjs.proxy(this._handleTagComplete,this),this._tag.onreadystatechange=createjs.proxy(this._handleReadyStateChange,this);var e=new createjs.Event("initialize");e.loader=this._tag,this.dispatchEvent(e),this._hideTag(),this._tag[this._tagSrcAttribute]=this._item.src},t.destroy=function(){this._clean(),this._tag=null,this.AbstractRequest_destroy()},t._handleReadyStateChange=function(){clearTimeout(this._loadTimeout);var e=this._tag;("loaded"==e.readyState||"complete"==e.readyState)&&this._handleTagComplete()},t._handleTagComplete=function(){this._rawResult=this._tag,this._result=this.resultFormatter&&this.resultFormatter(this)||this._rawResult,this._clean(),this._showTag(),this.dispatchEvent("complete")},t._clean=function(){this._tag.onload=null,this._tag.onreadystatechange=null,this._addedToDOM&&null!=this._tag.parentNode&&this._tag.parentNode.removeChild(this._tag)},t._hideTag=function(){this._startTagVisibility=this._tag.style.visibility,this._tag.style.visibility="hidden"},t._showTag=function(){this._tag.style.visibility=this._startTagVisibility},t._handleStalled=function(){},createjs.TagRequest=createjs.promote(e,"AbstractRequest")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t,n){this.AbstractRequest_constructor(e),this._tag=t,this._tagSrcAttribute=n,this._loadedHandler=createjs.proxy(this._handleTagComplete,this)}var t=createjs.extend(e,createjs.TagRequest);t.load=function(){this._tag.onstalled=createjs.proxy(this._handleStalled,this),this._tag.onprogress=createjs.proxy(this._handleProgress,this),this._tag.addEventListener&&this._tag.addEventListener("canplaythrough",this._loadedHandler,!1),this.TagRequest_load()},t._handleReadyStateChange=function(){clearTimeout(this._loadTimeout);var e=this._tag;("loaded"==e.readyState||"complete"==e.readyState)&&this._handleTagComplete()},t._handleStalled=function(){},t._handleProgress=function(e){if(e&&!(e.loaded>0&&0==e.total)){var t=new createjs.ProgressEvent(e.loaded,e.total);this.dispatchEvent(t)}},t._clean=function(){this._tag.removeEventListener&&this._tag.removeEventListener("canplaythrough",this._loadedHandler),this._tag.onstalled=null,this._tag.onprogress=null,this.TagRequest__clean()},createjs.MediaTagRequest=createjs.promote(e,"TagRequest")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e){this.AbstractRequest_constructor(e),this._request=null,this._loadTimeout=null,this._xhrLevel=1,this._response=null,this._rawResponse=null,this._canceled=!1,this._handleLoadStartProxy=createjs.proxy(this._handleLoadStart,this),this._handleProgressProxy=createjs.proxy(this._handleProgress,this),this._handleAbortProxy=createjs.proxy(this._handleAbort,this),this._handleErrorProxy=createjs.proxy(this._handleError,this),this._handleTimeoutProxy=createjs.proxy(this._handleTimeout,this),this._handleLoadProxy=createjs.proxy(this._handleLoad,this),this._handleReadyStateChangeProxy=createjs.proxy(this._handleReadyStateChange,this),!this._createXHR(e)}var t=createjs.extend(e,createjs.AbstractRequest);e.ACTIVEX_VERSIONS=["Msxml2.XMLHTTP.6.0","Msxml2.XMLHTTP.5.0","Msxml2.XMLHTTP.4.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],t.getResult=function(e){return e&&this._rawResponse?this._rawResponse:this._response},t.cancel=function(){this.canceled=!0,this._clean(),this._request.abort()},t.load=function(){if(null==this._request)return void this._handleError();this._request.addEventListener("loadstart",this._handleLoadStartProxy,!1),this._request.addEventListener("progress",this._handleProgressProxy,!1),this._request.addEventListener("abort",this._handleAbortProxy,!1),this._request.addEventListener("error",this._handleErrorProxy,!1),this._request.addEventListener("timeout",this._handleTimeoutProxy,!1),this._request.addEventListener("load",this._handleLoadProxy,!1),this._request.addEventListener("readystatechange",this._handleReadyStateChangeProxy,!1),1==this._xhrLevel&&(this._loadTimeout=setTimeout(createjs.proxy(this._handleTimeout,this),this._item.loadTimeout));try{this._item.values&&this._item.method!=createjs.AbstractLoader.GET?this._item.method==createjs.AbstractLoader.POST&&this._request.send(createjs.RequestUtils.formatQueryString(this._item.values)):this._request.send()}catch(e){this.dispatchEvent(new createjs.ErrorEvent("XHR_SEND",null,e))}},t.setResponseType=function(e){this._request.responseType=e},t.getAllResponseHeaders=function(){return this._request.getAllResponseHeaders instanceof Function?this._request.getAllResponseHeaders():null},t.getResponseHeader=function(e){return this._request.getResponseHeader instanceof Function?this._request.getResponseHeader(e):null},t._handleProgress=function(e){if(e&&!(e.loaded>0&&0==e.total)){var t=new createjs.ProgressEvent(e.loaded,e.total);this.dispatchEvent(t)}},t._handleLoadStart=function(){clearTimeout(this._loadTimeout),this.dispatchEvent("loadstart")},t._handleAbort=function(e){this._clean(),this.dispatchEvent(new createjs.ErrorEvent("XHR_ABORTED",null,e))},t._handleError=function(e){this._clean(),this.dispatchEvent(new createjs.ErrorEvent(e.message))},t._handleReadyStateChange=function(){4==this._request.readyState&&this._handleLoad()},t._handleLoad=function(){if(!this.loaded){this.loaded=!0;var e=this._checkError();if(e)return void this._handleError(e);this._response=this._getResponse(),this._clean(),this.dispatchEvent(new createjs.Event("complete"))}},t._handleTimeout=function(e){this._clean(),this.dispatchEvent(new createjs.ErrorEvent("PRELOAD_TIMEOUT",null,e))},t._checkError=function(){var e=parseInt(this._request.status);switch(e){case 404:case 0:return new Error(e)}return null},t._getResponse=function(){if(null!=this._response)return this._response;if(null!=this._request.response)return this._request.response;try{if(null!=this._request.responseText)return this._request.responseText}catch(e){}try{if(null!=this._request.responseXML)return this._request.responseXML}catch(e){}return null},t._createXHR=function(e){var t=createjs.RequestUtils.isCrossDomain(e),n={},r=null;if(window.XMLHttpRequest)r=new XMLHttpRequest,t&&void 0===r.withCredentials&&window.XDomainRequest&&(r=new XDomainRequest);else{for(var i=0,o=s.ACTIVEX_VERSIONS.length;o>i;i++){s.ACTIVEX_VERSIONS[i];try{r=new ActiveXObject(axVersions);break}catch(u){}}if(null==r)return!1}e.mimeType&&r.overrideMimeType&&r.overrideMimeType(e.mimeType),this._xhrLevel="string"==typeof r.responseType?2:1;var a=null;if(a=e.method==createjs.AbstractLoader.GET?createjs.RequestUtils.buildPath(e.src,e.values):e.src,r.open(e.method||createjs.AbstractLoader.GET,a,!0),t&&r instanceof XMLHttpRequest&&1==this._xhrLevel&&(n.Origin=location.origin),e.values&&e.method==createjs.AbstractLoader.POST&&(n["Content-Type"]="application/x-www-form-urlencoded"),t||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest"),e.headers)for(var f in e.headers)n[f]=e.headers[f];for(f in n)r.setRequestHeader(f,n[f]);return r instanceof XMLHttpRequest&&void 0!==e.withCredentials&&(r.withCredentials=e.withCredentials),this._request=r,!0},t._clean=function(){clearTimeout(this._loadTimeout),this._request.removeEventListener("loadstart",this._handleLoadStartProxy),this._request.removeEventListener("progress",this._handleProgressProxy),this._request.removeEventListener("abort",this._handleAbortProxy),this._request.removeEventListener("error",this._handleErrorProxy),this._request.removeEventListener("timeout",this._handleTimeoutProxy),this._request.removeEventListener("load",this._handleLoadProxy),this._request.removeEventListener("readystatechange",this._handleReadyStateChangeProxy)},t.toString=function(){return"[PreloadJS XHRRequest]"},createjs.XHRRequest=createjs.promote(e,"AbstractRequest")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t,n){this.AbstractLoader_constructor(),this.init(e,t,n)}var t=createjs.extend(e,createjs.AbstractLoader),n=e;t.init=function(e,t,n){this.useXHR=!0,this.preferXHR=!0,this._preferXHR=!0,this.setPreferXHR(e),this.stopOnError=!1,this.maintainScriptOrder=!0,this.next=null,this._paused=!1,this._basePath=t,this._crossOrigin=n,this._typeCallbacks={},this._extensionCallbacks={},this._loadStartWasDispatched=!1,this._maxConnections=1,this._currentlyLoadingScript=null,this._currentLoads=[],this._loadQueue=[],this._loadQueueBackup=[],this._loadItemsById={},this._loadItemsBySrc={},this._loadedResults={},this._loadedRawResults={},this._numItems=0,this._numItemsLoaded=0,this._scriptOrder=[],this._loadedScripts=[],this._lastProgress=0/0,this._availableLoaders=[createjs.ImageLoader,createjs.JavaScriptLoader,createjs.CSSLoader,createjs.JSONLoader,createjs.JSONPLoader,createjs.SoundLoader,createjs.ManifestLoader,createjs.SpriteSheetLoader,createjs.XMLLoader,createjs.SVGLoader,createjs.BinaryLoader,createjs.VideoLoader,createjs.TextLoader],this._defaultLoaderLength=this._availableLoaders.length},n.loadTimeout=8e3,n.LOAD_TIMEOUT=0,n.BINARY=createjs.AbstractLoader.BINARY,n.CSS=createjs.AbstractLoader.CSS,n.IMAGE=createjs.AbstractLoader.IMAGE,n.JAVASCRIPT=createjs.AbstractLoader.JAVASCRIPT,n.JSON=createjs.AbstractLoader.JSON,n.JSONP=createjs.AbstractLoader.JSONP,n.MANIFEST=createjs.AbstractLoader.MANIFEST,n.SOUND=createjs.AbstractLoader.SOUND,n.VIDEO=createjs.AbstractLoader.VIDEO,n.SVG=createjs.AbstractLoader.SVG,n.TEXT=createjs.AbstractLoader.TEXT,n.XML=createjs.AbstractLoader.XML,n.POST=createjs.AbstractLoader.POST,n.GET=createjs.AbstractLoader.GET,t.registerLoader=function(e){if(!e||!e.canLoadItem)throw new Error("loader is of an incorrect type.");if(-1!=this._availableLoaders.indexOf(e))throw new Error("loader already exists.");this._availableLoaders.unshift(e)},t.unregisterLoader=function(e){var t=this._availableLoaders.indexOf(e);-1!=t&&t<this._defaultLoaderLength-1&&this._availableLoaders.splice(t,1)},t.setUseXHR=function(e){return this.setPreferXHR(e)},t.setPreferXHR=function(e){return this.preferXHR=0!=e&&null!=window.XMLHttpRequest,this.preferXHR},t.removeAll=function(){this.remove()},t.remove=function(e){var t=null;if(!e||e instanceof Array){if(e)t=e;else if(arguments.length>0)return}else t=[e];var n=!1;if(t){for(;t.length;){var r=t.pop(),i=this.getResult(r);for(s=this._loadQueue.length-1;s>=0;s--)if(o=this._loadQueue[s].getItem(),o.id==r||o.src==r){this._loadQueue.splice(s,1)[0].cancel();break}for(s=this._loadQueueBackup.length-1;s>=0;s--)if(o=this._loadQueueBackup[s].getItem(),o.id==r||o.src==r){this._loadQueueBackup.splice(s,1)[0].cancel();break}if(i)delete this._loadItemsById[i.id],delete this._loadItemsBySrc[i.src],this._disposeItem(i);else for(var s=this._currentLoads.length-1;s>=0;s--){var o=this._currentLoads[s].getItem();if(o.id==r||o.src==r){this._currentLoads.splice(s,1)[0].cancel(),n=!0;break}}}n&&this._loadNext()}else{this.close();for(var u in this._loadItemsById)this._disposeItem(this._loadItemsById[u]);this.init(this.preferXHR,this._basePath)}},t.reset=function(){this.close();for(var e in this._loadItemsById)this._disposeItem(this._loadItemsById[e]);for(var t=[],n=0,r=this._loadQueueBackup.length;r>n;n++)t.push(this._loadQueueBackup[n].getItem());this.loadManifest(t,!1)},t.installPlugin=function(e){if(null!=e&&null!=e.getPreloadHandlers){var t=e.getPreloadHandlers();if(t.scope=e,null!=t.types)for(var n=0,r=t.types.length;r>n;n++)this._typeCallbacks[t.types[n]]=t;if(null!=t.extensions)for(n=0,r=t.extensions.length;r>n;n++)this._extensionCallbacks[t.extensions[n]]=t}},t.setMaxConnections=function(e){this._maxConnections=e,!this._paused&&this._loadQueue.length>0&&this._loadNext()},t.loadFile=function(e,t,n){if(null==e){var r=new createjs.ErrorEvent("PRELOAD_NO_FILE");return void this._sendError(r)}this._addItem(e,null,n),this.setPaused(t!==!1?!1:!0)},t.loadManifest=function(e,t,r){var i=null,s=null;if(e instanceof Array){if(0==e.length){var o=new createjs.ErrorEvent("PRELOAD_MANIFEST_EMPTY");return void this._sendError(o)}i=e}else if("string"==typeof e)i=[{src:e,type:n.MANIFEST}];else{if("object"!=typeof e){var o=new createjs.ErrorEvent("PRELOAD_MANIFEST_NULL");return void this._sendError(o)}if(void 0!==e.src){if(null==e.type)e.type=n.MANIFEST;else if(e.type!=n.MANIFEST){var o=new createjs.ErrorEvent("PRELOAD_MANIFEST_TYPE");this._sendError(o)}i=[e]}else void 0!==e.manifest&&(i=e.manifest,s=e.path)}for(var u=0,a=i.length;a>u;u++)this._addItem(i[u],s,r);this.setPaused(t!==!1?!1:!0)},t.load=function(){this.setPaused(!1)},t.getItem=function(e){return this._loadItemsById[e]||this._loadItemsBySrc[e]},t.getResult=function(e,t){var n=this._loadItemsById[e]||this._loadItemsBySrc[e];if(null==n)return null;var r=n.id;return t&&this._loadedRawResults[r]?this._loadedRawResults[r]:this._loadedResults[r]},t.getItems=function(e){for(var t=[],n=0,r=this._loadQueueBackup.length;r>n;n++){var i=this._loadQueueBackup[n],s=i.getItem();(e!==!0||i.loaded)&&t.push({item:s,result:this.getResult(s.id),rawResult:this.getResult(s.id,!0)})}return t},t.setPaused=function(e){this._paused=e,this._paused||this._loadNext()},t.close=function(){for(;this._currentLoads.length;)this._currentLoads.pop().cancel();this._scriptOrder.length=0,this._loadedScripts.length=0,this.loadStartWasDispatched=!1,this._itemCount=0,this._lastProgress=0/0},t._addItem=function(e,t,n){var r=this._createLoadItem(e,t,n);if(null!=r){var i=this._createLoader(r);null!=i&&(r._loader=i,this._loadQueue.push(i),this._loadQueueBackup.push(i),this._numItems++,this._updateProgress(),(this.maintainScriptOrder&&r.type==createjs.LoadQueue.JAVASCRIPT||r.maintainOrder===!0)&&(this._scriptOrder.push(r),this._loadedScripts.push(null)))}},t._createLoadItem=function(e,t,r){var i=createjs.LoadItem.create(e);if(null==i)return null;var s=createjs.RequestUtils.parseURI(i.src);s.extension&&(i.ext=s.extension),null==i.type&&(i.type=createjs.RequestUtils.getTypeByExtension(i.ext));var o="",u=r||this._basePath,a=i.src;if(!s.absolute&&!s.relative)if(t){o=t;var f=createjs.RequestUtils.parseURI(t);a=t+a,null==u||f.absolute||f.relative||(o=u+o)}else null!=u&&(o=u);i.src=o+i.src,i.path=o,(void 0===i.id||null===i.id||""===i.id)&&(i.id=a);var l=this._typeCallbacks[i.type]||this._extensionCallbacks[i.ext];if(l){var h=l.callback.call(l.scope,i,this);if(h===!1)return null;h===!0||null!=h&&(i._loader=h),s=createjs.RequestUtils.parseURI(i.src),null!=s.extension&&(i.ext=s.extension)}return this._loadItemsById[i.id]=i,this._loadItemsBySrc[i.src]=i,null==i.loadTimeout&&(i.loadTimeout=n.loadTimeout),null==i.crossOrigin&&(i.crossOrigin=this._crossOrigin),i},t._createLoader=function(e){if(null!=e._loader)return e._loader;for(var t=this.preferXHR,n=0;n<this._availableLoaders.length;n++){var r=this._availableLoaders[n];if(r&&r.canLoadItem(e))return new r(e,t)}return null},t._loadNext=function(){if(!this._paused){this._loadStartWasDispatched||(this._sendLoadStart(),this._loadStartWasDispatched=!0),this._numItems==this._numItemsLoaded?(this.loaded=!0,this._sendComplete(),this.next&&this.next.load&&this.next.load()):this.loaded=!1;for(var e=0;e<this._loadQueue.length&&!(this._currentLoads.length>=this._maxConnections);e++){var t=this._loadQueue[e];this._canStartLoad(t)&&(this._loadQueue.splice(e,1),e--,this._loadItem(t))}}},t._loadItem=function(e){e.on("fileload",this._handleFileLoad,this),e.on("progress",this._handleProgress,this),e.on("complete",this._handleFileComplete,this),e.on("error",this._handleError,this),e.on("fileerror",this._handleFileError,this),this._currentLoads.push(e),this._sendFileStart(e.getItem()),e.load()},t._handleFileLoad=function(e){e.target=null,this.dispatchEvent(e)},t._handleFileError=function(e){var t=new createjs.ErrorEvent("FILE_LOAD_ERROR",null,e.item);this._sendError(t)},t._handleError=function(e){var t=e.target;this._numItemsLoaded++,this._finishOrderedItem(t,!0),this._updateProgress();var n=new createjs.ErrorEvent("FILE_LOAD_ERROR",null,t.getItem());this._sendError(n),this.stopOnError||(this._removeLoadItem(t),this._loadNext())},t._handleFileComplete=function(e){var t=e.target,n=t.getItem(),r=t.getResult();this._loadedResults[n.id]=r;var i=t.getResult(!0);null!=i&&i!==r&&(this._loadedRawResults[n.id]=i),this._saveLoadedItems(t),this._removeLoadItem(t),this._finishOrderedItem(t)||this._processFinishedLoad(n,t)},t._saveLoadedItems=function(e){var t=e.getLoadedItems();if(null!==t)for(var n=0;n<t.length;n++){var r=t[n].item;this._loadItemsBySrc[r.src]=r,this._loadItemsById[r.id]=r,this._loadedResults[r.id]=t[n].result,this._loadedRawResults[r.id]=t[n].rawResult}},t._finishOrderedItem=function(e,t){var n=e.getItem();if(this.maintainScriptOrder&&n.type==createjs.LoadQueue.JAVASCRIPT||n.maintainOrder){e instanceof createjs.JavaScriptLoader&&(this._currentlyLoadingScript=!1);var r=createjs.indexOf(this._scriptOrder,n);return-1==r?!1:(this._loadedScripts[r]=t===!0?!0:n,this._checkScriptLoadOrder(),!0)}return!1},t._checkScriptLoadOrder=function(){for(var e=this._loadedScripts.length,t=0;e>t;t++){var n=this._loadedScripts[t];if(null===n)break;if(n!==!0){var r=this._loadedResults[n.id];n.type==createjs.LoadQueue.JAVASCRIPT&&(document.body||document.getElementsByTagName("body")[0]).appendChild(r);var i=n._loader;this._processFinishedLoad(n,i),this._loadedScripts[t]=!0}}},t._processFinishedLoad=function(e,t){this._numItemsLoaded++,this._updateProgress(),this._sendFileComplete(e,t),this._loadNext()},t._canStartLoad=function(e){if(!this.maintainScriptOrder||e.preferXHR)return!0;var t=e.getItem();if(t.type!=createjs.LoadQueue.JAVASCRIPT)return!0;if(this._currentlyLoadingScript)return!1;for(var n=this._scriptOrder.indexOf(t),r=0;n>r;){var i=this._loadedScripts[r];if(null==i)return!1;r++}return this._currentlyLoadingScript=!0,!0},t._removeLoadItem=function(e){var t=e.getItem();delete t._loader;for(var n=this._currentLoads.length,r=0;n>r;r++)if(this._currentLoads[r]==e){this._currentLoads.splice(r,1);break}},t._handleProgress=function(e){var t=e.target;this._sendFileProgress(t.getItem(),t.progress),this._updateProgress()},t._updateProgress=function(){var e=this._numItemsLoaded/this._numItems,t=this._numItems-this._numItemsLoaded;if(t>0){for(var n=0,r=0,i=this._currentLoads.length;i>r;r++)n+=this._currentLoads[r].progress;e+=n/t*(t/this._numItems)}this._lastProgress!=e&&(this._sendProgress(e),this._lastProgress=e)},t._disposeItem=function(e){delete this._loadedResults[e.id],delete this._loadedRawResults[e.id],delete this._loadItemsById[e.id],delete this._loadItemsBySrc[e.src]},t._sendFileProgress=function(e,t){if(this._isCanceled())return void this._cleanUp();if(this.hasEventListener("fileprogress")){var n=new createjs.Event("fileprogress");n.progress=t,n.loaded=t,n.total=1,n.item=e,this.dispatchEvent(n)}},t._sendFileComplete=function(e,t){if(!this._isCanceled()){var n=new createjs.Event("fileload");n.loader=t,n.item=e,n.result=this._loadedResults[e.id],n.rawResult=this._loadedRawResults[e.id],e.completeHandler&&e.completeHandler(n),this.hasEventListener("fileload")&&this.dispatchEvent(n)}},t._sendFileStart=function(e){var t=new createjs.Event("filestart");t.item=e,this.hasEventListener("filestart")&&this.dispatchEvent(t)},t.toString=function(){return"[PreloadJS LoadQueue]"},createjs.LoadQueue=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e){this.AbstractLoader_constructor(e,!0,createjs.AbstractLoader.TEXT)}var t=(createjs.extend(e,createjs.AbstractLoader),e);t.canLoadItem=function(e){return e.type==createjs.AbstractLoader.TEXT},createjs.TextLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e){this.AbstractLoader_constructor(e,!0,createjs.AbstractLoader.BINARY),this.on("initialize",this._updateXHR,this)}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.BINARY},t._updateXHR=function(e){e.loader.setResponseType("arraybuffer")},createjs.BinaryLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t){this.AbstractLoader_constructor(e,t,createjs.AbstractLoader.CSS),this.resultFormatter=this._formatResult,this._tagSrcAttribute="href",this._tag=document.createElement(t?"style":"link"),this._tag.rel="stylesheet",this._tag.type="text/css"}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.CSS},t._formatResult=function(e){if(this._preferXHR){var t=e.getTag(),n=document.getElementsByTagName("head")[0];if(n.appendChild(t),t.styleSheet)t.styleSheet.cssText=e.getResult(!0);else{var r=document.createTextNode(e.getResult(!0));t.appendChild(r)}}else t=this._tag;return t},createjs.CSSLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t){this.AbstractLoader_constructor(e,t,createjs.AbstractLoader.IMAGE),this.resultFormatter=this._formatResult,this._tagSrcAttribute="src",createjs.RequestUtils.isImageTag(e)?this._tag=e:createjs.RequestUtils.isImageTag(e.src)?this._tag=e.src:createjs.RequestUtils.isImageTag(e.tag)&&(this._tag=e.tag),null!=this._tag?this._preferXHR=!1:this._tag=document.createElement("img"),this.on("initialize",this._updateXHR,this)}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.IMAGE},t.load=function(){if(""!=this._tag.src&&this._tag.complete)return void this._sendComplete();var e=this._item.crossOrigin;1==e&&(e="Anonymous"),null==e||createjs.RequestUtils.isLocal(this._item.src)||(this._tag.crossOrigin=e),this.AbstractLoader_load()},t._updateXHR=function(e){e.loader.mimeType="text/plain; charset=x-user-defined-binary",e.loader.setResponseType&&e.loader.setResponseType("blob")},t._formatResult=function(e){var t=this;return function(n){var r=t._tag,i=window.URL||window.webkitURL;if(t._preferXHR)if(i){var s=i.createObjectURL(e.getResult(!0));r.src=s,r.onload=function(){i.revokeObjectURL(t.src)}}else r.src=e.getItem().src;r.complete?n(r):r.onload=function(){n(this)}}},createjs.ImageLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t){this.AbstractLoader_constructor(e,t,createjs.AbstractLoader.JAVASCRIPT),this.resultFormatter=this._formatResult,this._tagSrcAttribute="src",this.setTag(document.createElement("script"))}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.JAVASCRIPT},t._formatResult=function(e){var t=e.getTag();return this._preferXHR&&(t.text=e.getResult(!0)),t},createjs.JavaScriptLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e){this.AbstractLoader_constructor(e,!0,createjs.AbstractLoader.JSON),this.resultFormatter=this._formatResult}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.JSON&&!e._loadAsJSONP},t._formatResult=function(e){var t=null;try{t=createjs.DataUtils.parseJSON(e.getResult(!0))}catch(n){var r=new createjs.ErrorEvent("JSON_FORMAT",null,n);return this._sendError(r),n}return t},createjs.JSONLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e){this.AbstractLoader_constructor(e,!1,createjs.AbstractLoader.JSONP),this.setTag(document.createElement("script")),this.getTag().type="text/javascript"}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.JSONP||e._loadAsJSONP},t.cancel=function(){this.AbstractLoader_cancel(),this._dispose()},t.load=function(){if(null==this._item.callback)throw new Error("callback is required for loading JSONP requests.");if(null!=window[this._item.callback])throw new Error("JSONP callback '"+this._item.callback+"' already exists on window. You need to specify a different callback or re-name the current one.");window[this._item.callback]=createjs.proxy(this._handleLoad,this),window.document.body.appendChild(this._tag),this._tag.src=this._item.src},t._handleLoad=function(e){this._result=this._rawResult=e,this._sendComplete(),this._dispose()},t._dispose=function(){window.document.body.removeChild(this._tag),delete window[this._item.callback]},createjs.JSONPLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e){this.AbstractLoader_constructor(e,null,createjs.AbstractLoader.MANIFEST),this._manifestQueue=null}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.MANIFEST_PROGRESS=.25,n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.MANIFEST},t.load=function(){this.AbstractLoader_load()},t._createRequest=function(){var e=this._item.callback;this._request=null!=e?new createjs.JSONPLoader(this._item):new createjs.JSONLoader(this._item)},t.handleEvent=function(e){switch(e.type){case"complete":return this._rawResult=e.target.getResult(!0),this._result=e.target.getResult(),this._sendProgress(n.MANIFEST_PROGRESS),void this._loadManifest(this._result);case"progress":return e.loaded*=n.MANIFEST_PROGRESS,this.progress=e.loaded/e.total,(isNaN(this.progress)||1/0==this.progress)&&(this.progress=0),void this._sendProgress(e)}this.AbstractLoader_handleEvent(e)},t.destroy=function(){this.AbstractLoader_destroy(),this._manifestQueue.close()},t._loadManifest=function(e){if(e&&e.manifest){var t=this._manifestQueue=new createjs.LoadQueue;t.on("fileload",this._handleManifestFileLoad,this),t.on("progress",this._handleManifestProgress,this),t.on("complete",this._handleManifestComplete,this,!0),t.on("error",this._handleManifestError,this,!0),t.loadManifest(e)}else this._sendComplete()},t._handleManifestFileLoad=function(e){e.target=null,this.dispatchEvent(e)},t._handleManifestComplete=function(){this._loadedItems=this._manifestQueue.getItems(!0),this._sendComplete()},t._handleManifestProgress=function(e){this.progress=e.progress*(1-n.MANIFEST_PROGRESS)+n.MANIFEST_PROGRESS,this._sendProgress(this.progress)},t._handleManifestError=function(e){var t=new createjs.Event("fileerror");t.item=e.data,this.dispatchEvent(t)},createjs.ManifestLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t){this.AbstractMediaLoader_constructor(e,t,createjs.AbstractLoader.SOUND),createjs.RequestUtils.isAudioTag(e)?this._tag=e:createjs.RequestUtils.isAudioTag(e.src)?this._tag=e:createjs.RequestUtils.isAudioTag(e.tag)&&(this._tag=createjs.RequestUtils.isAudioTag(e)?e:e.src),null!=this._tag&&(this._preferXHR=!1)}var t=createjs.extend(e,createjs.AbstractMediaLoader),n=e;n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.SOUND},t._createTag=function(e){var t=document.createElement("audio");return t.autoplay=!1,t.preload="none",t.src=e,t},createjs.SoundLoader=createjs.promote(e,"AbstractMediaLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t){this.AbstractMediaLoader_constructor(e,t,createjs.AbstractLoader.VIDEO),createjs.RequestUtils.isVideoTag(e)||createjs.RequestUtils.isVideoTag(e.src)?(this.setTag(createjs.RequestUtils.isVideoTag(e)?e:e.src),this._preferXHR=!1):this.setTag(this._createTag())}var t=createjs.extend(e,createjs.AbstractMediaLoader),n=e;t._createTag=function(){return document.createElement("video")},n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.VIDEO},createjs.VideoLoader=createjs.promote(e,"AbstractMediaLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e){this.AbstractLoader_constructor(e,null,createjs.AbstractLoader.SPRITESHEET),this._manifestQueue=null}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.SPRITESHEET_PROGRESS=.25,n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.SPRITESHEET},t.destroy=function(){this.AbstractLoader_destroy,this._manifestQueue.close()},t._createRequest=function(){var e=this._item.callback;this._request=null!=e&&e instanceof Function?new createjs.JSONPLoader(this._item):new createjs.JSONLoader(this._item)},t.handleEvent=function(e){switch(e.type){case"complete":return this._rawResult=e.target.getResult(!0),this._result=e.target.getResult(),this._sendProgress(n.SPRITESHEET_PROGRESS),void this._loadManifest(this._result);case"progress":return e.loaded*=n.SPRITESHEET_PROGRESS,this.progress=e.loaded/e.total,(isNaN(this.progress)||1/0==this.progress)&&(this.progress=0),void this._sendProgress(e)}this.AbstractLoader_handleEvent(e)},t._loadManifest=function(e){if(e&&e.images){var t=this._manifestQueue=new createjs.LoadQueue;t.on("complete",this._handleManifestComplete,this,!0),t.on("fileload",this._handleManifestFileLoad,this),t.on("progress",this._handleManifestProgress,this),t.on("error",this._handleManifestError,this,!0),t.loadManifest(e.images)}},t._handleManifestFileLoad=function(e){var t=e.result;if(null!=t){var n=this.getResult().images,r=n.indexOf(e.item.src);n[r]=t}},t._handleManifestComplete=function(){this._result=new createjs.SpriteSheet(this._result),this._loadedItems=this._manifestQueue.getItems(!0),this._sendComplete()},t._handleManifestProgress=function(e){this.progress=e.progress*(1-n.SPRITESHEET_PROGRESS)+n.SPRITESHEET_PROGRESS,this._sendProgress(this.progress)},t._handleManifestError=function(e){var t=new createjs.Event("fileerror");t.item=e.data,this.dispatchEvent(t)},createjs.SpriteSheetLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e,t){this.AbstractLoader_constructor(e,t,createjs.AbstractLoader.SVG),this.resultFormatter=this._formatResult,this._tagSrcAttribute="data",t?this.setTag(document.createElement("svg")):(this.setTag(document.createElement("object")),this.getTag().type="image/svg+xml"),this.getTag().style.visibility="hidden"}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.SVG},t._formatResult=function(e){var t=createjs.DataUtils.parseXML(e.getResult(!0),"text/xml"),n=e.getTag();return!this._preferXHR&&document.body.contains(n)&&document.body.removeChild(n),null!=t.documentElement?(n.appendChild(t.documentElement),n.style.visibility="visible",n):t},createjs.SVGLoader=createjs.promote(e,"AbstractLoader")}(),this.createjs=this.createjs||{},function(){"use strict";function e(e){this.AbstractLoader_constructor(e,!0,createjs.AbstractLoader.XML),this.resultFormatter=this._formatResult}var t=createjs.extend(e,createjs.AbstractLoader),n=e;n.canLoadItem=function(e){return e.type==createjs.AbstractLoader.XML},t._formatResult=function(e){return createjs.DataUtils.parseXML(e.getResult(!0),"text/xml")},createjs.XMLLoader=createjs.promote(e,"AbstractLoader")}(),define("shared/utils/FileLoader",["superhero","preloadjs"],function(e,t){var n={};return e.Module.extend({loadManifest:function(e,t,n){var r=new createjs.LoadQueue;r.name=e,this._setup(r,n),r.loadManifest(t)},loadFile:function(e,t){var n=e.id,r=new createjs.LoadQueue;r.name=n,this._setup(r,t),r.loadFile(e)},disposeManifest:function(e){for(var t=0,n=e.length;t<n;t++)this.disposeFile(e[t].id)},disposeFile:function(e){var t=t||e;if(!n[e])return;var r=n[e].queue;r.remove(e),delete n[e]},getFile:function(e){return n[e]?n[e].result:null},_setup:function(e,t){e.setMaxConnections(10),t||e.setUseXHR(!1),_.bindAll(this,"_handleFileLoad","_handleQueueComplete","_handleQueueProgress","_handleQueueError"),e.on("fileload",this._handleFileLoad),e.on("complete",this._handleQueueComplete),e.on("progress",this._handleQueueProgress),e.on("error",this._handleQueueError)},_disposeQueue:function(e){e.off(),e.close()},_handleFileLoad:function(e){n[e.item.id]={result:e.result,queue:e.target},this.trigger("file:load:complete",{name:e.target.name})},_handleQueueComplete:function(e){this.trigger("manifest:load:complete",{name:e.target.name}),this._disposeQueue(e.target)},_handleQueueProgress:function(e){this.trigger("manifest:load:progress",{name:e.target.name,progress:e.loaded})},_handleQueueError:function(e){this.trigger("file:load:error",{src:e.target.src,id:e.target.id})}})}),define("shared/models/PickupModel",["superhero"],function(e){return e.Model.extend({defaults:{dates:[{stop:1,location:"Oostelijke Handelskade 2",time:"17:00 & 20:45"},{stop:2,location:"Herengracht 4",time:"17:45 & 19:15"},{stop:3,location:"Herengracht 258",time:"18:00 & 19:30 "},{stop:4,location:"Herengracht 477",time:"18:15 & 19:45"},{stop:5,location:"Herengracht 506",time:"18:30 & 20:00"},{stop:6,location:"Herengracht 577",time:"18:45 & 20:15"}]}})}),define("shared/views/components/CanalsComponent",["superhero","snap","shared/utils/ResizeManager","shared/utils/GestureManager","shared/utils/FileLoader","shared/models/PickupModel"],function(e,t,n,r,i,s){return e.Component.extend({BEZIER_PATH:"",BACKGROUND_WIDTH:8e3,BOAT_WIDTH:105,BOAT_OFFSET_Y:-50,BOAT_OFFSET_X:-52,SLIDE_DISTANCE:10,SCROLL_DISTANCE:200,SCROLL_TIME:1.2,POSITION_START:640,POSITIONS_DOCKS:[1486,2784,3940,4920,6096,7216],ui:{boatContainer:".component-canals-boat-container",boat:".component-canals-boat",boatShadow:".component-canals-boat-shadow",bg:".component-canals-background",fg:".component-canals-foreground",car1:"#component-canals-car-1",car2:"#component-canals-car-2",car3:"#component-canals-car-3",truck1:"#component-canals-truck-1",truck2:"#component-canals-truck-2",scooter1:"#component-canals-scooter-1",scooter2:"#component-canals-scooter-2",pickupPoints:".component-canals-pickuppoint",pickup1:"#component-canals-pickuppoint-1",pickup2:"#component-canals-pickuppoint-2",pickup3:"#component-canals-pickuppoint-3",pickup4:"#component-canals-pickuppoint-4",pickup5:"#component-canals-pickuppoint-5",pickup6:"#component-canals-pickuppoint-6"},events:{"mouseover .component-canals-pickuppoint":"_pickupPointRollOverHandler","mouseout .component-canals-pickuppoint":"_pickupPointRollOutHandler"},initialize:function(){_.bindAll(this,"_showIntro","_hideIntro","_onUpdateTimelineHandler","transitionIn","_enableControls"),this._pickupModel=new s,this._siteIntro=document.getElementById("site-intro"),this._listSponsors=document.getElementById("list-sponsors"),this._currentPosition=0,this._currentSelectedItem=-1,this._speed=1,this._scrollSpeed=.1,this._pathArray=[],this._controlsEnabled=!1},onInitialized:function(){this.fileLoader=new i,r.setElement(this.el.parentNode);var e=window.svg1=this.fileLoader.getFile("boatpath");this._convertPath(e.getElementsByTagName("g")[1].getElementsByTagName("path")[0].getAttribute("d")),this._setupEventListeners(),this._updateBackgroundSize(),this._setupTimeline(),this._setupForegroundElements()},onClose:function(){r.close()},selectItem:function(e){if(this._currentSelectedItem===e-1)return;this._tweenIntro&&this._tweenIntro.kill(),this._currentSelectedItem=e;var t=this.POSITIONS_DOCKS[e-1],n=Math.abs(t-this.tl.time()),r=n/300;TweenMax.to(this.tl,r,{time:t,ease:Power1.easeInOut,onUpdate:this._onUpdateTimelineHandler}),this._enableControls(),this._showSignupButton()},_setupEventListeners:function(){r.enableSlide("x"),this.listenTo(n,"resize",this._windowResizeHandler),this.listenTo(r,"scroll:up",this._scrollHandler),this.listenTo(r,"scroll:down",this._scrollHandler),this.listenTo(r,"slide:left:moving",this._slideHandler),this.listenTo(r,"slide:right:moving",this._slideHandler)},transitionIn:function(){var e=this;this._tweenIntro=TweenMax.to(this.tl,2.5,{time:this.POSITION_START,ease:Power2.easeInOut,delay:.5,onComplete:function(){e._currentPosition=e.POSITION_START,e._enableControls()}})},_enableControls:function(){this._controlsEnabled=!0},_disableControls:function(){this._controlsEnabled=!1},_setupTimeline:function(){this.tl=new TimelineMax({useFrames:!0,repeat:-1,paused:!0}),TweenMax.set(this.ui.boat,{x:this._pathArray[0].x,y:this._pathArray[0].y}),TweenMax.set(this.ui.boatShadow,{x:this._pathArray[0].x,y:this._pathArray[0].y}),this.tl.to(this.ui.boat,this.BACKGROUND_WIDTH/this._speed,{bezier:{type:"cubic",values:this._pathArray,autoRotate:!0},force3D:!0,ease:Linear.easeNone},0),this.tl.to(this.ui.boatShadow,this.BACKGROUND_WIDTH/this._speed,{bezier:{type:"cubic",values:this._pathArray,autoRotate:!0},force3D:!0,ease:Linear.easeNone},0),this.tl.to(this.ui.bg,this.BACKGROUND_WIDTH/this._speed,{x:-this.BACKGROUND_WIDTH,ease:Linear.easeNone},0),this.tl.to(this.ui.boatContainer,this.BACKGROUND_WIDTH/this._speed,{x:-this.BACKGROUND_WIDTH,ease:Linear.easeNone},0),this.tl.to(this.ui.fg,this.BACKGROUND_WIDTH/this._speed,{x:-this.BACKGROUND_WIDTH,ease:Linear.easeNone},0)},_setupForegroundElements:function(){var e=20;TweenMax.set(this.ui.car1,{x:1080,y:531,transformOrigin:"50% 50%"}),this.car1Timeline=new TimelineMax({repeat:-1}),this.car1Timeline.to(this.ui.car1,.1,{y:529,yoyo:!0,repeat:e/.1-1}),this.car1Timeline.to(this.ui.car1,e/2,{x:2320,ease:Linear.easeNone},0),this.car1Timeline.to(this.ui.car1,0,{scaleX:-1},e/2),this.car1Timeline.to(this.ui.car1,e/2,{x:1080,ease:Linear.easeNone},e/2);var t=10;TweenMax.set(this.ui.car2,{x:4610,y:546,transformOrigin:"50% 50%"}),this.car2Timeline=new TimelineMax({repeat:-1}),this.car2Timeline.to(this.ui.car2,.1,{y:544,yoyo:!0,repeat:t/.1-1}),this.car2Timeline.to(this.ui.car2,t/2,{x:5200,ease:Linear.easeNone},0),this.car2Timeline.to(this.ui.car2,0,{scaleX:-1},t/2),this.car2Timeline.to(this.ui.car2,t/2,{x:4610,ease:Linear.easeNone},t/2);var n=10;TweenMax.set(this.ui.car3,{x:6800,y:704,scaleY:-1,transformOrigin:"50% 50%"}),this.car3Timeline=new TimelineMax({repeat:-1}),this.car3Timeline.to(this.ui.car3,.1,{y:706,yoyo:!0,repeat:n/.1-1}),this.car3Timeline.to(this.ui.car3,n/2,{x:7450,ease:Linear.easeNone},0),this.car3Timeline.to(this.ui.car3,0,{scaleX:-1},n/2),this.car3Timeline.to(this.ui.car3,n/2,{x:6800,ease:Linear.easeNone},n/2);var r=30;TweenMax.set(this.ui.truck1,{x:2260,y:652,scaleY:-1,transformOrigin:"50% 50%"}),this.truck1Timeline=new TimelineMax({repeat:-1}),this.truck1Timeline.to(this.ui.truck1,.1,{y:654,yoyo:!0,repeat:r/.1-1}),this.truck1Timeline.to(this.ui.truck1,r/2,{x:1110,ease:Linear.easeNone},0),this.truck1Timeline.to(this.ui.truck1,0,{scaleX:-1},r/2),this.truck1Timeline.to(this.ui.truck1,r/2,{x:2260,ease:Linear.easeNone},r/2);var i=20;TweenMax.set(this.ui.truck2,{x:4160,y:494,transformOrigin:"50% 50%"}),this.truck2Timeline=new TimelineMax({repeat:-1}),this.truck2Timeline.to(this.ui.truck2,.1,{y:492,yoyo:!0,repeat:i/.1-1}),this.truck2Timeline.to(this.ui.truck2,i/2,{x:3640,ease:Linear.easeNone},0),this.truck2Timeline.to(this.ui.truck2,0,{scaleX:-1},i/2),this.truck2Timeline.to(this.ui.truck2,i/2,{x:4160,ease:Linear.easeNone},i/2);var s=10;TweenMax.set(this.ui.scooter1,{x:3e3,y:581,scaleY:-1,transformOrigin:"50% 50%"}),this.scooter1Timeline=new TimelineMax({repeat:-1}),this.scooter1Timeline.to(this.ui.scooter1,.1,{y:582,yoyo:!0,repeat:s/.1-1}),this.scooter1Timeline.to(this.ui.scooter1,s/2,{x:2500,ease:Linear.easeNone},0),this.scooter1Timeline.to(this.ui.scooter1,0,{scaleX:-1},s/2),this.scooter1Timeline.to(this.ui.scooter1,s/2,{x:3e3,ease:Linear.easeNone},s/2);var o=20;TweenMax.set(this.ui.scooter2,{x:6450,y:544,transformOrigin:"50% 50%"}),this.scooter2Timeline=new TimelineMax({repeat:-1}),this.scooter2Timeline.to(this.ui.scooter2,.1,{y:543,yoyo:!0,repeat:o/.1-1}),this.scooter2Timeline.to(this.ui.scooter2,o/2,{x:5830,ease:Linear.easeNone},0),this.scooter2Timeline.to(this.ui.scooter2,0,{scaleX:-1},o/2),this.scooter2Timeline.to(this.ui.scooter2,o/2,{x:6450,ease:Linear.easeNone},o/2)},_createPoint:function(e){for(var t=0;t<e.length;t+=2){var n={};n.x=e[t]+this.BOAT_OFFSET_X,n.y=e[t+1]+this.BOAT_OFFSET_Y,this._pathArray.unshift(n)}},_convertPath:function(e){var n=t.path.toCubic(e);for(var r=0;r<n.length;r++){var i=n[r],s;i.shift(),s=this._createPoint(i)}},_updateBackgroundSize:function(){var e=Math.ceil(n.viewportWidth()/2);TweenMax.set(this.el,{x:e}),this.ui.bg.style.width=Math.ceil(this.BACKGROUND_WIDTH+n.viewportWidth())+"px"},_updatePosition:function(e){e=Math.max(0,e),TweenMax.to(this.tl,this.SCROLL_TIME,{totalTime:e,ease:Power1.easeOut,overwrite:5,onUpdate:this._onUpdateTimelineHandler,onComplete:this._onUpdateTimelineHandler})},_showIntro:function(){TweenMax.to(this._siteIntro,.3,{autoAlpha:1,y:0,ease:Power1.easeOut}),TweenMax.to(this._listSponsors,.3,{autoAlpha:1,y:0,ease:Power1.easeOut})},_hideIntro:function(){TweenMax.to(this._siteIntro,.2,{autoAlpha:0,y:0,ease:Linear.easeNone}),TweenMax.to(this._listSponsors,.2,{autoAlpha:0,y:0,ease:Linear.easeNone})},_showSignupButton:function(e){for(var t=0,n=this.POSITIONS_DOCKS.length;t<n;t++)if(t==e){var r="pickup"+(t+1);TweenMax.to(this.ui[r],.2,{scale:1,autoAlpha:1,ease:Back.easeOut})}else{var r="pickup"+(t+1);TweenMax.to(this.ui[r],.2,{scale:0,autoAlpha:0,ease:Back.easeIn})}},_checkItemInRange:function(e){var t=e%this.BACKGROUND_WIDTH,n=!0;for(var r=0,i=this.POSITIONS_DOCKS.length;r<i;r++){var s=this.POSITIONS_DOCKS[r];if(t>s-250&&t<s+250){n=!1;if(this._currentSelectedItem!==r){this._currentSelectedItem=r,this._showSignupButton(r);var o=this._pickupModel.get("dates")[r];this.trigger("dock:active",{id:r,name:o.location})}}}n&&this._currentSelectedItem!==undefined&&this._currentSelectedItem!==null&&(this._currentSelectedItem=null,this._showSignupButton(null),this.trigger("dock:active",{id:null}));if(t>this.POSITION_START-150&&t<this.POSITION_START+150)this._introIsVisible||(this._introIsVisible=!0,this._showIntro());else if(this._introIsVisible||this._introIsVisible==undefined)this._introIsVisible=!1,this._hideIntro()},_slideHandler:function(e){if(this._controlsEnabled===!0){this._currentPosition=this.tl.totalTime();var t=e.delta,n=this._currentPosition-parseInt(t*this.SLIDE_DISTANCE);this._updatePosition(n)}},_scrollHandler:function(e){if(this._controlsEnabled===!0){this._currentPosition=this.tl.totalTime();var t=e.delta,n=this._currentPosition-parseInt(t*this.SCROLL_DISTANCE);this._updatePosition(n)}},_pickupPointRollOverHandler:function(e){var t=e.delegateTarget;TweenMax.killTweensOf(t),TweenMax.to(t,.5,{scale:.95,ease:Back.easeOut}),TweenMax.to(t,.2,{rotation:20,ease:Power1.easeOut}),TweenMax.to(t,.4,{rotation:-20,ease:Power1.easeInOut,delay:.2}),TweenMax.to(t,.4,{rotation:0,ease:Back.easeOut,delay:.6})},_pickupPointRollOutHandler:function(e){var t=e.delegateTarget;TweenMax.killTweensOf(t),TweenMax.to(t,.3,{rotation:0,ease:Power1.easeInOut}),TweenMax.to(t,.3,{scale:1,ease:Back.easeOut})},_windowResizeHandler:function(e){this._updateBackgroundSize()},_onUpdateTimelineHandler:function(){this._checkItemInRange(this.tl.totalTime())}})}),define("desktop/views/components/PaginatorComponent",["superhero"],function(e){return e.Component.extend({ui:{buttons:".paginator-link",lines:".paginator-copy-lines",line1:".paginator-copy-line--1",line2:".paginator-copy-line--2"},events:{},initialize:function(){_.bindAll(this,"_buttonMouseenterHandler","_buttonMouseleaveHandler")},onInitialized:function(){this._setupButtons(),this._setupEventListeners()},onClose:function(){this._removeEventListeners()},setButtonActive:function(e,t){this._currentActiveButtonId=e;for(var n,r,i,s=0,o=this.ui.buttons.length;s<o;s++)n=this.ui.buttons[s],r=n.querySelector(".paginator-link-dot"),i=n.querySelector(".paginator-link-anchor"),s==e?(n.classList.add("paginator-link--active"),TweenMax.to(r,.1,{scale:0}),TweenMax.to(i,.35,{scale:1,ease:Back.easeOut})):(n.classList.remove("paginator-link--active"),TweenMax.to(r,.2,{scale:1,ease:Back.easeOut}),TweenMax.to(i,.3,{scale:0}));e!=null&&e!=undefined?this._showPickupCopy(t):this._hidePickupCopy()},_showPickupCopy:function(e){e=e||"Pickup point",this.ui.line1.textContent=e,TweenMax.to(this.ui.lines,.35,{y:16,ease:Power1.easeInOut})},_hidePickupCopy:function(){TweenMax.to(this.ui.lines,.35,{y:0,ease:Power1.easeInOut})},_setupEventListeners:function(){for(var e=0,t=this.ui.buttons.length;e<t;e++)this.ui.buttons[e].addEventListener("mouseenter",this._buttonMouseenterHandler),this.ui.buttons[e].addEventListener("mouseleave",this._buttonMouseleaveHandler)},_removeEventListeners:function(){for(var e=0,t=this.ui.buttons.length;e<t;e++)this.ui.buttons[e].removeEventListener("mouseenter",this._buttonMouseenterHandler),this.ui.buttons[e].removeEventListener("mouseleave",this._buttonMouseleaveHandler)},_setupButtons:function(){for(var e,t,n,r=0,i=this.ui.buttons.length;r<i;r++)e=this.ui.buttons[r],t=e.querySelector(".paginator-link-dot"),n=e.querySelector(".paginator-link-anchor"),TweenMax.set(n,{scale:0})},_buttonMouseenterHandler:function(e){var t=e.currentTarget,n=parseInt(t.dataset.value,10);if(n==this._currentActiveButtonId)return;var r=t.querySelector(".paginator-link-dot"),i=t.querySelector(".paginator-link-anchor");t.classList.add("paginator-link--active"),TweenMax.to(r,.1,{scale:0}),TweenMax.to(i,.35,{scale:1,ease:Back.easeOut})},_buttonMouseleaveHandler:function(e){var t=e.currentTarget,n=parseInt(t.dataset.value,10);if(n==this._currentActiveButtonId)return;var r=t.querySelector(".paginator-link-dot"),i=t.querySelector(".paginator-link-anchor");t.classList.add("paginator-link--active"),TweenMax.to(r,.2,{scale:1,ease:Back.easeOut}),TweenMax.to(i,.3,{scale:0})}})}),define("desktop/views/pages/HomeView",["superhero","shared/views/components/CanalsComponent","desktop/views/components/PaginatorComponent"],function(e,t,n){return e.LayoutView.extend({className:"page page-home",template:"pages/home",regions:{signup:".signup"},components:{canals:{selector:".component-canals",type:t},paginator:{selector:".paginator",type:n}},ui:{paginator:".paginator",paginatorLink:".paginator-link",siteLogo:".site-logo-large",siteIntro:".site-intro",sponsors:".list-sponsors"},initialize:function(){},onInitialized:function(){TweenMax.set(this.components.canals.el,{autoAlpha:0}),TweenMax.set(this.ui.paginator,{y:"100%"}),this._timelineIn=new TimelineMax({paused:!0}),this._timelineIn.insert(new TweenMax.fromTo(this.components.canals.el,1,{autoAlpha:0},{autoAlpha:1}),.3),this._timelineIn.addCallback(this.components.canals.transitionIn,.3),this._timelineIn.insert(new TweenMax.to(this.ui.paginator,.5,{y:"0%",ease:Power3.easeOut}),2.5),this._timelineIn.insert(new TweenMax.staggerFrom(this.ui.paginatorLink,.7,{y:"50%",ease:Back.easeOut},.02),2.6),this._timelineIn.insert(new TweenMax.fromTo(this.ui.siteLogo,.5,{autoAlpha:0,y:30},{autoAlpha:1,y:0,ease:Power3.easeOut}),2.5),this._timelineIn.insert(new TweenMax.fromTo(this.ui.siteIntro,.5,{autoAlpha:0,y:30},{autoAlpha:1,y:0,ease:Power3.easeOut}),2.6),this._timelineIn.insert(new TweenMax.fromTo(this.ui.sponsors,.5,{autoAlpha:0,y:30},{autoAlpha:1,y:0,ease:Power3.easeOut}),2.8),this._timelineOut=new TimelineMax({paused:!0}),this._timelineOut.insert(new TweenMax.to(this.ui.paginator,.3,{y:"100%",ease:Power2.easeIn}),0),this._timelineOut.insert(new TweenMax.to(this.components.canals.el,.5,{autoAlpha:0}),0),this._timelineOut.insert(new TweenMax.to(this.ui.siteLogo,.3,{autoAlpha:0,ease:Power1.easeIn}),0),this._timelineOut.insert(new TweenMax.to(this.ui.siteIntro,.3,{autoAlpha:0,ease:Power1.easeIn}),0),this._timelineOut.insert(new TweenMax.to(this.ui.sponsors,.3,{autoAlpha:0,ease:Power1.easeIn}),0),this._setupEventListeners()},onClose:function(){this._timelineIn&&this._timelineIn.kill(),this._timelineOut&&this._timelineOut.kill()},transitionIn:function(){this._timelineIn.play(0)},transitionOut:function(e){this._timelineOut.play(0),TweenMax.delayedCall(.6,e)},gotoItem:function(e){this.components.canals.selectItem(e)},selectItem:function(e,t){this.components.canals.selectItem(e),this.components.paginator.setButtonActive(e-1,t)},_setupEventListeners:function(){this.listenTo(this.components.canals,"dock:active",this._dockActiveHandler)},_dockActiveHandler:function(e){this.components.paginator.setButtonActive(e.id,e.name)}})}),define("desktop/views/pages/AboutView",["superhero","shared/utils/ResizeManager"],function(e,t){return e.View.extend({className:"page page-about",template:"pages/about",ui:{inner:".page-inner",logo:".site-logo-small",header:".panel-header",stripe:".panel-header-stripe",body:".panel-text",buttonSelect:".button-about-select"},initialize:function(){_.bindAll(this,"_resizeManager")},onInitialized:function(){this._resize(),this._setupEventListener(),this._setupIntroTimeline()},_setupIntroTimeline:function(){this._introTimeline=new TimelineMax({paused:!0}),this._introTimeline.insert(TweenMax.fromTo(this.ui.logo,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut})),this._introTimeline.insert(TweenMax.fromTo(this.ui.header,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.1),this._introTimeline.insert(TweenMax.fromTo(this.ui.stripe,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.2),this._introTimeline.insert(TweenMax.fromTo(this.ui.body,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.3),this._introTimeline.insert(TweenMax.fromTo(this.ui.buttonSelect,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.4)},transitionIn:function(){this._introTimeline.play()},transitionOut:function(e){TweenMax.to(this.el,.5,{autoAlpha:0,y:-50,ease:Power2.easeIn,onComplete:e})},_setupEventListener:function(){this.listenTo(t,"resize",this._resizeManager)},_resize:function(){var e=t.viewportHeight(),n=this.ui.inner.offsetHeight,r=80;n<e-160&&(r=(e-n)/2),this.ui.inner.style.marginTop=Math.floor(r)+"px"},_resizeManager:function(){this._resize()}})}),define("desktop/views/pages/WhosBehindThisView",["superhero","shared/utils/ResizeManager"],function(e,t){return e.View.extend({className:"page page-whosbehindthis",template:"pages/whosbehindthis",ui:{inner:".page-inner",logo:".site-logo-small",header:".panel-header",stripe:".panel-header-stripe",listItems:".whosbehindthis-list li",buttonSelect:".button-whosbehindthis-select"},initialize:function(){_.bindAll(this,"_resizeManager")},onInitialized:function(){this._resize(),this._setupEventListener(),this._setupIntroTimeline()},_setupIntroTimeline:function(){this._introTimeline=new TimelineMax({paused:!0}),this._introTimeline.insert(TweenMax.fromTo(this.ui.logo,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut})),this._introTimeline.insert(TweenMax.fromTo(this.ui.header,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.1),this._introTimeline.insert(TweenMax.fromTo(this.ui.stripe,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.2),this._introTimeline.insert(TweenMax.fromTo([this.ui.listItems[0],this.ui.listItems[1]],.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.3),this._introTimeline.insert(TweenMax.fromTo([this.ui.listItems[2],this.ui.listItems[3]],.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.4),this._introTimeline.insert(TweenMax.fromTo(this.ui.buttonSelect,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.5)},transitionIn:function(){this._introTimeline.play()},transitionOut:function(e){TweenMax.to(this.el,.5,{autoAlpha:0,y:-50,ease:Power2.easeIn,onComplete:e})},_setupEventListener:function(){this.listenTo(t,"resize",this._resizeManager)},_resize:function(){var e=t.viewportHeight(),n=this.ui.inner.offsetHeight,r=80;n<e-160&&(r=(e-n)/2),this.ui.inner.style.marginTop=Math.floor(r)+"px"},_resizeManager:function(){this._resize()}})}),define("desktop/views/pages/NotInvitedView",["superhero","shared/utils/ResizeManager"],function(e,t){return e.View.extend({className:"page page-notinvited",template:"pages/notinvited",ui:{inner:".page-inner",logo:".site-logo-small",header:".panel-header",stripe:".panel-header-stripe",body:".panel-text",buttonSelect:".button-notinvited-request"},initialize:function(){_.bindAll(this,"_resizeManager")},onInitialized:function(){this._resize(),this._setupEventListener(),this._setupIntroTimeline()},_setupIntroTimeline:function(){this._introTimeline=new TimelineMax({paused:!0}),this._introTimeline.insert(TweenMax.fromTo(this.ui.logo,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut})),this._introTimeline.insert(TweenMax.fromTo(this.ui.header,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.1),this._introTimeline.insert(TweenMax.fromTo(this.ui.stripe,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.2),this._introTimeline.insert(TweenMax.fromTo(this.ui.body,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.3),this._introTimeline.insert(TweenMax.fromTo(this.ui.buttonSelect,.5,{opacity:0,y:40},{opacity:1,y:0,ease:Power2.easeOut}),.4)},transitionIn:function(){this._introTimeline.play()},transitionOut:function(e){TweenMax.to(this.el,.5,{autoAlpha:0,y:-50,ease:Power2.easeIn,onComplete:e})},_setupEventListener:function(){this.listenTo(t,"resize",this._resizeManager)},_resize:function(){var e=t.viewportHeight(),n=this.ui.inner.offsetHeight,r=80;n<e-160&&(r=(e-n)/2),this.ui.inner.style.marginTop=Math.floor(r)+"px"},_resizeManager:function(){this._resize()}})}),define("shared/utils/Social",["underscore","superhero"],function(e,t){var n=t.View.extend({initialize:function(){var e=this;window.fbAsyncInit=function(){FB.init({appId:Settings.fb_app_id,xfbml:!0,status:!1,version:"v2.1"}),e.trigger("social:loaded",{network:"facebook"})},function(e,t,n){var r,i=e.getElementsByTagName(t)[0];if(e.getElementById(n))return;r=e.createElement(t),r.id=n,r.src="//connect.facebook.net/en_US/sdk.js",i.parentNode.insertBefore(r,i)}(document,"script","facebook-jssdk"),window.twttr=function(e,t,n){var r,i,s=e.getElementsByTagName(t)[0];if(e.getElementById(n))return;return i=e.createElement(t),i.id=n,i.src="//platform.twitter.com/widgets.js",s.parentNode.insertBefore(i,s),window.twttr||(r={_e:[],ready:function(e){r._e.push(e)}})}(document,"script","twitter-wjs")},loadFacebook:function(e){var t=this;e&&e.callback&&e.callback(e.callbackParams[0],e.callbackParams[1],e.callbackParams[2])},loadTwitter:function(e){var t=this;window.twttr.ready(function(n){t.trigger("social:loaded",{network:"twitter"}),e&&e.callback&&e.callback(e.callbackParams[0],e.callbackParams[1],e.callbackParams[2])})}});return new n}),define("shared/utils/SocialSharer",["underscore","superhero","shared/utils/Social"],function(e,t,n){var r=t.View.extend({clickedFrom:"",initialize:function(){this.listenTo(n,"social:loaded",this.onLoaded),e.bindAll(this,"_twitterCallback","_facebookCallback","shareFacebook","shareTwitter","onLoaded")},onLoaded:function(e){this.setupTwitterCallback()},setupTwitterCallback:function(){window.twttr.ready(function(e){try{window.twttr.events.unbind("tweet",this._twitterCallback)}catch(t){}window.twttr.events.bind("tweet",this._twitterCallback)}.bind(this))},shareFacebook:function(e,t,n){this.clickedFrom=n;var e=e||document.location.href+document.location.hash;FB&&FB.ui({method:"feed",link:e,picture:Settings.imageURL+"/share.png",name:t&&t.title,description:t&&t.body},this._facebookCallback)},shareTwitter:function(e,t,n){this.clickedFrom=n;var e=e||document.location.href+document.location.hash,t=t||"";this.fireTwitterButtonClick(e,t)},fireTwitterButtonClick:function(e,t){var n=document.createElement("a"),r="https://twitter.com/intent/tweet?text="+encodeURIComponent(t.body)+"&url="+encodeURIComponent(e);n.setAttribute("href",r),n.setAttribute("target","_blank"),document.body.appendChild(n),twttr.widgets.load(),n.click(),document.body.removeChild(n)},_twitterCallback:function(){var e=this;this.clickedFrom=="final-screen-game"&&nl.shcc.eventBus.trigger("share-score:shareSuccess",{type:"twitter"}),e.trigger("shared",{type:"twitter"}),e.trigger("shared:twitter")},_facebookCallback:function(e){var t=this;e&&!e.error_code?(this.clickedFrom=="final-screen-game"&&nl.shcc.eventBus.trigger("share-score:shareSuccess",{type:"facebook"}),t.trigger("shared",{type:"facebook"}),t.trigger("shared:facebook")):t.trigger("error:facebook")}});return new r}),define("shared/models/CompaniesModel",["superhero"],function(e){return e.Model.extend({})}),define("shared/models/CompaniesCollection",["superhero","shared/models/CompaniesModel"],function(e,t){var n=e.Collection.extend({url:Settings.baseURL+"/api/companies",model:t});return new n}),define("shared/views/pages/SignupView",["superhero","shared/utils/SocialSharer","shared/models/CompaniesCollection","shared/models/PickupModel"],function(e,t,n,r){return e.View.extend({className:"page page-signup",template:"pages/signup",ui:{overlay:".signup-overlay",scroll:".signup-scroll",closeButton:".pickup-close",stopLocation:".address",stopTime:".time",agencies:".pickup-agencies",list:".list-agencies",selectedAgency:".selected-agency",form:".pickup-form",input:".input",inputName:".input-name",inputEmail:".input-email",inputValidation:".input-validation",inputValidationName:".input-validation-name",inputValidationEmail:".input-validation-email",inputValidationNameContent:".input-validation-content--name",inputValidationEmailContent:".input-validation-content--email",inputValidationMobile:".input-validation--mobile",inputValidationMobileContent:".input-validation-content--mobile",thanks:".pickup-thanks"},selectedCompany:null,selectedStop:null,validationMessage:{invalidDomein:"Your email domain is invalid. Please enter your company email address",invalidEmail:"Your email address is invalid.",duplicateEmail:"This email address is already taken.",emptyField:"This field is required.",tooManyInvites:'The guest list is full. Please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>'},events:{"click .pickup-close":"hideError","mouseover .pickup-close":"closeButtonMouseOverHandler","mouseout .pickup-close":"closeButtonMouseOutHandler","click .button-submit":"_submitForm","click .signup-scroll":"_signupScrollClickHandler","focusin .input-name":"_inputFocusHandler","focusin .input-email":"_inputFocusHandler","click .share-facebook":"_shareFacebookClickHandler","click .share-twitter":"_shareTwitterClickHandler"},initialize:function(e){_.bindAll(this,"_selectCompany"),this._options=e||{},this._pickupModel=new r},onInitialized:function(){var e=this._options.id;if(!e)return;this._setCompaniesList(e),this._getStopData(e),TweenMax.set(this.ui.scroll,{x:"100%",force3D:!0})},transitionIn:function(){TweenMax.fromTo(this.ui.scroll,.5,{x:"100%"},{x:"0%",force3D:!0,ease:Power2.easeOut}),this.ui.overlay&&TweenMax.fromTo(this.ui.overlay,.5,{alpha:0},{alpha:.2,force3D:!0})},transitionOut:function(e){TweenMax.fromTo(this.ui.scroll,.5,{x:"0%"},{x:"100%",force3D:!0,onComplete:e,ease:Power2.easeIn}),this.ui.overlay&&TweenMax.to(this.ui.overlay,.5,{alpha:0,force3D:!0})},_resetCompanyList:function(){var e=this.ui.list;while(e.firstChild)e.removeChild(e.firstChild)},_getStopData:function(e){var t=this._pickupModel.get("dates");this.selectedStop=e;for(var n=0,r=t.length;n<r;n++)t[n].stop==e&&this._setStopData(t[n].location,t[n].time)},_setStopData:function(e,t){var n=this.ui.stopLocation,r=this.ui.stopTime;n.textContent=e,r.textContent=t},_createCompanyListItem:function(e,t){var n=this.ui.list,r=document.createElement("li"),i=document.createElement("a"),s=document.createElement("span");i.textContent=t,i.appendChild(s),i.classList.add("button"),i.classList.add("button-agency"),i.setAttribute("data-company-id",e),i.setAttribute("data-company-name",t),i.addEventListener("click",this._selectCompany),r.classList.add("list-item"),r.classList.add("list-item-agencies"),r.appendChild(i),n.appendChild(r)},_setCompaniesList:function(e){var t=e-1,r;this._resetCompanyList();for(var i=0,s=n.length;i<s;i++)r=n.models[i].attributes,r.stop==t&&this._createCompanyListItem(r.id,r.name)},_UIHideCompanyList:function(){TweenMax.to(this.ui.agencies,.3,{opacity:0,display:"none",ease:Linear.easeNone})},_UIShowForm:function(){TweenMax.to(this.ui.form,.3,{opacity:1,ease:Linear.easeNone})},_UIHideForm:function(){TweenMax.to(this.ui.form,.3,{opacity:0,display:"none",ease:Linear.easeNone})},_UIShowThanks:function(){TweenMax.to(this.ui.thanks,.3,{opacity:1,ease:Linear.easeNone})},_selectCompany:function(e){var t=e.currentTarget,n=t.getAttribute("data-company-id"),r=t.getAttribute("data-company-name");r&&(this.selectedCompany=n,this.ui.selectedAgency.textContent=r,this._UIHideCompanyList(),this._UIShowForm())},_submitForm:function(e){var t=this.ui.inputName.value,n=this.ui.inputEmail.value,r=this.selectedCompany,i=this.selectedStop;basicEmailRegex=new RegExp(/.+\@.+\..+/),isEverythingValid=!0,e.preventDefault(),this.isEmpty(t)&&(isEverythingValid=!1,this.showError("emptyFieldName")),basicEmailRegex.test(n)||(isEverythingValid=!1,this.showError("invalidEmail")),this.isEmpty(n)&&(isEverythingValid=!1,this.showError("emptyFieldEmail"));if(isEverythingValid){var s=this,o={url:Settings.baseURL+"/api/confirm",type:"POST",data:{name:t,email:n,company:r,stop:i},success:function(e){s._UIHideForm(),s._UIShowThanks()},error:function(e){this.showError(e.responseJSON.key)}.bind(this)};Backbone.ajax(o)}},isEmpty:function(e){return!e||e.length==0},isMobile:function(){if(Settings.device.type==="mobile")return!0},showError:function(e){this.isMobile()?this._showErrorMobile(e):this._showErrorDesktop(e)},_showErrorMobile:function(e){(e==="invalid_domain"||e==="validation_error"||e==="invalidEmail"||e==="emptyFieldEmail"||e==="too_many_invites")&&this.ui.inputEmail.classList.add("error"),e==="emptyFieldName"&&this.ui.inputName.classList.add("error")},_showErrorDesktop:function(e){e==="invalid_domain"&&(this.ui.inputValidationEmailContent.textContent=this.validationMessage.invalidDomein,TweenMax.to(this.ui.inputValidationEmail,.3,{scale:1,autoAlpha:1,ease:Back.easeOut})),e==="too_many_invites"&&(0,this.ui.inputValidationEmailContent.innerHTML=this.validationMessage.tooManyInvites,TweenMax.to(this.ui.inputValidationEmail,.3,{scale:1,autoAlpha:1,ease:Back.easeOut})),e==="validation_error"&&(this.ui.inputValidationEmailContent.textContent=this.validationMessage.duplicateEmail,TweenMax.to(this.ui.inputValidationEmail,.3,{scale:1,autoAlpha:1,ease:Back.easeOut})),e==="invalidEmail"&&(this.ui.inputValidationEmailContent.textContent=this.validationMessage.invalidEmail,TweenMax.to(this.ui.inputValidationEmail,.3,{scale:1,autoAlpha:1,ease:Back.easeOut})),e==="emptyFieldName"&&(this.ui.inputValidationNameContent.textContent=this.validationMessage.emptyField,TweenMax.to(this.ui.inputValidationName,.3,{scale:1,autoAlpha:1,ease:Back.easeOut})),e==="emptyFieldEmail"&&(this.ui.inputValidationEmailContent.textContent=this.validationMessage.emptyField,TweenMax.to(this.ui.inputValidationEmail,.3,{scale:1,autoAlpha:1,ease:Back.easeOut}))},hideError:function(e){this.isMobile()?this._hideErrorMobile(e):this._hideErrorDesktop(e)},_hideErrorMobile:function(){this.ui.inputName.classList.remove("error"),this.ui.inputEmail.classList.remove("error")},_hideErrorDesktop:function(){TweenMax.to(this.ui.inputValidationName,.2,{scale:0,autoAlpha:0,ease:Back.easeIn}),TweenMax.to(this.ui.inputValidationEmail,.2,{scale:0,autoAlpha:0,ease:Back.easeIn})},_signupScrollClickHandler:function(t){if(!t.target.classList.contains("signup-scroll"))return;e.history.navigate("/",{trigger:!0})},_shareFacebookClickHandler:function(e){var n={title:e.delegateTarget.dataset.title,body:e.delegateTarget.dataset.description};t.shareFacebook(Settings.baseURL,n)},_shareTwitterClickHandler:function(e){var n={body:e.delegateTarget.dataset.description};t.shareTwitter(Settings.baseURL,n)},closeButtonMouseOverHandler:function(e){TweenMax.fromTo(this.ui.closeButton,.4,{rotation:0},{rotation:90,ease:Back.easeInOut})},closeButtonMouseOutHandler:function(e){TweenMax.fromTo(this.ui.closeButton,.4,{rotation:90},{rotation:0,ease:Back.easeInOut})},_inputFocusHandler:function(e){this.isMobile()?e.delegateTarget.classList.remove("error"):(e.delegateTarget.classList.contains("input-name")&&TweenMax.to(this.ui.inputValidationName,.2,{scale:0,autoAlpha:0,ease:Back.easeIn}),e.delegateTarget.classList.contains("input-email")&&TweenMax.to(this.ui.inputValidationEmail,.2,{scale:0,autoAlpha:0,ease:Back.easeIn}))}})}),define("desktop/routers/ApplicationRouter",["superhero","desktop/views/pages/HomeView","desktop/views/pages/AboutView","desktop/views/pages/WhosBehindThisView","desktop/views/pages/NotInvitedView","shared/views/pages/SignupView"],function(e,t,n,r,i,s){return e.Router.extend({routes:{"":"home","signup/(:id)":"signup",about:"about",whosbehindthis:"whosbehindthis",notinvited:"notinvited"},home:function(){e.RegionManager.get("main").show(t),e.RegionManager.get("signup").clear()},about:function(){e.RegionManager.get("main").show(n)},whosbehindthis:function(){e.RegionManager.get("main").show(r)},notinvited:function(){e.RegionManager.get("main").show(i)},signup:function(n){e.RegionManager.get("signup")&&e.RegionManager.get("signup").allowSameView(!0);var r=e.RegionManager.get("main").show(t),i=e.RegionManager.get("signup").show(s,{id:n});r.gotoItem(n)}})}),define("desktop/views/components/MenuComponent",["superhero"],function(e){return e.Component.extend({ui:{items:".list-item-navigation",buttons:".link-navigation"},events:{},initialize:function(){_.bindAll(this,"_buttonMouseenterHandler","_buttonMouseleaveHandler")},onInitialized:function(){this._setupButtons(),this._setupEventListeners()},onClose:function(){this._removeEventListeners()},show:function(){TweenMax.fromTo(this.el,.5,{autoAlpha:0},{autoAlpha:150,delay:1}),TweenMax.staggerFromTo(this.ui.items,.5,{y:-150},{y:0,delay:1},.1)},_setupButtons:function(){for(var e=0;e<this.ui.buttons.length;e++){var t=this.ui.buttons[e];new SplitText(t,{type:"chars",position:"relative"})}},_setupEventListeners:function(){for(var e=0,t=this.ui.buttons.length;e<t;e++)this.ui.buttons[e].addEventListener("mouseenter",this._buttonMouseenterHandler),this.ui.buttons[e].addEventListener("mouseleave",this._buttonMouseleaveHandler)},_removeEventListeners:function(){for(var e=0,t=this.ui.buttons.length;e<t;e++)this.ui.buttons[e].removeEventListener("mouseenter",this._buttonMouseenterHandler),this.ui.buttons[e].removeEventListener("mouseleave",this._buttonMouseleaveHandler)},_buttonMouseenterHandler:function(e){var t=e.currentTarget,n=t.children;for(var r=0;r<n.length;r++)TweenMax.fromTo(n[r],.2,{scaleY:1,y:0,transformOrigin:"50% 59%"},{delay:r/50,y:-4,repeat:1,yoyo:!0,overwrite:2,ease:Quad.easeIn})},_buttonMouseleaveHandler:function(e){}})}),define("desktop/views/components/PreloaderComponent",["superhero","shared/utils/FileLoader","shared/models/CompaniesCollection"],function(e,t,n){return e.Component.extend({_loaded:0,ui:{preloaderContent:".preloader-content",preloaderFigure:".preloader-figure",preloaderBoatMask:".preloader-boat-mask",preloaderBoat:".preloader-boat",preloaderValue:".preloader-value"},events:{},initialize:function(){_.bindAll(this,"_timelineIntroComplete","_timelineOutroComplete","_companiesCollectionCompleteHandler")},onInitialized:function(){window.devicePixelRatio>=2?this._manifest=[{id:"boatpath",src:"/static/img/desktop/boatpath.svg"},{id:"bg-canals-01",src:"/static/img/desktop/<EMAIL>"},{id:"bg-canals-02",src:"/static/img/desktop/<EMAIL>"},{id:"bg-canals-03",src:"/static/img/desktop/<EMAIL>"},{id:"bg-canals-04",src:"/static/img/desktop/<EMAIL>"},{id:"anchor",src:"/static/img/desktop/<EMAIL>"},{id:"buttonWave",src:"/static/img/desktop/<EMAIL>"}]:this._manifest=[{id:"boatpath",src:"/static/img/desktop/boatpath.svg"},{id:"bg-canals-01",src:"/static/img/desktop/bg-canals_01.png"},{id:"bg-canals-02",src:"/static/img/desktop/bg-canals_02.png"},{id:"bg-canals-03",src:"/static/img/desktop/bg-canals_03.png"},{id:"bg-canals-04",src:"/static/img/desktop/bg-canals_04.png"},{id:"anchor",src:"/static/img/desktop/anchor.png"},{id:"buttonWave",src:"/static/img/desktop/button-wave.png"}],this._timelineIntro=new TimelineMax({paused:!0,delay:.1,onComplete:this._timelineIntroComplete}),this._timelineIntro.insert(new TweenMax.fromTo(this.ui.preloaderContent,.7,{autoAlpha:0},{autoAlpha:1}),0),this._timelineIntro.insert(new TweenMax.fromTo(this.ui.preloaderFigure,.7,{scale:.6},{scale:1,ease:Back.easeOut}),0),this._timelineIntro.insert(new TweenMax.fromTo(this.ui.preloaderBoatMask,.7,{autoAlpha:0,scale:.3},{autoAlpha:1,scale:1,ease:Back.easeOut}),.2),this._timelineIntro.insert(new TweenMax.fromTo(this.ui.preloaderBoat,.5,{x:-250},{x:0,ease:Power4.easeOut}),.6),this._timelineIntro.insert(new TweenMax.fromTo(this.ui.preloaderValue,.5,{autoAlpha:0,y:-40},{autoAlpha:1,y:0,ease:Back.easeOut}),0),this._timelineOutro=new TimelineMax({paused:!0,onComplete:this._timelineOutroComplete}),this._timelineOutro.insert(new TweenMax.fromTo(this.ui.preloaderBoat,.5,{x:0},{x:250,ease:Power2.easeIn}),0),this._timelineOutro.insert(new TweenMax.fromTo(this.ui.preloaderBoatMask,.4,{autoAlpha:1,scale:1},{autoAlpha:0,scale:.3,ease:Back.easeIn}),.1),this._timelineOutro.insert(new TweenMax.fromTo(this.ui.preloaderFigure,.4,{autoAlpha:1,scale:1},{autoAlpha:0,scale:.6,ease:Back.easeIn}),.3),this._timelineOutro.insert(new TweenMax.fromTo(this.ui.preloaderValue,.5,{autoAlpha:1},{autoAlpha:0,ease:Power1.easeOut}),0),this._timelineIntro.play()},onClose:function(){this._timelineIntro&&this._timelineIntro.kill(),this._timelineOutro&&this._timelineOutro.kill(),this._removeEventListeners()},_setupEventListeners:function(){this.listenTo(this._fileLoader,"manifest:load:complete",this._manifestLoadedHandler),this.listenTo(this._fileLoader,"manifest:load:progress",this._manifestProgressHandler)},_removeEventListeners:function(){},_startLoader:function(){this._fileLoader=new t,this._fileLoader.loadManifest("application",this._manifest,!0),n.fetch({success:this._companiesCollectionCompleteHandler,error:this._companiesCollectionErrorHandler}),this._setupEventListeners()},_updatePreloader:function(e){this.ui.preloaderValue.textContent=e+"%"},_loadCompleteHandler:function(){this._loaded++,this._loaded==2&&this._timelineOutro.play()},_timelineIntroComplete:function(){this._startLoader()},_timelineOutroComplete:function(){this.trigger("complete")},_manifestLoadedHandler:function(){this._loadCompleteHandler()},_manifestProgressHandler:function(e){this._updatePreloader(Math.round(e.progress*100))},_companiesCollectionCompleteHandler:function(e){this._loadCompleteHandler()},_companiesCollectionErrorHandler:function(e){0}})}),define("desktop/views/ApplicationView",["superhero","desktop/views/components/MenuComponent","desktop/views/components/PreloaderComponent"],function(e,t,n){return e.LayoutView.extend({regions:{main:".main"},events:{'click a[href^="/"]':"_globalClickHandler"},components:{preloader:{selector:".preloader",type:n},menuComponent:{selector:".site-navigation",type:t}},initialize:function(){_.bindAll(this,"_startHistory","_startHistoryComplete")},onInitialized:function(){this._setupEventListeners()},_setupEventListeners:function(){this.listenTo(this.components.preloader,"complete",this._loadCompleteHandler)},_loadCompleteHandler:function(){TweenMax.delayedCall(.4,this._startHistory)},_startHistory:function(){Modernizr.history?e.history.start({pushState:!0,silent:!1}):e.history.start({silent:!1,hashChange:!0}),TweenMax.delayedCall(.5,this._startHistoryComplete)},_startHistoryComplete:function(){this.components.menuComponent.show(),this.components.preloader.remove()},_globalClickHandler:function(t){t.preventDefault();var n=t.delegateTarget.pathname;n.indexOf("signup")>-1?e.history.loadUrl(n,{trigger:!0}):e.history.navigate(n,{trigger:!0})}})}),define("desktop/main",["superhero","desktop/routers/ApplicationRouter","desktop/views/ApplicationView"],function(e,t,n){var r=e.createNameSpace("nl.creativecruise");r.applicationRouter=new t,r.applicationView=new n({el:document.getElementById("application")})}),require.config({urlArgs:"cb="+(new Date).getTime(),paths:{jquery:"vendor/jquery.custom",underscore:"vendor/lodash.min",backbone:"vendor/exoskeleton",superhero:"vendor/superhero",tweenmax:"vendor/tweenmax.min",preloadjs:"vendor/preloadjs-0.6.0.min",snap:"vendor/snap.svg",modernizr:"vendor/modernizr.custom"}}),require(["vendor/polyfills/classlist.min","vendor/polyfills/placeholders.min","vendor/polyfills/html5-dataset","vendor/splittext","superhero","tweenmax","modernizr","desktop/main"],function(){}),define("boot-desktop",function(){});