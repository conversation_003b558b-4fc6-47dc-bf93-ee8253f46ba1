<cfoutput>
	<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
		<cfinclude template="head.cfm" />
		<body>
			<cfinclude template="jssalesscript.cfm">
			<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct">
				<div class="bodyText" style="margin:6px;">#application.objCMS.renderZone(zone='Main',event=event)#</div>
			<cfelse>
			
				<cfinclude template="headerContent.cfm">
				<div id="blue_content_interior">
					<cfinclude template="mastHead.cfm">
				</div>
				
				<div id="white_content_interior">
					<cfinclude template="subNavigation.cfm">
					<div class="pageSize white_content_area_interior">
						<div class="clear"></div>
						<table width="100%" cellpadding="0" cellspacing="0" border="0" id="interior_content">
							<tr>
								<td width="25%" valign="top">
									<cfinclude template="sideNavigation.cfm">
								</td>
								<td>
									<table width="100%" cellpadding="0" cellspacing="0" border="0">
										<tr>
											<td valign="top" style="padding:0px 15px;">#application.objCMS.renderZone(zone='Main',event=event)#</td>
										</tr>
										<cfif application.objCMS.getZoneItemCount(zone='B',event=event)>
											<tr>
												<td width="40%" valign="top" style="padding:0px 15px;">#application.objCMS.renderZone(zone='B',event=event)#</td>
											</tr>
										</cfif>
									</table>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<cfinclude template="footerContent.cfm">
			</cfif>
			<cfinclude template="toolBar.cfm" />
			<cfinclude template="adRoll.cfm" />
		</body>
	</html>
</cfoutput>