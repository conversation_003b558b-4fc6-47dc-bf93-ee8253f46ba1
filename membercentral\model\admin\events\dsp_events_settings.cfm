<cfsavecontent variable="local.tabJS">
	<cfoutput>
	<script type="text/javascript">
		function saveEventSettings() {
			var saveResult = function(r) {
				$('##btnSaveEvSettings').prop('disabled',false);
				if (r.success && r.success.toLowerCase() == 'true'){
					$('##saveEVSettingsResponse').html('Settings saved.').addClass('text-success').show().fadeOut(7000);
				} else {
					alert('We were unable to save event settings.');
				}
			};

			if ($('##sponsorFeatureImageConfigID').val() > 0 && ( $('##evSponsorFeatureImageSizeID').val() == 0 || $('##swSponsorFeatureImageSizeID').val() == 0 )) {
				alert('If selecting a sponsor featured image config., you must select both the sizes to show on Events detail and SeminarWeb detail');
				return false;
			}

			$('##btnSaveEvSettings').prop('disabled',true);

			var objParams = { defaultAction:$('##evListDefaultAction').val(), 
								ResultsFieldSet:$('##ResultsFieldSet').val(), evshowPhotosRegStep1:$('##evshowPhotosRegStep1').val(), 
								evForceLoginForReg:$('##evForceLoginForReg').is(':checked'), NewAccFieldSet:$('##NewAccFieldSet').val(), 
								adminEvRegSearchFormFieldSetID:$('##adminEvRegSearchFormFieldSet').val(), adminEvRegResultsFieldSetID:$('##adminEvRegResultsFieldSet').val(), 
								adminEvRegNewAcctFieldSetID:$('##adminEvRegNewAcctFieldSet').val(), sponsorFeatureImageConfigID:$('##sponsorFeatureImageConfigID').val(),
								evSponsorFeatureImageSizeID:$('##evSponsorFeatureImageSizeID').val(), swSponsorFeatureImageSizeID:$('##swSponsorFeatureImageSizeID').val() };
			TS_AJX('ADMINEVENT','saveEventSettings',objParams,saveResult,saveResult,20000,saveResult);
		}
		function onChangeSponsorFtdImgConfig(cid,evsizeid,swsizeid) {
			$('##evSponsorFeatureImageSizeID,##swSponsorFeatureImageSizeID').find('option[value!="0"]').remove();
			if (cid > 0) {
				$('.sponsorFtdImgSizeRow').removeClass('d-none');
				var objParams = { featureImageConfigID:cid, limitWidth:400, limitHeight:400 };
				$.getJSON('/?event=proxy.ts_json&c=FTDIMAGES&m=getFeaturedImageSizesForConfigID', objParams)
					.done(function(r) { populateFeatureImageSizes(r,evsizeid,swsizeid); })
					.fail(populateFeatureImageSizes);
			} else {
				$('.sponsorFtdImgSizeRow').addClass('d-none');
			}
		}
		function populateFeatureImageSizes(r,evsizeid,swsizeid) { 
			if (r.success) {
				if(r.arrsizes && r.arrsizes.length) {
					$.each(r.arrsizes, function (i, item) {
						var optionText = item.featuredimagesizename + ' (' + item.width + ' x ' + item.height + ')';
						$('##evSponsorFeatureImageSizeID,##swSponsorFeatureImageSizeID').append( $('<option>', { value:item.featureImagesizeid, text:optionText }) );
					});
					if (evsizeid && swsizeid) {
						$('##evSponsorFeatureImageSizeID').val(evsizeid);
						$('##swSponsorFeatureImageSizeID').val(swsizeid);
					}

					$('.noEligibleSizeWarning').addClass('d-none');
				} else {
					$('.noEligibleSizeWarning').removeClass('d-none');
				}
			} else {
				alert('We were unable to load this featured image config sizes.');
			}
			return false;
		}

		<cfif val(local.qryEvFeaturedImgSetttings.sponsorFeatureImageConfigID) gt 0>
			$(function() {
				onChangeSponsorFtdImgConfig(#val(local.qryEvFeaturedImgSetttings.sponsorFeatureImageConfigID)#,#val(local.qryEvFeaturedImgSetttings.evSponsorFeatureImageSizeID)#,#val(local.qryEvFeaturedImgSetttings.swSponsorFeatureImageSizeID)#);
			});
		</cfif>
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.tabJS)#">

<cfoutput>
<form id="frmEventSettings" name="frmEventSettings">
	<div class="row">
		<div class="col-md-12">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">
						Cross Calendar Event Settings
					</div>
				</div>
				<div class="card-body pb-3">
					<div class="form-group">
						<div class="form-label-group">
							<select id="evListDefaultAction" name="evListDefaultAction" class="form-control">
								<option value="listAll"<cfif local.evListdefaultAction eq 'listAll'> selected</cfif>>List All Events</option>
								<option value="listMonth"<cfif local.evListdefaultAction eq 'listMonth'> selected</cfif>>List By Month</option>
								<option value="viewMonth"<cfif local.evListdefaultAction eq 'viewMonth'> selected</cfif>>View By Month</option>
							</select>
							<label for="evListDefaultAction">Events List Default Action</label>
						</div>
					</div>

					<div class="mt-4"><b>Event Registration</b></div>
					<div class="form-group row mt-2">
						<div class="col-sm-4 align-self-center">Member Field Set for Registrant Identifier Results</div>
						<div class="col-sm-8">
							#local.strResultsFSSelector.html#
							<div class="form-text small text-dim">(First Name, Last Name, Company always appear.)</div>
						</div>
					</div>
					<div class="form-group row mt-2">
						<div class="col-sm-4">
							<label for="evshowPhotosRegStep1">Include Member Photos in Registrant Identifier Results</label>
						</div>
						<div class="col-sm-8">
							<select name="evshowPhotosRegStep1" id="evshowPhotosRegStep1" class="form-control form-control-sm">
								<option value="1" <cfif local.evshowPhotosRegStep1 is 1>selected</cfif>>Yes, Show Photos</option>
								<option value="0" <cfif local.evshowPhotosRegStep1 is not 1>selected</cfif>>No, Do Not Show Photos</option>
							</select>
						</div>
					</div>
					<div class="form-group row mt-2">
						<div class="col-sm-4 align-self-center">Member Field Set for Registrant Identifier New Account Form</div>
						<div class="col-sm-8">
							#local.strNewAcctFSSelector.html#
						</div>
					</div>
					<div class="custom-control custom-switch">
						<input type="checkbox" name="evForceLoginForReg" id="evForceLoginForReg" value="1" class="custom-control-input"<cfif local.evForceLoginForReg is 1> checked="checked"</cfif>>
						<label class="custom-control-label" for="evForceLoginForReg">Force Login for Event Registration</label>
					</div>

					<div class="mt-4"><b>Adding Registrants from Control Panel</b></div>
					<div class="form-group row mt-2">
						<div class="col-sm-4 align-self-center">Member Field Set for Add Registrant Search Form</div>
						<div class="col-sm-8">
							#local.strAdminEvRegSearchFormFSSelector.html#
						</div>
					</div>
					<div class="form-group row mt-2">
						<div class="col-sm-4 align-self-center">Member Field Set for Add Registrant Search Results</div>
						<div class="col-sm-8">
							#local.strAdminEvRegResultsFSSelector.html#
						</div>
					</div>
					<div class="form-group row mt-2">
						<div class="col-sm-4 align-self-center">Member Field Set for Add Registrant Create Account</div>
						<div class="col-sm-8">
							#local.strAdminEvRegNewAcctFSSelector.html#
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="row my-3">
		<div class="col-md-12">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">
						Sponsors Featured Image Settings
					</div>
				</div>
				<div class="card-body pt-1 pb-3">
					<div class="mb-3 mt-2">This setting applies to both Events and SeminarWeb programs.</div>

					<div class="form-group">
						<div class="form-label-group">
							<select name="sponsorFeatureImageConfigID" id="sponsorFeatureImageConfigID" class="form-control" onchange="onChangeSponsorFtdImgConfig(this.value);">
								<option value="0">Select a Featured Image Config</option>
								<cfloop query="local.qrySiteFeaturedImageConfigs">
									<option value="#local.qrySiteFeaturedImageConfigs.featureImageConfigID#"<cfif local.qrySiteFeaturedImageConfigs.featureImageConfigID eq val(local.qryEvFeaturedImgSetttings.sponsorFeatureImageConfigID)> selected="selected"</cfif>>#local.qrySiteFeaturedImageConfigs.featuredImageConfigName#</option>
								</cfloop>
							</select>
							<label for="sponsorFeatureImageConfigID">Featured Image Configuration</label>
						</div>
					</div>
					<div class="form-group d-none sponsorFtdImgSizeRow">
						<div class="form-label-group">
							<select name="evSponsorFeatureImageSizeID" id="evSponsorFeatureImageSizeID" class="form-control">
								<option value="0">Not Displayed</option>
							</select>
							<label for="evSponsorFeatureImageSizeID">Size on Events Detail</label>
						</div>
						<div class="form-text text-dim small">(max width x height is 400 x 400)</div>
						<div class="alert alert-warning mt-2 d-none noEligibleSizeWarning">
							This featured image config does not contain any eligible sizes.
						</div>
					</div>
					<div class="form-group mt-3 d-none sponsorFtdImgSizeRow">
						<div class="form-label-group">
							<select name="swSponsorFeatureImageSizeID" id="swSponsorFeatureImageSizeID" class="form-control">
								<option value="0">Not Displayed</option>
							</select>
							<label for="swSponsorFeatureImageSizeID">Size on SeminarWeb Detail</label>
						</div>
						<div class="form-text text-dim small">(max width x height is 400 x 400)</div>
						<div class="alert alert-warning mt-2 d-none noEligibleSizeWarning">
							This featured image config does not contain any eligible sizes.
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="form-group">
		<button type="button" name="btnSaveEvSettings" id="btnSaveEvSettings" class="btn btn-sm btn-primary" onclick="saveEventSettings();">Save Settings</button>
		<span id="saveEVSettingsResponse"></span>
	</div>
</form>
</cfoutput>