<cfoutput>
<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
<!doctype html>
<html>
   <head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
		<link rel="stylesheet" href="/css/main.css" />
		#application.objCMS.getBootstrapHeadHTML()#
		#application.objCMS.getResponsiveHeadHTML()#
		<link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
		<link href="/css/responsive.css" rel="stylesheet" type="text/css">
		#application.objCMS.getFontAwesomeHTML(includeVersion4Support=false)#
		<style type="text/css">
		 ##mainContent { width: auto; margin-left: auto }
		</style>
	</head>
	<body>
		<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
		<!-- wrapper start -->
		<div class="wrapper">
         <!-- container start -->
			<div class="container">
            <!--Header Start-->
            <header id="header" class=" header outer-width">
               <div class="headertop">
                  <div class="logo"> <a href="/"><img src="/images/logo.png" alt="logo"></a></div>
                  <div class="loginSec">
                    <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<a href="/?pg=login">Login</a>
					<cfelse>
						<a href="/?logout">Logout</a>
					</cfif>
                     <ul>
                        <li><a href="/" onMouseOver="Tip('Home');" onmouseout="UnTip();"><img src="/images/icon_home.png"></a></li>
						<li><a href="/?pg=events&evAction=viewMonth" onMouseOver="Tip('Calendar');" onmouseout="UnTip();"><img src="/images/icon_calendar.png"></a></li>
						<li><a href="/?pg=search" onMouseOver="Tip('Search');" onmouseout="UnTip();"><img src="/images/icon_search.png"></a></li>
						<li><a href="/?pg=RenewMembership" onMouseOver="Tip('Join/Renew');" onmouseout="UnTip();"><img src="/images/icon_joinrenew.png"></a></li>
						<li><a href="/?pg=uploadDocuments" onMouseOver="Tip('Upload Documents');" onmouseout="UnTip();"><img src="/images/icon_upload.png"></a></li>
                     </ul>
                  </div>
               </div>
               <div class="newheader">
                  <header>
                     <nav id='cssmenu'>
                        <div id="head-mobile"></div>
                        <div class="button"></div>
                        <cfif structKeyExists(local.strMenus, "primaryNav")>
							#local.strMenus.primaryNav.menuHTML.rawcontent#
						</cfif>
                     </nav>
                  </header>
               </div>
            </header>
            <!--Header End--> 
            <!--Content Start-->
            <div class="content">
				<div class="content">
					<div class="container-fluid">
						<div class="row-fluid">
							<div id="links" style="text-align:right;padding-right:25px">
							   <b>
								  <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
									 <a href="/?pg=login">Login to Education Portal</a>
									 <cfelse>
									 <a href="/?logout">Logout</a>
								  </cfif>
							   </b>
							</div>
							#application.objCMS.renderZone(zone='Main',event=event)#
						</div>
					</div>
				</div>
            </div>
            <!--Content End--> 
		</div>
         <!-- container End --> 
         <!--Footer Start-->
         <footer class="footer">
            <div class="container">
               <div class="footer-wrapper">
                  <div class="row-fluid">
                     <div class="span12 footCol mainLogo">
                        <p>Mississippi Association for Justice ©2020 <a href="/?pg=PrivacyPolicy">Privacy Policy</a> </p>
                     </div>
                  </div>
               </div>
            </div>
         </footer>
		<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct" and application.objCMS.getZoneItemCount(zone='ToolBar',event=event)>
			<div class="noprint">
				<cfoutput>#application.objCMS.renderZone(zone='ToolBar',event=event)#</cfoutput>
			</div>
		</cfif>
         <!--Footer End--> 
      </div>
	  <cfelse>
			<div class="content" style="padding:10px;">
				#application.objCMS.renderZone(zone='Main',event=event)#
			</div>
		</cfif>
      <!-- wrapper end --> 
      <!--javascript Files--> 
	  <script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
      <script src="/javascript/custom.js" type="text/javascript"></script>
   </body>
</html>
</cfoutput>