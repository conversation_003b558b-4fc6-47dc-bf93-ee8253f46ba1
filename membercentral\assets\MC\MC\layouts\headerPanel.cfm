<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title><cfoutput>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</cfoutput></title>
	<link rel="apple-touch-icon" sizes="180x180" href="/images/apple-icon-180x180.png">
		<link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
		<link name="favicon" type="image/x-icon" href="/images/favicon.ico" rel="shortcut icon" />
	<link href="/css/main.css" rel="stylesheet" type="text/css">
   <cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
		<script type="text/javascript">if (window.event+''=='undefined')event=0;</script>	
	</cfif>
	<script type="text/javascript">(function(d,s){var DID="608a456d-3403-4e98-b4ba-e8d38deebf78";var js,fjs=d.getElementsByTagName(s)[0];js=d.createElement(s);js.async=1;js.src="https://track.cbdatatracker.com/Home?v=3&id='"+DID+"'";fjs.parentNode.insertBefore(js,fjs);}(document,'script'));</script>
</head>