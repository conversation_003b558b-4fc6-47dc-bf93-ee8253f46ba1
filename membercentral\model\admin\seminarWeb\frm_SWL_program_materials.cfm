<cfsavecontent variable="local.swlJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/clipboard.js/1.7.1/clipboard.min.js"></script>	
	<script type="text/javascript">
		var #toScript(local.uploadSWLMaterialDocsLink, "SWLUploadDocsLink")#
		var SWLMaterialDocsTable;
		var uppyDocuments = null;
		
		function initSWLMaterialDocsTable(){
			SWLMaterialDocsTable = $('##SWLMaterialDocsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"paging": false,
				"info": false,
				"ajax": {
					"url": "#local.SWLMaterialDocsLink#",
					"type": "post",
					"data": function(d) {
						if (window.reorderData && window.reorderData.length > 0) {
							d.reorderData = JSON.stringify(window.reorderData);
							window.reorderData = [];
						}
						return d;
					}
				},
				"autoWidth": false,
				"columns": [
					{ "data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div class="row-drag-handle"><i class="fa-light fa-bars"></i></div>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "5%",
						"orderable": false
					},
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<input type="text" name="docTitle_'+data.documentid+'" id="docTitle_'+data.documentid+'" class="form-control form-control-sm" maxlength="500" value="'+data.doctitle+'" originalDocTitle="'+data.doctitleenc+'" onblur="saveSWLMaterialDocTitle('+data.documentid+',this)">';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "50%",
						"orderable": false
					},
					{ "data": "fileext","width": "5%", "className": "text-center text-uppercase","orderable": false},
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="/docDownload/'+data.documentid+'" class="btn btn-sm text-primary p-1 mx-1" target="_blank" title="View Document"><i class="fa-solid fa-eye"></i></a>';
								renderData += '<a href="javascript:void(0);" class="btn btn-sm text-danger p-1 mx-1" id="btnDelDoc'+data.documentid+'" onclick="confirmDeleteSWLMaterialDoc('+data.documentid+');return false;" title="Remove '+ data.filenameenc +'"><i class="fa-solid fa-trash-can"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "15%",
						"className": "text-center",
						"orderable": false
					}
				],
				"ordering": false,
				"searching": false,
				rowReorder: {
					dataSrc: "columnid",
					selector: '.row-drag-handle'
				},
				"drawCallback": function(settings) {
					onDrawSWLMaterialDocs();
				}
			});

			SWLMaterialDocsTable.on('row-reorder', function (e, diff, edit) {
				let orderData = [];
				diff.forEach(function(item) {
					orderData.push({
						id: SWLMaterialDocsTable.row(item.node).data().documentid,
						newOrder: item.newPosition
					});
				});
				window.reorderData = orderData;
			});
		}
		function saveSWLMaterialDocTitle(id,thisObj){
			var docTitle = $(thisObj).val();
			var saveSWLMaterialDocTitleResult = function(s) {
				if (s.success && s.success.toLowerCase() == 'true') {
					$(thisObj).attr("originaldoctitle",docTitle)
					displaySavedResponseForSWLMaterials();
				} else {
					alert('We were unable to save the document title.');
				}
			};
			
			if($.trim(docTitle).length == 0){
				$(thisObj).val($(thisObj).attr("originaldoctitle"));
			} else if($(thisObj).attr("originaldoctitle") != docTitle){
				var objParams = { seminarID:sw_seminarid, documentID:id, docTitle:docTitle };
				TS_AJX('ADMINSWL','saveSWLMaterialDocTitle',objParams,saveSWLMaterialDocTitleResult,saveSWLMaterialDocTitleResult,15000,saveSWLMaterialDocTitleResult);
			} 
		}
		function confirmDeleteSWLMaterialDoc(id) {
			var deleteSWLMaterialDocResult = function(s) {
				if (s.success && s.success.toLowerCase() == 'true') {
					reloadSWLMaterialDocTable();
					displaySavedResponseForSWLMaterials();
				}
				else {
					alert('We were unable to delete this document.');
					delBtnElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
				}
			};
			let delBtnElement = $('##btnDelDoc'+id);
			mca_initConfirmButton(delBtnElement, function(){
				var objParams = { seminarID:sw_seminarid, documentID:id };
				TS_AJX('ADMINSWL','deleteSWLMaterialDocument',objParams,deleteSWLMaterialDocResult,deleteSWLMaterialDocResult,15000,deleteSWLMaterialDocResult);
			});
		}
		function uppySWLFileUploadPromise(uppyInstance) {
			return uppyInstance.retryAll()
			.then((result) => uppyInstance.upload())
			.then((result) => {
				if (result.failed.length > 0) {
					console.error('Failed ['+uppyInstance.getID()+']:');
					result.failed.forEach((file) => {
						console.error(file.error);
					});
					return false;
				} else {
					return true;
				}
			}).catch(error => {
				console.error('Error ['+uppyInstance.getID()+']:');
				console.log(error);
				return false;
			});
		}
		async function onDocSaveComplete() {
			let uploadDocsSuccess = false, uploadImgSuccess = false;
			let arrError = [];

			if (uppyDocuments)
				uppyDocuments.getPlugin('XHRUpload').setOptions({ endpoint: SWLUploadDocsLink });

			if (uppyDocuments && uppyDocuments.getFiles().length){
				
				uploadDocsSuccess = await uppySWLFileUploadPromise(uppyDocuments);
				if(!uploadDocsSuccess){
					arrError[arrError.length] = "An error occured while uploading the documents.";
					uploadDocsSuccess = true;
				}
			}
			else uploadDocsSuccess = true;
		}
		function hideSWLMaterialDocsTableIfEmpty() { 
			if (SWLMaterialDocsTable.rows().count() == 0) {
				$('.SWLMaterialDocsTool').addClass('d-none');
				$('.uppyUploadWrap').removeClass('d-none');
				$('.addMoreFileLinkWrap').addClass('d-none');
				$('.removeAllFileLinkWrap').addClass('d-none');
			} else {
				$('.SWLMaterialDocsTool').removeClass('d-none');
				$('.uppyUploadWrap').addClass('d-none');
				$('.addMoreFileLinkWrap').removeClass('d-none');
				$('.removeAllFileLinkWrap').removeClass('d-none');
			}
		}
		function addMoreFiles() {
			$('.uppyUploadWrap').removeClass('d-none');
			$('.addMoreFileLinkWrap').addClass('d-none');
			
		}
		function onDrawSWLMaterialDocs() {
			hideSWLMaterialDocsTableIfEmpty();
		}
		function reloadSWLMaterialDocTable(){
			SWLMaterialDocsTable.draw();
		}			

		function initSWLMaterials(){
			const allowedDocumentFileTypes = ['.doc','.docx','.ppt','.pptx','.xls','.xlsx','.pdf'];

			dynamicallyLoadUppy().then((result) => {
				uppyDocuments = new Uppy({ id: 'uppyDocuments', debug: true, autoProceed: false, restrictions: {allowedFileTypes: allowedDocumentFileTypes}, locale:{
					strings: {
						// removing the filename from this default message so that this message get deduped on display
						exceedsSize: 'Exceeds maximum allowed size of %{size}',
						dropPasteFiles: 'Drag & drop all downloadable materials for your webinar or %{browseFiles}',
					},
				}})
				.use(XHRUpload, {
					endpoint: SWLUploadDocsLink
				})
				.on('restriction-failed', (file, error) => {
					const fileExt = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
					const customMessage = `Sorry, we can't upload ${fileExt} files.\n\nThe file ${file.name} isn't supported. Please upload a different file type.`;
					uppyDocuments.info(customMessage, 'error', 7000); // Show for 7 seconds
				})
				.on('complete', (result) => {
					if (!result.failed.length){ 
						uppyDocuments.cancelAll();
						$('##SWLMaterialDocsTable').DataTable().ajax.reload();
						displaySavedResponseForSWLMaterials();
					}
					else {
						result.successful.forEach((file) => {uppyDocuments.removeFile(file.id)});
					}
				})
				.use(Dashboard, { target: '##mcSWLMaterialDocUploader', inline: true, isWide: false, width:'100%', height:'300px', disableThumbnailGenerator: true, hideUploadButton:false, hideRetryButton: false, proudlyDisplayPoweredByUppy:false });
			});

			initSWLMaterialDocsTable();		
		}
		function displaySavedResponseForSWLMaterials(){
			if(!$("##program-materials .card-header:first ##saveResponse").length)
				$("##program-materials .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
			$('##program-materials .card-header:first ##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(5000);
			if(programAdded)
				$('##nextButton, ##prevButton').prop('disabled',false);
			else
				$('##program-materials .card-footer .save-button').prop('disabled',false);
		}
		function removeAllFiles() {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'md',
				title: 'Remove Uploaded Docs',
				iframe: true,
				contenturl: '#local.removeAllDocsConfirmationLink#',
				strmodalfooter : {
					showclose: true,
					closebuttonlabel: 'Cancel',
				}
			});
		}
	</script>
	<style>
		##mcSWLMaterialDocUploader .uppy-Dashboard-browse {clear: both;display: block;margin: 0 auto;text-transform: capitalize;}
		.row-drag-handle {
			cursor: move;
			text-align: center;
			color: ##999;
			padding: 8px;
		}
		.row-drag-handle:hover {
			color: ##333;
		}
		.row-drag-handle i {
			font-size: 14px;
		}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.swlJS#">

<cfoutput>
	<div class="form-group mt-2 text-right uppyUploadWrap d-none">
		<button type="button" name="btnUpdateSWLProgramMaterials" id="btnUpdateSWLProgramMaterials" class="btn btn-sm btn-primary d-none" onclick="onDocSaveComplete();">Save</button>
	</div>
	<div class="form-group mt-2 uppyUploadWrap d-none">
		<div id="mcSWLMaterialDocUploader"></div>
	</div>
	<div class="form-group">
		<div class="alert alert-info pl-1 speaker-well-info">
			<i class="fa-solid fa-circle-info mr-1"></i> <strong>IMPORTANT</strong>
			<ul class="mt-2 mb-0">
				<li>To manually email materials, go to the Registrants tab and click 'Email Materials'.</li>
			</ul>
		</div>	
		<div class="toolButtonBar mb-1 addMoreFileLinkWrap d-none">
			<a href="javascript:void(0);" id="addMoreFileLink" onclick="addMoreFiles();return false;"  data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to Add more files."><i class="fa-regular fa-circle-plus"></i> Add More Files</a>
		</div>
		<table id="SWLMaterialDocsTable" class="table table-sm table-striped table-bordered SWLMaterialDocsTool d-none" style="width:100%">
			<thead>
				<tr>
					<th style="width: 5%;">&nbsp;</th>
					<th style="width: 50%;">Document Title</th>
					<th style="width: 5%;">File type</th>
					<th style="width: 15%;">Actions</th>
				</tr>
			</thead>
		</table>
		<div class="mb-1 removeAllFileLinkWrap d-none text-right">
			<a href="javascript:void(0);" id="removeAllFileLink" class="text-danger" onclick="removeAllFiles();return false;"  data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to remove all files."><i class="fa-regular fa-circle-minus"></i> Remove All Files</a>
		</div>
	</div>	
</cfoutput>