// Base64 Object - https://gist.github.com/ncerminara/11257943
var Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t="";var n,r,i,s,o,u,a;var f=0;e=Base64._utf8_encode(e);while(f<e.length){n=e.charCodeAt(f++);r=e.charCodeAt(f++);i=e.charCodeAt(f++);s=n>>2;o=(n&3)<<4|r>>4;u=(r&15)<<2|i>>6;a=i&63;if(isNaN(r)){u=a=64}else if(isNaN(i)){a=64}t=t+this._keyStr.charAt(s)+this._keyStr.charAt(o)+this._keyStr.charAt(u)+this._keyStr.charAt(a)}return t},decode:function(e){var t="";var n,r,i;var s,o,u,a;var f=0;e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");while(f<e.length){s=this._keyStr.indexOf(e.charAt(f++));o=this._keyStr.indexOf(e.charAt(f++));u=this._keyStr.indexOf(e.charAt(f++));a=this._keyStr.indexOf(e.charAt(f++));n=s<<2|o>>4;r=(o&15)<<4|u>>2;i=(u&3)<<6|a;t=t+String.fromCharCode(n);if(u!=64){t=t+String.fromCharCode(r)}if(a!=64){t=t+String.fromCharCode(i)}}t=Base64._utf8_decode(t);return t},_utf8_encode:function(e){e=e.replace(/\r\n/g,"\n");var t="";for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);if(r<128){t+=String.fromCharCode(r)}else if(r>127&&r<2048){t+=String.fromCharCode(r>>6|192);t+=String.fromCharCode(r&63|128)}else{t+=String.fromCharCode(r>>12|224);t+=String.fromCharCode(r>>6&63|128);t+=String.fromCharCode(r&63|128)}}return t},_utf8_decode:function(e){var t="";var n=0;var r=c1=c2=0;while(n<e.length){r=e.charCodeAt(n);if(r<128){t+=String.fromCharCode(r);n++}else if(r>191&&r<224){c2=e.charCodeAt(n+1);t+=String.fromCharCode((r&31)<<6|c2&63);n+=2}else{c2=e.charCodeAt(n+1);c3=e.charCodeAt(n+2);t+=String.fromCharCode((r&15)<<12|(c2&63)<<6|c3&63);n+=3}}return t}};

// memberForm
function fnShowReloadScreen() {
	$('#refeshingScreen').show();
}
function fnRefreshQueue() {
	var refreshQueueResult = function(r) { self.location.href = self.location.href; };
	$('#spanMIQLink').html('<img src="/assets/common/images/progress.gif" width="16" height="16"> <b>Refreshing...</b>');
	var objParams = { memberid:mcma_memberid };
	TS_AJX('ADMMEMBER','processMemberInQueue',objParams,refreshQueueResult,refreshQueueResult,120000,refreshQueueResult);
}
function fnShowMIQMessage(miq,miuq) {
	var divMIQHTML = [];
	if (miq)
		divMIQHTML[divMIQHTML.length] = 'This member\'s groups may have changed and will refresh shortly. <span id="spanMIQLink"><a href="javascript:fnRefreshQueue();">Refresh Immediately if needed.</a></span>';
	if (miuq)
		divMIQHTML[divMIQHTML.length] = 'This member has information to be updated and will be processed soon.';
	if (divMIQHTML.length) {
		$('#divMIQ span.content').html(divMIQHTML.join('<br />'));
		$('#divMIQ').removeClass('d-none');
	}
}
function viewSuppressionListEmails(){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		title: 'Review and Remove Blocks',
		contenturl: mcma_link_suppressedemails,
		strmodalfooter: {
			showclose: true
		}
	});
}
function viewListTroubleshooter(){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		title: 'List Troubleshooter',
		contenturl: mcma_link_listtroubleshooterIssues,
		iframe: true,
		strmodalfooter: {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.returnToListsTab',
			extrabuttonlabel:'Return to Lists Tab',
		}
	});
}
function removeEmailFromSuppressionList(email, typesList, subuserIDList, el){
	mca_hideAlert('err_suppressionlist');
	var removeResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			if($(el).closest('ul').find('li').length > 1) $(el).closest('li').remove();
			else {
				MCModalUtils.hideModal();
				self.location.href = self.location.href;
			}
		}
		else {
			mca_showAlert('err_suppressionlist', 'Some error occured while removing the email from suppression list');
			$(el).siblings('.divProgressRemove').toggleClass('d-none', true);
			$(el).attr('disabled',false);
		}
	};

	$(el).attr('disabled',true);
	$(el).siblings('.divProgressRemove').toggleClass('d-none', false);
	var objParams = { email:email, typesList:typesList, subuserIDList:subuserIDList };
	TS_AJX('ADMINEMAILBLAST','removeEmailFromSuppressionList',objParams,removeResult,removeResult,60000,removeResult);
}
function editSuppressedMemberEmail(email){
	$('#divManageSuppressionsContainer').addClass('d-none');
	$('#divUpdateEmailContainer').removeClass('d-none').html('<h5>Update Email Address</h5>' + mca_getLoadingHTML()).load(
		mcma_link_editsuppressedemail + '&email=' + email,
		function(response, status, xhr) {
			if (status == 'error') {
				$(this).html('Unable to load update email form for this member.');
			}
		}
	);
}
function updateSuppressedMemberEmail(objParams){
	var updateResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			MCModalUtils.hideModal();
			self.location.href = self.location.href;
		}
		else {
			mca_showAlert('err_emailupdate', 'Some error occured while updating the member email');
			$('#btnUpdateEmailAddr').attr('disabled', false);
			$('span.divProgressUpdate').toggleClass('d-none', true);
		}
	};

	$('#btnUpdateEmailAddr').attr('disabled', true);
	$('span.divProgressUpdate').toggleClass('d-none', false);
	TS_AJX('ADMMEMBER','updateSuppressedMemberEmail',objParams,updateResult,updateResult,20000,updateResult);
}
function mca_loadlastmemviewed() {
	var mca_loadlastmemviewedResult = function(r) {
		if (r.success && r.success == 'true' && r.members.length) { 
			var source = $('#mca_lastmemvisit_template').html();
			var template = Handlebars.compile(source);
			$('div#divLastMemVisit').html(template(r));
		} else
			$('div#divLastMemVisit').html('');
	};
	$('div#divLastMemVisit').html('<div class="text-right"><i class="fa-light fa-circle-notch fa-spin fa-lg"></i></div>');
	var objParams = { memberid:mcma_actormemberid };
	TS_AJX('ADMMEMBER','getLastMembersViewed',objParams,mca_loadlastmemviewedResult,mca_loadlastmemviewedResult,10000,mca_loadlastmemviewedResult);
}

// common fns
function closeBox() { 
	if (typeof reqReload == "undefined") MCModalUtils.hideModal();
	else customCloseBox();
}
function addPayment(po) {
	mca_addPayment(po);
}
function closeAddPayment(po) { 
	switch(mcma_currtab) {
		case 'dues': reloadMemberSubs();
		break;
		case 'events': reloadEventGrid();
		break;
		case 'transactions': reloadTransTab();
		break;
		case 'referral': refreshRetainedCaseGrid();
		break;
		case 'store': reloadStoreTable();
		break;
	}
	
	if (Number(mcma_hasrights_allocpmt) == 1) mca_allocIndivPayment(po);
	else MCModalUtils.hideModal();
}
function allocIndivPayment(po) {
	mca_allocIndivPayment(po);
}
function closeAllocPayment() { 
	switch(mcma_currtab) {
		case 'dues': reloadMemberSubs();
		break;
		case 'events': reloadEventGrid(); 
		break;
		case 'transactions': reloadTransTab();
		break;
		case 'referral': refreshRetainedCaseGrid();
		break;
		case 'store': reloadStoreTable();
		break;
	}
	MCModalUtils.hideModal();
}
function clearDateRangeField(f,t) {
	$('#'+f).val('');
	$('#'+t).val('');
}

// demographics
function fnCopyInfo() {
	$('#div_mf_demo_copywrapper').show();
	$('#div_mf_demo_mainwrapper').hide();
}
function fnCopyInfoDone() {
	$('#div_mf_demo_mainwrapper').show();
	$('#div_mf_demo_copywrapper').hide();
}
function fnmergeAccount() {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Possible Matching Members',
		iframe: true,
		contenturl: mcma_link_mergemembers+'&memberID='+mcma_memberid+'&mode=direct',
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: false,
			buttons: [
				{ class: "btn-link py-1", clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.selectOtherMember', label: 'Select a Member Not Listed Above', 
					name: 'btnSelectOtherMember', id: 'btnSelectOtherMember', disabled: true },
				{ class: "btn-primary py-1 ml-auto", clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.chooseMergeAccounts', label: 'Merge Selected Members', iconclass: 'fa-solid fa-users',
					name: 'btnMergeSelectedMembers', id: 'btnMergeSelectedMembers', disabled: true }
			]
		}
	});
}
function fnMergeSelectedAcct(memField, mID, mNum, mName) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Merge Member Accounts',
		iframe: true,
		contenturl: mcma_link_mergemembers+'&memberID='+mcma_memberid+'&frmMergeMemberID=' + mID + '&mergeact=compare&mode=direct',
		strmodalfooter : {
			classlist: 'd-none',
		}
	});
}
function fnResetPWD(isMFA) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: isMFA > 0 ? 'Reset Login/MFA' : 'Reset Login',
		iframe: true,
		contenturl: mcma_link_logincrd+'&memberID='+mcma_memberid+'&mode=direct',
		strmodalfooter: { showclose: true , autoClose:false}
	});	
	$('#MCModal').on('hidden.bs.modal',customCloseMemModal);
}
function fnDoResetPWD() {
	self.location.href = mcma_link_resetpwd+'&memberID='+mcma_memberid;
}
function fnLoadLoginAsMemberLink() {
	var loadLoginAsMemberLinkResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') $('#loginAsMemberInput').val(r.loginlink);
	};
	var objParams = { memberid:mcma_memberid, username:mcma_username };
	TS_AJX('ADMMEMBER','generateLoginAsMemberLink',objParams,loadLoginAsMemberLinkResult,loadLoginAsMemberLinkResult,10000,loadLoginAsMemberLinkResult);
}
function fnLoginAsMember() {
	var msg = '<div>Use the following link to login as this member.<br/><b>This link should NOT be shared with anyone. It expires in 5 minutes.</b></div> \
		<div class="my-3"><textarea id="loginAsMemberInput" readonly="readonly" rows="3" placeholder="Loading... Please Wait" onclick="this.select();" class="form-control form-control-sm"></textarea></div> \
		<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="far fa-question-circle"></i></span><span><strong class="d-block">Warning!</strong> Because you are currently logged into this site, we suggest you use this link in a different browser or private window to not cause conflicts with your current session.</span></div>';

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Login as Member',
		iframe: false,
		strmodalbody: { 
			content: msg
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true
		}
	});

	fnLoadLoginAsMemberLink();
}
function fnShowLoginHistory() {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Login History for ' + $('#mc_memnameprinted').text(),
		iframe: true,
		contenturl: mcma_link_loginhistory+'&memberID='+mcma_memberid+'&mode=direct',
		strmodalfooter : {
			classlist: 'text-right',
			showclose: true
		}
	});
}
function editMember(m,w) {
	if (w) window.open(mcma_link_edit+'&memberID=' + m,'_blank');
	else top.location.href = mcma_link_edit+'&memberID=' + m;
}
function fnEditDetails() {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Edit Member',
		iframe: true,
		contenturl: mcma_link_editinfo+'&mode=direct&memberID='+mcma_memberid,
		strmodalbody : {
			classlist: 'p-2'
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: 'fnClickSaveMember',
			extrabuttonlabel: 'Save Information',
		}
	});
}
function fnClickSaveMember() {
	$('#MCModalBodyIframe')[0].contentWindow.postMessage({ messagetype: 'MCModalEvent', eventname: 'savemember' }, mcma_hostname);
}
function closeDelete() {
	self.location.href = mcma_link_search;
}
function fnUpdateMemberPhoto() {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Update Member Photo',
		iframe: true,
		contenturl: mcma_link_updatephoto+'&memberID='+mcma_memberid+'&mode=direct',
		strmodalbody : {
			classlist: 'p-2'
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false
		}
	});
}
function fnSendSingleWelcomeEmail(){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Send Welcome Email',
		iframe: true,
		contenturl: mcma_link_welcome+'&memberID='+mcma_memberid+'&mode=direct',
		strmodalbody : {
			classlist: 'p-2'
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false
		}
	});
}
function loadGroups() {
	$('div#div_mf_demo_grps').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Loading groups...</div>').load(
		mcma_link_showgroups+'&memberID='+mcma_memberid+'&mode=stream', 
		function(response, status, xhr) { 
			if (status == 'error') {
				$(this).html('Unable to load group membership for this member.');
			} else {
				mcActivateTooltip($('div#div_mf_demo_grps'));
			}
		}
	);
}
function fnManageGroups() {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Group Assignments for',
		iframe: true,
		contenturl: mcma_link_addgroupsmanually+'&memberID='+mcma_memberid+'&mode=direct',
		
	});
	$('#MCModal').on('hidden.bs.modal', onCloseFn);
}
function onCloseFn(){
	top.fnShowReloadScreen();
	top.location.href=top.location.href;
}
function removeFromGroup(gid) { 
	var msg = '<div class="m-3"><div>Are you sure you want to remove this manual group assignment?<br/>Click OK to continue or CANCEL to return to the member page.</div><div class="m-1 text-right"><button class="btn btn-sm btn-primary" type="button" onClick="fnDoRemoveFromGroup(' + gid + ');">OK</button> <button class="btn btn-sm btn-secondary" type="button" onClick="top.closeBox();">Cancel</button></div></div>';
	if (confirm(msg)) {
		fnDoRemoveFromGroup(gid);
	}
			
}
function fnDoRemoveFromGroup(gid) { 
	var removeMemberResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') { } else { alert('There was a problem updating the group membership.'); }
		reqReload = 1;
		customCloseBox();
	};
	var objParams = { memberid:mcma_memberid, groupid:gid };
	TS_AJX('GRPADM','removeGroupMember',objParams,removeMemberResult,removeMemberResult,10000,removeMemberResult);
}
function loadLicenses() {
	$('div#div_mf_demo_pl').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Loading licenses...</div>').load(
		mcma_link_showlicenses+'&memberID='+mcma_memberid+'&mode=stream', 
		function(response, status, xhr) { 
			if (status == 'error') {
				$(this).html('Unable to load licenses for this member.');
			}
		}
	);
}
function fnManageProLicenses() {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Professional Licenses',
		iframe: true,
		contenturl: mcma_link_addlicense+'&memberID='+mcma_memberid+'&mode=direct',
		strmodalfooter: { showclose: true }
	});
	$('#MCModal').on('hidden.bs.modal',customCloseMemModal);
}
function getLinkedRecordFilterParams(){
	var roleTypeIDList = $('#linkedMemberRoleType').val() || [];
	var recordTypeIDList = $('#linkedMemberRecordType').val() || [];

	var objParams = {
		fRoleTypeIDList:roleTypeIDList.join(','),
		fRecordTypeIDList:recordTypeIDList.join(','),
		fLastName:$('#fLastName').val()
	}

	return objParams;
}
function filterLinkedRecordsTable(){
	linkedRecordsTable.draw();
}
function initLinkedRecordsTable(){
	let arrColumns = [];

	if(mcma_showmemberphotos){
		arrColumns.push(
			{
				"data": null,
				"render": function ( data, type ) {
					let renderData = '';
					if (type === 'display') {
						renderData = data.hasmemberphotothumb ? '<img class="mc_memthumb" src="/memberphotosth/'+data.membernumber.toLowerCase()+'.jpg">' : '<img src="/assets/common/images/directory/default.jpg" width="80" height="100">';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top text-center w-5"
			}
		);
	}
	arrColumns.push(
		{ 	
			"data": null,
			"render": function ( data, type ) {
				let renderData = '<a href="'+mcma_link_edit+'&memberID='+data.memberid+'" target="_blank">'+data.combinedname+'</a>';
				if (data.company.length) renderData += '<div>'+data.company+'</div>';
				renderData += data.combinedaddresses + data.recordfields;
				return type === 'display' ? renderData : data;
			},
			"className": "align-top"
		},
		{ 	
			"data": null,
			"render": function ( data, type ) {
				let renderData = '';
				renderData += '<span class="badge badge-info">'+data.relationshiplist.replace('|','</span> &nbsp; <span class="badge badge-info">')+'</span><br/>';
				if(data.mc_recordtype.length) renderData += data.mc_recordtype+'<br/>';
				if(data.mc_membertype.length) renderData += data.mc_membertype+'<br/>';
				renderData += data.isinactiveaccount ? '<span class="text-danger font-weight-bold">ACCOUNT INACTIVE</span><br/>' : data.mc_memberstatus+'<br/>';
				if(data.relationshipclasses.length) renderData += data.relationshipclasses;
				return type === 'display' ? renderData : data;
			},
			"className": "align-top text-right"
		}
	);
	linkedRecordsTable = $('#linkedRecordsTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50, 100 ],
		"dom": "<'row'<'col-6'<'float-left mt-2'l><'float-left p-1 m-2 selCountDisp'>><'col-6'p>>" + "<'row'<'col-12'tr>>" + "<'row'<'col-5'i><'col-7'p>>",
		"language": {
			"lengthMenu": "_MENU_",
			"emptyTable": "No Linked Records Found"
		},
		"ajax": { 
			"url": mcma_link_linkedrecords,
			"type": "post",
			"data": function(d) {
				let objFilters = getLinkedRecordFilterParams();
				$.each(objFilters, function(key,val) { d[key] = val; });
			}
		},
		"autoWidth": false,
		"columns": arrColumns,
		"ordering": false,
		"initComplete": function( settings, json ) {
			$('#div_mf_demo_linked_loading').remove();
			$('#div_mf_demo_linked').removeClass('d-none');
		}
	});
}
function fnManageRecordRelationships() {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Manage Linked Records',
		iframe: true,
		contenturl: mcma_link_managelinkedrecords+'&memberID='+mcma_memberid+'&mode=direct',
		strmodalfooter: { showclose: true }
	});
}
function fnRecordRelationshipCleanup() { 
	reqReload = 1; 
	customCloseBox();
}
function loadChangeHistory(at) {
	$('div#div_mf_demo_chghist').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Loading history...</div>').load(
		mcma_link_changehistory+'&at='+at+'&memberID='+mcma_memberid+'&mode=stream', 
		function(response, status, xhr) { 
			if (status == 'error') {
				$(this).html('Unable to load change history for this member.');
			}
		}
	);
}
function toggleATRow(rowID) {
	var st = $('#atChanges_' + rowID).css("display");
	if (st == 'none') {
		$('#atChanges_' + rowID).show();
		$('#atTreeImg_' + rowID).attr("src","/assets/common/images/tree-open.jpg");
	} else {
		$('#atChanges_' + rowID).hide();
		$('#atTreeImg_' + rowID).attr("src","/assets/common/images/tree-closed.jpg");
	}
}
function toggleATGrid() {
	var st = $('#mcg_gridboxAT').css("display");
	if (st == 'none') {
		$('#mcg_gridboxAT').show();
		$('#auditResultsShow').hide();
		$('#auditResultsHide').show();
	} else {
		$('#mcg_gridboxAT').hide();
		$('#auditResultsShow').show();
		$('#auditResultsHide').hide();
	}
}
function toggleGCHGrid(display) {
	if (display == 'hide') {
		$('#btnShowHistoryGCH').removeClass('d-none');
		$('.hideContent').addClass('d-none');
	} else {
		$('#btnShowHistoryGCH').addClass('d-none');
		$('.hideContent').removeClass('d-none');
	}
}
function loadCondGrpChangeHistory() {
	$('div#div_mf_demo_cgchghist').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Loading history...</div>').load(
		mcma_link_grpchangehistory+'&memberID='+mcma_memberid+'&mode=stream', 
		function(response, status, xhr) { 
			if (status == 'error') {
				$(this).html('Unable to load condition and group change history for this member.');
			}
		}
	);
}
function exportCondGrpChangeHistory() {
	self.location.href = mcma_link_congrpchangehistory+'&memberID='+mcma_memberid+'&mode=stream';
}
function loadAddrFieldsets() {
	$('div#div_mf_demo_addrmfs').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Loading data...</div>').load(
		mcma_link_showaddrdata+'&memberID='+mcma_memberid+'&mode=stream', 
		function(response, status, xhr) {
			if (status == 'error') {
				$(this).html('Unable to load address and fieldset data for this member.');
			} else {
				if (Clipboard.isSupported()) {
					var addcpyClip = new Clipboard('a.addrcpy', {
						text: function(trigger) {
							var mcc = $('#mc_company').text();
							return $('#mc_memnameprinted').text().replace(/\r/gi,'').replace(/\n/gi,'').replace(/\t/gi,'') + '\r\n' + (mcc.length > 0 ? mcc + '\r\n' : '') + Base64.decode(trigger.getAttribute('data-clipboard-text')).replace(/<br\/>/gi,'\r\n');
						}
					});
					addcpyClip.on('success', function(e) {
						Promise.all([MCLoader.loadJS('/assets/common/javascript/noty/3.1.4/noty.min.js'),MCLoader.loadCSS('/assets/common/javascript/noty/3.1.4/noty.css')]).then(function(){
							var mc_noty_msg = '<div class=\"mc-noty-item\"><i class=\"fa-regular fa-thumbs-up fa-lg\"></i><div class=\"mc-noty\">We\'ve copied the following to your clipboard:<br/><br/>' + e.text.replace(/\n/gi,'<br\/>') + '</div></div>';
							new Noty({ 
								type:'success',
								layout:'bottomLeft', 
								theme:'bootstrap-v4', 
								text:mc_noty_msg,
								closeWith:['button','click'],
								timeout:3000 
							}).show();
						});
					});
					$('a.addrcpy')
						.on('mousedown', function(client,args) { $(this).children('i').removeClass().addClass('fa-regular fa-calendar-circle-plus'); })
						.on('mouseup', function(client,args) { $(this).children('i').removeClass().addClass("fa-regular fa-copy"); });
					mcActivateTooltip($('div#div_mf_demo_addrmfs'));
				} else {
					$('a.addrcpy').hide();
				}
			}
		}
	);
}
function fnRefreshDistricts(aid) {
	$('a#a_rd_'+aid).hide();
	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Refresh Scheduled',
		strmodalbody: {
			content: '<div style="padding:10px;">Districting data for this address will be refreshed shortly.</div>',
		},
		strmodalfooter : {
			classlist: 'd-none'
		}
	});
	var fnRefreshDistrictsResult = function(r) { };
	var objParams = { aid:aid };
	TS_AJX('ADMMEMBER','refreshAddressDistricting',objParams,fnRefreshDistrictsResult,fnRefreshDistrictsResult,30000,fnRefreshDistrictsResult);
}
function fnForceRefreshMemberGroups() {
	var span = $('span#span_mf_demo_grpsrefresh');
	var refreshResult = function(r) { 
		loadGroups(); 
		span.html('<a href="javascript:fnForceRefreshMemberGroups();" style="color:#aeaeae;"><i class="fa-regular fa-sync"></i> Refresh Groups</a>');
	};
	span.html('<i class="fa-light fa-circle-notch fa-spin"></i> Refreshing Groups...');
	var objParams = { memberid:mcma_memberid };
	TS_AJX('ADMMEMBER','forceRefreshMemberGroups',objParams,refreshResult,refreshResult,120000,refreshResult);
}
function fnShowMemberKey(mk) {
	var msg = '<div>[[MemberKey]] is a merge code you can use in email communications with members. This merge code converts to an encrypted string which identifies this member on your site.</div> \
		<div class="mt-3">This key is unique to this member and <b>changed each time it is generated</b>, so the value you see below should not be hardcoded into any communications.</div> \
		<div class="my-3"><textarea id="memberKeyInput" readonly="readonly" class="form-control form-control-sm" rows="3" onclick="this.select();">'+mk+'</textarea></div>';
	
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'MemberKey for this Member',
		iframe: false,
		strmodalbody: { 
			content: msg
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true
		}
	});
}
function fnLoadDemographicTab(mpl,sub) {
	loadAddrFieldsets();
	initLinkedRecordsTable();
	if (mpl) loadLicenses();
	if (sub) initMemberSubscriptions();
	loadGroups();
	loadChangeHistory('m');
	loadCondGrpChangeHistory();

	if (!Clipboard.isSupported()) $('div#copyInfoDiv').hide();
	$('a.copyInfoLink')
		.on('mousedown', function(client,args) { $(this).children('i').removeClass().addClass('fa-regular fa-calendar-circle-plus'); })
		.on('mouseup', function(client,args) { $(this).children('i').removeClass().addClass('fa-regular fa-copy'); });
	var addcpyClip = new Clipboard('a.copyInfoLink, button.copyInfoLink', {
		text: function(trigger) {
			var fsid = trigger.getAttribute('data-fsid');
			var clipBoardText = Base64.decode($('div#div_mf_demo_copyfs'+fsid).attr('data-clipboard-text')).replace(/<br\/>/gi,'\r\n');
			return decodeHTMLEntities(clipBoardText);
		}
	});
	addcpyClip.on('success', function(e) {
		Promise.all([MCLoader.loadJS('/assets/common/javascript/noty/3.1.4/noty.min.js'),MCLoader.loadCSS('/assets/common/javascript/noty/3.1.4/noty.css')]).then(function(){
			var mc_noty_msg = '<div class=\"mc-noty-item\"><i class=\"fa-regular fa-thumbs-up fa-lg\"></i><div class=\"mc-noty\">We\'ve copied the following to your clipboard:<br/><br/>' + e.text.replace(/\n/gi,'<br\/>') + '</div></div>';
			new Noty({ 
				type:'success',
				layout:'bottomLeft',
				theme:'bootstrap-v4',
				text:mc_noty_msg,
				closeWith:['button','click'],
				timeout:3000 
			}).show();
			fnCopyInfoDone();
		});
	});
}
function decodeHTMLEntities(htmlEncodedString) {
	var textArea = document.createElement('textarea');
	textArea.innerHTML = htmlEncodedString;
	return textArea.value;
}

// additional
function loadCustomTabContent(mode) {
	$('.saveADBtn').attr('disabled',true);

	if(mode === undefined) {
		mode = 'init';
	} else if (mode == 'reload') {
		if (arrUploaders.length) {
			if ($('#frmMemberAD input.mca_newcustomfiles').filter(function(){return this.value==1}).length) {
				if (myFunc == null) {
					myFunc = setInterval( function() { loadCustomTabContent('reload'); }, 1000);
				} 
				return false;
			} else if (myFunc != null) {
				clearInterval(myFunc);
				myFunc = null;
			}
		} else if (myFunc != null) {
			clearInterval(myFunc);
			myFunc = null;
		}

		$.each(arrCFareas, function(i,el) {
			this.loaded = 0;
		});
	}

	if ($('#divMCFCustomTabContainer').is(':hidden')) $('#divMCFCustomTabContainer').show(200); 
	$('#divMCFFormSubmitArea').html('').hide();

	$.each(arrCFareas, function(i,el) {
		if (this.loaded == 0) loadCustomTabFieldSetContent(this.fsid);
	});
}
function loadCustomTabFieldSetContent(fsid) {
	var divpfsid = $('#divMCFSTabFS_'+fsid);
	var divfsid = $('#divMCFSTab_'+fsid);
	divfsid.html('<div class="c" style="margin-top:20px;height:160px;"><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Loading custom field' + (fsid == 0 ? 's' : ' set') + '...</div>');

	var fd = { mid:mcma_memberid, fsid:fsid };
	$.ajax({
		url: '/?event=proxy.ts_json&c=ADMMEMBER&m=getCustomTabInfo',
		type: 'POST',
		data: { fd:JSON.stringify(fd) },
		dataType: 'json',
		success: function(response) { 
			var tmpResponse = JSON.stringify(response).replace(/\^~~~\^/g,'');
			var r = JSON.parse(tmpResponse);
			if (r.success) {
				if (r.arrFields.length == 0) divpfsid.hide(200); 
				else {
					divfsid.html(MCFTABtemplate(r));
					divpfsid.show();
					mcActivateTooltip(divpfsid);
				}

				enableSaveBtnAfterLoad(fsid);
			} else {
				divfsid.html('Unable to display custom fields.');
			}
		}, fail: function(r) { 
			divfsid.html('Unable to display custom fields.');
		}
	});
}
function enableSaveBtnAfterLoad(fsid) {
	arrCFareas.find(function (f) {
		return f.fsid == fsid;
	}).loaded = 1;

	if (arrCFareas.find(function (f) {
		return f.loaded == 0;
	}) === undefined) {
		var scope = $('#frmMemberAD');
		removeDuplicateFields(scope);
		initializeCustomFieldsControls(scope);
		$('.saveADBtn').attr('disabled', false);
	}
}
function initializeCustomFieldsControls(scope,mode) {
	if (mode === undefined) mode = 'init';
	
	$.each(scope.find('.datecf'), function(i,el) {
		var datefieldid = $(this).attr('id');
		var mcminvaluedate = $(this).data('mcminvaluedate');
		var mcmaxvaluedate = $(this).data('mcmaxvaluedate');

		if (mcminvaluedate !== undefined && mcmaxvaluedate !== undefined) {
			mca_setupDatePickerField(datefieldid,mcminvaluedate,mcmaxvaluedate);
		} else if (mcminvaluedate !== undefined) {
			mca_setupDatePickerField(datefieldid,mcminvaluedate);
		} else if (mcmaxvaluedate !== undefined) {
			mca_setupDatePickerField(datefieldid,'',mcmaxvaluedate);
		} else {
			mca_setupDatePickerField(datefieldid);
		}
	});

	scope.find('.btnClearDateCF').click(function(event){
		var linkedDateControlID = $(this).data('linkeddatecontrol');
		$('#' + linkedDateControlID).val('').change();
		event.preventDefault();
	});

	mca_setupSelect2();
	mca_setupCalendarIcons('frmMemberAD');

	initMCFileUploaders(scope);
}
function removeDuplicateFields(scope) {
	var arrCustomFields = [];
	$.each(scope.find('input,select,textarea'), function (i, el) {
		var thisElemID = $(this).attr('id');
		if (arrCustomFields.find(function (el) {
			return el === thisElemID;
		}) === undefined) arrCustomFields.push(thisElemID);else $(this).closest('div.form-group.row').remove();
	});
}
function editCustomTabContent(id,cid) {
	var editCustomTabContentResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			$('div#'+id+'wrapper').html(r.html);
			var div = document.getElementById(id+'wrapper');
			var x = div.getElementsByTagName("script");
			for(var i=0;i<x.length;i++) eval(x[i].text);
		}
	};

	$('div#'+id+'wrapper').html('<div style="border:1px solid #000;height:100px;text-align:center;padding-top:20px;"><i class="fa-light fa-circle-notch fa-spin fa-2x"></i> <b>Loading Content Editor...</b></div>');
	$('span#a_'+id).hide();

	var objParams = { objName:id, contentID:cid };
	TS_AJX('CONTENT','showContentEditor',objParams,editCustomTabContentResult,editCustomTabContentResult,10000,editCustomTabContentResult);
}
function validateADForm(){
	$("button.saveADBtn").prop('disabled',true);
	var mdForm = $('#frmMemberAD');

	var mcFieldsHavingDefVal = mdForm.find('input:text[data-mcdefvalue], select[data-mcdefvalue]').not(':disabled');
	var radioCheckBoxElements = mdForm.find('input:radio[data-mcdefvalue], input:checkbox[data-mcdefvalue]');
	var elemArr = [];
	$.each( radioCheckBoxElements, function() {
		var elemName = this.name;
		if( $.inArray( elemName, elemArr ) < 0 ){
			elemArr.push(elemName);
			mcFieldsHavingDefVal.push(this);
		}
	});
	$.map(mcFieldsHavingDefVal,mc_fillDefaultValue);

	var strErr = '';
	var errorMsgArray = [];

	var memberCustomField = mdForm.find('input:not([readonly]):text[data-mcreq="1"], input:not([readonly]):button[data-mcreq="1"], select:not([readonly])[data-mcreq="1"], textarea:not([readonly])[data-mcreq="1"]').not(':hidden').not(':disabled');
	var radioCheckBoxElements = mdForm.find('input:not([readonly]):radio[data-mcreq="1"], input:not([readonly]):checkbox[data-mcreq="1"]');
	var elemArr = [];
	$.each( radioCheckBoxElements, function() {
		var elemName = this.name;
		if( $.inArray( elemName, elemArr ) < 0 ){
			elemArr.push(elemName);
			memberCustomField.push(this);
		}
	});
	var memberCustomFieldErrorMsgArray = $.map(memberCustomField,validateMCCustomField_isRequired);
	Array.prototype.push.apply(errorMsgArray, memberCustomFieldErrorMsgArray);

	/*text controls offering whole number*/
	var textControlIntegerCustomField = mdForm.find('input:not([readonly])[data-mcdisplaytypecode="TEXTBOX"][data-mcdatatypecode="INTEGER"]').not(':hidden').not(':disabled');
	var textControlIntegerCustomFieldErrorMsgArray = $.map(textControlIntegerCustomField,validateMCCustomField_textControlValidInteger);
	Array.prototype.push.apply(errorMsgArray, textControlIntegerCustomFieldErrorMsgArray);

	/*text controls offering decimal number*/
	var textControlDecimalCustomField = mdForm.find('input:not([readonly])[data-mcdisplaytypecode="TEXTBOX"][data-mcdatatypecode="DECIMAL2"]').not(':hidden').not(':disabled');
	var textControlDecimalCustomFieldErrorMsgArray = $.map(textControlDecimalCustomField,validateMCCustomField_textControlValidDecimal);
	Array.prototype.push.apply(errorMsgArray, textControlDecimalCustomFieldErrorMsgArray);

	/*text controls/textarea offering character range*/
	var stringCustomFieldRangeRestriction = mdForm.find('input:not([readonly])[data-mcdisplaytypecode="TEXTBOX"][data-mcdatatypecode="STRING"], textarea:not([readonly])[data-mcdisplaytypecode="STRING"]').not(':hidden').not(':disabled');
	var stringCustomFieldRangeRestrictionErrorMsgArray = $.map(stringCustomFieldRangeRestriction,validateMCCustomField_textControlValidTextRange);
	Array.prototype.push.apply(errorMsgArray, stringCustomFieldRangeRestrictionErrorMsgArray);

	var contentObjCustomFieldRangeRestriction = mdForm.find('.mca_contentobj');
	var contentObjCustomFieldRangeRestrictionErrorMsgArray = $.map(contentObjCustomFieldRangeRestriction,validateMCCustomField_contentObjValidTextRange);
	Array.prototype.push.apply(errorMsgArray, contentObjCustomFieldRangeRestrictionErrorMsgArray);

	/*multiselect range of options selected*/
	var multiselectFieldRangeFields = mdForm.find('select[multiple]');
	var multiselectFieldRangeFieldsErrorMsgArray = $.map(multiselectFieldRangeFields,validateMCCustomField_validSelectRange);
	Array.prototype.push.apply(errorMsgArray, multiselectFieldRangeFieldsErrorMsgArray);

	/*drop empty elements*/
	var finalErrors = $.map(errorMsgArray, function(thisError){
		if (thisError.length) return thisError;
		else return null;
	});
	if (finalErrors.length) strErr += finalErrors.join('<br/>');

	if (strErr.length > 0) {
		showMCADAlert(strErr);
		location.href="#customFrmTop";
		$("button.saveADBtn").prop('disabled',false);
		return false;
	} else {
		hideMCADAlert();
	}

	var arrFrmData = mdForm.serializeArray();
	var fd = new Object();

	var arrContentObjFields = mdForm.find('input.mca_contentobj').map( function() { return $(this).data('mcfieldcode'); }).get();
	var arrCheckboxMultiSelectFields = $.unique(mdForm.find('input[type="checkbox"],select[multiple]').map( function() { return $(this).attr('name'); }).get());

	$.each(arrFrmData, function() {
		if (fd[this.name] !== undefined && typeof this.value !== undefined) {
			fd[this.name] = fd[this.name] + ',' + this.value || '';
		} else {
			fd[this.name] = this.value || '';
		}

		if (arrContentObjFields.length && arrContentObjFields.indexOf(this.name) != -1) {
			if (typeof CKEDITOR.instances[this.name] !== undefined) {
				try { fd[this.name] = CKEDITOR.instances[this.name].getData(); } catch(e) { delete fd[this.name]; };
			} else {
				delete fd[this.name];
			}
		}
	});

	/*empty checkbox/multiselect handling*/
	$.each(arrCheckboxMultiSelectFields, function() {
		if (fd[this] === undefined) fd[this] = '';
	});

	$('#divMCFCustomTabContainer').hide();
	var loadingHTML = $('#divMCFSaveLoading').html();

	$.each(arrUploaders, function() { 
		if (this.fileUploaded == 0) this.uploader.start(); 
	});
	
	$("#divMCFFormSubmitArea").html(loadingHTML).show().load(mcma_link_saveaddtl, fd, function() { loadCustomTabContent('reload'); });
}
function validateMCCustomField_isRequired(thisField) {
	var fld = $(thisField);
	var fldName = $(thisField).attr('name');
	var displayTypeCode = fld.data('mcdisplaytypecode');
	var dataTypeCode = fld.data('mcdatatypecode');
	var returnMsg = '';
	switch(displayTypeCode) {
		case 'TEXTBOX':
		case 'TEXTAREA':
		case 'DATE':
			if (fld.val().trim() == '') {
				returnMsg =  'Enter a value for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			}
		break;
		case 'SELECT':
			if (fld.val() == '' && fld.find('option:not(:disabled)').length > 1) {
				returnMsg =  'Select a value for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			}
		break;
		case 'RADIO':
		case 'CHECKBOX':
			if ($('input[name="' + fldName + '"]').not(':disabled').length > 0 && !$('input[name="' + fldName + '"]').not(':disabled').is(':checked')) {
				returnMsg =  'Select a value for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			}
		break;
		case 'DOCUMENT':
			var oldFileExists = $("#"+fld.data('mcfieldcode')+"_old").val().trim().length > 0;
			var newFileAttached = parseFloat($("#"+fld.data('mcfieldcode')+"_newFile").val()) === 1;
			var fileRemoved = parseFloat($("#"+fld.data('mcfieldcode')+"_removeDoc").val()) === 1;
			if ((!oldFileExists && !newFileAttached) || (fileRemoved && !newFileAttached)) {
				returnMsg =  'Select a file for the ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			}
		break;
	}
	return returnMsg;
}
function validateMCCustomField_textControlValidInteger(thisField) {
	var returnMsg = '';
	var fld = $(thisField);
	var fldval = Number(fld.val().trim());
	var minVal = fld.data('mcminvalueint');
	var maxVal = fld.data('mcmaxvalueint');

	if (fldval != '') {
		if (fldval !== parseInt(fldval)) {
			returnMsg = 'Enter a valid whole number for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (minVal !== undefined && maxVal !== undefined && ( fldval < Number(minVal) || fldval > Number(maxVal) ) ) {
			returnMsg = 'Enter a whole number between ' + minVal + ' and ' + maxVal + ' for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (minVal !== undefined && fldval < Number(minVal)) {
			returnMsg = 'Enter a whole number greater than ' + minVal + ' for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (maxVal !== undefined && fldval > Number(maxVal)) {
			returnMsg = 'Enter a whole number less than ' + maxVal + ' for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		}
	}
	return returnMsg;
}
function validateMCCustomField_textControlValidDecimal(thisField) {
	var returnMsg = '';
	var fld = $(thisField);
	var fldval = Number(fld.val().trim());
	var minVal = fld.data('mcminvaluedecimal2');
	var maxVal = fld.data('mcmaxvaluedecimal2');

	if (fldval != '') {
		if (fldval !== parseFloat(fldval)) {
			returnMsg = 'Enter a valid decimal number for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (minVal !== undefined && maxVal !== undefined && ( fldval < Number(minVal) || fldval > Number(maxVal) ) ) {
			returnMsg = 'Enter a decimal number between ' + minVal + ' and ' + maxVal + ' for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (minVal !== undefined && fldval < Number(minVal)) {
			returnMsg = 'Enter a decimal number greater than ' + minVal + ' for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (maxVal !== undefined && fldval > Number(maxVal)) {
			returnMsg = 'Enter a decimal number less than ' + maxVal + ' for ' + fld.data('mcfieldlabel').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		}
	}
	return returnMsg;
}
function validateMCCustomField_textControlValidTextRange(thisField) {
	var returnMsg = '';
	var fld = $(thisField);
	var fldval = fld.val().trim();
	var minVal = fld.data('mcminchars');
	var maxVal = fld.data('mcmaxchars');
	var fldValLen = fldval.length;

	if (fldValLen > 0) {
		if (minVal !== undefined && maxVal !== undefined && ( fldValLen < Number(minVal) || fldValLen > Number(maxVal) ) ) {
			returnMsg = (fld.data('mcfieldlabel') + ' length must be between ' + minVal + ' and ' + maxVal + ' characters').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (minVal !== undefined && fldValLen < Number(minVal)) {
			returnMsg = (fld.data('mcfieldlabel') + ' length must be greater than ' + minVal + ' characters').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (maxVal !== undefined && fldValLen > Number(maxVal)) {
			returnMsg = (fld.data('mcfieldlabel') + ' length must be under ' + maxVal + ' characters').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		}
	}
	return returnMsg;
}
function validateMCCustomField_validSelectRange(thisField) {
	var returnMsg = '';
	var fld = $(thisField);
	var fldValLen = (fld.val() || '').length;

	if (fldValLen > 0) {
		var minSel = fld.data('mcminselected');
		var maxSel = fld.data('mcmaxselected');

		if (minSel !== undefined && maxSel !== undefined && ( fldValLen < Number(minSel) || fldValLen > Number(maxSel) ) ) {
			returnMsg = (fld.data('mcfieldlabel') + ' must have between ' + minSel + ' and ' + maxSel + ' selections').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (minSel !== undefined && fldValLen < Number(minSel)) {
			returnMsg = (fld.data('mcfieldlabel') + ' must have at least ' + minSel + ' selections').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		} else if (maxSel !== undefined && fldValLen > Number(maxSel)) {
			returnMsg = (fld.data('mcfieldlabel') + ' must be less than ' + maxSel + ' selections').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		}
	}
	return returnMsg;
}
function validateMCCustomField_contentObjValidTextRange(thisField) {
	var returnMsg = '';
	var fld = $(thisField);
	var fldName = fld.attr('name').replace('_attributeHolder','');
	var fldval = '';

	try { fldval = CKEDITOR.instances[fldName].getData(); } catch(e) { };
	if (typeof CKEDITOR.instances[fldName] !== undefined) {
		var minVal = fld.data('mcminchars');
		var maxVal = fld.data('mcmaxchars');
		var fldValLen = fldval.length;

		if (fldValLen > 0) {
			if (minVal !== undefined && maxVal !== undefined && ( fldValLen < Number(minVal) || fldValLen > Number(maxVal) ) ) {
				returnMsg = (fld.data('mcfieldlabel') + ' length must be between ' + minVal + ' and ' + maxVal + ' characters').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			} else if (minVal !== undefined && fldValLen < Number(minVal)) {
				returnMsg = (fld.data('mcfieldlabel') + ' length must be greater than ' + minVal + ' characters').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			} else if (maxVal !== undefined && fldValLen > Number(maxVal)) {
				returnMsg = (fld.data('mcfieldlabel') + ' length must be under ' + maxVal + ' characters').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			}
		}
	}
	return returnMsg;
}
initMCFileUploaders = function(scope) {
	arrUploaders = [];

	$.each(scope.find('.mca_fileselect'), function() {
		var el = $(this);
		var mcFileSelBtnID = el.attr('id');
		var mcFileFieldCode = el.data('mcfieldcode');
		var mcFileColumnID = el.data('mccolumnid');
		var mcFileOldVal = $('#'+mcFileFieldCode+'_old').val();
		var uploadURL = mcma_link_savedoc+'&cid='+mcFileColumnID+'&cname='+encodeURI(el.data('mcfieldlabel'))+'&coldval='+mcFileOldVal+'&memberID='+mcma_memberid;

		var uploader = new plupload.Uploader({
			runtimes:'html5',
			browse_button:mcFileSelBtnID,
			url:uploadURL,
			file_data_name:mcFileFieldCode,
			multi_selection:false
		});

		uploader.bind('FilesAdded', function(up, files) {
			plupload.each(files, function(file) {
				$('#'+mcFileFieldCode+'_newFile').val(1);
				$('#'+mcFileFieldCode+'_newFileDetails').html(file.name + ' (' + plupload.formatSize(file.size) + ')');
			});
		});

		uploader.bind('FileUploaded', function () {
			$('#' + mcFileFieldCode + '_newFile').val(0);
			arrUploaders.find(function (f) {
				return f.columnID == mcFileColumnID;
			}).fileUploaded = 1;
		});

		uploader.init();
		arrUploaders.push({ uploader:uploader, columnID:mcFileColumnID, fileUploaded:0 });
	});
};
function mc_fillDefaultValue(thisField) {
	var fld = $(thisField);
	var fldName = $(thisField).attr('name');
	var displayTypeCode = fld.data('mcdisplaytypecode');

	switch(displayTypeCode) {
		case 'TEXTBOX':
		case 'DATE':
			if (fld.val().trim() == '') {
				fld.val(fld.data('mcdefvalue'));
			}
		break;
		case 'SELECT':
			if (fld.val() == '__NOVALUE__') {
				fld.val(fld.data('mcdefvalue'));
			}
		break;
		case 'RADIO':
		case 'CHECKBOX':
			if (!$('input[name="' + fldName + '"]').is(':checked') || $('input[name="' + fldName + '"]:checked').val() == '__NOVALUE__') {
				$('input[name="' + fldName + '"][value="' + fld.data('mcdefvalue') + '"]').prop('checked',true);
			}
		break;
	}
}
function confirmDocDelete(cid,srid) {
	if (confirm("Are you sure you wish to delete this document?")) {
		$('#md_' + cid + '_removeDoc').val(1);
		$('#mcfdoc_' + cid).hide();
		$('#div_mcfdoc_remMsg_' + cid).show();
	}
}
function hideMCADAlert() { 
	$('#mccustomtab_frm_err').html('').hide(); 
}
function showMCADAlert(msg) { 
	$('#mccustomtab_frm_err').html(msg).show(); 
}
function fnLoadAdditionalTab(fs) {
	arrCFareas = [];
	arrUploaders = [];
	myFunc = null;

	MCFTABsource = $('#mca_MCFTAB_template').html();
	MCFTABtemplate = Handlebars.compile(MCFTABsource);

	var fslength = fs.length;
	for (var i = 0; i < fslength; i++) { arrCFareas.push({ fsid:fs[i], loaded:0 }); }
	arrCFareas.push({ fsid:0, loaded:0 }); /* fields not in fieldsets section */

	loadCustomTabContent();
}

// documents
function addDocument(memberID) {
	
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Add New Document',
		iframe: true,
		contenturl: mcma_link_adddoc+'&memberID='+memberID+'&mode=direct',
		strmodalfooter : {
			classlist: 'd-flex',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSubmit").click',
			extrabuttonlabel: 'Save',
		}
	});
}
function viewDocument(did,dvid,lang) {
	window.open('/docDownload/'+did+'&VID='+dvid+'&lang='+lang);
}
function editDocument(mdid,mid,did,dvid) {
	
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Edit Document',
		iframe: true,
		contenturl: mcma_link_editdoc+'&_mdid='+mdid+'&memberID='+mid+'&documentID='+did+'&documentVersionID='+dvid+'&mode=direct',
		strmodalfooter : {
			classlist: 'd-flex',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSubmit").click',
			extrabuttonlabel: 'Save',
		}
	});
} 
function closeViewDocument() { reloadDocPage(); closeBox(); }
function removeDocument(mdid,did) {
	var removeDocResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			memberDocumentsListTable.draw();
		} else {
			delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-alt"></i>');
			if(r.errmsg) alert(r.errmsg);
			else alert('We were unable to delete this document. Try again.');
		}
	};
	let delElement = $('#btnDelDoc'+mdid);
	mca_initConfirmButton(delElement, function(){
		var objParams = { memberDocumentID:mdid, documentID:did };
		TS_AJX('ADMINMEMBERS','removeMemberDocument',objParams,removeDocResult,removeDocResult,10000,removeDocResult);
	});
}
function reloadDocPage() { memberDocumentsListTable.draw();}
function editPanel(pmID) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Edit Panel Member Settings',
		iframe: true,
		contenturl: mcma_link_editpanel+'&panelMemberID='+pmID+'&mode=direct',
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSavePanel").click',
			extrabuttonlabel: 'Save',
		}
	});
}
function goToCustom() {
	self.location.href = mcma_link_editmember+'&memberID='+mcma_memberid+'&tab=ad';
}

// dues
function addSub() {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Add Subscription',
		iframe: true,
		contenturl: mcma_link_sub
	});
	$('#frmFilter')[0].reset();
	if (!$('#divFilterForm').is('visible')) {
		$('#divFilterForm').hide();
	}
}
function reloadMemberSubs() { memberSubTable.draw(); memberSubHistoryTable.draw(); }
function editMemberSub(sid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Edit Subscription',
		iframe: true,
		contenturl: mcma_link_sub+'&sid='+sid
	});
}
function renewMemberSub(sid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'md',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Confirm Renewal Generation',
		iframe: true,
		contenturl: mcma_link_confirmrenew+'&sid='+sid+'&mid='+mid+'&chkAll=0&fSubscribers='+ sid +'&fNotSubscribers=""' ,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSubmit").click',
			extrabuttonlabel: 'Generate Renewals',
		}
	});
}
function generatePaperStatement(sid,act) {
	let title = '<span id="topModalTitle">Statement Options</span>';
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: title,
		iframe: true,
		contenturl: mcma_link_generatePaperStatement+'&fChkedSubs='+sid+'&act='+act,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto d-none',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnContinue").click',
			extrabuttonlabel: 'Continue',
		}
	});
}

function markBilledSub(sid)	{
	var markBilledResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') reloadMemberSubs();
		else alert('Unable to Mark As Billed.');
	};
	if (confirm('Are you sure you want to update this subscription to Billed?')) {
		var objParams = { actorMemberID:mcma_actormemberid, memberID:mcma_memberid, subscriberID:sid };
		TS_AJX('ADMSUBS','markBilledMemberSubscription',objParams,markBilledResult,markBilledResult,20000,markBilledResult);
	}
}
function emailOfferSub(sid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Select Email Template',
		iframe: true,
		contenturl: mcma_link_startgenoffers+'&subscriberOnly='+sid,
		strmodalfooter: {
			classlist: 'd-flex justify-content-between',
			showclose: false,
			buttons: [
				{
					class:"btn btn-sm btn-secondary d-none",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnChangeTemplate").click',
					label: 'Change Template', 
					name: 'btnChangeTemplate',
					id: 'btnChangeTemplate'
				},
				{
					class:"btn btn-sm btn-secondary d-none mr-auto",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnTestTemplate").click',
					label: 'Send Test E-mail', 
					name: 'btnTestTemplate',
					id: 'btnTestTemplate'
				},
				{
					class:"btn btn-sm btn-primary ml-auto",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSelectTemplate").click',
					label: 'Select Template', 
					name: 'btnSelectTemplate',
					id: 'btnSelectTemplate'
				},
				{
					class: "btn btn-primary btn-sm ml-auto d-none",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnProcessTemplate").click',
					label: 'Send Emails', 
					name: 'btnProcessTemplate',
					id: 'btnProcessTemplate'
				}
			]
		}
	});
}
function showSubRenewalLink(sid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Subscription Renewal Link and Code',
		iframe: true,
		contenturl: mcma_link_subrenewal+'&sid='+sid
	});
}
function getSubSelections()	{
	var objSubs = new Object();
	objSubs.fNotSubscribers = '';
	objSubs.fSubscribers = document.forms['frmRenew'].fSubscribers.value;
	objSubs.chkAll = 0;
	objSubs.success = true;
	
	return objSubs;
}

function assocCC(sid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		title: 'Associate Pay Method to Subscription',
		contenturl: mcma_link_subassoc+'&sid='+sid,
		iframe: true
	});
}
function acceptSubscription(sid,sDate,eDate) {
	MCModalUtils.showModal({
		verticallycentered: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false,
			},
			size: 'lg',
			title: 'Should Subscribers Receive Email Notifications?',
			iframe: true,
			contenturl: linkAcceptSubscription+'&sid='+sid,
			strmodalbody:{
				classlist:'mcModalBodyCustom'
			},
			strmodalfooter: {
				classlist: '',
				showclose: false,
				buttons: [
					{
						class:"btn btn-sm btn-primary btnSubAction",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSend").click',
						label: 'Send Emails', 
						name: 'btnSend',
						id: 'btnSend'
					},
					{
						class: "btn btn-sm btn-primary btnSubAction",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSkip").click',
						label: 'Skip Emails', 
						name: 'btnSkip',
						id: 'btnSkip'
					}
				]
			}
	});
}
function closeAssocCC() {
	reloadMemberSubs(); 
	MCModalUtils.hideModal();
}
function markInactiveSub(sid) {
	var inactivateResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') reloadMemberSubs(); 
		else alert('Unable to inactivate subscription.');
	};

	if (confirm('Marking this subscription inactive will also inactivate any addon subscriptions that belong to it.\r\nAre you sure you wish to do this?')) {
		var objParams = { actorMemberID:mcma_actormemberid, memberID:mcma_memberid, subscriberID:sid };
		TS_AJX('ADMSUBS','inactivateMemberSubscription',objParams,inactivateResult,inactivateResult,20000,inactivateResult);
	}
}
function markActiveSub(sid)	{
	var activateResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') reloadMemberSubs();
		else alert('Unable to activate subscription.');
	};

	if (confirm('Marking this subscription active will also activate any addon subscriptions that belong to it.\r\nAre you sure you wish to do this?')) {
		var objParams = { actorMemberID:mcma_actormemberid, memberID:mcma_memberid, subscriberID:sid };
		TS_AJX('ADMSUBS','activateMemberSubscription',objParams,activateResult,activateResult,20000,activateResult);
	}
}
function markExpiredSub(sid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Expire Subscription',
		iframe: true,
		contenturl: mcma_link_expiresub+'&sid='+sid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnExpireSub").click',
			extrabuttonlabel: 'Expire Subscription',
		}
	});
}
function removeMemberSub(sid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Remove Subscription',
		iframe: true,
		contenturl: mcma_link_removesub+'&sid='+sid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnRemoveSub").click',
			extrabuttonlabel: 'Remove Subscription',
			extrabuttoniconclass: 'fa-solid fa-trash-can'
		}
	});
}
function cleanupInvoices(sid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Cleanup Invoices For Subscription',
		iframe: true,
		contenturl: mcma_link_cleanupinv+'&sid='+sid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnCleanupSub").click',
			extrabuttonlabel: 'Continue',
		}
	});
}
function markOverrideActivationSub(sid) {
	var activateResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') reloadMemberSubs();
		else alert('Unable to activate subscription.');
	};

	if (confirm('Activating this subscription cannot be undone.\r\nAre you sure you wish to do this?')) {
		var objParams = { actorMemberID:mcma_actormemberid, subscriberID:sid };
		TS_AJX('ADMSUBS','overrideActivationMemberSubscription',objParams,activateResult,activateResult,20000,activateResult);
	}
}
function onChangeSubStatus(checkActivations) {
	if (checkActivations) {
		$('#frmFilter')[0].reset();
		$('#frmFilter select').trigger('change');
		if (!$('#divFilterForm').is('visible')) {
			$('#divFilterForm').hide();
		}
		$('#checkActivations').val(1);
	}else{
		$('#checkActivations').val(0);
	}
	reloadMemberSubs();
}
function clearSubsFilter() {
	$('#frmFilter')[0].reset();
	$('#frmFilter select').trigger('change');
	onChangeSubStatus(false);
}
function toggleSubATRow(rowID) {
	var st = $('#atSubChanges_'+rowID).css("display");
	if (st == 'none') {
		$('#atSubChanges_'+rowID).show();
		$('#atSubTreeImg_'+rowID).attr("src","/assets/common/images/tree-open.jpg");
	} else {
		$('#atSubChanges_'+rowID).hide();
		$('#atSubTreeImg_'+rowID).attr("src","/assets/common/images/tree-closed.jpg");
	}
}
function toggleSubATGrid() {
	var st = $('#mcg_gridboxSubAT').css("display");
	if (st == 'none') {
		$('#mcg_gridboxSubAT').show();
		$('#subAuditResultsShow').hide();
		$('#subAuditResultsHide').show();
	} else {
		$('#mcg_gridboxSubAT').hide();
		$('#subAuditResultsShow').show();
		$('#subAuditResultsHide').hide();
	}
}
function doSelectSubscriber(id) {
	$('#showFullHist tr').removeClass("bg-paleyellow"); 
	$('#showFullHist tr[mc-data-subscriberid='+id+']').addClass("bg-paleyellow");
}
function filterSubscriptionList() {
	$('#frmFilter')[0].reset();
	if (!$('#divFilterForm').is(':visible')) {
		$('#divFilterForm').show();
	}
}
function callChainedSelect(element1,element2,method,varName,elemIdDefault,selBoxDefVal,isMultiselect,jsonlib){
	var strSelected = $("#" + element1).val();
	chainedSelect(
		element2,			/* select box id  */
		elemIdDefault,		/* select box default value */
		selBoxDefVal,		/* select box default text */
		strSelected,		/* value of the select */
		method,				/* method to be run */
		varName,			/* parameter variable*/ 
		isMultiselect,		/* refresh multiselect */
		jsonlib,			/* json library */
		'json'				/* return format */
	);
}
function chainedSelect(elemIdName,elemIdDefault,elemValueDefault,selected,method,varName,isMultiselect,jsonlib,format){
	var strURL = mcma_link_adminhome+'&mode=stream&pg=admin&mca_jsonlib='+jsonlib+'&mca_jsonfunc='+method+'&'+varName+'='+ selected;
	var txtColumnID = 1;
	
	$.ajax({
		url: strURL,
		dataType: 'json',
		success: function(response){
			$('#' + elemIdName).empty();
			var selectOption = false;
			var elemIdDefaultArr = new Array();
			var tempString = elemIdDefault.toString();
			elemIdDefaultArr = tempString.split(',');					
			 for (var i = 0; i < response.DATA.length; i++) {
				var o = new Option(response.DATA[i][txtColumnID], response.DATA[i][0]);
				/* jquerify the DOM object 'o' so we can use the html method */
				$(o).html(response.DATA[i][txtColumnID]);
				$('#' + elemIdName).append(o);
				
				for (var subStr in elemIdDefaultArr ) {
					 elemIdDefaultArr[subStr] = parseInt(elemIdDefaultArr[subStr], 10);
					if (elemIdDefaultArr[subStr] > 0 && response.DATA[i][0] == elemIdDefaultArr[subStr]) {
						selectOption = true;
						break;
					}					
				}						
			 }
			 if (selectOption){						 	
				for (var subStr in elemIdDefaultArr ) {
					 elemIdDefaultArr[subStr] = parseInt(elemIdDefaultArr[subStr], 10);
					 $('#' + elemIdName + " option[value='" + elemIdDefaultArr[subStr] + "']").prop('selected',true);			
				}								
			 }
		},
		error: function(ErrorMsg){
			/* alert(ErrorMsg); */
		}
	})
}	

// events
function initMemberEventsTable(){
	let domString = "<'row'<'col-sm-12 col-md-5'<'row'<'col-auto'l><'col-sm-12 col-md pl-md-0'i>>><'col-sm-12 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
	evRegistrantsTable = $('#evRegistrantsTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 50,
		"lengthMenu": [ 10, 25, 50, 100 ],
		"dom": domString,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": mcma_link_regeventslist,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if (data.status == "D") renderData += '<div><span class="badge badge-danger">Deleted</span></div>';
						renderData += '<div><a href="'+data.editeventlink+'">'+data.eventtitle+'</a>';
						if(data.isflagged) renderData += '<i class="fa-solid fa-flag fa-sm text-danger ml-2"></i>';
						renderData += '</div><div class="small text-dim">'+data.eventdate+'</div>';
						if (data.eventsubtitle.length) renderData += '<div class="small text-dim">'+data.eventsubtitle+'</div>';
						if (data.regroles.length) renderData += '<div class="small text-dim">'+data.regroles+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "40%",
				'className': 'align-top'
			},
			{
				"data": null, 
				"render": function (data, type) {
					return type === 'display' ? '<span title="'+data.registereddatetime+'">'+data.registereddate+'</span>' : data;
				},
				"width": "10%",
				'className': 'align-top'
			},
			{ "data": "attendedinfo", "width": "10%", 'className': 'align-top', "orderable": false },
			{ "data": "totalregfeedisplay", "width": "10%", 'className': 'align-top', "orderable": false },
			{ "data": "amountduedisplay", "width": "10%", 'className': 'align-top', "orderable": false },
			{ 
				"data": null,
				"render": function (data, type, row, meta) {
					let renderData = '';
					if (type === 'display') {
						let arrGridAction = [];
						
						if (data.canedit) {
							arrGridAction = [
								{ title:"Pay for Registration", btnClass:"btn-outline-green", iconClass: "fa-money-bill", isVisible:data.amountdue > 0, onClickFnCall:'mca_addPayment(\''+data.addpaymentencstring+'\')' },
								{ title:"Edit This Registrant's Credit", btnClass:"btn-outline-warning", iconClass: "fa-file-certificate", isVisible:true, onClickFnCall:'manageAC('+data.eventid+','+data.registrantid+')' },
								{ title:"View This Registrant's Certificates", btnClass:"btn-outline-warning", iconClass: "fa-certificate", isVisible:(data.showcert || data.showcert2), onClickFnCall:'viewCertificate('+data.eventid+','+data.registrantid+')' },
								{ title:"Print This Registration", btnClass:"btn-outline-dark", iconClass: "fa-print", isVisible:true, onClickFnCall:'printRegistrant('+data.registrantid+','+data.memberid+','+data.eventid+','+data.calendarid+')' },
								{ title:"Resend Email Confirmation", btnClass:"btn-outline-primary", iconClass: "fa-envelope", isVisible:true, onClickFnCall:'sendConfirmation('+data.calendarid+','+data.eventid+','+data.registrantid+','+data.memberid+')' },
								{ title:"Email Materials", btnClass:"btn-outline-primary", iconClass: "fa-envelope-open-text", isVisible:data.haseventdocs, onClickFnCall:'emailRegistrantMaterials('+data.calendarid+','+data.eventid+','+data.registrantid+','+data.memberid+')' },
								{ title:"Edit This Registration", btnClass:"btn-outline-primary", iconClass: "fa-pencil", isVisible:true, onClickFnCall:'editRegistrant('+data.calendarid+','+data.eventid+','+data.registrantid+','+data.memberid+')' },
								{ title:"Print This Registrant's Badge", btnClass:"btn-outline-dark", iconClass: "fa-badge", isVisible:true, onClickFnCall:'printBadgeRegistrant('+data.registrantid+','+data.eventid+')' },
								{ title:"Remove This Registration", btnClass:"btn-outline-danger", iconClass: "fa-circle-minus", isVisible:true, onClickFnCall:'removeRegistrant('+data.registrantid+')' }
							];
							if (!data.badgeprinterenabled || !data.hasbadgedevices || !data.hasbadgetemplates ) {
								arrGridAction.splice(7, 1);
							}
							// delete email material action
							if (!data.showemailmaterials) {
								arrGridAction.splice(5, 1);
							}
						}
						if (data.cancleanupinvoices) {
							arrGridAction.push({ title:"Cleanup Invoices", btnClass:"btn-outline-danger", iconClass: "fa-file-invoice-dollar", isVisible:true, onClickFnCall:'cleanupRegInvoices('+data.registrantid+')' });
						}
						
						$.each(arrGridAction, function(index, item) {
							renderData += '<a href="#" class="btn btn-xs '+(item.isVisible ? item.btnClass : '')+' px-1 m-1'+ (!item.isVisible ? ' text-muted disabled' : '') +'" '+ (item.isVisible ? 'onclick="'+item.onClickFnCall+';return false;" title="'+item.title+'"' : '') +'><i class="fa-solid '+item.iconClass+'"></i></a>';
						});
					}
					return type === 'display' ? renderData : data;
				},
				"width": "20%",
				"className": "text-center align-top",
				"orderable": false
			}
		],
		"order": [[1, 'desc']],
		"searching": false
	});
}
function reloadEventGrid() { evRegistrantsTable.draw(); }
function editEvent(eID) {
	self.location.href = mcma_link_editev+'&bc=e&eID='+eID;
}
function printRegistrant(rid,mid,eid,cid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Print Registrant',
		iframe: true,
		contenturl: mcma_link_printreg+'&cID='+ cid + '&eID='+ eid +'&mid=' + mid + '&registrantID=' + rid
	});
}
function printBadgeRegistrant(rid,eid){
	MCModalUtils.showModal({
			isslideout: true,
			size: 'lg',
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			title: 'Print Registrant Badge',
			iframe: true,
			contenturl: mcma_link_printbadgereg+'&eid=' + eid + '&registrantID=' + rid,
			strmodalfooter: {
				classlist: 'd-none'
			}
		});

}
function sendConfirmation(cid,eid,rid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Resend Registration Confirmation',
		iframe: true,
		contenturl: mcma_link_sendevconfirm+'&cID='+cid+'&eID='+eid+'&mid='+mid+'&registrantID='+rid,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmSendConf :submit").click',
			extrabuttonlabel: 'Resend Confirmation',
			extrabuttoniconclass: 'fa-light fa-share'
		}
	});
}
function emailRegistrantMaterials(cid,eid,rid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Email Registrant Materials',
		iframe: true,
		contenturl: mcma_link_sendevmaterials+'&cID='+cid+'&eID='+eid+'&mid='+mid+'&registrantID='+rid,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmSendMaterials :submit").click',
			extrabuttonlabel: 'Send Materials'
		}
	});
}
function removeRegistrant(id) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Remove Registrant Confirmation',
		iframe: true,
		contenturl: mcma_link_removereg+'&registrantID='+id,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.doCallRemoveReg',
			extrabuttonlabel: 'Remove Registrant'
		}
	});
}
function doRemoveReg(objParams) {
	var removeData = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			reloadEventGrid();
			if(r.showrefund && r.showrefund.toString() == 'true'){
				showRefundPaymentOnRegRemoveSuccess(objParams.registrantMemberID);
			} else{
				MCModalUtils.hideModal();
			}
		} else {
			alert('Unable to Remove Registrant - We were unable to remove this registrant. Contact MemberCentral for assistance.');
		}
	};
	
	TS_AJX('ADMINEVENT','removeRegistrant',objParams,removeData,removeData,10000,removeData);
}
function cleanupRegInvoices(rid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Cleanup Invoices For Registrant',
		iframe: true,
		contenturl: mcma_link_cleanupinvreg+'&rid='+rid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmRegInvCleanup :submit").click',
			extrabuttonlabel: 'Continue',
		}
	});
}
function editRegistrant(cid,eid,rid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Edit Registrant',
		iframe: true,
		contenturl: mcma_link_addreg+'&cID='+cid+'&eID='+eid+'&mid='+mid+'&registrantID='+rid,
		strmodalfooter : {
			classlist: 'd-none'
		}
	});
}
function returnToRegistration(cid,eid,rid,mid){
	MCModalUtils.hideModal();
	$('.modal-backdrop').remove();
	editRegistrant(cid,eid,rid,mid);	
}
function closeUpdateRegistration() { 
	reloadEventGrid();
	MCModalUtils.hideModal();
}
function manageAC(eid,rid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Manage Attendance and Credits',
		iframe: true,
		contenturl: mcma_link_mngattcredit+'&eid='+eid+(rid?'&_rid='+rid:'')
	});
}
function viewCertificate(eid,rid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Registrant Certificate',
		iframe: true,
		contenturl: mcma_link_viewcert+'&eid='+eid+'&rid='+rid
	});
}
function registerForEvent() {
	if ($('#divFilterForm').is(':visible')) {
		$('#divFilterForm').hide();
	}
	var mc_membername = $('#mc_membername').text();

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Select Event for ' + mc_membername.substr(0,30) + (mc_membername.length > 30 ? '...' : ''),
		iframe: true,
		contenturl: mcma_link_selev+'&mid='+mcma_memberid,
		strmodalfooter : {
			classlist: 'd-none'
		}
	});
}
function doRegisterForEvent(c,e) {
	MCModalUtils.hideModal();
	$('.modal-backdrop').remove();
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Edit Registrant',
		iframe: true,
		contenturl: mcma_link_addreg+'&cID='+c+'&eID='+e+'&regaction=usemid&mid='+mcma_memberid,
		strmodalfooter : {
			classlist: 'd-none'
		}
	});
}
function dofilterEV() {    
    reloadEventGrid();
}
function clearFilterEV() {
	$('#frmFilter')[0].reset();
	dofilterEV();
}
function showRegistrationFilters() {
	if (!$('#divFilterForm').is(':visible')) {
		$('#divFilterForm').show();
	}
}

// invoices
function allocPayments(po) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Allocate Payments',
		iframe: true,
		contenturl: mca_link_addpmt+'&pa=selcreditpay&po='+escape(po)
	});
}
function manageCards(mid) {
	if (Number(mcma_hasrights_payprofilemanage) == 1)
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Manage Pay Methods',
			iframe: true,
			contenturl: mcma_link_managecards+'&mid='+mid
		});
}
function refundPayment(mid,ptid) {
	if (Number(mcma_hasrights_refundpmt) == 1) {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Issue Refund',
			iframe: true,
			contenturl: mcma_link_refpayment+'&mid=' + mid + (typeof ptid == 'undefined' ? '' : '&ptid=' + ptid)
		});
	}
}
function closeRefundPayment() { reloadTransTab(); MCModalUtils.hideModal(); }
function closeWriteOffTransaction() { reloadTransTab(); MCModalUtils.hideModal(); }
function closeChargebackTransaction() { reloadTransTab(); MCModalUtils.hideModal(); }
function addSale() {
	if (Number(mcma_hasrights_transaddsale) == 1) {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Add Sale',
			iframe: true,
			contenturl: mcma_link_addsale+'&mid='+mcma_memberid
		});
	}
}
function closeAddSale() { reloadTransTab(); MCModalUtils.hideModal(); }
function viewTransactionInfo(tid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'View Transaction',
		iframe: true,
		contenturl: mcma_link_viewtransinfo+'&tid='+tid
	});
}
function viewInvoiceInfo(vid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Invoice ' + $('#invoiceRow_'+vid).data('invoiceNumber'),
		iframe: true,
		contenturl: mcma_link_viewinvinfo+'&vid='+vid
	});
}
function closeMoveIT(vid) {
	reloadTransTab(); 
	if (typeof vid == 'undefined') MCModalUtils.hideModal();
}
function adjustTransaction(tid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Adjust Sale',
		iframe: true,
		contenturl: mcma_link_adjtrans+'&tid='+tid
	});
}
function closeAdjustTransaction() { 
	reloadTransTab(); 
	MCModalUtils.hideModal();
}
function closeVoidTransaction(vid) { 
	reloadTransTab(); 
	if (typeof vid == 'undefined') MCModalUtils.hideModal();
}
function closeReclassTransaction() { 
	reloadTransTab(); 
	MCModalUtils.hideModal();
}
function editTransaction(mid,tid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Accept Pending Payment',
		iframe: true,
		contenturl: mcma_link_edittrans+'&mid='+mid+ '&tid='+tid
	});
}
function closeEditTransaction(vid) { 
	reloadTransTab(); 
	if (typeof vid == 'undefined' || vid == 0) MCModalUtils.hideModal();
}
function editInvoice(vid,mid) { 
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: vid > 0 ? 'Edit Invoice' : 'Create Invoice',
		iframe: true,
		contenturl: mcma_link_editinv+'&vid='+vid+'&mid='+mid,
		strmodalfooter : {
			classlist: 'd-flex justify-content-between',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmInvoice :submit").click',
			extrabuttonlabel: 'Save Invoice'
		}
	});
}
function doneEditInvoice() { reloadTransTab(); }
function reloadTransTab() { checkUnAlloc(); checkOutstanding(); checkOutstandingInv(); filterMemInvoiceGrid(); filterTransactions(); }
function checkUnAlloc() {  
	var checkResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true')
			showUnAllocAmt(r.unallocatedamount,r.isdueinvoice);
		else 
			showUnAllocAmt(0,0);
	};
	var objParams = { memberid:mcma_memberid };
	TS_AJX('ADMINMEMBERS','getMemberCreditAndOutstandingAmount',objParams,checkResult,checkResult,5000,checkResult);
}
function checkOutstanding() { 
	var checkResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') { 
			if (r.outstandingamount && parseFloat(r.outstandingamount) > 0) showOutstandAmt(r.outstandingamount);
			else showOutstandAmt(0);
		} else showOutstandAmt(0);
	};
	var objParams = { memberid:mcma_memberid };
	TS_AJX('ADMINMEMBERS','getMemberOutstandingAmount',objParams,checkResult,checkResult,5000,checkResult);
}
function checkOutstandingInv() { 
	var checkResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') { 
			if (r.outstandingamount && parseFloat(r.outstandingamount) > 0) showOutstandInvAmt(r.outstandingamount);
			else showOutstandInvAmt(0);
		} else showOutstandInvAmt(0);
	};
	var objParams = { memberid:mcma_memberid };
	TS_AJX('ADMINMEMBERS','getMemberOutstandingInvAmount',objParams,checkResult,checkResult,5000,checkResult);
}
function showUnAllocAmt(a,isdue) {
	if (a > 0) {
		$('#unallocSPAN').html('<a href="javascript:viewCBDtl();"><span class="text-success font-weight-bold">$' + formatCurrency(a) + '</span>&nbsp;in unallocated payments</a>');
		
		if (Number(mcma_hasrights_refundpmt) == 1) {
			$('#refDIV').html('<a href="javascript:refundPayment('+mcma_memberid+');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Refund this member\'s unallocated payments."><i class="fa-regular fa-circle-dollar-to-slot"></i> Issue Refund</a>');
		}
		if (Number(mcma_hasrights_allocpmt) == 1) {
			if(isdue > 0)
				$('#allocDIV').html('<a href="javascript:allocPayments(\''+mca_link_allocpmtencstring+'\');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Allocate this member\'s payments."><i class="fa-regular fa-money-bill-1-wave"></i> Allocate Payments</a>');
			else 
				$('#allocDIV').html('<span data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="No receivables to allocate." class="dim"><i class="fa-regular fa-money-bill-1-wave"></i> Allocate Payments</span>');

		}
	} else {
		$('#unallocSPAN').html('<span class="text-muted">$0.00 in unallocated payments</span>');
		if (Number(mcma_hasrights_refundpmt) == 1) {
			$('#refDIV').html('<span data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="No unallocated payments to refund." class="dim"><i class="fa-regular fa-circle-dollar-to-slot"></i> Issue Refund</span>');
		}
		if (Number(mcma_hasrights_allocpmt) == 1) {
			$('#allocDIV').html('<span data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="No unallocated payments to allocate." class="dim"><i class="fa-regular fa-money-bill-1-wave"></i> Allocate Payments</span>');
		}
	}
	mcActivateTooltip($('#transactions'));
}
function showOutstandAmt(a) {
	if (a > 0) $('#outstandSPAN').html('<a href="javascript:showMemberDueAmts();"><span class="text-success font-weight-bold">$' + formatCurrency(a) + '</span>&nbsp;in receivables;</a>'); 
	else $('#outstandSPAN').html('<span class="text-muted">$0.00 in receivables;</span>');
}
function showOutstandInvAmt(a) {
	if (a > 0) $('#outstandInvSPAN').html('<span class="text-success font-weight-bold">$' + formatCurrency(a) + '</span>&nbsp;in receivables on invoices assigned to member');
	else $('#outstandInvSPAN').html('<span class="text-muted">$0.00 in receivables on invoices assigned to member</span>');
}
function viewCBDtl() {
	$('#frmFilter')[0].reset();
	$('#tran_fAR').val(0);
	$('#tran_fCB').val(1);
	transactionListTable.draw();
}
function filterTransactions() {
	$('#tran_fAR,#tran_fCB').val(0);
	transactionListTable.draw();
}		
function formatAmtFilter(a) {
	if (a.val() != '') a.val(formatCurrency(a.val()));
}
function showMemberDueAmts() {
	$('#frmFilter')[0].reset();
	$('#tran_fCB').val(0);
	$('#tran_fAR').val(1);
	transactionListTable.draw();
}
function clearFilterTransactions() {
	$('#frmFilter')[0].reset();
	filterTransactions();
}
function initInvoicesTable(){
	let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

	invoiceListTable = $('#invoiceList').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"dom": domString,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": invoiceListLink,
			"type": "post",
			"data": function(d) {
				$.each($('#frmInvFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						var title='Due: '+ data.dateDue + ', Billed: '+ data.dateBilled +', Created: '+data.dateCreated;
						renderData += '<div title="'+title+'">'+data.dateDue+'</div>';
						renderData += '<div class="text-dim">'+data.invoiceStatus+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%"
			},
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						var inPaymentQueue = "";
						if(data.inPaymentQueue) inPaymentQueue = " (Queued)";

						if(data.hasCard)
							renderData += '<span class="text-warning float-right ml-1" title="Pay Method Associated"><i class="fa-solid fa-credit-card"></i></span>';
						renderData += '<div>'+data.invoiceNumber+inPaymentQueue+'</div>';
						renderData += '<div class="text-dim">'+data.invoiceProfile+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "15%"
			},
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div><a href="javascript:editMember('+data.assignedTomemberid+')" title="View Member Record for '+data.membernameEnc+'">'+data.membername+'</a></div>';
						renderData += '<div class="text-dim small">'+data.company+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				'className': 'align-top',
				"width": "45%"
			},			
			{ "data": "InvAmt", 'className': 'text-right align-top', "width": "10%", "orderable": false },
			{ "data": "InvDue", 'className': 'text-right align-top', "width": "10%", "orderable": false },
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if(data.inPaymentQueue) 
							var title = "This invoice is currently queued for payment. Click to view invoice details.";
						else 
							var title = 'View invoice detail for '+data.invoiceNumber;

						renderData += '<a href="javascript:viewInvoiceInfo('+data.invoiceID+')" title="'+title+'" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-pencil"></i></a>'
						if($.trim(data.addPaymentEncString).length){
							renderData += '<a href="javascript:mca_addPayment(\''+data.addPaymentEncString+'\');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Pay Invoice"><i class="fa-solid fa-money-bill-alt text-green"></i></a>'
						}else{
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-money-bill-alt"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "text-center align-top",
				"orderable": false
			}
		],
		"order": [[0, 'desc']],
		"searching": false
	});
}
function initTransactionsTable(){
	let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

	transactionListTable = $('#transactionList').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"dom": domString,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": transactionListLink,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div title="'+data.transactionDate + ' ' + data.transactionTime+'">'+data.transactionDate+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%"
			},
			{ "data": "type", "width": "10%", "orderable": false },
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div class="d-flex"><span>'+data.detail+'</span>';
						if (data.statusID == 3) {
							renderData += '<span class="badge badge-warning ml-auto">Pending</span>';
						} else if (data.statusID == 2) {
							renderData += '<span class="badge badge-danger ml-auto">Voided</span>';
						}
						renderData += '</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "40%",
				"orderable": false
			},
			{ "data": "debit", 'className': 'text-right', "width": "10%", "orderable": false },
			{ "data": "credit", 'className': 'text-right', "width": "10%", "orderable": false },
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" title="View detail for '+data.detailEnc+'" onclick="viewTransactionInfo('+data.transactionID+');return false;"><i class="fa-solid fa-eye"></i></a>'
						if (data.showPaymentAction) {
							if(data.canApplyPayment && $.trim(data.addPaymentEncString).length){
								renderData += '<a href="javascript:mca_addPayment(\''+data.addPaymentEncString+'\');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Pay for '+data.detailEnc+'"><i class="fa-solid fa-money-bill-alt text-green"></i></a>'
							}else{
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-money-bill-alt"></i></a>';
							}
						}
						if (data.showRefundAction) {
							if(data.canRefund) {
								renderData += '<a href="javascript:refundPayment(' + mcma_memberid + ',' + data.transactionID + ');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Refund Payment"><i class="fa-solid fa-money-bill-alt text-darkred"></i></a>'
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-money-bill-alt"></i></a>';
							}
						}
						if (data.showEditAction) {
							if(data.canEdit) {
								renderData += '<a href="javascript:editTransaction(' + mcma_memberid + ',' + data.transactionID + ');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Accept Pending Payment"><i class="fa-solid fa-money-bill-alt text-green"></i></a>'
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-money-bill-alt"></i></a>';
							}
						}
						if (data.showAdjustAction) {
							if(data.canAdjust) {
								renderData += '<a href="javascript:adjustTransaction(' + data.transactionID + ');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Adjust Revenue"><i class="fa-solid fa-money-bill-wave-alt text-green"></i></a>'
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-money-bill-alt"></i></a>';
							}
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "20%",
				"className": "text-center",
				"orderable": false
			}
		],
		"order": [[0, 'desc']],
		"searching": false
	});
}
function initFailedPaymentListTable(){
	failedPaymentListTable = $('#failedPaymentList').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 5,
		"lengthMenu": [5, 10, 25, 50 ],
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": failedPaymentListLink,
			"type": "post"
		},
		"autoWidth": false,
		"columns": [
			{ "data": "datePaid", "width": "15%", "className": "align-top" },
			{ "data": "type", "width": "10%", "className": "align-top", "orderable": false },
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						var cofDetail = "";
						if($.trim(data.cofDetail).length){
							cofDetail = '<div class="pt-1">' + data.cofDetail + '</div>';
						}
						renderData += data.payDescription + '<div class="pt-1 text-dim small">' + data.profileName + '<br/>' + cofDetail + '</div><div class="pt-1 text-dim small"><i>' + data.responseReasonText +'</i></div>';
					}
					return type === 'display' ? renderData : data;
				},
				"orderable": false, 
				"className": "align-top",
				"width": "65%"
			},			
			{ "data": "payAmount", "width": "10%", "className": "text-right align-top", "orderable": false }
		],
		"order": [[0, 'desc']],
		"searching": true
	});
}
function filterAssociatedInvoices() {
	if (!$('#divFilterForm').is(':visible')) {
		$('#divFilterForm').show();
	}
}
function filterIndividualTransactions() {
	if (!$('#tran_filterbox').is(':visible')) {
		$('#tran_filterbox').show();
	}
}
function clearFilterInvoices() {
	$('#frmInvFilter')[0].reset();
	$('#frmInvFilter [data-toggle="custom-select2"]').trigger('change');
	filterMemInvoiceGrid();
}
function importAuthorizeNetPayment(mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Import Authorize.Net Payment',
		iframe: true,
		contenturl: mcma_link_impauthorizepmt+'&mid='+mid
	});
}
function loadFailedPayments() {
	$('#btnShowFailedPayments').remove();
	$('#failedPaymentListTable').removeClass('d-none');
	initFailedPaymentListTable();
}
function prepInvoicesFilterForm() {
	mca_setupDatePickerRangeFields('duedateStart','duedateEnd');
	mca_setupDatePickerRangeFields('billeddateStart','billeddateEnd');
	mca_setupSelect2();
}
function filterMemInvoiceGrid() {
	if (!checkMemInvoiceFilterForm()) return false;

	invoiceListTable.draw();
}
function checkMemInvoiceFilterForm() {
	var das = $('#dueAmtStart');
	var dae = $('#dueAmtEnd');
	if (das.val().length > 0) das.val(formatCurrency(das.val()));
	if (dae.val().length > 0) dae.val(formatCurrency(dae.val()));
	var la = formatCurrency(das.val()).replace(/\,/g,'');
	var ha = formatCurrency(dae.val()).replace(/\,/g,'');
	if (parseFloat(la) > parseFloat(ha)) {
		alert('The low due amount must be lower than the high due amount if using Amount Due to filter data.');
		return false;
	}
	return true;
}

// lists
function validateListEmail() {
	var validateResult = function(r) {
		$('#emailText').html('');
		$("select#list option").removeAttr('disabled');
		if (r.success && r.success.toLowerCase() == 'true'){
			if (r.arrcurrlists.length) {
				for(var i=0; i<r.arrcurrlists.length; i++) {
					$("select#list option[value='"+ r.arrcurrlists[i] + "']").attr("disabled","disabled");
				}
			}
			/*re-build select2 again for toggling disabled options*/
			mca_setupSelect2($('#divListsAddForm'));
		} else {
			alert('We were unable to validate this email address.');
		}
	};

	var email = $('#list_emailaddr').val();

	if (!_CF_checkEmail(email, false) || email.length == 0) {
		$('#emailImg').html('<i class="fa-solid fa-circle-exclamation text-danger"></i>');
		$('#emailText').html('Email is either blank or not valid');
		return false;
	} else {
		$('#emailText').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> <b>Validating and checking lists</b>');
		$('#emailImg').html('');

		var objParams = { externalMemberID:mcma_extmemberid, email:email };
		TS_AJX('ADMINMEMBERS','getEmailCurrentLists',objParams,validateResult,validateResult,10000,validateResult);
	}
}
function validateName(thisObj) {
	var name = $(thisObj).val();
	var nameRegex = /^([a-zA-Z\(\"\&]{1,})+([a-zA-Z0-9\,\"\|\/\(\)\'\.\-\&\s]{0,1})+([a-zA-Z\)\.\"\&]{1,})$/;
	var strLength =  $.trim(name).length;
	var strErr = '';

	if (strLength == 0)
		strErr += 'Enter a name.';
	else if (!nameRegex.test($.trim(name)))
		strErr += 'Name contains a character not allowed.';

	if(strErr.length) {
		$(thisObj).closest('.form-group').find("span[id*='nameBox']").children("span[id*='nameImg']").html('<i class="fa-solid fa-circle-exclamation text-danger"></i>');
		$(thisObj).closest('.form-group').find("span[id*='nameBox']").children("span[id*='nameText']").html(strErr);
		return false;
	} else {
		$(thisObj).closest('.form-group').find("span[id*='nameBox']").children("span[id*='nameText']").html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> <b>Validating and checking lists</b>');
		$(thisObj).closest('.form-group').find("span[id*='nameBox']").children("span[id*='nameImg']").html('<i class="fa-solid fa-circle-exclamation text-danger"></i>');
	}
	$(thisObj).closest('.form-group').find("span[id*='nameBox']").children("span[id*='nameText']").html('');
	return true;
}
function saveLists() {
	var exceptionOccurred = false;
	
	$('span#saveListsResponse').removeClass('text-green').addClass('text-danger');
	$('.saveResultMessageArea').hide();
	dimSaveListsBtn();

	$.each(mcma_arrlist, function(index,mid){ 
		var emailValue = $("#list_emailaddr_"+mid+"_").val();
		var listValue = $("#list_list_"+mid+"_").val();
		var em_name = 'emailText_'+mid;
		var eBox_name = 'emailBox_'+mid;
		var eimg_name = 'emailImg_'+mid;
		var nameValue = $("#list_fullname_"+mid+"_").val();
		var nm_name = 'nameText_'+mid;
		var nBox_name = 'nameBox_'+mid;
		var nimg_name = 'nameImg_'+mid;
		var listEmailHasProblems = doesListEmailHaveProblems(emailValue,listValue,em_name,eBox_name,eimg_name);
		var listNameHasProblems = doesListNameHaveProblems(nameValue,listValue,nm_name,nBox_name,nimg_name);

		if (listEmailHasProblems) {
			$('#saveListsResponse').html('Email "'+emailValue+'" for List "'+listValue+'" is either blank or not valid');
			exceptionOccurred = true;
			return false;
		}else if(listNameHasProblems){
			$('#saveListsResponse').html('Name "'+nameValue+'" for List "'+listValue+'" is invalid');
			exceptionOccurred = true;
			return false;
		}
	});
	
	if (exceptionOccurred === false) {
		var emailValue = $("#list_emailaddr").val();
		var nameValue = $("#list_fullname").val();				
		var listValue = $("#list").val() || '';

		if (listValue.length) {
			listValue = listValue.join(',');

			var listEmailHasProblems = doesListEmailHaveProblems(emailValue,listValue,'emailText','emailBox','emailImg');
			var listNameHasProblems = doesListNameHaveProblems(nameValue,listValue,'nameText','nameBox','nameImg');
			if (listEmailHasProblems === true) {
				$('#saveListsResponse').html('Email "'+emailValue+'" for List "'+listValue+'" is either blank or not valid.');
				exceptionOccurred = true;
			}else if (listNameHasProblems === true) {
				$('#saveListsResponse').html('Name "'+nameValue+'" for List "'+listValue+'" is invalid.');
				exceptionOccurred = true;
			}
		} else if ($('#divListsAddForm').is(':visible')) {
			$('#saveListsResponse').html('Select atleast one list.');
			exceptionOccurred = true;
		}
	}

	if ($('#divListsClaimForm').is(':visible')) {
		var claim_addr = $('#claim_emailaddr').val().trim();
		if (!_CF_checkEmail(claim_addr, false) || claim_addr.length === 0) {
			$('#claimEmailAddrErr').html('<i class="fa-solid fa-exclamation-triangle"></i> Claim Email address is either blank or not valid.');
			exceptionOccurred = true;
		}
	}
		
	resetSaveListsBtn();
	
	if (exceptionOccurred === false) {
		$('#divlistForm').hide();
		$('#listRefreshingScreen').show();
		
		var arrFrmData = $('#frmMemberLists').serializeArray();

		var fd = new Object();
		
		$.each(arrFrmData, function() {
			if (fd[this.name] !== undefined) {
				if (!fd[this.name].push) {
					fd[this.name] = [fd[this.name]];
				}
				fd[this.name].push(this.value || '');
			} else {
				fd[this.name] = this.value || '';
			}
		});

		var saveListsResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				listscallback(r);
			} else {
				alert('We were unable to save list settings.');
			}
			$('#divlistForm').show();
			$('#listRefreshingScreen').hide();
		};

		var objParams = { fd:JSON.stringify(fd) };
		TS_AJX('ADMINMEMBERS','saveLists',objParams,saveListsResult,saveListsResult,60000,saveListsResult);
	} else {
		$('html, body').animate({
			scrollTop: $('#saveListsBtn').offset().top - 150
		}, 750);
	}
}
function loadListChangeHistory(at) {
	$('div#div_mf_list_chghist').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Loading history...</div>').load(
		mcma_link_listchangehistory+'&at='+at+'&memberID='+mcma_memberid+'&mode=stream', 
		function(response, status, xhr) { 
			if (status == 'error') {
				$(this).html('Unable to load list change history for this member.');
			}
		}
	);
}
function toggleListATGrid() {
	$('#listATGridContainer').toggleClass('d-none');
	$('#listAuditResultsShow').toggleClass('d-none');
	$('#listAuditResultsHide').toggleClass('d-none');
}
function copyName(thisObj){
	var nameObj = $(thisObj).closest('.form-group').find("input[name*='list_fullname']");

	if(validateName(nameObj) && $.trim(nameObj.val()).length){
		$.each( $("input[name*='list_fullname']"), function( key, value ) {
			if($(value).attr("name").split('_').length == 4){
				var lockNameId='MCOption_lockName_'+$(value).attr("name").split('_')[2];
				var NameId='list_fullname_'+$(value).attr("name").split('_')[2]+'_';
			}else{
				var lockNameId='MCOption_lockName';
				var NameId='list_fullname';
			}
			if(!parseInt($("select[name="+lockNameId+"]").val())){				
				$("input[name="+NameId+"]").val($.trim(nameObj.val()));
				$("span[id*='nameText']").html('');
				$("span[id*='nameImg']").html('');
			}
		});
	}
}
function doesListEmailHaveProblems(email, list, em_name, eBox_name, eimg_name) {
	var em = document.getElementById(em_name);
	var eBox = document.getElementById(eBox_name);
	var eimg = document.getElementById(eimg_name);
	var re = /[^a-zA-Z0-9\-_]/;
	var emailRegEx = new RegExp("^[a-zA-Z_0-9-'\&\+~]+(\.[a-zA-Z_0-9-'\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$", "gi");
	var strLength =  $.trim(email).length;
	var thisForm = document.forms["frmMemberLists"];
	
	if( list != '' && list !== undefined) {
		if (!_CF_checkEmail(email, false) || strLength === 0) {
			eimg.html = '<i class="fa-solid fa-circle-exclamation text-danger"></i>';
			em.innerHTML = 'Email is either blank or not valid';
			eBox.className = 'text-danger';
			return true;
		}else if(!(emailRegEx.test(email))){
			eimg.html = '<i class="fa-solid fa-circle-exclamation text-danger"></i>';
			em.innerHTML = 'Email is either blank or not valid';
			eBox.className = 'text-danger';
			return true;
		}else {
			eimg.html = '';
			em.innerHTML = '';
			eBox.className = '';
			return false;
		}
	} else {
		eimg.html = '';
		em.innerHTML = '';
		eBox.className = '';
		return false;
	} 
}
function doesListNameHaveProblems(nameValue, list, nm_name, nBox_name, nimg_name) {
	var nm = document.getElementById(nm_name);
	var nBox = document.getElementById(nBox_name);
	var nimg = document.getElementById(nimg_name);
	var nameRegex = /^([a-zA-Z\(\"\&]{1,})+([a-zA-Z0-9\,\"\|\/\(\)\'\.\-\&\s]{0,1})+([a-zA-Z\)\.\"\&]{1,})$/;
	var strLength =  $.trim(nameValue).length;
	var thisForm = document.forms["frmMemberLists"];
	var strErr = '';

	if( list != '' && list !== undefined){
		if (strLength == 0)
			strErr += 'Enter a name.';
		else if (!nameRegex.test($.trim(nameValue)))
			strErr += 'Name contains a character not allowed.';

		if(strErr.length) {
			nimg.html = '<i class="fa-solid fa-circle-exclamation text-danger"></i>';
			nm.innerHTML = strErr;
			nBox.className = 'text-danger';
			return true;
		} else {
			nimg.html = '';
			nm.innerHTML = '';
			nBox.className = '';
			return false;
		}
	} else {
		eimg.html = '';
		em.innerHTML = '';
		eBox.className = '';
		return false;
	} 
}
dimSaveListsBtn = function() {
	$('#saveListsResponse,.listInputResponses').html('');
	$('.btnSaveLists').html('Saving..').attr('disabled',true);
};
resetSaveListsBtn = function() {
	$('.btnSaveLists').html('Save Settings').attr('disabled',false);
};
listscallback = function(responseObj) {
	var preventRefresh = false;
	var triggerRefresh = false;
	var successMessage = "";
	var confirmTitle = "";
		
	if (responseObj.savesuccess == '1') { 
		$('span#saveListsResponse').removeClass('text-danger').addClass('text-green');
		$('span#saveListsResponse').html('List settings have been saved.');
	} else { 
		$('span#saveListsResponse').html('List settings could not be saved.');
		preventRefresh = true;
	}

	for (var i=0;i<responseObj.resultarray.length;i++) {					
		if (!responseObj.resultarray[i].success){
			preventRefresh = true;
			$('#messageHolder_' + responseObj.resultarray[i].listmemberid).show();
			if (!responseObj.resultarray[i].email.length)
				$('#message_' + responseObj.resultarray[i].listmemberid).html('No email address supplied!');
			else if (responseObj.resultarray[i].matchingfirstname !== undefined && responseObj.resultarray[i].matchingfirstname.length)
				$('#message_' + responseObj.resultarray[i].listmemberid).html('Email Address in use on THIS list by ' + responseObj.resultarray[i].matchingfirstname + ' ' + responseObj.resultarray[i].matchinglastname + ' (' + responseObj.resultarray[i].externalmemberid + ')');
			else if (responseObj.resultarray[i].externalmemberid !== undefined && responseObj.resultarray[i].externalmemberid.length)
				$('#message_' + responseObj.resultarray[i].listmemberid).html('Email Address in use on THIS list by non-associated MemberNumber.<br/>Info from List Membership: ' + responseObj.resultarray[i].matchinglistfullname + ' (' + responseObj.resultarray[i].externalmemberid + ')');
			else if (responseObj.resultarray[i].listmemberid !== undefined && responseObj.resultarray[i].listmemberid != '' && responseObj.resultarray[i].listmemberid > 0)
				$('#message_' + responseObj.resultarray[i].listmemberid).html('Email Address in use, but missing a MemberNumber.<br/>Info from List Membership: ' + responseObj.resultarray[i].matchinglistfullname);				
		} else {
			if(responseObj.resultarray[i].certifiedemail){
				$('#certified_email_'+responseObj.resultarray[i].listmemberid).show();
				$('#uncertified_email_'+responseObj.resultarray[i].listmemberid).hide();
			} else {
				$('#certified_email_'+responseObj.resultarray[i].listmemberid).hide();
				$('#uncertified_email_'+responseObj.resultarray[i].listmemberid).show();
			}
		}
	}
	
	if (typeof responseObj.addresult != 'undefined'){
		if (responseObj.addresult) {
			triggerRefresh = true;
			successMessage = "Email address successfully added.";
			confirmTitle = "Add to List Confirmation";
		}
		else {
			preventRefresh = true;
			$('#message_add').html("Unable to add Email address. Address already on list.");
			$('#messageHolder_add').show();
		}
	}
	
	if (responseObj.reclaimresult){
		$('#messageHolder_claim').show();
		if (!responseObj.reclaimresult.matchingmemberships) {
			$('#message_claim').html("No matches found.");
			preventRefresh = true;
		}
		else if (responseObj.reclaimresult.success){
			successMessage = "Claimed List Memberships: " + responseObj.reclaimresult.matchingmemberships;
			confirmTitle = "Claim List Confirmation";
			triggerRefresh = true;
		}
		else {
			$('#message_claim').html("Failed. Email Address in use on at least one list by " + responseObj.reclaimresult.matchingfirstname + " " + responseObj.reclaimresult.matchinglastname + " (" + responseObj.reclaimresult.externalmemberid + ")");
			preventRefresh = true;
		}
	}

	resetSaveListsBtn();
	
	if ((triggerRefresh) && (!preventRefresh)) {
		showsuccesspopup(confirmTitle, successMessage);
	}
	else {
		$('html, body').animate({
			scrollTop: $('#saveListsBtn').offset().top - 125
		}, 750);
	}
};
var showsuccesspopup = function(confirmTitle, successMessage){
	$('.btnSaveLists').attr('disabled',true);
	if(successMessage.length > 0) {
		var msg = '<div style="m-3"><b>'+ successMessage +'</b>&nbsp;&nbsp;</div>';
		MCModalUtils.showModal({
			verticallycentered: true,
			size: 'md',
			title: confirmTitle,
			iframe: false ,
			strmodalbody: { 
				content: msg
			},
			strmodalfooter : {
				classlist: 'd-none'
			}
		});
		$('#MCModal').on('hidden.bs.modal', function() {
			redirectToEdit();
		});
		var redirectToEdit = function(){
			self.location.href = mcma_link_lists;
		}
	}
	else
		self.location.href = mcma_link_lists;
}
function showAddLMBox() {
	if (!$('#divListsAddForm').is(':visible')) {
		$('#saveListsResponse').html('');
		$('.divListTool').hide();
		$('#divListsAddForm').show();
	}
}
function showListFilters() {
	if (!$('#divFilterForm').is(':visible')) {
		$('#saveListsResponse').html('');
		$('.divListTool').hide();
		$('#divFilterForm,#divCurrentListMemberships').show();
	}
}
function showClaimLMBox() {
	if (!$('#divListsClaimForm').is(':visible')) {
		$('#saveListsResponse').html('');
		$('.divListTool').hide();
		$('#divListsClaimForm').show();
	}
}
function doLMSearch() {
	$('#frmListFilter button[name="btnSubmit"]').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Please Wait...').prop('disabled',true).css('color','#777');
	$('.btnSaveLists').attr('disabled',true);
	$('#divlistForm').hide();
	$('#listLoadingScreen').show();
	document.forms['frmListFilter'].submit();
}
function reloadListPage() {
	self.location.href = mcma_link_lists;
}
function returnToCurrentListMemberships() {
	$('.divListTool').hide();
	$('#divCurrentListMemberships').show();
}
function initListsTab() {
	$('.MCOption_lockName').on('change', function() {
		if(parseInt($(this).val())){
			$("#list_fullname_"+$(this).attr("name").split('_')[2]+"_").attr("readonly","readonly");
		}else{
			$("#list_fullname_"+$(this).attr("name").split('_')[2]+"_").removeAttr("readonly");
		}
	});
	
	$('select[data-multiselectdeliverysettings="1"]').on('change', function() {
		var arrSelected = $(this).val() || [];

		if (arrSelected.length > 1) {
			if (arrSelected.includes('mail') && (arrSelected.includes('digest') || arrSelected.includes('mimedigest') || arrSelected.includes('index'))) {
				var arrFiltered = arrSelected.filter(function(value){ return !['digest','mimedigest','index'].includes(value); });
				$(this).val(arrFiltered);
				$(this).trigger('change.select2');
			}
		}
	});

	mca_setupSelect2();
	loadListChangeHistory();
}

// referrals
function refreshRefTabGrids() { 
	if (typeof referralsTable != "undefined") referralsTable.draw();
	if (typeof retainedCasesTable != "undefined") retainedCasesTable.draw();
	if (typeof refHistoryTable != "undefined") refHistoryTable.draw();
	if (typeof panelTable != "undefined") panelTable.draw();
	if (typeof memberDocumentsListTable != "undefined") reloadDocPage();
}
function initMemberPanelsTable(){
	let subPanelItemsTemplateSource = $('#mc_subPanelItemsTempate').html().replace(/\n/g,'');
	let subPanelItemsTemplate = Handlebars.compile(subPanelItemsTemplateSource);

	panelTable = $('#panelTable').DataTable({
		"processing": true,
		"serverSide": true,
		"paginate": false,
		"info": false,
		"language": {
			"lengthMenu": "_MENU_",
			"emptyTable": "No Panel Records found"
		},
		"ajax": { 
			"url": mcma_link_viewpanels,
			"type": "post"
		},
		"autoWidth": false,
		"columns": [
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if (data.arrsubpanels.length > 0) {
							renderData += '<a href="javascript:void(0);" class="grpToggleBtn" ><i class="fas fa-fw pr-2 fa-folder-minus"></i> ' + data.name + '</a>';
						} else {
							renderData += '<span><i class="fas fa-folder-tree fa-fw pr-2 invisible"></i> '+data.name+'</span>';
						}
						if (!data.inpanelrotationgrp) {
							renderData += '<i title="This member is not in the panel rotation permission group." class="fa-solid fa-exclamation-triangle text-warning fa-sm float-right"></i>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "62%",
				"orderable": false
			},
			{ "data": "memberstatusname", "width": "20%", "orderable": false },
			{
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if(data.addsubpanelrights) {
							renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" title="Add Sub-Panel" onclick="javascript:addSubPanelWindow('+data.panelid+','+data.memberid+');return false;"><i class="fa-solid fa-circle-plus"></i></a>';
						} else {
							renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1 invisible"><i class="fa-solid fa-circle-plus"></i></a>';
						}
						renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" title="Edit '+data.name+'" onclick="javascript:editPanelWindow('+data.panelmemberid+');return false;"><i class="fa-solid fa-pencil"></i></a>';
						if(data.deletepanelrights) {
							renderData += '<a href="#" class="btn btn-xs btn-outline-danger p-1 m-1" onclick="javascript:removePanel('+data.panelmemberid+');return false;" title="Remove '+data.name+'"><i class="fa-solid fa-circle-minus"></i></a>';
						} else {
							renderData += '<a href="#" class="btn btn-xs btn-outline-danger p-1 m-1 invisible"><i class="fa-solid fa-circle-minus"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"orderable": false,
				"width": "15%",
				"className": "text-center"
			}
		],
		"searching": false,
		"ordering": false,
		"drawCallback": function(settings) {
			$('#panelTable tbody a.grpToggleBtn').trigger('click');
		}
	});
	$('#panelTable tbody').on('click', 'a.grpToggleBtn', function () {
		var tr = $(this).closest('tr');
		var row = panelTable.row( tr );
		if ( row.child.isShown() ) {
			row.child.hide();
			$(this).children('i').removeClass('fa-folder-minus').addClass('fa-folder-plus');
		} else {
			row.child( subPanelItemsTemplate(row.data()), 'border-top-0 p-0' ).show();
			$(this).children('i').removeClass('fa-folder-plus').addClass('fa-folder-minus');
		}
	});
}
function initReferralsTable(){
	referralsTable = $('#referralsTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50, 100 ],
		"info": true,
		"language": {
			"lengthMenu": "_MENU_",
			"emptyTable": "No Referrals Found."
		},
		"ajax": { 
			"url": mcma_link_referrals,
			"type": "post",
			"data":{
				"refDateRange" :function() { return $('#refDateRange').val() },
			}
		},
		"autoWidth": false,
		"columns": [
			{ "data": "clientreferralid", "width": "10%", "className": "align-top" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display')	{
						renderData += '<div>'+data.clientname+'</div>';
						if (data.description.length < 150) {
							renderData += '<div class="text-dim small">'+data.description+'</div>';
						} else {
							let summary = data.description.substring(0,150);
							let extraText = data.description.substring(150,data.description.length);
							renderData += '<div id="memRefDesc'+data.clientreferralid+'" class="text-dim small">'+summary+'<span class="mc_expandText">... <a href="#" onclick="toggleSummaryText(\'memRefDesc'+data.clientreferralid+'\');return false;">Read more</a></span><span class="mc_collapseText d-none">'+extraText+' <a href="#" class="ml-2" onclick="toggleSummaryText(\'memRefDesc'+data.clientreferralid+'\');return false;">Read less</a></span></div>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "42%",
				"className": "align-top",
				"orderable": true
			},
			{ "data": "referraldate", "width": "12%", "className": "text-center align-top" },
			{ "data": "statusname", "width": "28%", "className": "align-top" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display')	
						renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1" onclick="editReferral('+data.clientreferralid+');return false;" title="View/Edit Referral"><i class="fas fa-eye"></i></a>';
					return type === 'display' ? renderData : data;
				},
				"width": "8%",
				"className": "text-center align-top",
				"orderable": false
			}
		],
		"searching": true,
		"order": [[1, 'asc']]
	});
}
function initRetainedCasesTable(){
	retainedCasesTable = $('#retainedCasesTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50, 100 ],
		"info": true,
		"language": {
			"lengthMenu": "_MENU_",
			"emptyTable": "No Cases Found."
		},
		"ajax": { 
			"url": mcma_link_refcases,
			"type": "post",
			"data":{
				"casedaterange" :function() { return $('#caseDateRange').val() },
			}
		},
		"autoWidth": false,
		"columns": [
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display')	{
						if(data.iscasefee) {
							renderData += '';
						} else {
							if (data.hasCaseFees) {
								renderData += '<a href="javascript:toggleParentCaseDisplay('+data.clientreferralid+');" id="refID_'+data.clientreferralid+'"><i class="fa-regular fa-plus-square fa-fw caseToggleBtn pr-2"></i> '+data.clientreferralid+'</a>';
							} else {
								renderData += '<span><i class="fa-solid fa-plus-square fa-fw pr-2 invisible"></i> '+data.clientreferralid+'</span>';
							}
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "align-top",
				"orderable": false
			},
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					return type === 'display' ? (data.iscasefee ? '' : data.clientname) : data;
				},
				"width": "24%",
				"className": "align-top",
				"orderable": true
			},
			{ "data": "referraldate", "width": "10%", "className": "text-left align-top" },
			{ "data": "collectedfeetotal", "width": "17%", "className": "text-right align-top","orderable": false },
			{ "data": "referralduestotal", "width": "9%", "className": "text-right  align-top","orderable": false },
			{ "data": "amttobepaidtotal", "width": "7%", "className": "text-right align-top","orderable": false },
			{ "data": "paidtodatetotal", "width": "13%", "className": "text-right align-top","orderable": false },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display')	{
						if(data.iscasefee) {
							renderData += '<a href="#" class="btn btn-xs p-1 mr-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
							if(data.addpaymentstring.length)
								renderData += '<a href="#" class="btn btn-xs text-primary p-1 mr-1" onclick="mca_addPayment(\''+data.addpaymentstring+'\');return false;" title="Pay for Fees"><i class="fa-solid fa-money-bill"></i></a>';
							else
								renderData += '<a href="#" class="btn btn-xs p-1 mr-1 invisible"><i class="fa-solid fa-money-bill"></i></a>';
						} else {
							renderData += '<a href="#" class="btn btn-xs text-primary p-1 mr-1" onclick="editCase('+data.clientreferralid+');return false;" title="Edit Client"><i class="fa-solid fa-pencil"></i></a>';
							renderData += '<a href="#" class="btn btn-xs text-primary p-1 mr-1" onclick="sendStatement('+data.clientreferralid+');return false;" title="View and E-mail Statement"><i class="fa-solid fa-envelope"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "text-center align-top",
				"orderable": false
			}
		],
		"searching": true,
		"order": [[1, 'asc']]
	});
}
function initRefHistoryTable(){
	refHistoryTable = $('#refHistoryTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50, 100 ],
		"info": true,
		"language": {
			"lengthMenu": "_MENU_",
			"emptyTable": "No Referral History Found."
		},
		"ajax": { 
			"url": mcma_link_refhistory,
			"type": "post",
			"data":{
				"refHistoryDateRange" :function() { return $('#refHistoryDateRange').val() },
			}
		},
		"autoWidth": false,
		"columns": [
			{ "data": "clientreferralid", "width": "10%", "className": "align-top" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display')	{
						renderData += '<div>'+data.clientname+'</div>';
						if (data.description.length < 150) {
							renderData += '<div class="text-dim small">'+data.description+'</div>';
						} else {
							let summary = data.description.substring(0,150);
							let extraText = data.description.substring(150,data.description.length);
							renderData += '<div id="memRefHistDesc'+data.clientreferralid+'" class="text-dim small">'+summary+'<span class="mc_expandText">... <a href="#" onclick="toggleSummaryText(\'memRefHistDesc'+data.clientreferralid+'\');return false;">Read more</a></span><span class="mc_collapseText d-none">'+extraText+' <a href="#" class="ml-2" onclick="toggleSummaryText(\'memRefHistDesc'+data.clientreferralid+'\');return false;">Read less</a></span></div>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "30%",
				"className": "align-top",
				"orderable": true
			},
			{ "data": "referraldate", "width": "12%", "className": "text-center align-top" },
			{ "data": "statusname", "width": "20%", "className": "align-top" },
			{ "data": "isretainedcase", "width": "10%", "className": "text-center align-top", "orderable": false },
			{ "data": "isamountdue", "width": "10%", "className": "text-center align-top", "orderable": false },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display')	{
						if(data.isretainedcase)
							renderData += '<a href="#" class="btn btn-xs text-primary p-1 mr-1" onclick="editCase('+data.clientreferralid+');return false;" title="View Case"><i class="fa-solid fa-eye"></i></a>';
						else
							renderData += '<a href="#" class="btn btn-xs text-primary p-1 mr-1" onclick="editReferral('+data.clientreferralid+');return false;" title="View Referral"><i class="fa-solid fa-eye"></i></a>';
					}
						
					return type === 'display' ? renderData : data;
				},
				"width": "8%",
				"className": "text-center align-top",
				"orderable": false
			}
		],
		"searching": true,
		"order": [[0, 'asc']]
	});
}
function toggleParentCaseDisplay(cid) {
	let caseToggleBtn = $('#refID_'+cid+' i.caseToggleBtn');		
	caseToggleBtn.toggleClass('fa-plus-square fa-minus-square');
	if (caseToggleBtn.hasClass('fa-minus-square')) {
		showRefCaseFees(cid);
	} else {
		hideRefCaseFees(cid);
	}
}
function showRefCaseFees(cid) {
	$('tr.refCaseFeeOf'+cid).removeClass('d-none');
}
function hideRefCaseFees(cid) {
	$('tr.refCaseFeeOf'+cid).addClass('d-none');
}
function addPanelWindow(){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Add Member to Panel',
		iframe: true,
		contenturl: mcma_link_addpanel+'&memberID='+mcma_memberid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSavePanel").click',
			extrabuttonlabel: 'Save',
		}
	});
}
function editPanelWindow(pmID){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Edit Panel Member Settings',
		iframe: true,
		contenturl: mcma_link_editpanel+'&panelMemberID='+pmID,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSavePanel").click',
			extrabuttonlabel: 'Save',
		}
	});
}
function addSubPanelWindow(pID,mID){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'md',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Add Sub-Panel',
		iframe: true,
		contenturl: mcma_link_addsubpanel+'&panelID='+pID+'&memberID='+mID,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSaveSubPanel").click',
			extrabuttonlabel: 'Save',
		}
	});
}
function removePanel(pmID) {
	var removePanelData	= function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			refreshRefTabGrids();
		} else {
			alert('We were unable to delete this Panel.');
		}
	};
	var msg = 'Are you sure you want to remove this Panel?';
	if( confirm(msg)){
		var objParams = { panelMemberID:pmID, referralID:mcma_refid };
		TS_AJX('ADMINMEMBERS','deletePanelMember',objParams,removePanelData,removePanelData,10000,removePanelData);
	}
}
function removeSubPanel(pmID) {
	var removePanelData	= function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			refreshRefTabGrids();
		} else {
			alert('We were unable to delete this Sub-Panel.');
		}
	};
	var msg = 'Are you sure you want to remove this Sub-Panel?';
	if( confirm(msg)){
		var objParams = { panelMemberID:pmID };
		TS_AJX('ADMINMEMBERS','deleteSubPanelMember',objParams,removePanelData,removePanelData,10000,removePanelData);
	}
}
function rcRangeChange(val) {
	retainedCasesTable.draw();
}
function refRangeChange(val) {
	referralsTable.draw();
}
function refHistoryRangeChange(val) {
	refHistoryTable.draw();
}
function editReferral(rID){
	window.open(mcma_link_editclient+"&clientReferralID="+rID);
}
function editCase(rID){
	window.open(mcma_link_editclient+"&clientReferralID="+rID);
}
function refreshRetainedCaseGrid() { 
	retainedCasesTable.draw();
}
function sendStatement(rID) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'E-mail Case Statement',
		iframe: true,
		contenturl: mcma_link_viewcasestatement+'&clientReferralID='+rID,
		strmodalfooter : {
		showclose: true,
		classlist: 'd-flex',
		buttons: [
				{
					class:"btn btn-sm btn-primary",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSend").click',
					label: 'E-Mail Case Statement', 
					name: 'btnSend',
					id: 'btnSend'
				},
				{
					class: "btn btn-sm btn-primary",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnViewStatement").click',
					label: 'View Statement', 
					name: 'btnViewStatement',
					id: 'btnViewStatement'
				}
			]
	}
	});
}
function sendProgressReport() {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
			title: 'Email Progress Report',
			iframe: true,
			contenturl: mcma_link_viewprogreport,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				buttons: [
					{
						class:"btn btn-sm btn-primary",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSend").click',
						label: 'E-Mail Progress Report', 
						name: 'btnSend',
						id: 'btnSend'
					},
					{
						class: "btn  btn-sm btn-primary ",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnViewProgressReport").click',
						label: 'View Progress Report', 
						name: 'btnViewProgressReport',
						id: 'btnViewProgressReport'
					}
				]
			}
	});
}
function toggleSummaryText(targetID) {
	$('#'+targetID).find('.mc_expandText,.mc_collapseText').toggleClass('d-none');
	return false;
}

// seminarweb
function loadSWTab() {
	if ($('#rSWLDateFrom').length) { 
		mca_setupDatePickerRangeFields('rSWLDateFrom','rSWLDateTo');
		mca_setupCalendarIcons('frmSWLRegFilter'); 
	}
	if ($('#rSWODDateFrom').length) { 
		mca_setupDatePickerRangeFields('rSWODDateFrom','rSWODDateTo');
		mca_setupDatePickerRangeFields('cSWODDateFrom','cSWODDateTo');
		mca_setupCalendarIcons('frmSWODRegFilter');
	}
}
function filterSWLProgramRegistrations() {
	if (!$('#divSWLRegFilterForm').is(':visible')) {
		$('#divSWLRegFilterForm').show();
	}
}
function dofilterSWLProgramRegistrations() {
	SWLRegistrantsListTable.draw();
}
function filterSWODProgramRegistrations() {
	if (!$('#divSWODRegFilterForm').is(':visible')) {
		$('#divSWODRegFilterForm').show();
	}
}
function doFilterSWODProgramRegistrations() {
	SWODRegistrantsListTable.draw();
}
function resetSWRegFilters(ft) {
	switch(ft) {
		case 'swl': $('#frmSWLRegFilter')[0].reset();
					dofilterSWLProgramRegistrations();
			break;
		case 'swod': $('#frmSWODRegFilter')[0].reset();
					doFilterSWODProgramRegistrations();
			break;
	}
}

// store
function reloadStoreTable() {
	storeOrderTable.draw();
}

// manage member opt-out or opt-ins
function manageMemberConsentLists(listMode) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Manage ' + (listMode.toLowerCase() == 'opt-out' ? 'Opt-Outs' : 'Opt-Ins'),
		iframe: true,
		contenturl: mcma_link_managememconsentlists+'&listMode='+listMode
	});
}