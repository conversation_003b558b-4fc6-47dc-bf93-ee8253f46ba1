<cfoutput>
	<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
		<cfinclude template="head.cfm" />
		<body>
			<cfinclude template="jssalesscript.cfm">
			<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct">
				<div class="bodyText" style="margin:6px;">#application.objCMS.renderZone(zone='Main',event=event)#</div>
			<cfelse>
			
				<cfinclude template="headerContent.cfm">
				<div id="blue_content_interior">
					<cfinclude template="mastHead.cfm">
				</div>
				
				<div id="white_content_interior">
					<cfinclude template="subNavigation.cfm">
					<div class="pageSize white_content_area_interior">
						<div class="clear"></div>
						<table width="100%" cellpadding="0" cellspacing="0" border="0" id="interior_content">
							<tr>
								<td width="20%" valign="top">
									<cfinclude template="sideNavigation.cfm">
								</td>
								<td width="3%">&nbsp;</td>
								<td width="77%" valign="top">#application.objCMS.renderZone(zone='Main',event=event)#</td>
							</tr>
						</table>
					</div>
				</div>
				<cfinclude template="footerContent.cfm">
			</cfif>
			<cfinclude template="toolBar.cfm" />
			<cfinclude template="adRoll.cfm" />
		</body>
	</html>
</cfoutput>