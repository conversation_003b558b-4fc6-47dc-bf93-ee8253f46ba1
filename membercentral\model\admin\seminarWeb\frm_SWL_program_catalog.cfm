<cfsavecontent variable="local.catalogJS">
	<cfoutput>
	<cfif local.keyExists("strRevenueGLAcctWidget")>
		#local.strRevenueGLAcctWidget.js#
	</cfif>
	<script language="JavaScript">
		var #toScript(local.bundleIsActive,'bundleIsActive')#
		var #toScript(local.bundleLinks,'bundleLinks')#
		var #toScript(local.notSoldInCatalogIssue, 'notSoldInCatalogIssue')#
		var #toScript(local.sellInCatalogWithActiveBundleSuccess, 'sellInCatalogWithActiveBundleSuccess')#
		var #toScript(local.sellingSuccessStatement, 'sellingSuccessStatement')#
		var #toScript(local.notSellInCatalogInactiveBundleStatement, 'notSellInCatalogInactiveBundleStatement')#
		var #toScript(local.sellDatesInFutureIssue, 'sellDatesInFutureIssue')#
		var #toScript(local.expiredDatesIssue, 'expiredDatesIssue')#
		var #toScript(local.ratesIssue, 'ratesIssue')#
		bundleIsActive = bundleIsActive === 'true';
		var validDate = false;
		var validRate = false;
		var doRefreshSetupAlerts = false;
		function saveSWLRateCatalog(type='',callback,skipvalidation=0) {
			mca_hideAlert('err_catalog');
			if (isSWProgramLocked()) return false;
			
			var saveRateCatalogResult = function(r) {
				$('##frmSWLProgramCatalog,##divSWLCatalogDetailsSaveLoading').toggle();

				if (r.success && r.success.toLowerCase() == 'true') {
					
					$('##saveCatalogResponse').html('Details saved.').addClass('text-success').show().fadeOut(5000);
					if(type=="featureImage"){
						if(programAdded) 
							self.location.href = getSWEditProgramLink() + "&showBadge=catalog&tab=catalog&programAdded="+ programAdded + "&lastIncompleteSectionIndex="+lastIncompleteSectionIndex;			
						else 
							self.location.href = getSWEditProgramLink() + "&showBadge=catalog&tab=catalog";
					}		
					<cfif local.hasManageSWLRatesRights>
						var allowCatalogOption = $('input[name="allowCatalogOption"]:checked').length;
						if (allowCatalogOption == 0) {
							mcftd_clearCurrentFeaturedImage($(".mcftd_div_imageContainer").data("ftdext"));
							if ($('##incSponsorWidgetContainerswlProgramSponsors').find('table').length) 
								loadSponsors_swlProgramSponsors();
							$('##enableSponsor').prop('disabled', false);

							refreshSetupAlerts('SWL');
						}
						else {
							// setting up the reloading of setup alerts only after grid reload is complete, so correct rate count is drawn for checking
							doRefreshSetupAlerts = true;
						}

						reloadSWRatesGrid();
					</cfif>	
					if(!skipvalidation) {
						if(!$("##program-catalog .card-header:first ##saveResponse").length)
							$("##program-catalog .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
						$('##program-catalog .card-header:first ##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(10000);
					}
					if(programAdded)
						$('##nextButton').prop('disabled',false);
					else
						$('##program-catalog .card-footer .save-button').prop('disabled',false);
					if (callback) {
						callback();
					}
				} else {
					var arrReq = [];
					arrReq.push(r.err && r.err.length ? r.err : 'We were unable to save catalog.');
					mca_showAlert('err_catalog', arrReq.join('<br/>'));
				}
			};
			
			var arrReq = [];
			<cfif local.hasManageSWLRatesRights and local.isPublisher>
				var allowCatalogOption = $('input[name="allowCatalogOption"]:checked').length;
				if (allowCatalogOption == 1) {
					if (skipvalidation == 0) {
						if ($('##dateStartCatalog').val().trim().length == 0)
							arrReq.push('Enter the starting catalog availability date.');
						if ($('##dateEndCatalog').val().trim().length == 0)
							arrReq.push('Enter the ending catalog availability date.');
						if($('input[name="enableFeaturedImage"]:checked').length == 1 && $('##swlFtdImgContainer img').length == 0)
							arrReq.push('Add custom image to replace default webinar image.');
						if($('input[name="enableSponsor"]:checked').length == 1 && $('##incSponsorWidgetContainerswlProgramSponsors table tr[id^="sponsorUsageRow_"]').length == 0)
							arrReq.push('Add sponsor for registration page.');
						if($('##swProgramRatesTableCatalog .dataTables_empty').length > 0 && programAdded)
							arrReq.push('Add rates.');
					}
				} else {
					mca_clearDateRangeField('dateStartCatalog','dateEndCatalog');
					$('button[name="btnClearGLAccount_revenueGLAccountIDCatalog"]').trigger('click');
					$('input[name="isFeatured"]:checked').prop("checked", false);
					$('input[name="enableFeaturedImage"]:checked').prop("checked", false);
					$('input[name="enableSponsor"]:checked').prop("checked", false);
					showSponsor();
				}
			</cfif>
				
			var revGL = 0;
			<cfif local.qrySeminar.handlesOwnPayment is 1>					
				if ($('##revenueGLAccountIDCatalog').length && $('##revenueGLAccountIDCatalog').val() != '')
				revGL = $('##revenueGLAccountIDCatalog').val();
			</cfif>				
			
			if (type != "featureImage" && arrReq.length ) {
				$('##err_catalog').html(arrReq.join('<br/>')).removeClass('d-none');
				$('html,body').animate({scrollTop: $('##err_catalog').offset().top-120},500);
				if(programAdded)
					$('##nextButton').prop('disabled',false);
				else
					$('##program-catalog .card-footer .save-button').prop('disabled',false);
				return false;
			}
			
			$('##frmSWLProgramCatalog,##divSWLCatalogDetailsSaveLoading').toggle();
			
			var objParams = { 
				seminarID:sw_seminarid,
				<cfif local.hasManageSWLRatesRights>					
					allowCatalog:$('input[name=allowCatalogOption]:checked').length, 
					dateCatalogStart:$('##dateStartCatalog').val(), 
					dateCatalogEnd:$('##dateEndCatalog').val(),
					revenueGLAccountID:revGL,
					isFeatured:$('input[name=isFeatured]:checked').length,
					enableFeaturedImage:$('input[name=enableFeaturedImage]:checked').length,
					enableSponsor:$('input[name=enableSponsor]:checked').length,
				</cfif>
				isPriceBasedOnActual:$('input[name=isPriceBasedOnActualOption]:checked').length,
				freeRateDisplay:$('##freeRateDisplayCatalog').val()
			};
			TS_AJX('ADMINSWL','saveSWLProgramCatalog',objParams,saveRateCatalogResult,saveRateCatalogResult,10000,saveRateCatalogResult);
		}
		function showAllowCatalogSettings(){
			if ($('##allowCatalogOption').is(':checked')) {
				$('##allowCatalogHolder').removeClass("d-none");
			} else {
				$('##allowCatalogHolder').addClass("d-none");
			}
		}
		function showSponsor(){
			if ($('##enableSponsor').is(':checked')) {
				$('##enableSponsorHolder').removeClass("d-none");
			} else {
				$('##enableSponsorHolder').addClass("d-none");
			}
		}
		function initSWLCatalog(){
			mca_setupSelect2ByID('subjectArea');
			$('##subjectArea').on('select2:select', function (e) { addSWProgramCategory(e.params.data); }).on('select2:unselect', function (e) { deleteSWProgramCategory(e.params.data); });
			mca_setupDatePickerRangeFields('dateStartCatalog','dateEndCatalog');
			mca_setupCalendarIcons('frmSWLProgramOptions');
			mca_setupCalendarIcons('frmSWLProgramCatalog');
			$.fn.DataTable.isDataTable('##swProgramRatesTableCatalog') ? swProgramRatesTable.draw() : initSWProgramRatesTable('SWL','Catalog');
			getAvailableSWCategories();
		}
		function disableSponsor_swlProgramSponsors(arrIncludedSponsorsLength){
			if($('input[name="enableSponsor"]:checked').length == 1)
				{
					if(arrIncludedSponsorsLength > 0)
						$('##enableSponsor').attr('disabled',true);
					else 
						$('##enableSponsor').attr('disabled',false);	
				}
		}
		<cfif val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0 AND local.hasEditRights>
			function addSWLFtdImg() {
				mcftd_editFeaturedImage('#local.arrSWLConfigs[1].ftdExt#');
				$('##enableFeaturedImage').prop('checked',false);
			}
			function mcftd_#local.arrSWLConfigs[1].ftdExt#_onClearCurrentFeaturedImage() {
				$('##enableFeaturedImage').prop('checked',false).prop('disabled',false);
				$('##swlFtdImgContainer').addClass('d-none');
			}
		</cfif>
		$(function() {
			<cfif val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0>
				if ($('##swlFtdImgContainer').find('.mcftd_div_previewImageContainer').length) {
					$('##enableFeaturedImage').prop('checked',true).prop('disabled',true);
				} else {
					$('##swlFtdImgContainer').addClass('d-none');
					$('##enableFeaturedImage').prop('checked',false).prop('disabled',false);
				}
			</cfif>
			if ($('##enableSponsor').is(':checked')) 
				$('##enableSponsorHolder').removeClass("d-none");
			else 
				$('##enableSponsorHolder').addClass("d-none");
			if (isSWProgramLocked()) 
				$('##enableFeaturedImage').prop('disabled',true); 
		});
	</script>
	<cfif val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0 AND local.hasEditRights>
		#local.strSWLFeaturedImages.js#
	</cfif>
	<style>
		.readOnly {
			pointer-events: none;
		}
		.custom-control-input:disabled~.custom-control-label {
			color: ##3b3e66;
		}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.catalogJS#">
<cfoutput>
<cfif val(local.qrySeminar.preventSeminarFees)>
	<div class="alert alert-info"><strong>Prevent Seminar Fees</strong> setting is turned <strong>on</strong> for this Seminar. You'll need to turn it off before you can add rate or syndication pricing.</div>
</cfif>
<div id="err_catalog" class="alert alert-danger mb-2 mt-2 d-none"></div>
<form name="frmSWLProgramCatalog" id="frmSWLProgramCatalog" method="post" onsubmit="saveSWLRateCatalog();" autocomplete="off">
	<div class="text-right mb-2">
		<span id="saveCatalogResponse" class="d-none"></span>
		<button type="button" name="btnSaveSWLProgramCatalog" class="btn btn-sm btn-primary d-none" onclick="saveSWLRateCatalog();">Save Changes</button>
	</div>
	<cfif local.hasManageSWLRatesRights>	
		<div class="form-group mt-2 mb-3">
			<div class="custom-control custom-switch">
				<input type="checkbox" name="allowCatalogOption" id="allowCatalogOption" onclick="showAllowCatalogSettings();" class="custom-control-input" <cfif (local.isPublisher AND len(local.qrySeminar.dateCatalogStart) is not 0)> checked="checked"<cfelseif !local.isPublisher AND local.qrySeminarSyndicateObj.sellCatalog AND len(local.qrySeminar.dateCatalogStart)> checked="checked"<cfelseif !local.isPublisher AND NOT len(local.qrySeminar.dateCatalogStart)>disabled</cfif>>
				<label class="custom-control-label" for="allowCatalogOption">
					I want to sell this webinar in the catalog. <cfif local.isPublisher><i class="fa-solid fa-info-circle pl-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="" data-original-title="This webinar can also be sold in a bundle. To only sell it in a bundle, this toggle should be turned off."></i></cfif>
				</label>

				<cfif !local.isPublisher AND NOT len(local.qrySeminar.dateCatalogStart)>
					<div class="alert alert-danger">
						The Publisher has decided to stop selling the program in the catalog.
					</div>
				</cfif>
				
				<div class="form-group my-2 <cfif local.isPublisher AND len(local.qrySeminar.dateCatalogStart) is 0>d-none<cfelseif !local.isPublisher AND (!local.qrySeminarSyndicateObj.sellCatalog OR !len(local.qrySeminar.dateCatalogStart))>d-none</cfif>" id="allowCatalogHolder">
					<div class="mt-2 mb-3">
						<div class="form-row">
							<div class="col-3">
								<div class="form-label-group mb-0">
									<div class="input-group dateFieldHolder">
										<input type="text" name="dateStartCatalog" id="dateStartCatalog" value="#DateFormat(local.qrySeminar.dateCatalogStart,'m/d/yyyy')#" class="form-control dateControl <cfif NOT local.isPublisher>readOnly</cfif>">
										<cfif local.isPublisher>
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="dateStartCatalog"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('dateStartCatalog');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
										</cfif>
										<label for="dateStartCatalog">Start Date</label>
									</div>
								</div>
							</div>
							<div class="col-3">
								<div class="form-label-group mb-0">
									<div class="input-group dateFieldHolder">
										<input type="text" name="dateEndCatalog" id="dateEndCatalog" value="#DateFormat(local.qrySeminar.dateCatalogEnd,'m/d/yyyy')#" class="form-control dateControl <cfif NOT local.isPublisher>readOnly</cfif>">
										<cfif local.isPublisher>
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="dateEndCatalog"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('dateEndCatalog');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
										</cfif>
										<label for="dateEndCatalog">End Date</label>
									</div>
								</div>
							</div>
							<div class="col-auto align-self-center">12:00AM</div>
						</div>
					</div>
					
					<cfset local.isPriceBasedOnActual = local.isPublisher ? local.qrySeminar.isPriceBasedOnActual : local.qryOptInSeminarRateSettings.isPriceBasedOnActual>
					<div class="custom-control custom-switch">
						<input type="checkbox" name="isPriceBasedOnActualOption" id="isPriceBasedOnActualOption" disabled class="custom-control-input" <cfif local.isPriceBasedOnActual is 1> checked="checked"</cfif>>
						<label class="custom-control-label text-body" for="isPriceBasedOnActualOption">
							Registrant must qualify for rate based on their group membership. <i class="fa-solid fa-info-circle pl-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="" data-original-title="We'll automatically toggle this setting if any of your Rates have qualifying groups listed."></i>
						</label>
					</div>
					
					<cfif local.keyExists("strRevenueGLAcctWidget")>
						<div class="mt-2">#local.strRevenueGLAcctWidget.html#</div>
					</cfif>
					
					<cfset local.freeRateDisplay = local.isPublisher ? local.qrySeminar.freeRateDisplay : local.qryOptInSeminarRateSettings.freeRateDisplay>
					<div class="form-group mt-3">
						<div class="form-label-group">
							<select name="freeRateDisplayCatalog" id="freeRateDisplayCatalog" class="form-control">
								<option value="FREE" <cfif local.freeRateDisplay eq "FREE">selected</cfif>>Display amount as FREE</option>
								<option value="$0.00" <cfif local.freeRateDisplay eq "$0.00">selected</cfif>>Display amount as $0.00</option>
								<option value="" <cfif local.freeRateDisplay eq "">selected</cfif>>Hide amount</option>
							</select>
							<label for="freeRateDisplayCatalog">How should we display no-charge enrollments of this program?</label>
						</div>
					</div>
					<cfif local.qrySeminar.allowOptInRateChange IS 0 AND NOT local.isPublisher>
						<div class="text-center my-1 alert alert-info">
							The publisher of this program prevents adding, removing, or changing rates.<br/>
							You may, however, set group permissions to these rates if desired.
						</div>
					<cfelse>
						<div class="toolButtonBar mt-4">
							<cfif local.hasSWLRateChangeRights AND NOT val(local.qrySeminar.preventSeminarFees)>
								<div id="manageRatesActionBtn"><a href="##" onclick="editSWRate(0);return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Add Rate."><i class="fa-regular fa-circle-plus"></i> Add Rate</a></div>
							</cfif>
							<cfif NOT val(local.qrySeminar.preventSeminarFees)>
								<cfif local.hasSWLRateChangeRights>
									<div id="copyRatesActionBtn"><a href="##" onclick="copyRatesFromSWProgramPrompt();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to copy rates from other program."><i class="fa-regular fa-copy"></i> Copy Rates from Other Program</a></div>
								</cfif>
							</cfif>
						</div>
					</cfif>

					<div class="my-3">
						<table id="swProgramRatesTableCatalog" class="table table-sm table-bordered table-hover" style="width:100%;">
							<thead>
								<tr>
									<th id="columnid"></th>
									<th>Rate Name</th>
									<th>Rate</th>
									<th>Actions</th>
								</tr>
							</thead>
						</table>
					</div>
					
					<div class="form-group mb-3">
						<div class="custom-control custom-switch">
							<input type="checkbox" name="isFeatured" id="isFeatured" class="custom-control-input" <cfif local.isFeaturedProgram> checked="checked"</cfif>>
							<label class="custom-control-label" for="isFeatured">
								I want to advertise the webinar on the catalog's homepage in the "Featured Offerings" section.
							</label>
						</div>
					</div>
					
					<cfif local.isPublisher>
						<cfif val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0>
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="enableFeaturedImage" id="enableFeaturedImage" class="custom-control-input" onclick="addSWLFtdImg();">
									<label class="custom-control-label" for="enableFeaturedImage"> I want to customize the registration page by replacing the default webinar image with a custom image.</label>
									<div id="swlFtdImgContainer" class="mt-3">#local.strSWLFeaturedImages.html#</div>
								</div>
							</div>
						</cfif>
						
						<div class="form-group mb-3">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="enableSponsor" id="enableSponsor" class="custom-control-input" onclick="showSponsor();" <cfif local.qrySeminar.enableSponsor> checked="checked"</cfif>>
								<label class="custom-control-label  text-body" for="enableSponsor">
									I want to customize the registration page by adding a webinar sponsor.
								</label>
								<div class="form-group my-3 <cfif local.qrySeminar.enableSponsor is 0>d-none</cfif>" id="enableSponsorHolder">
									#local.strSponsors.html#
								</div>
							</div>
						</div>
						
						<b>Choose Subject Areas for this Webinar</b>
						<div class="form-group mt-2 mb-3">
							<div class="form-label-group">
								<div class="input-group flex-nowrap">
									<select name="subjectArea" id="subjectArea" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" data-allowclear="false" placeholder="No Subject Areas"></select>								
									<label for="subjectArea">Subject Areas</label>
									<div class="input-group-append">
										<button type="button" name="btnAddSubjectArea" id="btnAddSubjectArea" class="btn input-group-text" onclick="addNewSWCategory();">Create Subject Area</button>
									</div>
								</div>
							</div>
						</div>
					</cfif>
				</div>
			</div>
		</div>
	</cfif>
</form>
<cfif local.hasManageSWLRatesRights>
	<script id="mc_swRateGroupingTemplate" type="text/x-handlebars-template">
		<div id="err_rategrouping" class="alert alert-danger mb-2 d-none"></div>
		<form name="frmGrouping" id="frmGrouping" onsubmit="saveSWRateGrouping();return false;">
		<input type="hidden" name="rateGroupingID" id="rateGroupingID" value="{{rateGroupingID}}">
		<div class="form-group">
			<div class="form-label-group">
				<input type="text" name="rateGrouping" id="rateGrouping" value="{{rateGrouping}}" class="form-control" maxlength="200" autocomplete="off">
				<label for="rateGrouping">Rate Grouping</label>
			</div>
		</div>
		</form>
	</script>
</cfif>
<div id="divSWLCatalogDetailsSaveLoading" style="display:none;">
	<div class="text-center">
		<br/>
		<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
		<br/><br/>
		<b>Please wait while we save these catalog details.</b>
		<br/>
	</div>
</div>
</cfoutput>