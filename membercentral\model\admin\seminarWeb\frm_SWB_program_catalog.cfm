<cfsavecontent variable="local.catalogDetailsJS">
	<cfoutput>
	#application.objWebEditor.showEditorHeadScripts()#
	<cfif local.keyExists("strRevenueGLAcctWidget")>
		#local.strRevenueGLAcctWidget.js#
	</cfif>
	<script type="text/javascript">
		var #toScript(local.notSoldInCatalogIssue, 'notSoldInCatalogIssue')#
		var #toScript(local.sellingSuccessStatement, 'sellingSuccessStatement')#
		var #toScript(local.sellDatesInFutureIssue, 'sellDatesInFutureIssue')#
		var #toScript(local.expiredDatesIssue, 'expiredDatesIssue')#
		var #toScript(local.ratesIssue, 'ratesIssue')#
		var validDate = false;
		var validRate = false;
		var doRefreshSetupAlerts = false;
		function saveSWBRateCatalog(type='',callback,skipvalidation=0) {
			mca_hideAlert('err_catalog');
			if (isSWProgramLocked()) return false;
			var saveRateCatalogResult = function(r) {
				$('##frmSWBProgramCatalog,##divSWBCatalogDetailsSaveLoading').toggle();
				if (r.success && r.success.toLowerCase() == 'true') {
					if(type=="featureImage"){
						if(programAdded) 
							self.location.href = getSWEditProgramLink() + "&showBadge=catalog&tab=catalog&programAdded="+ programAdded + "&lastIncompleteSectionIndex="+lastIncompleteSectionIndex;			
						else 
							self.location.href = getSWEditProgramLink() + "&showBadge=catalog&tab=catalog";
					}
					<cfif local.hasManageSWBRatesRights>
						var allowCatalogOption = $('input[name="allowCatalogOption"]:checked').length;
						if (allowCatalogOption == 0) {
							mcftd_clearCurrentFeaturedImage($(".mcftd_div_imageContainer").data("ftdext"));
							if ($('##incSponsorWidgetContainerswbProgramSponsors').find('table').length) 
								loadSponsors_swbProgramSponsors();
								$('##enableSponsor').prop('disabled', false);

							refreshSetupAlerts('SWB');
						}
						else {
							// setting up the reloading of setup alerts only after grid reload is complete, so correct rate count is drawn for checking
							doRefreshSetupAlerts = true;
						}

						reloadSWRatesGrid();
					</cfif>	
					if(!skipvalidation) {
						if(!$("##program-catalog .card-header:first ##saveResponse").length)
							$("##program-catalog .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
						$('##program-catalog .card-header:first ##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(10000);
					}
					if(programAdded)
						$('##nextButton').prop('disabled',false);
					else 
						$('##program-catalog .card-footer .save-button').prop('disabled',false);
					if (callback) {
						callback();
					}
				} else {
					var arrReq = [];
					$('##btnSaveSWBProgramCatalog').prop('disabled',false);
					arrReq.push(r.err && r.err.length ? r.err : 'We were unable to save catalog.');
					$('##err_catalog').html(arrReq.join('<br/>')).removeClass('d-none');
					$('html,body').animate({scrollTop: $('##err_catalog').offset().top-120},500);
					if(programAdded)
						$('##nextButton').prop('disabled',false);
					else
						$('##program-catalog .card-footer .save-button').prop('disabled',false);
				}
			};
			
			var arrReq = [];
			var allowCatalogOption = $('input[name="allowCatalogOption"]:checked').length;
			<cfif local.hasManageSWBRatesRights and local.isPublisher>
				if (allowCatalogOption == 1) {
					if (skipvalidation == 0) {
						if ($('##dateStartCatalog').val().trim().length == 0)
							arrReq.push('Enter the starting catalog availability date.');
						if ($('##dateEndCatalog').val().trim().length == 0)
							arrReq.push('Enter the ending catalog availability date.');
						if($('input[name="enableFeaturedImage"]:checked').length == 1 && $('##swbFtdImgContainer img').length == 0)
							arrReq.push('Add custom image to replace default webinar image.');
						if($('input[name="enableSponsor"]:checked').length == 1 && $('##incSponsorWidgetContainerswbProgramSponsors table tr[id^="sponsorUsageRow_"]').length == 0)
							arrReq.push('Add sponsor for registration page.');
						if (programAdded && $('##swProgramRatesTableCatalog .dataTables_empty').length) {
							arrReq.push('Add rates to the Rates table below.');
						}
					}
				} else {
					mca_clearDateRangeField('dateStartCatalog','dateEndCatalog');
					$('button[name="btnClearGLAccount_revenueGLAccountIDCatalog"]').trigger('click');
					$('input[name="isFeatured"]:checked').prop("checked", false);
					$('input[name="enableFeaturedImage"]:checked').prop("checked", false);
					$('input[name="enableSponsor"]:checked').prop("checked", false);
					$('input[name="includeSpeakers"]:checked').prop("checked", false);
				}
			</cfif>	
			
			var revGL = 0;
			<cfif local.qryBundle.handlesOwnPayment is 1>					
				if ($('##revenueGLAccountIDCatalog').length && $('##revenueGLAccountIDCatalog').val() != '')
				revGL = $('##revenueGLAccountIDCatalog').val();
			</cfif>
			
			if (type != "featureImage" && arrReq.length ) {
				$('##btnSaveSWBProgramCatalog').prop('disabled',false);
				$('##err_catalog').html(arrReq.join('<br/>')).removeClass('d-none');
				$('html,body').animate({scrollTop: $('##err_catalog').offset().top-120},500);
				if(programAdded)
					$('##nextButton').prop('disabled',false);
				else
					$('##program-catalog .card-footer .save-button').prop('disabled',false);
				return false;
			}
			
			$('##frmSWBProgramCatalog,##divSWBCatalogDetailsSaveLoading').toggle();
			
			var objParams = { 
				bundleID:sw_bundleid,
				<cfif local.hasManageSWBRatesRights>					
					allowCatalog:$('input[name=allowCatalogOption]:checked').length, 
					dateCatalogStart:$('##dateStartCatalog').val(), 
					dateCatalogEnd:$('##dateEndCatalog').val(),
					revenueGLAccountID:revGL,
					isFeatured:$('input[name=isFeatured]:checked').length,
				</cfif>
				isPriceBasedOnActual:$('input[name=isPriceBasedOnActualOption]:checked').length,
				freeRateDisplay:$('##freeRateDisplayCatalog').val(),
				includeSpeakers: $('input[name=includeSpeakers]:checked').length
			};

			if ($('##incSponsorWidgetContainerswbProgramSponsors').find('table').length && allowCatalogOption == 0) {
				// Find the table tag within the container
				var sponsorUsageIds = [];
				var sponsorTable = $('##incSponsorWidgetContainerswbProgramSponsors').find('table');

				// Filter all buttons that start with id btnDelSponsorswbProgramSponsors_
				var deleteButtons = sponsorTable.find('[id^="btnDelSponsorswbProgramSponsors_"]');
				
				// Loop over each delete button
				deleteButtons.each(function() {
					// Fetch the ID and extract the integer after '_'
					var id = $(this).attr('id');
					var sponsorId = id.split('_')[1];
					sponsorUsageIds.push(parseInt(sponsorId));
				});

				// Form a comma-separated list of integer IDs
				var sponsorUsageIdsList = sponsorUsageIds.join(',');
				objParams.sponsorUsageIdsList = sponsorUsageIdsList;
			}

			TS_AJX('ADMINSWB','saveSWBProgramCatalog',objParams,saveRateCatalogResult,saveRateCatalogResult,10000,saveRateCatalogResult);
		}
		function showAllowCatalogSettings(){
			if ($('##allowCatalogOption').is(':checked')) {
				$('##allowCatalogHolder').removeClass("d-none");
			} else {
				$('##allowCatalogHolder').addClass("d-none");
			}
		}
		function showBundleSponsor(){
			if ($('##enableSponsor').is(':checked')) {
				$('##enableSponsorHolder').removeClass("d-none");
			} else {
				$('##enableSponsorHolder').addClass("d-none");
			}
		}
		function disableSponsor_swbProgramSponsors(arrIncludedSponsorsLength){
			if($('input[name="enableSponsor"]:checked').length == 1)
				{
					if(arrIncludedSponsorsLength > 0)
						$('##enableSponsor').prop('disabled',true);
					else  {
						$('##enableSponsor').prop('disabled',false);
						$('##enableSponsor').prop('checked',false);
						$('##enableSponsorHolder').addClass('d-none');	
					}
				}
		}
		<cfif val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0 AND local.hasEditRights>
			function addSWBFtdImg() {
				mcftd_editFeaturedImage('#local.arrSWBConfigs[1].ftdExt#');
				$('##enableFeaturedImage').prop('checked',false);
			}
			function mcftd_#local.arrSWBConfigs[1].ftdExt#_onClearCurrentFeaturedImage() {
				$('##enableFeaturedImage').prop('checked',false).prop('disabled',false);
				$('##swbFtdImgContainer').addClass('d-none');
			}
		</cfif>
		function initSWBCatalog(){
			mca_setupDatePickerRangeFields('dateStartCatalog','dateEndCatalog');
			mca_setupCalendarIcons('frmSWBProgramCatalog');
			$.fn.DataTable.isDataTable('##swProgramRatesTableCatalog') ? swProgramRatesTable.draw() : initSWProgramRatesTable('SWB','Catalog');
		}

		$(function() {
			<cfif val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0>
				if ($('##swbFtdImgContainer').find('.mcftd_div_previewImageContainer').length) {
					$('##enableFeaturedImage').prop('checked',true).prop('disabled',true);
				} else {
					$('##swbFtdImgContainer').addClass('d-none');
					$('##enableFeaturedImage').prop('checked',false).prop('disabled',false);
				}
			</cfif>
		});
	</script>
	<cfif val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0 AND local.hasEditRights>
		#local.strSWBFeaturedImages.js#
	</cfif>
	<style>
		.readOnly {
			pointer-events: none;
		}
		.custom-control-input:disabled~.custom-control-label {
			color: ##3b3e66;
		}
	</style>
	</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.catalogDetailsJS#">
<cfoutput>
<div id="priceGrpErr" class="alert alert-danger mb-2 d-none"></div>
<form name="frmSWBProgramCatalog" id="frmSWBProgramCatalog">
	<div id="err_catalog" class="alert alert-danger mb-2 mt-2 d-none"></div>
	<cfif local.hasManageSWBRatesRights>	
		<div class="form-group mt-2 mb-3">
			<div class="custom-control custom-switch">
				<input type="checkbox" name="allowCatalogOption" id="allowCatalogOption" onclick="showAllowCatalogSettings();" class="custom-control-input" <cfif (local.isPublisher AND len(local.qryBundle.dateCatalogStart) is not 0)> checked="checked"<cfelseif !local.isPublisher AND local.qryBundleSyndicateObj.sellCatalog AND len(local.qryBundle.dateCatalogStart)> checked="checked"<cfelseif !local.isPublisher AND NOT len(local.qryBundle.dateCatalogStart)>disabled</cfif>>
				<label class="custom-control-label" for="allowCatalogOption">
					I want to sell this bundle in the catalog.
				</label>

				<cfif !local.isPublisher AND NOT len(local.qryBundle.dateCatalogStart)>
					<div class="alert alert-danger">
						The Publisher has decided to stop selling the program in the catalog.
					</div>
				</cfif>
				
				<div class="form-group my-2 <cfif local.isPublisher AND len(local.qryBundle.dateCatalogStart) is 0>d-none<cfelseif !local.isPublisher AND (!local.qryBundleSyndicateObj.sellCatalog OR !len(local.qryBundle.dateCatalogStart))>d-none</cfif>" id="allowCatalogHolder">
					<div class="mt-2 mb-3">
						<div class="form-row">
							<div class="col-3">
								<div class="form-label-group mb-0">
									<div class="input-group dateFieldHolder">
										<input type="text" name="dateStartCatalog" id="dateStartCatalog" value="#DateFormat(local.qryBundle.dateCatalogStart,'m/d/yyyy')#" class="form-control dateControl <cfif NOT local.isPublisher>readOnly</cfif>">
										<cfif local.isPublisher>
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="dateStartCatalog"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('dateStartCatalog');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
										</cfif>
										<label for="dateStartCatalog">Start Date</label>
									</div>
								</div>
							</div>
							<div class="col-3">
								<div class="form-label-group mb-0">
									<div class="input-group dateFieldHolder">
										<input type="text" name="dateEndCatalog" id="dateEndCatalog" value="#DateFormat(local.qryBundle.dateCatalogEnd,'m/d/yyyy')#" class="form-control dateControl <cfif NOT local.isPublisher>readOnly</cfif>">
										<cfif local.isPublisher>
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="dateEndCatalog"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('dateEndCatalog');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
										</cfif>
										<label for="dateEndCatalog">End Date</label>
									</div>
								</div>
							</div>
							<div class="col-auto align-self-center">12:00AM</div>
						</div>
					</div>

					<cfset local.isPriceBasedOnActual = local.isPublisher ? local.qryBundle.isPriceBasedOnActual : local.qryOptInBundleRateSettings.isPriceBasedOnActual>
					<div class="custom-control custom-switch">
						<input type="checkbox" name="isPriceBasedOnActualOption" id="isPriceBasedOnActualOption" disabled class="custom-control-input" <cfif local.isPriceBasedOnActual is 1> checked="checked"</cfif>>
						<label class="custom-control-label text-body" for="isPriceBasedOnActualOption">
							Registrant must qualify for rate based on their group membership. <i class="fa-solid fa-info-circle pl-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="" data-original-title="We'll automatically toggle this setting if any of your Rates have qualifying groups listed."></i>
						</label>
					</div>
					<cfif local.keyExists("strRevenueGLAcctWidget")>
						<div class="mt-2">#local.strRevenueGLAcctWidget.html#</div>
					</cfif>

					<cfset local.freeRateDisplay = local.isPublisher ? local.qryBundle.freeRateDisplay : local.qryOptInBundleRateSettings.freeRateDisplay>
					<div class="form-group mt-3">
						<div class="form-label-group">
							<select name="freeRateDisplayCatalog" id="freeRateDisplayCatalog" class="form-control">
								<option value="FREE" <cfif local.freeRateDisplay eq "FREE">selected</cfif>>Display amount as FREE</option>
								<option value="$0.00" <cfif local.freeRateDisplay eq "$0.00">selected</cfif>>Display amount as $0.00</option>
								<option value="" <cfif local.freeRateDisplay eq "">selected</cfif>>Hide amount</option>
							</select>
							<label for="freeRateDisplayCatalog">How should we display no-charge enrollments of this program?</label>
						</div>
					</div>
					<cfif local.qryBundle.allowOptInRateChange IS 0 AND NOT local.isPublisher>
						<div class="text-center my-1 alert alert-info">
							The publisher of this bundle prevents adding, removing, or changing rates.<br/>
							You may, however, set group permissions to these rates if desired.
						</div>
					<cfelse>
						<div class="toolButtonBar mt-4">
							<cfif local.hasSWBRateChangeRights>
								<div id="manageRatesActionBtn"><a href="##" onclick="editSWBRate(0);return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Add Rate."><i class="fa-regular fa-circle-plus"></i> Add Rate</a></div>
							
								<div id="copyRatesActionBtn"><a href="##" onclick="copyRatesFromSWProgramPrompt();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to copy rates from other bundle."><i class="fa-regular fa-copy"></i> Copy Rates from Other Bundle</a></div>
							</cfif>
						</div>
					</cfif>

					<div class="my-3">
						<table id="swProgramRatesTableCatalog" class="table table-sm table-bordered table-hover" style="width:100%;">
							<thead>
								<tr>
									<th id="columnid"></th>
									<th>Rate Name</th>
									<th>Rate</th>
									<th>Actions</th>
								</tr>
							</thead>
						</table>
					</div>

					<div class="form-group mb-3">
						<div class="custom-control custom-switch">
							<input type="checkbox" name="isFeatured" id="isFeatured" class="custom-control-input" <cfif local.isFeaturedProgram> checked="checked"</cfif>>
							<label class="custom-control-label" for="isFeatured">
								I want to advertise the bundle on the catalog's homepage in the "Featured Offerings" section.
							</label>
						</div>
					</div>

					<cfif local.isPublisher>
						<cfif val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0>
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="enableFeaturedImage" id="enableFeaturedImage" onclick="addSWBFtdImg();" class="custom-control-input">
									<label class="custom-control-label" for="enableFeaturedImage">I want to customize the registration page by replacing the default bundle image with a custom image.</label>
									<div id="swbFtdImgContainer" class="mt-3">#local.strSWBFeaturedImages.html#</div>
								</div>
							</div>
						</cfif>

						<div class="form-group mb-3">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="enableSponsor" id="enableSponsor" onclick="showBundleSponsor();" class="custom-control-input" <cfif arrayLen(local.getSponsor.arrincludedsponsors)> checked="checked" disabled</cfif>>
								<label class="custom-control-label" for="enableSponsor">
									I want to customize the registration page by adding a bundle sponsor.
								</label>

								<div class="form-group <cfif !arrayLen(local.getSponsor.arrincludedsponsors)>d-none</cfif> pt-2" id="enableSponsorHolder">
									<div class="form-label-group">
										#local.strSponsors.html#
									</div>
								</div>
							</div>
						</div>
						
						<div class="form-group mb-3">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="includeSpeakers" id="includeSpeakers" class="custom-control-input" <cfif local.qryBundle.includeSpeakers EQ true> checked="checked"</cfif>>
								<label class="custom-control-label" for="includeSpeakers">
									I want to include the speaker profiles on the registration page.
								</label>
							</div>
						</div>
					</cfif>
				</div>
			</div>
		</div>
	</cfif>
</form>

<cfif local.hasManageSWBRatesRights>
	<script id="mc_swRateGroupingTemplate" type="text/x-handlebars-template">
		<div id="err_rategrouping" class="alert alert-danger mb-2 d-none"></div>
		<form name="frmGrouping" id="frmGrouping" onsubmit="saveSWBRateGrouping();return false;">
		<input type="hidden" name="rateGroupingID" id="rateGroupingID" value="{{rateGroupingID}}">
		<div class="form-group">
			<div class="form-label-group">
				<input type="text" name="rateGrouping" id="rateGrouping" value="{{rateGrouping}}" class="form-control" maxlength="200" autocomplete="off">
				<label for="rateGrouping">Rate Grouping</label>
			</div>
		</div>
		</form>
	</script>
</cfif>
<div id="divSWBCatalogDetailsSaveLoading" style="display:none;">
	<div class="text-center">
		<br/>
		<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
		<br/><br/>
		<b>Please wait while we save these catalog details.</b>
		<br/>
	</div>
</div>
</cfoutput>