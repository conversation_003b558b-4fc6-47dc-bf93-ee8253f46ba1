<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20120731 at Fri Apr  8 13:26:23 2016
 By ,,,
Copyright (c) 2000 Adobe Systems Incorporated. All Rights Reserved. U.S. Patent Des. pending.
</metadata>
<defs>
<font id="MyriadPro-Regular" horiz-adv-x="300" >
  <font-face 
    font-family="Myriad Pro"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 5 3 3 4 3 2 2 4"
    ascent="750"
    descent="-250"
    x-height="484"
    cap-height="674"
    bbox="-46 -250 1126 881"
    underline-thickness="50"
    underline-position="-50"
    unicode-range="U+0020-FB04"
  />
<missing-glyph horiz-adv-x="500" 
d="M0 0v700h500v-700h-500zM250 395l170 255h-340zM280 350l170 -255v510zM80 50h340l-170 255zM50 605v-510l170 255z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="809" 
d="M705 690l-26 -66q-41 26 -98 26q-121 0 -121 -142v-24h282v-441q0 -140 -58 -199q-27 -27 -69 -41t-81 -14l-9 69q67 5 95 37q19 21 26.5 59.5t7.5 122.5v340h-193v-417h-88v417h-204v-417h-88v417h-67v67h67v17q0 103 58 161q47 47 125 47q43 0 85 -21l-25 -65
q-28 17 -60 17q-40 0 -63 -26q-33 -33 -33 -113v-17h205v27q0 97 55.5 153.5t156.5 56.5q73 0 120 -31z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="522" 
d="M169 0h-88v417h-67v67h67v27q0 97 55.5 153.5t156.5 56.5q34 0 69 -9t54 -23l-25 -67q-41 28 -102 28q-121 0 -121 -142v-24h282v-441q0 -140 -58 -199q-27 -27 -69 -41t-81 -14l-9 69q67 5 95 37q19 21 26.5 59.5t7.5 122.5v340h-193v-417z" />
    <glyph glyph-name="ff" unicode="ff" horiz-adv-x="583" 
d="M460 0h-88v417h-203v-417h-88v417h-67v67h67v14q0 111 54 166q47 45 123 45q37 0 69 -17l-19 -66q-24 13 -51 13q-89 0 -89 -135v-20h204v23q0 115 57 169q46 45 117 45q41 0 75 -14l-11 -68q-26 11 -57 11q-94 0 -94 -140v-26h117v-67h-116v-417z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="523" 
d="M169 0h-88v417h-67v67h67v27q0 97 55.5 153.5t156.5 56.5q34 0 69 -9t54 -23l-25 -67q-41 28 -102 28q-121 0 -121 -142v-24h282v-484h-88v417h-193v-417z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="523" 
d="M169 0h-88v417h-67v67h67v21q0 109 69 167q57 49 155 49q87 0 146 -32v-689h-88v640q-24 13 -72 13q-65 0 -94 -40t-29 -104v-25h114v-67h-113v-417z" />
    <glyph glyph-name="ffi" unicode="ffi" horiz-adv-x="815" 
d="M705 690l-26 -66q-41 26 -98 26q-121 0 -121 -142v-24h282v-484h-88v417h-193v-417h-88v417h-204v-417h-88v417h-67v67h67v17q0 103 58 161q47 47 125 47q43 0 85 -21l-25 -65q-28 17 -60 17q-40 0 -63 -26q-33 -33 -33 -113v-17h205v27q0 97 55.5 153.5t156.5 56.5
q73 0 120 -31z" />
    <glyph glyph-name="ffl" unicode="ffl" horiz-adv-x="815" 
d="M349 691l-24 -66q-25 15 -60 15q-41 0 -64 -26q-33 -34 -33 -112v-18h205v21q0 109 69 167q57 49 155 49q86 0 145 -32v-689h-88v640q-24 13 -72 13q-64 0 -93 -40t-29 -104v-25h114v-67h-113v-417h-88v417h-204v-417h-88v417h-67v67h67v11q0 110 57 166q50 48 130 48
q42 0 81 -18z" />
    <glyph glyph-name=".notdef" horiz-adv-x="500" 
d="M0 0v700h500v-700h-500zM250 395l170 255h-340zM280 350l170 -255v510zM80 50h340l-170 255zM50 605v-510l170 255z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="212" 
 />
    <glyph glyph-name="space" unicode="&#xa0;" horiz-adv-x="212" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="230" 
d="M149 193h-68l-14 481h96zM115 -11h-1q-25 0 -41.5 18t-16.5 44q0 27 16.5 44.5t42.5 17.5q27 0 43 -17.5t16 -44.5t-16 -44.5t-43 -17.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="337" 
d="M51 692h86l-16 -240h-54zM200 692h86l-16 -240h-54z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="497" 
d="M188 255h104l19 145h-104zM154 0h-60l26 196h-83v59h92l19 145h-87v59h95l26 191h59l-26 -191h105l25 191h59l-25 -191h82v-59h-91l-18 -145h86v-59h-95l-26 -196h-60l26 196h-104z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="513" 
d="M281 -86h-63v101q-93 2 -152 44l24 67q63 -43 142 -43q54 0 87 27.5t33 70.5q0 40 -27.5 68t-85.5 51q-83 32 -124 72t-41 102t41 106t109 55v100h63v-97q75 -2 130 -35l-25 -66q-61 35 -126 35q-53 0 -79.5 -26t-26.5 -61q0 -37 27.5 -61t96.5 -53q80 -33 117.5 -75
t37.5 -107q0 -63 -41.5 -111t-116.5 -60v-104z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="792" 
d="M187 661h1q68 0 108.5 -51t40.5 -143q0 -98 -44 -151t-111 -53q-65 0 -108 51t-44 144q0 94 44.5 148.5t112.5 54.5zM184 608h-1q-39 0 -61.5 -41.5t-22.5 -105.5q-2 -63 21.5 -104t63.5 -41q42 0 63 40t21 107q0 64 -20.5 104.5t-63.5 40.5zM231 -12h-56l383 674h56z
M611 392h1q69 0 109 -50.5t40 -142.5q0 -99 -44 -152.5t-111 -53.5q-65 0 -108 51t-44 145q0 95 44.5 149t112.5 54zM608 339h-1q-39 0 -61.5 -41t-22.5 -105q-2 -64 21.5 -105.5t63.5 -41.5q42 0 63 40.5t21 108.5q0 64 -20.5 104t-63.5 40z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="605" 
d="M602 0h-104q-11 11 -63 65q-83 -76 -197 -76q-95 0 -151 55t-56 134q0 125 132 197v3q-57 74 -57 141q0 66 46 116t126 50q64 0 106.5 -39t42.5 -104q0 -54 -34.5 -97.5t-113.5 -82.5v-3q118 -137 164 -188q45 69 65 197h80q-29 -168 -97 -249q17 -18 111 -119zM253 55h1
q82 0 138 61q-88 94 -189 212q-87 -54 -87 -137q0 -58 38.5 -97t98.5 -39zM272 625h-1q-39 0 -61.5 -28.5t-22.5 -69.5q0 -55 51 -121q57 32 84.5 62t27.5 70q0 35 -20 61t-58 26z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="188" 
d="M51 692h86l-16 -240h-54z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="284" 
d="M195 694h68q-125 -170 -125 -410q0 -226 125 -405h-68q-131 173 -131 406q1 236 131 409z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="284" 
d="M88 -121h-68q125 177 125 408t-125 407h68q131 -171 131 -407q-1 -233 -131 -408z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="415" 
d="M269 685l60 -35l-95 -124v-2l151 20v-68l-151 18v-2l96 -121l-63 -35l-61 140h-2l-64 -141l-57 36l95 122v2l-148 -19v68l147 -19v2l-94 123l61 34l62 -139h2z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="596" 
d="M266 532h64v-235h226v-60h-226v-237h-64v237h-226v60h226v235z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="207" 
d="M78 -117l-62 -7q40 110 60 240l98 10q-18 -64 -46.5 -136t-49.5 -107z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="307" 
d="M30 302h247v-64h-247v64z" />
    <glyph glyph-name="hyphen" unicode="&#xad;" horiz-adv-x="307" 
d="M30 302h247v-64h-247v64z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="207" 
d="M111 -11h-1q-25 0 -41.5 18t-16.5 45t17 45t43 18t42.5 -17.5t16.5 -45.5q0 -27 -16.5 -45t-43.5 -18z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="343" 
d="M66 -40h-67l278 725h69z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="513" 
d="M253 -11h-1q-97 0 -156 88t-60 246q0 160 62.5 249t164.5 89t158.5 -87t56.5 -242q0 -165 -59 -254t-166 -89zM256 57h1q65 0 98.5 71.5t33.5 199.5q0 125 -32.5 195t-99.5 70q-60 0 -96 -71.5t-36 -193.5q-1 -126 34.5 -198.5t96.5 -72.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="513" 
d="M236 0v568h-2l-113 -61l-17 67l142 76h75v-650h-85z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="513" 
d="M460 0h-415v54l69 67q136 129 189 200t53 140q0 56 -30 91.5t-98 35.5q-72 0 -138 -55l-28 62q78 66 184 66q97 0 147.5 -56t50.5 -133q0 -83 -53 -159.5t-171 -189.5l-51 -48v-2h291v-73z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="513" 
d="M42 33l24 67q67 -40 144 -40t112.5 38.5t34.5 87.5q-1 64 -49.5 97t-118.5 33h-49v66h49q55 0 100 28.5t45 81.5q0 42 -28.5 70t-85.5 28q-70 0 -130 -43l-24 64q70 50 171 50q90 0 138 -45t48 -110q0 -52 -31 -92.5t-89 -61.5v-2q63 -12 104 -56t41 -110
q0 -82 -63 -138.5t-174 -56.5q-102 0 -169 44z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="513" 
d="M400 0h-83v178h-302v57l290 415h95v-404h91v-68h-91v-178zM104 246h213v217q0 51 3 102h-3q-26 -49 -54 -96l-159 -221v-2z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="513" 
d="M433 650v-74h-248l-25 -167q30 4 53 4q82 0 140 -35q41 -23 67 -66t26 -101q0 -96 -68.5 -159t-173.5 -63q-98 0 -162 40l22 67q61 -36 140 -36q64 0 109 39t44 103q0 65 -46.5 104t-139.5 39q-49 0 -91 -7l42 312h311z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="513" 
d="M416 660v-72q-26 1 -66 -5q-99 -16 -156.5 -79.5t-69.5 -150.5h2q56 76 158 76q87 0 141.5 -58t54.5 -152t-59.5 -162t-157.5 -68q-104 0 -166.5 75.5t-62.5 201.5q0 185 110 293q82 81 208 96q33 5 64 5zM264 57h1q56 0 91 43t35 113q0 69 -36.5 109t-97.5 40
q-40 0 -74.5 -22t-52.5 -58q-9 -18 -9 -38q1 -83 38.5 -135t104.5 -52z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="513" 
d="M57 650h410v-58l-283 -592h-90l281 575v2h-318v73z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="513" 
d="M262 661h1q90 0 140 -47t50 -113q0 -102 -108 -152v-3q131 -52 131 -168q0 -84 -63 -136.5t-158 -52.5q-99 0 -158.5 50.5t-59.5 124.5q0 120 128 175v3q-52 24 -78 63t-26 83q0 76 57 124.5t144 48.5zM256 53h1q59 0 94.5 32.5t35.5 83.5q0 56 -36.5 89t-106.5 53
q-56 -16 -86.5 -52t-30.5 -83q-2 -51 33.5 -87t95.5 -36zM259 598h-1q-53 0 -83 -30t-30 -74q-1 -46 31.5 -75t92.5 -45q45 15 72 45.5t27 72.5q0 44 -27.5 75t-81.5 31z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="513" 
d="M96 -10v72q29 -2 71 4q80 9 136 62q69 64 86 173h-3q-57 -70 -154 -70q-86 0 -139 57t-53 143q0 94 63 162t160 68q99 0 157 -74t58 -200q0 -197 -112 -305q-77 -74 -189 -87q-52 -6 -81 -5zM256 594h-1q-55 0 -91 -44.5t-36 -113.5q0 -61 34 -99.5t91 -38.5q87 0 129 68
q7 14 7 32q1 88 -33 142t-100 54z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="207" 
d="M112 342h-1q-25 0 -41.5 18t-16.5 44q0 28 16.5 45.5t42.5 17.5q27 0 43 -17.5t16 -45.5q0 -27 -16 -44.5t-43 -17.5zM112 -11h-1q-25 0 -41.5 18t-16.5 44q0 28 16.5 45.5t42.5 17.5q27 0 43 -17.5t16 -45.5q0 -27 -16 -44.5t-43 -17.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="207" 
d="M78 -117l-61 -6q40 108 60 239l97 10q-18 -64 -46.5 -136t-49.5 -107zM119 342h-1q-25 0 -41.5 18t-16.5 44q0 28 16.5 45.5t42.5 17.5q27 0 43 -17.5t16 -45.5q0 -27 -16 -44.5t-43 -17.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="596" 
d="M66 239v54l464 239v-69l-391 -196v-2l391 -196v-69z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="596" 
d="M556 337h-516v60h516v-60zM556 141h-516v60h516v-60z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="596" 
d="M530 295v-57l-464 -238v69l394 196v2l-394 196v69z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="406" 
d="M219 192h-78l-2 25q-10 87 62 173q37 44 53.5 74.5t16.5 62.5q0 39 -24.5 62.5t-70.5 23.5q-62 0 -101 -31l-24 63q54 41 143 41q81 0 124 -43t43 -105q0 -46 -21.5 -84.5t-64.5 -88.5q-61 -74 -57 -148zM179 -11h-1q-25 0 -41.5 18t-16.5 44q0 28 16.5 45.5t42.5 17.5
q27 0 43 -17.5t16 -45.5q0 -27 -16 -44.5t-43 -17.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="737" 
d="M449 255l21 112q-20 8 -50 8q-66 0 -113 -53.5t-47 -126.5q0 -34 17 -55t49 -21q41 0 77.5 43t45.5 93zM508 -22l16 -43q-76 -40 -178 -40q-127 0 -215 86t-88 227q0 159 101.5 271.5t263.5 112.5q129 0 209.5 -81.5t80.5 -207.5q0 -108 -51.5 -173t-125.5 -65
q-33 0 -54 22t-20 66h-3q-54 -88 -142 -88q-45 0 -76.5 34.5t-31.5 91.5q0 96 67.5 166.5t169.5 70.5q63 0 107 -21l-33 -181q-22 -110 29 -112q41 -2 74.5 49t33.5 133q0 112 -64 181t-178 69q-126 0 -213 -92t-87 -240q0 -123 72 -197t185 -74q87 0 151 35z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="542" 
d="M76 2v662q63 15 159 15q125 0 184 -49q57 -42 57 -119q0 -49 -30.5 -89t-83.5 -59v-3q56 -13 97.5 -56t41.5 -112q0 -80 -58 -135q-67 -63 -232 -63q-77 0 -135 8zM163 606v-218h79q67 0 106 32t39 82q0 110 -147 110q-49 0 -77 -6zM163 323v-257q24 -4 73 -4
q75 0 124 31.5t49 99.5q0 65 -49 97.5t-125 32.5h-72z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="580" 
d="M529 92l17 -70q-62 -32 -178 -32q-147 0 -239.5 90t-92.5 251q0 158 97.5 255.5t254.5 97.5q99 0 160 -29l-22 -72q-56 28 -135 28q-120 0 -191.5 -73.5t-71.5 -203.5q0 -125 68.5 -197.5t189.5 -72.5q85 0 143 28z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="666" 
d="M75 2v663q94 14 186 14q183 0 273 -84q96 -87 96 -242q0 -165 -97 -262q-99 -97 -299 -97q-99 0 -159 8zM163 601v-533q22 -4 88 -4q139 -1 213 74t74 212q1 122 -68.5 191t-204.5 69q-61 0 -102 -9z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="487" 
d="M75 0v674h363v-73h-275v-224h254v-72h-254v-305h-88z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="646" 
d="M590 354v-324q-96 -37 -208 -37q-159 0 -252 89q-94 91 -94 251q0 153 99 250.5t266 97.5q105 0 169 -31l-22 -71q-61 29 -149 29q-124 0 -197.5 -72.5t-73.5 -198.5q0 -127 70.5 -199.5t189.5 -72.5q80 0 116 18v201h-136v70h222z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="652" 
d="M75 674h88v-282h326v282h88v-674h-88v316h-326v-316h-88v674z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="370" 
d="M213 230v444h88v-451q0 -234 -204 -234q-56 0 -93 16l12 71q33 -13 74 -13q62 0 92.5 36.5t30.5 130.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="542" 
d="M76 0v674h87v-325h3q23 33 53 72l206 253h108l-244 -286l263 -388h-103l-221 331l-65 -74v-257h-87z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="472" 
d="M75 0v674h88v-601h288v-73h-376z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="804" 
d="M660 0l-16 296q-11 205 -11 291h-2q-48 -155 -85 -256l-119 -327h-66l-110 321q-50 149 -77 262h-2q-3 -132 -13 -298l-18 -289h-83l47 674h111l115 -326q39 -117 68 -228h2q25 96 72 228l120 326h111l42 -674h-86z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="658" 
d="M158 0h-82v674h96l215 -341q78 -126 120 -219l3 1q-10 113 -10 276v283h82v-674h-88l-214 342q-84 133 -125 225l-3 -1q6 -102 6 -278v-288z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="532" 
d="M76 0v665q74 14 167 14q127 0 189 -56q60 -52 60 -140q0 -91 -53 -144q-69 -74 -206 -74q-46 0 -70 6v-271h-87zM163 603v-261q27 -7 72 -7q79 0 124 37.5t45 105.5q0 65 -42.5 98.5t-116.5 33.5q-56 0 -82 -7z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="689" 
d="M657 -26l-25 -72q-112 28 -258 77q-30 10 -41 10q-127 5 -212 95t-85 246q0 160 87.5 257.5t225.5 97.5q137 0 220 -95t83 -246q0 -125 -52 -208.5t-140 -114.5v-4q108 -27 197 -43zM343 60h1q100 0 158 80t58 200q0 110 -56 192t-157 82q-70 0 -121 -40.5t-74.5 -103.5
t-23.5 -138q0 -113 58 -192.5t157 -79.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="538" 
d="M76 0v664q73 15 167 15q134 0 196 -56q53 -47 53 -128q0 -63 -35.5 -107.5t-91.5 -63.5v-3q72 -25 99 -136q37 -156 51 -185h-90q-16 28 -44 161q-15 70 -46 99.5t-90 31.5h-82v-292h-87zM163 603v-245h89q69 0 110.5 35t41.5 93q0 63 -41.5 94t-113.5 31q-56 0 -86 -8z
" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="493" 
d="M42 34l23 72q70 -43 155 -43q66 0 103.5 31t37.5 83q0 47 -29 77.5t-98 56.5q-178 61 -178 190q0 80 60.5 131.5t157.5 51.5q90 0 148 -32l-24 -72q-52 32 -127 32q-63 0 -95 -30.5t-32 -70.5q0 -45 30.5 -73t103.5 -56q89 -35 130 -80.5t41 -117.5q0 -83 -61 -138.5
t-174 -55.5q-48 0 -96 12.5t-76 31.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="497" 
d="M204 0v600h-205v74h499v-74h-206v-600h-88z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="558" 
d="M320 0h-96l-221 674h95l105 -332q51 -166 72 -252h2q26 105 77 251l114 333h93z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="846" 
d="M277 0h-91l-171 674h92l80 -341q32 -138 50 -233h2q11 68 55 234l90 340h91l82 -342q36 -154 47 -231h2q13 70 54 233l89 340h89l-191 -674h-91l-85 350q-33 135 -44 221h-2q-14 -89 -52 -220z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="571" 
d="M546 0h-101l-87 149q-44 71 -77 133h-2q-23 -47 -73 -134l-81 -148h-100l206 341l-198 333h101l89 -158q42 -73 62 -114h3q24 50 61 114l91 158h101l-205 -328z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="541" 
d="M314 0h-88v286l-214 388h100l95 -186q55 -113 67 -139h2q12 28 68 139l97 186h98l-225 -387v-287z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="553" 
d="M30 0v51l374 547v3h-342v73h455v-53l-372 -545v-3h377v-73h-492z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="284" 
d="M264 -112h-183v798h183v-55h-114v-688h114v-55z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="341" 
d="M342 -40h-68l-272 725h68z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="284" 
d="M20 686h183v-798h-183v55h114v688h-114v55z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="596" 
d="M536 189h-70l-167 388h-2l-167 -388h-69l206 461h63z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="500" 
d="M0 -75h500v-50h-500v50z" />
    <glyph glyph-name="grave" unicode="`" 
d="M22 693h96l88 -143h-62z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="569" 
d="M73 125v585h88v-303h2q51 88 165 88q90 0 147 -68.5t57 -177.5q0 -123 -65 -191.5t-153 -68.5q-109 0 -162 91h-2l-5 -80h-76q4 44 4 125zM161 281v-87q0 -17 3 -32q13 -46 49 -74.5t83 -28.5q69 0 108 51t39 135q0 77 -38 129t-106 52q-46 0 -83 -29.5t-50 -78.5
q-5 -18 -5 -37z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="448" 
d="M403 84l15 -66q-59 -28 -139 -28q-109 0 -175 68t-66 179q0 113 71.5 185.5t188.5 72.5q74 0 122 -26l-20 -67q-44 23 -102 23q-80 0 -125.5 -52.5t-45.5 -130.5q0 -82 47.5 -131.5t120.5 -49.5q56 0 108 23z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="564" 
d="M403 710h88v-585q0 -81 4 -125h-79l-4 84h-2q-21 -42 -64 -68.5t-100 -26.5q-89 0 -148.5 68.5t-59.5 177.5q-1 118 62.5 189t154.5 71q53 0 91 -21t55 -52h2v288zM403 203v84q0 22 -4 38q-10 43 -44.5 72t-82.5 29q-67 0 -106 -52.5t-39 -133.5q0 -78 37.5 -129
t105.5 -51q45 0 81 28.5t48 77.5q4 15 4 37z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="292" 
d="M169 0h-88v417h-67v67h67v23q0 115 57 169q46 45 117 45q41 0 75 -14l-11 -68q-26 11 -57 11q-94 0 -94 -140v-26h117v-67h-116v-417z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="559" 
d="M487 352v-281q0 -157 -69 -222q-65 -58 -177 -58q-101 0 -161 40l22 68q59 -39 142 -39q72 0 114 41t42 129v53h-2q-21 -36 -60.5 -58.5t-93.5 -22.5q-90 0 -148 67.5t-58 169.5q0 117 65 186.5t154 69.5q106 0 151 -84h2l3 73h78q-4 -45 -4 -132zM399 207v87q0 24 -5 39
q-13 41 -44.5 67.5t-78.5 26.5q-64 0 -104 -50t-40 -134q0 -74 37.5 -124t105.5 -50q42 0 75.5 24.5t46.5 66.5q7 20 7 47z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="555" 
d="M73 0v710h88v-302h2q23 41 63 62q41 25 90 25q28 0 54.5 -9.5t54 -31t44 -64.5t16.5 -102v-288h-88v279q0 64 -27 103.5t-85 39.5q-41 0 -73 -25t-45 -63q-6 -14 -6 -42v-292h-88z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM116 566h-1q-23 0 -38 16t-15 38q0 24 15.5 39.5t39.5 15.5t39 -15.5t15 -39.5q0 -23 -15 -38.5t-40 -15.5z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="243" 
d="M-36 -211l-10 69q67 5 96 37q19 21 26.5 59.5t7.5 122.5v407h88v-441q0 -140 -58 -199q-27 -27 -69 -41t-81 -14zM127 566h-1q-23 0 -38 16t-15 38q0 24 15.5 39.5t39.5 15.5t39 -15.5t15 -39.5q0 -23 -15 -38.5t-40 -15.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="469" 
d="M161 710v-448h2q20 28 42 55l143 167h105l-187 -199l213 -285h-107l-166 232l-45 -50v-182h-88v710h88z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="236" 
d="M73 0v710h88v-710h-88z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="834" 
d="M73 0v353q0 51 -4 131h77l4 -78h3q52 89 153 89q48 0 84 -26.5t52 -70.5h2q19 36 55 63q45 34 106 34q27 0 53 -9.5t51 -31.5t40.5 -65.5t15.5 -103.5v-285h-86v274q0 72 -26.5 110.5t-78.5 38.5q-36 0 -64 -22.5t-41 -56.5q-7 -23 -7 -44v-300h-86v291q0 60 -26 96
t-75 36q-38 0 -68 -26t-41 -62q-7 -18 -7 -43v-292h-86z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="555" 
d="M73 0v353q0 78 -4 131h78l5 -79h2q20 38 62 64t98 26q28 0 55.5 -9.5t54.5 -31t44 -64t17 -101.5v-289h-88v279q0 64 -27 104t-85 40q-41 0 -73 -26t-45 -64q-6 -17 -6 -41v-292h-88z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="569" 
d="M73 -198v524q0 97 -4 158h79l5 -83h2q55 94 171 94q89 0 147.5 -68.5t58.5 -177.5q0 -124 -64.5 -192t-155.5 -68q-103 0 -149 76h-2v-263h-88zM161 281v-84q0 -21 4 -36q11 -46 47.5 -74.5t83.5 -28.5q69 0 108 51t39 136q0 76 -38 128t-105 52q-46 0 -83.5 -30
t-50.5 -79q-5 -28 -5 -35z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="563" 
d="M403 -198v272h-2q-21 -38 -61.5 -61.5t-96.5 -23.5q-87 0 -146 68t-59 177q0 126 66.5 193.5t152.5 67.5q106 0 149 -84h2l3 73h84q-4 -53 -4 -133v-549h-88zM403 197v91q0 22 -4 37q-11 43 -45 71.5t-81 28.5q-67 0 -106.5 -51.5t-39.5 -134.5q0 -77 37 -128.5
t106 -51.5q43 0 77 25.5t49 70.5q7 21 7 42z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="327" 
d="M73 0v333q0 91 -4 151h77l4 -95h3q16 48 53 77t82 29q8 0 24 -2v-83q-18 2 -30 2q-46 0 -77.5 -31t-40.5 -81q-3 -20 -3 -41v-259h-88z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="396" 
d="M40 24l21 66q57 -34 117 -34q45 0 69 20t24 52q0 31 -20 50.5t-69 37.5q-129 47 -129 137q0 60 46 101t121 41q72 0 118 -30l-21 -63q-48 28 -99 28q-38 0 -59 -19t-21 -47q0 -29 20.5 -46t70.5 -37q64 -24 95.5 -58.5t31.5 -86.5q0 -66 -48 -106t-132 -40q-78 0 -136 34
z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="331" 
d="M93 573l86 27v-116h126v-67h-126v-260q0 -94 66 -94q31 0 51 6l4 -67q-34 -12 -78 -12q-61 0 -95 37q-34 38 -34 127v263h-75v67h75v89z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="481" 
d="M13 484h94l95 -271q26 -73 39 -125h3q12 43 41 125l94 271h92l-190 -484h-84z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="736" 
d="M18 484h90l65 -245q28 -117 35 -155h3q10 45 43 154l79 246h74l75 -242q26 -82 43 -158h3q10 60 37 157l69 243h87l-156 -484h-80l-74 231q-29 95 -43 160h-2q-15 -72 -44 -161l-78 -230h-80z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="463" 
d="M16 484h98l69 -104q27 -41 49 -79h3q18 32 49 80l67 103h96l-165 -234l169 -250h-100l-71 109q-20 30 -52 84h-2q-9 -17 -51 -84l-70 -109h-97l172 247z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="471" 
d="M9 484h96l105 -286q18 -47 32 -99h2q1 3 12.5 41.5t18.5 59.5l96 284h93l-132 -345q-49 -128 -85.5 -196t-80.5 -107q-55 -46 -108 -56l-22 73q42 13 77 43q45 35 74 98q7 16 7 21q0 8 -7 23z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="428" 
d="M18 0v51l219 285q38 46 64 76v2h-263v70h369v-55l-217 -282q-35 -46 -62 -75v-2h283v-70h-393z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="284" 
d="M28 263v51q48 0 64.5 20.5t16.5 53.5q0 28 -8 84t-8 83q0 66 39.5 98.5t106.5 32.5h20v-55h-17q-80 -1 -80 -86q0 -15 7 -71q8 -50 8 -76q1 -88 -72 -108v-2q73 -18 72 -109q0 -27 -8 -77q-7 -56 -7 -72q0 -86 80 -87h17v-55h-21q-145 0 -145 137q0 33 9 82q7 65 7 81
q0 75 -81 75z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="239" 
d="M86 750h67v-1000h-67v1000z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="284" 
d="M256 314v-51q-81 0 -81 -75q0 -25 8 -81q8 -54 8 -82q0 -137 -145 -137h-21v55h17q80 1 80 87q0 16 -7 72q-8 50 -8 77q-1 91 72 109v2q-73 20 -72 108q0 26 8 76q7 56 7 71q0 85 -80 86h-17v55h20q67 0 106.5 -32.5t39.5 -98.5q0 -27 -8 -83t-8 -84q0 -33 16.5 -53.5
t64.5 -20.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="596" 
d="M109 211h-60q-2 72 31.5 111t87.5 39q30 0 58.5 -9.5t83.5 -35.5q87 -42 124 -42q52 0 54 84h59q3 -78 -29.5 -114.5t-84.5 -36.5q-50 0 -145 44q-6 3 -19.5 9.5t-19 9.5t-16.5 8t-17.5 7t-15.5 5t-16.5 4t-15.5 1q-58 0 -59 -84z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="230" 
d="M163 -195h-96l14 478h68zM115 488h1q26 0 42 -17.5t16 -44.5t-16 -44.5t-43 -17.5q-26 0 -42.5 17.5t-16.5 44.5t16.5 44.5t42.5 17.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="513" 
d="M331 -15h-63v102q-88 10 -144 70q-57 62 -57 169q0 97 55 164.5t146 82.5v101h63v-99q62 -1 109 -25l-20 -67q-44 25 -105 25q-72 0 -116 -51.5t-44 -127.5q0 -80 44.5 -127.5t112.5 -47.5q64 0 114 25l15 -64q-43 -25 -110 -29v-101z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="513" 
d="M471 0h-416v50q52 26 83 73t31 102q0 37 -5 70h-102v63h93q-11 66 -11 108q0 88 54 141.5t138 53.5q69 0 109 -25l-19 -66q-36 20 -90 20q-56 0 -82.5 -34t-26.5 -91q0 -48 11 -107h143v-63h-135q6 -73 -4 -112q-16 -63 -69 -108v-2h298v-73z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="513" 
d="M256 534h1q70 0 115 -40l71 75l45 -46l-79 -71q35 -47 35 -118q0 -76 -35 -122l75 -74l-44 -44l-69 76q-45 -42 -117 -42q-71 0 -114 40l-67 -74l-43 44l72 70q-37 50 -37 121q0 73 39 123l-76 71l45 46l68 -76q48 41 115 41zM255 471h-1q-53 0 -85.5 -40.5t-32.5 -100.5
q0 -67 36.5 -103t81.5 -36q48 0 83.5 35.5t35.5 106.5q0 56 -31.5 97t-86.5 41z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="513" 
d="M292 0h-83v176h-155v49h155v73h-155v49h131l-169 303h94l104 -206q24 -51 40 -91h3q16 45 40 93l108 204h92l-179 -303h130v-49h-156v-73h156v-49h-156v-176z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="239" 
d="M86 174h67v-350h-67v350zM86 674h67v-350h-67v350z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="519" 
d="M131 352v-1q0 -57 105 -94q91 -34 114 -49q38 30 38 77q0 62 -89 93q-104 40 -123 51q-45 -31 -45 -77zM418 650l-22 -57q-50 32 -119 32q-45 0 -74.5 -20.5t-29.5 -53.5q0 -35 29.5 -54.5t95.5 -41.5q80 -26 121 -60.5t41 -96.5q0 -70 -64 -121q38 -36 38 -89
q0 -73 -56 -111.5t-134 -38.5q-100 0 -157 42l24 57q56 -40 133 -40q49 0 80 21.5t31 59.5q0 35 -26 57.5t-96 47.5q-88 30 -131 64t-43 93q0 69 74 118q-16 11 -26.5 34.5t-10.5 48.5q0 65 52.5 103.5t131.5 38.5t138 -33z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" 
d="M57 570h-1q-21 0 -35 15t-14 36t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5q0 -21 -14 -36t-35 -15zM243 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="677" 
d="M340 648h1q126 0 213 -90t87 -218q0 -129 -87.5 -219.5t-213.5 -90.5q-127 0 -215.5 90.5t-88.5 219.5q0 128 88.5 218t215.5 90zM340 607h-1q-107 0 -180 -78t-73 -192q0 -112 73 -189t181 -77q106 -1 178.5 77t72.5 192q0 112 -72.5 189.5t-178.5 77.5zM474 493
l-12 -39q-43 23 -97 23q-68 0 -104 -38.5t-36 -101.5q0 -61 37 -101t104 -40q61 0 101 26l12 -38q-47 -31 -123 -31q-85 0 -134 51.5t-49 129.5q0 83 56 134.5t135 51.5q36 0 67 -8.5t43 -18.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="346" 
d="M304 267h-59l-5 42h-2q-41 -47 -102 -47q-48 0 -75.5 27t-27.5 68q0 54 53 84.5t148 28.5v10q0 8 -2.5 17t-9.5 20t-23.5 18t-39.5 7q-50 0 -85 -26l-16 42q46 33 114 33q127 0 127 -140v-106q0 -38 5 -78zM235 373v54q-136 4 -136 -66q0 -24 17 -37.5t41 -13.5
q45 0 68 36q10 14 10 27z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="419" 
d="M232 436l-133 -184l134 -184h-71l-133 184l132 184h71zM396 436l-133 -184l134 -184h-71l-133 184l132 184h71z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="596" 
d="M40 399h516v-278h-64v218h-452v60z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="419" 
d="M184 499v-65h-38v164q38 5 63 5q38 0 56 -13q17 -12 17 -35q0 -28 -34 -39v-2q21 -7 29 -40q5 -27 12 -40h-40q-4 5 -13 40q-4 25 -34 25h-18zM185 573v-47h18q38 0 38 24q0 25 -35 25q-13 0 -21 -2zM210 683h1q70 0 118.5 -47.5t48.5 -116.5t-49 -117t-119 -48
q-71 0 -120 48t-49 117t49 116.5t120 47.5zM210 650h-1q-53 0 -89.5 -38.5t-36.5 -92.5q0 -55 37 -93t91 -38q53 -1 89 37.5t36 92.5q0 55 -36 93.5t-90 38.5z" />
    <glyph glyph-name="macron" unicode="&#xaf;" 
d="M38 643h225v-57h-225v57z" />
    <glyph glyph-name="macron" unicode="&#x2c9;" 
d="M38 643h225v-57h-225v57z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="318" 
d="M162 685h1q59 0 95 -38.5t36 -91.5q0 -58 -40 -95.5t-94 -37.5q-57 0 -94 37.5t-37 91.5q0 57 38.5 95.5t94.5 38.5zM161 638h-1q-36 0 -56.5 -26.5t-20.5 -60.5q0 -35 22 -58.5t56 -23.5t56.5 24t22.5 61q0 32 -20 58t-59 26z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="596" 
d="M266 571h64v-194h226v-60h-226v-200h-64v200h-226v60h226v194zM40 60h516v-60h-516v60z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="311" 
d="M12 444v37l63 58q68 62 97 100t29 74q0 30 -19.5 49.5t-56.5 19.5q-42 0 -84 -31l-21 46q50 41 123 41q65 0 98.5 -33.5t33.5 -78.5q0 -51 -32 -94t-101 -103l-30 -28v-2h170v-55h-270z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="305" 
d="M45 760l-17 47q48 31 115 31q61 0 91.5 -27t30.5 -67q0 -30 -20.5 -53.5t-56.5 -36.5v-2q41 -5 67 -32t26 -64q0 -49 -42.5 -83t-115.5 -34q-72 0 -115 28l18 50q38 -26 93 -26q43 0 66 20.5t22 46.5q0 35 -31 52.5t-75 17.5h-27v46h28q34 0 62.5 16t28.5 47
q0 20 -16 34.5t-47 14.5q-43 0 -85 -26z" />
    <glyph glyph-name="acute" unicode="&#xb4;" 
d="M189 693h96l-122 -143h-62z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="553" 
d="M403 76h-3q-18 -34 -54 -59.5t-85 -25.5q-76 0 -106 54v-60q0 -134 11 -183h-79q-14 42 -14 178v504h88v-286q0 -61 27 -98.5t82 -37.5q41 0 71.5 23.5t43.5 56.5q8 21 8 46v296h88v-350q0 -41 10 -58.5t36 -18.5l-7 -64q-15 -4 -31 -4q-75 0 -86 87z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="512" 
d="M293 -48h-62v313h-7q-68 0 -128.5 50.5t-61.5 139.5q0 42 12.5 79t42 70t86 52.5t134.5 19.5q77 0 117 -8v-716h-62v664h-71v-664z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="207" 
d="M103 200h-1q-25 0 -41.5 18t-16.5 44q0 28 17 45.5t43 17.5t42.5 -17.5t16.5 -45.5q0 -27 -16.5 -44.5t-43.5 -17.5z" />
    <glyph glyph-name="periodcentered" unicode="&#x2219;" horiz-adv-x="207" 
d="M103 200h-1q-25 0 -41.5 18t-16.5 44q0 28 17 45.5t43 17.5t42.5 -17.5t16.5 -45.5q0 -27 -16.5 -44.5t-43.5 -17.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" 
d="M135 3h55l-33 -53q29 -3 49.5 -22t20.5 -47q0 -42 -29.5 -61.5t-75.5 -19.5q-40 0 -68 18l15 42q26 -14 54 -14q19 0 31.5 7.5t11.5 23.5q0 35 -78 42z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="244" 
d="M177 444h-70v328h-2l-72 -35l-11 52l94 44h61v-389z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="355" 
d="M179 591h1q71 0 114 -45.5t43 -116.5q-1 -81 -48.5 -124t-111.5 -43q-68 0 -113.5 45t-45.5 117q0 75 47 121t114 46zM177 541h-1q-44 0 -67.5 -35t-23.5 -81q0 -48 27.5 -80.5t65.5 -32.5q40 0 66 32t26 83q0 43 -24 78.5t-69 35.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="419" 
d="M157 252l-134 184h71l133 -184l-133 -184h-70zM320 252l-134 184h71l133 -184l-133 -184h-70z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="759" 
d="M198 267h-70v328h-2l-72 -35l-11 52l94 44h61v-389zM195 -11h-57l380 672h56zM663 0h-66v103h-200v39l193 253h73v-243h60v-49h-60v-103zM597 152v119q0 8 3 62h-2q-4 -6 -15.5 -25.5t-17.5 -28.5l-96 -126l1 -1h127z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="759" 
d="M184 267h-70v328h-2l-72 -35l-11 52l94 44h61v-389zM175 -11h-56l379 672h56zM440 0v37l63 59q68 62 97 100t29 74q0 30 -19.5 49.5t-56.5 19.5q-42 0 -84 -31l-21 46q50 41 123 41q65 0 98.5 -33.5t33.5 -78.5q0 -51 -32 -93.5t-101 -104.5l-30 -28v-2h170v-55h-270z
" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="759" 
d="M67 583l-18 46q46 32 114 32q61 0 92 -27t31 -67q0 -30 -20 -53.5t-56 -36.5v-2q41 -5 67 -32t26 -65q0 -48 -42.5 -82t-115.5 -34q-72 0 -115 28l18 49q40 -25 93 -25q43 0 66 20t22 46q0 36 -31 53.5t-76 17.5h-27v46h28q34 0 62.5 16t28.5 47q0 20 -16 34.5t-48 14.5
q-44 0 -83 -26zM232 -11h-57l380 672h56zM674 0h-66v103h-200v39l193 253h73v-243h60v-49h-60v-103zM608 152v119q0 8 3 62h-2q-4 -6 -15.5 -25.5t-17.5 -28.5l-97 -126l1 -1h128z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="406" 
d="M237 488h1q26 0 42 -17.5t16 -45.5q0 -27 -16 -44.5t-43 -17.5q-26 0 -42.5 17.5t-16.5 44.5q0 28 16.5 45.5t42.5 17.5zM197 286h78l2 -26q10 -87 -62 -173q-37 -44 -53.5 -74.5t-16.5 -62.5q0 -39 24.5 -62.5t70.5 -23.5q60 0 102 31l23 -63q-56 -41 -143 -41
q-81 0 -124 43t-43 105q0 46 21.5 84.5t64.5 88.5q61 74 57 148z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122zM160 827h106l93 -116h-71z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122zM348 827h106l-128 -117h-71z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122zM276 824h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122zM232 715h-47q-1 46 17.5 74t50.5 28q21 0 59 -20q32 -18 48 -18q12 0 18.5 8t8.5 31h46q2 -97 -67 -97q-23 0 -61 19q-36 19 -47 19q-21 0 -26 -44z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122zM217 719h-1q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5zM402 719h-1q-21 0 -34.5 15t-13.5 36
t14.5 35.5t35.5 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122zM307 879h1q45 0 72 -26t27 -65q0 -38 -28 -63t-72 -25t-72 25.5t-28 62.5q0 39 27.5 65t72.5 26zM306 843h-1q-21 0 -34 -16t-13 -39q0 -20 13.5 -35.5
t34.5 -15.5q22 0 35.5 15t13.5 38q0 22 -13.5 37.5t-35.5 15.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="788" 
d="M89 0h-90l308 674h421v-73h-286l26 -215h251v-72h-240l30 -241h246v-73h-322l-30 237h-206zM226 308h168l-22 194q-10 80 -12 109h-4q-17 -45 -43 -106z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="585" 
d="M530 91l17 -69q-57 -31 -175 -32l-25 -40q30 -6 50 -26t20 -49q0 -41 -29.5 -61t-73.5 -20q-40 0 -71 17l14 45q23 -14 55 -14q18 0 30.5 8t12.5 23q0 35 -78 43l42 77q-128 15 -205.5 102.5t-77.5 235.5q0 158 97.5 255.5t255.5 97.5q101 0 159 -29l-21 -72
q-58 28 -136 28q-120 0 -191.5 -73.5t-71.5 -204.5q0 -125 68.5 -197.5t190.5 -72.5q84 0 143 28z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM120 827h106l93 -116h-71z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM303 827h106l-128 -117h-71z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM223 824h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM163 721h-1q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5zM348 721h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5t34.5 -14.5t13.5 -35.5
q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674zM-26 827h106l93 -116h-71z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674zM159 827h106l-128 -117h-71z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674zM85 824h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674zM28 721h-1q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5zM213 721h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="671" 
d="M-2 307v70h82v291q95 14 186 14q184 0 272 -83q96 -87 96 -246q0 -166 -97 -263q-99 -97 -298 -97q-96 0 -159 7v307h-82zM348 377v-70h-180v-240q22 -4 87 -4q140 0 213.5 74.5t73.5 214.5q1 124 -68 192.5t-204 68.5q-61 0 -102 -9v-227h180z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="658" 
d="M158 0h-82v674h96l215 -341q78 -126 120 -219l3 1q-10 113 -10 276v283h82v-674h-88l-214 342q-84 133 -125 225l-3 -1q6 -102 6 -278v-288zM257 715h-47q-1 46 17.5 74t50.5 28q21 0 59 -20q32 -18 48 -18q12 0 18.5 8t8.5 31h46q2 -97 -67 -97q-23 0 -61 19
q-36 19 -47 19q-21 0 -26 -44z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM200 830h106l93 -116h-71z
" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM385 830h106l-128 -117
h-71z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM310 827h67l105 -114h-74
l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM268 717h-47
q-1 46 17.5 74t50.5 28q21 0 59 -20q32 -18 48 -18q12 0 18.5 8t8.5 31h46q2 -97 -67 -97q-23 0 -61 19q-36 19 -47 19q-21 0 -26 -44z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM253 721h-1
q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5zM438 721h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="596" 
d="M40 483l44 44l214 -219l214 219l44 -44l-214 -219l214 -220l-44 -44l-214 219l-214 -219l-44 44l215 220z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="689" 
d="M112 -43l-49 39l62 87q-89 97 -89 250q0 158 88 255t223 97q95 0 168 -53l59 81l53 -36l-62 -86q87 -95 87 -248q0 -168 -89 -261t-218 -93q-96 0 -170 54zM176 158l296 410q-55 47 -127 47q-104 0 -162 -82t-58 -196q0 -97 49 -180zM513 515l-295 -408q51 -48 126 -48
q101 0 160 81t59 200q0 98 -47 175h-3z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397zM184 827h106l93 -116h-71z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397zM367 827h106l-128 -117h-71z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397zM295 824h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397zM234 721h-1q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5z
M419 721h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="541" 
d="M314 0h-88v286l-214 388h100l95 -186q55 -113 67 -139h2q12 28 68 139l97 186h98l-225 -387v-287zM321 822h106l-128 -117h-71z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="531" 
d="M76 0v674h86v-131q38 7 96 7q117 0 178 -55q55 -50 55 -130q0 -104 -69.5 -159t-183.5 -55q-41 0 -76 5v-156h-86zM162 473v-246q28 -6 75 -6q80 0 123.5 36t43.5 98q0 63 -41.5 94.5t-112.5 31.5q-56 0 -88 -8z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="548" 
d="M161 0h-88v452q0 154 72 218q55 51 145 51q76 0 127.5 -45t51.5 -124q0 -46 -23 -82q-72 -30 -72 -81q0 -16 7 -31.5t16 -25.5t28 -30q26 -26 40 -42.5t27.5 -47t13.5 -64.5q0 -71 -48.5 -115t-130.5 -44q-65 0 -106 21l16 67q40 -20 84 -20q46 0 72 24t26 60
q0 52 -54 103q-39 37 -58.5 66.5t-19.5 66.5q0 80 89 123q9 18 9 43q0 51 -28.5 80t-75.5 29q-60 0 -90 -46t-30 -150v-456z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28zM95 693h96l88 -143h-62z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28zM275 693h96l-122 -143h-62z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28zM206 693h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28zM163 567h-48q-1 48 17 76t51 28q20 0 62 -22q30 -17 43 -17q24 0 27 42h47q2 -101 -68 -101q-22 0 -62 21q-30 18 -43 18q-23 0 -26 -45z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28zM148 570h-1q-21 0 -35 15t-14 36t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5q0 -21 -14 -36t-35 -15zM334 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28zM240 723h1q44 0 71.5 -27t27.5 -67q0 -39 -28.5 -65.5t-71.5 -26.5q-44 0 -73 26.5t-29 65.5q0 40 28 67t74 27zM239 685h-1q-22 0 -35.5 -16.5t-13.5 -39.5q0 -21 14 -37.5t36 -16.5q23 0 37.5 16t14.5 39
t-14.5 39t-37.5 16z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="773" 
d="M734 233h-325q-2 -84 41 -130t114 -46q74 0 132 27l16 -62q-63 -33 -159 -33q-64 0 -113.5 28t-70.5 79h-3q-23 -50 -70 -78.5t-109 -28.5q-72 0 -111.5 40.5t-39.5 97.5q0 85 76 131t215 44v20q0 12 -4 27.5t-14.5 35t-35 32.5t-58.5 13q-72 0 -124 -36l-21 58
q68 43 151 43q123 0 156 -102h2q24 48 68 75t101 27q42 0 76 -15.5t54.5 -39.5t34.5 -55t19 -59t5 -54t-3 -39zM329 172v71q-206 4 -206 -109q0 -37 24 -58.5t60 -21.5q45 -1 75.5 23t41.5 60q5 18 5 35zM409 296h244q1 28 -8 57.5t-37 54.5t-69 25q-60 0 -93.5 -43.5
t-36.5 -93.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="447" 
d="M403 83l15 -65q-49 -27 -126 -28l-25 -40q30 -6 50 -25t20 -48q0 -42 -29.5 -62.5t-74.5 -20.5q-41 0 -70 17l15 45q23 -14 55 -14q43 0 43 32q-1 34 -78 42l42 77q-92 11 -147 77.5t-55 166.5q0 113 71.5 185.5t188.5 72.5q74 0 122 -26l-20 -67q-44 23 -102 23
q-80 0 -125.5 -52.5t-45.5 -131.5q0 -83 47.5 -132t120.5 -49q53 0 108 23z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
M136 693h96l88 -143h-62z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
M304 693h96l-122 -143h-62z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
M233 693h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
M176 570h-1q-21 0 -35 15t-14 36t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5q0 -21 -14 -36t-35 -15zM362 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM-28 693h96l88 -143h-62z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM155 693h96l-122 -143h-62z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM86 693h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM24 570h-1q-21 0 -35 15t-14 36t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5q0 -21 -14 -36t-35 -15zM210 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="541" 
d="M267 55h1q65 0 104 52.5t39 135.5q0 92 -34 134q-41 47 -106 47q-71 0 -107 -58t-36 -129q0 -77 39.5 -129.5t99.5 -52.5zM116 507l-19 43l116 55q-35 26 -104 60l39 55q79 -37 140 -81l127 61l22 -44l-106 -50q82 -67 127 -159q43 -86 43 -195q0 -67 -20 -119t-54 -82.5
t-74.5 -46t-85.5 -15.5q-100 0 -164.5 67.5t-64.5 179.5q0 111 66.5 179t156.5 69q65 0 113 -43l2 3q-45 73 -118 129z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="555" 
d="M73 0v353q0 78 -4 131h78l5 -79h2q20 38 62 64t98 26q28 0 55.5 -9.5t54.5 -31t44 -64t17 -101.5v-289h-88v279q0 64 -27 104t-85 40q-41 0 -73 -26t-45 -64q-6 -17 -6 -41v-292h-88zM203 567h-48q-1 48 17 76t51 28q20 0 62 -22q30 -17 43 -17q24 0 27 42h47
q2 -101 -68 -101q-22 0 -62 21q-30 18 -43 18q-23 0 -26 -45z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53zM146 693h96l88 -143h-62z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53zM310 693h96l-122 -143h-62z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53zM244 693h61l96 -143h-67l-58 94h-2
l-58 -94h-66z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53zM200 567h-48q-1 48 17 76t51 28
q20 0 62 -22q30 -17 43 -17q24 0 27 42h47q2 -101 -68 -101q-22 0 -62 21q-30 18 -43 18q-23 0 -26 -45z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53zM182 570h-1q-21 0 -35 15t-14 36
t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5q0 -21 -14 -36t-35 -15zM368 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="596" 
d="M298 387h-1q-23 0 -37.5 15.5t-14.5 39.5q0 25 15 40.5t38 15.5q24 0 38.5 -15.5t14.5 -40.5q0 -24 -14.5 -39.5t-38.5 -15.5zM556 237h-516v60h516v-60zM298 36h-1q-23 0 -37.5 15.5t-14.5 39.5q0 25 15 40.5t38 15.5q24 0 38.5 -15.5t14.5 -40.5q0 -24 -14.5 -39.5
t-38.5 -15.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="549" 
d="M276 495h1q69 0 122 -34l42 60l39 -27l-43 -62q74 -72 74 -186q0 -124 -71 -190.5t-165 -66.5q-71 0 -123 34l-44 -61l-37 30l42 60q-75 70 -75 187t68 186.5t170 69.5zM161 119l198 281q-34 29 -85 29q-74 0 -112 -56.5t-38 -131.5q0 -72 35 -121zM389 362l-198 -279
q34 -28 81 -28q68 0 110.5 53t42.5 135q0 68 -34 118z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88zM141 693h96l88 -143h-62z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88zM322 693h96l-122 -143h-62z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88zM245 693h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88zM186 570h-1q-21 0 -35 15t-14 36t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5
q0 -21 -14 -36t-35 -15zM372 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="471" 
d="M9 484h96l105 -286q18 -47 32 -99h2q1 3 12.5 41.5t18.5 59.5l96 284h93l-132 -345q-49 -128 -85.5 -196t-80.5 -107q-55 -46 -108 -56l-22 73q42 13 77 43q45 35 74 98q7 16 7 21q0 8 -7 23zM285 693h96l-122 -143h-62z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="569" 
d="M73 670h88v-262h2q54 87 164 87q87 1 146 -67.5t59 -179.5q0 -125 -65 -192t-155 -67q-100 0 -149 76h-2v-263h-88v868zM161 281v-90q0 -25 9 -44q15 -41 50 -65t79 -24q65 0 104.5 51.5t39.5 137.5q0 75 -38.5 126.5t-103.5 51.5q-43 0 -79 -25.5t-52 -69.5
q-9 -32 -9 -49z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="471" 
d="M9 484h96l105 -286q18 -47 32 -99h2q1 3 12.5 41.5t18.5 59.5l96 284h93l-132 -345q-49 -128 -85.5 -196t-80.5 -107q-55 -46 -108 -56l-22 73q42 13 77 43q45 35 74 98q7 16 7 21q0 8 -7 23zM155 570h-1q-21 0 -35 15t-14 36t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5
q0 -21 -14 -36t-35 -15zM341 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122zM185 788h240v-55h-240v55z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28zM129 643h225v-57h-225v57z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122zM181 817h52q11 -49 72 -49q62 0 73 49h50q-1 -47 -32 -76.5t-92 -29.5t-91 29t-32 77z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28zM117 682h52q5 -31 23.5 -50t48.5 -19q33 0 51 20t21 49h52q0 -56 -34 -90.5t-90 -34.5q-62 0 -93 36.5t-31 88.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="612" 
d="M359 674l231 -674l-37 -2q-53 -57 -53 -93q0 -23 14.5 -37t36.5 -14q23 0 45 9l14 -44q-32 -24 -80 -24q-43 0 -68.5 22.5t-25.5 64.5q0 53 59 122l-71 208h-239l-69 -212h-91l230 674h104zM203 280h204l-67 194q-14 41 -35 123h-2q-23 -90 -34 -122z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="482" 
d="M414 297v-181q0 -71 7 -116h-25q-39 -52 -39 -96q0 -50 48 -50q24 0 46 10l14 -46q-34 -24 -83 -24q-39 0 -64 22.5t-25 62.5q0 53 48 124l-7 58h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30
t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="580" 
d="M529 92l17 -70q-62 -32 -178 -32q-147 0 -239.5 90t-92.5 251q0 158 97.5 255.5t254.5 97.5q99 0 160 -29l-22 -72q-56 28 -135 28q-120 0 -191.5 -73.5t-71.5 -203.5q0 -125 68.5 -197.5t189.5 -72.5q85 0 143 28zM400 829h106l-128 -117h-71z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="448" 
d="M403 84l15 -66q-59 -28 -139 -28q-109 0 -175 68t-66 179q0 113 71.5 185.5t188.5 72.5q74 0 122 -26l-20 -67q-44 23 -102 23q-80 0 -125.5 -52.5t-45.5 -130.5q0 -82 47.5 -131.5t120.5 -49.5q56 0 108 23zM302 693h96l-122 -143h-62z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="580" 
d="M529 92l17 -70q-62 -32 -178 -32q-147 0 -239.5 90t-92.5 251q0 158 97.5 255.5t254.5 97.5q99 0 160 -29l-22 -72q-56 28 -135 28q-120 0 -191.5 -73.5t-71.5 -203.5q0 -125 68.5 -197.5t189.5 -72.5q85 0 143 28zM314 828h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="448" 
d="M403 84l15 -66q-59 -28 -139 -28q-109 0 -175 68t-66 179q0 113 71.5 185.5t188.5 72.5q74 0 122 -26l-20 -67q-44 23 -102 23q-80 0 -125.5 -52.5t-45.5 -130.5q0 -82 47.5 -131.5t120.5 -49.5q56 0 108 23zM229 693h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="580" 
d="M529 92l17 -70q-62 -32 -178 -32q-147 0 -239.5 90t-92.5 251q0 158 97.5 255.5t254.5 97.5q99 0 160 -29l-22 -72q-56 28 -135 28q-120 0 -191.5 -73.5t-71.5 -203.5q0 -125 68.5 -197.5t189.5 -72.5q85 0 143 28zM348 719h-1q-21 0 -35 15t-14 36q0 20 14.5 35t35.5 15
q20 0 34 -15t14 -35q0 -21 -14 -36t-34 -15z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="448" 
d="M403 84l15 -66q-59 -28 -139 -28q-109 0 -175 68t-66 179q0 113 71.5 185.5t188.5 72.5q74 0 122 -26l-20 -67q-44 23 -102 23q-80 0 -125.5 -52.5t-45.5 -130.5q0 -82 47.5 -131.5t120.5 -49.5q56 0 108 23zM265 570h-1q-21 0 -35.5 15.5t-14.5 36.5t15 36t36 15t35 -15
t14 -36t-14 -36.5t-35 -15.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="580" 
d="M529 92l17 -70q-62 -32 -178 -32q-147 0 -239.5 90t-92.5 251q0 158 97.5 255.5t254.5 97.5q99 0 160 -29l-22 -72q-56 28 -135 28q-120 0 -191.5 -73.5t-71.5 -203.5q0 -125 68.5 -197.5t189.5 -72.5q85 0 143 28zM387 713h-67l-105 114h74l64 -69h2l63 69h71z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="448" 
d="M403 84l15 -66q-59 -28 -139 -28q-109 0 -175 68t-66 179q0 113 71.5 185.5t188.5 72.5q74 0 122 -26l-20 -67q-44 23 -102 23q-80 0 -125.5 -52.5t-45.5 -130.5q0 -82 47.5 -131.5t120.5 -49.5q56 0 108 23zM294 550h-63l-96 143h67l59 -95h2l59 95h66z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="666" 
d="M75 2v663q94 14 186 14q183 0 273 -84q96 -87 96 -242q0 -165 -97 -262q-99 -97 -299 -97q-99 0 -159 8zM163 601v-533q22 -4 88 -4q139 -1 213 74t74 212q1 122 -68.5 191t-204.5 69q-61 0 -102 -9zM343 712h-67l-105 114h74l64 -69h2l63 69h71z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="574" 
d="M403 710h88v-585q0 -81 4 -125h-79l-4 84h-2q-21 -42 -64 -68.5t-100 -26.5q-89 0 -148.5 68.5t-59.5 177.5q-1 118 62.5 189t154.5 71q53 0 91 -21t55 -52h2v288zM403 203v84q0 22 -4 38q-10 43 -44.5 72t-82.5 29q-67 0 -106 -52.5t-39 -133.5q0 -78 37.5 -129
t105.5 -51q45 0 81 28.5t48 77.5q4 15 4 37zM572 522l-37 30q36 40 36 88q0 37 -30 65l62 14q14 -9 25 -29t11 -44q0 -70 -67 -124z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="671" 
d="M-2 307v70h82v291q95 14 186 14q184 0 272 -83q96 -87 96 -246q0 -166 -97 -263q-99 -97 -298 -97q-96 0 -159 7v307h-82zM348 377v-70h-180v-240q22 -4 87 -4q140 0 213.5 74.5t73.5 214.5q1 124 -68 192.5t-204 68.5q-61 0 -102 -9v-227h180z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="564" 
d="M568 552h-77v-427q0 -81 4 -125h-79l-4 84h-2q-21 -42 -64 -68.5t-100 -26.5q-89 0 -148.5 68.5t-59.5 177.5q-1 118 62.5 189t154.5 71q53 0 91 -21t55 -52h2v130h-197v55h197v103h88v-103h77v-55zM403 203v84q0 22 -4 38q-10 43 -44.5 72t-82.5 29q-67 0 -106 -52.5
t-39 -133.5q0 -78 37.5 -129t105.5 -51q45 0 81 28.5t48 77.5q4 15 4 37z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM140 788h240v-55h-240v55z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
M142 643h225v-57h-225v57z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM131 825h52q11 -49 72 -49q62 0 73 49h50q-1 -47 -32 -76.5t-92 -29.5t-91 29t-32 77z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
M130 682h52q5 -31 23.5 -50t48.5 -19q33 0 51 20t21 49h52q0 -56 -34 -90.5t-90 -34.5q-62 0 -93 36.5t-31 88.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM251 721h-1q-21 0 -35 15t-14 36q0 20 14.5 35t35.5 15q20 0 34 -15t14 -35q0 -21 -14 -36t-34 -15z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
M256 570h-1q-21 0 -35.5 15.5t-14.5 36.5t15 36t36 15t35 -15t14 -36t-14 -36.5t-35 -15.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="492" 
d="M446 -138l14 -47q-34 -21 -81 -21q-43 0 -67.5 21t-24.5 60q0 38 24.5 71.5t56.5 54.5h-293v673h365v-73h-277v-213h261v-72h-261v-243h292v-72q-2 0 -7.5 -0.5t-8.5 -0.5q-34 -22 -58 -51t-24 -55q0 -19 12.5 -29.5t32.5 -10.5q24 0 44 8z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="501" 
d="M385 -136l14 -45q-31 -24 -81 -24q-41 0 -65 20.5t-24 57.5q0 34 22 66t50 51v1q-4 0 -12.5 -0.5t-14.5 -0.5q-109 1 -172.5 67.5t-63.5 176.5q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -27 -3 -41h-339q1 -84 47.5 -125.5t117.5 -41.5
q77 0 134 25l16 -63q-13 -6 -46 -17q-33 -14 -64 -46.5t-31 -61.5q0 -20 12 -31t29 -11q28 0 47 10zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM292 710h-67l-105 114h74l64 -69h2l63 69h71z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
M286 550h-63l-96 143h67l59 -95h2l59 95h66z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="646" 
d="M590 354v-324q-96 -37 -208 -37q-159 0 -252 89q-94 91 -94 251q0 153 99 250.5t266 97.5q105 0 169 -31l-22 -71q-61 29 -149 29q-124 0 -197.5 -72.5t-73.5 -198.5q0 -127 70.5 -199.5t189.5 -72.5q80 0 116 18v201h-136v70h222zM326 827h67l105 -114h-74l-63 69h-2
l-64 -69h-71z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="559" 
d="M487 352v-281q0 -157 -69 -222q-65 -58 -177 -58q-101 0 -161 40l22 68q59 -39 142 -39q72 0 114 41t42 129v53h-2q-21 -36 -60.5 -58.5t-93.5 -22.5q-90 0 -148 67.5t-58 169.5q0 117 65 186.5t154 69.5q106 0 151 -84h2l3 73h78q-4 -45 -4 -132zM399 207v87q0 24 -5 39
q-13 41 -44.5 67.5t-78.5 26.5q-64 0 -104 -50t-40 -134q0 -74 37.5 -124t105.5 -50q42 0 75.5 24.5t46.5 66.5q7 20 7 47zM249 696h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="646" 
d="M590 354v-324q-96 -37 -208 -37q-159 0 -252 89q-94 91 -94 251q0 153 99 250.5t266 97.5q105 0 169 -31l-22 -71q-61 29 -149 29q-124 0 -197.5 -72.5t-73.5 -198.5q0 -127 70.5 -199.5t189.5 -72.5q80 0 116 18v201h-136v70h222zM231 827h52q11 -49 72 -49q62 0 73 49
h50q-1 -47 -32 -76.5t-92 -29.5t-91 29t-32 77z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="559" 
d="M487 352v-281q0 -157 -69 -222q-65 -58 -177 -58q-101 0 -161 40l22 68q59 -39 142 -39q72 0 114 41t42 129v53h-2q-21 -36 -60.5 -58.5t-93.5 -22.5q-90 0 -148 67.5t-58 169.5q0 117 65 186.5t154 69.5q106 0 151 -84h2l3 73h78q-4 -45 -4 -132zM399 207v87q0 24 -5 39
q-13 41 -44.5 67.5t-78.5 26.5q-64 0 -104 -50t-40 -134q0 -74 37.5 -124t105.5 -50q42 0 75.5 24.5t46.5 66.5q7 20 7 47zM148 682h52q5 -31 23.5 -50t48.5 -19q33 0 51 20t21 49h52q0 -56 -34 -90.5t-90 -34.5q-62 0 -93 36.5t-31 88.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="646" 
d="M590 354v-324q-96 -37 -208 -37q-159 0 -252 89q-94 91 -94 251q0 153 99 250.5t266 97.5q105 0 169 -31l-22 -71q-61 29 -149 29q-124 0 -197.5 -72.5t-73.5 -198.5q0 -127 70.5 -199.5t189.5 -72.5q80 0 116 18v201h-136v70h222zM362 719h-1q-21 0 -35 15t-14 36
q0 20 14.5 35t35.5 15q20 0 34 -15t14 -35q0 -21 -14 -36t-34 -15z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="559" 
d="M487 352v-281q0 -157 -69 -222q-65 -58 -177 -58q-101 0 -161 40l22 68q59 -39 142 -39q72 0 114 41t42 129v53h-2q-21 -36 -60.5 -58.5t-93.5 -22.5q-90 0 -148 67.5t-58 169.5q0 117 65 186.5t154 69.5q106 0 151 -84h2l3 73h78q-4 -45 -4 -132zM399 207v87q0 24 -5 39
q-13 41 -44.5 67.5t-78.5 26.5q-64 0 -104 -50t-40 -134q0 -74 37.5 -124t105.5 -50q42 0 75.5 24.5t46.5 66.5q7 20 7 47zM280 573h-1q-21 0 -35.5 15.5t-14.5 36.5t15 36t36 15t35 -15t14 -36t-14 -36.5t-35 -15.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="646" 
d="M590 354v-324q-96 -37 -208 -37q-159 0 -252 89q-94 91 -94 251q0 153 99 250.5t266 97.5q105 0 169 -31l-22 -71q-61 29 -149 29q-124 0 -197.5 -72.5t-73.5 -198.5q0 -127 70.5 -199.5t189.5 -72.5q80 0 116 18v201h-136v70h222zM298 -222l-17 41q75 12 75 65
q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="559" 
d="M487 352v-281q0 -157 -69 -222q-65 -58 -177 -58q-101 0 -161 40l22 68q59 -39 142 -39q72 0 114 41t42 129v53h-2q-21 -36 -61 -58.5t-93 -22.5q-90 0 -148 67.5t-58 169.5q0 117 64.5 186.5t153.5 69.5q107 0 152 -84h2l3 73h78q-4 -45 -4 -132zM399 207v87q0 24 -5 39
q-13 41 -45 67.5t-79 26.5q-64 0 -103.5 -50t-39.5 -134q0 -74 37 -124t105 -50q42 0 75.5 24.5t47.5 66.5q7 20 7 47zM338 709l17 -37q-78 -11 -78 -65q0 -32 35 -58l-64 -14q-39 25 -39 71q0 49 38 74.5t91 28.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="652" 
d="M75 674h88v-282h326v282h88v-674h-88v316h-326v-316h-88v674zM292 824h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="555" 
d="M73 0v710h88v-302h2q23 41 63 62q41 25 90 25q28 0 54.5 -9.5t54 -31t44 -64.5t16.5 -102v-288h-88v279q0 64 -27 103.5t-85 39.5q-41 0 -73 -25t-45 -63q-6 -14 -6 -42v-292h-88zM85 843h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="657" 
d="M636 479h-57v-479h-88v301h-325v-301h-88v479h-57v58h57v137h88v-137h325v137h88v-137h57v-58zM491 377v102h-325v-102h325z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="555" 
d="M485 0h-88v279q0 64 -27 103.5t-85 39.5q-41 0 -73 -25t-45 -63q-6 -14 -6 -42v-292h-88v552h-77v55h77v103h88v-103h194v-55h-194v-144h2q22 39 63 62q43 25 90 25q28 0 54.5 -9.5t54 -31t44 -64.5t16.5 -102v-288z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674zM43 715h-47q-1 46 17.5 74t50.5 28q21 0 59 -20q32 -18 48 -18q12 0 18.5 8t8.5 31h46q2 -97 -67 -97q-23 0 -61 19q-36 19 -47 19q-21 0 -26 -44z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM42 567h-48q-1 48 17 76t51 28q20 0 62 -22q30 -17 43 -17q24 0 27 42h47q2 -101 -68 -101q-22 0 -62 21q-30 18 -43 18q-23 0 -26 -45z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674zM1 788h240v-55h-240v55z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM5 643h225v-57h-225v57z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674zM-4 823h52q11 -49 72 -49q62 0 73 49h50q-1 -47 -32 -76.5t-92 -29.5t-91 29t-32 77z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM-4 682h52q5 -31 23.5 -50t48.5 -19q33 0 51 20t21 49h52q0 -56 -34 -90.5t-90 -34.5q-62 0 -93 36.5t-31 88.5z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="239" 
d="M163 674v-674h-30q-42 -59 -42 -98q0 -24 14.5 -37.5t36.5 -13.5q26 0 46 9l13 -46q-31 -23 -81 -23q-42 0 -67 22.5t-25 64.5q0 47 47 122v674h88z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="234" 
d="M116 571h-1q-24 0 -39 16t-15 40q0 23 16 39t40 16t39.5 -15.5t15.5 -39.5t-15 -40t-41 -16zM161 484v-484h-30q-42 -59 -42 -98q0 -24 14.5 -37.5t36.5 -13.5q26 0 46 9l13 -46q-31 -23 -81 -23q-42 0 -67.5 22.5t-25.5 64.5q0 50 48 122v484h88z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674zM121 721h-1q-21 0 -35 15t-14 36q0 20 14.5 35t35.5 15q20 0 34 -15t14 -35q0 -21 -14 -36t-34 -15z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="609" 
d="M75 674h88v-674h-88v674zM452 230v444h88v-451q0 -234 -204 -234q-56 0 -93 16l12 71q33 -13 74 -13q62 0 92.5 36.5t30.5 130.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="477" 
d="M161 0h-88v484h88v-484zM116 566h-1q-23 0 -38 16t-15 38q0 24 15.5 39.5t39.5 15.5t39 -15.5t15 -39.5q0 -23 -15 -38.5t-40 -15.5zM198 -211l-10 69q67 5 96 37q19 21 26.5 59.5t7.5 122.5v407h88v-441q0 -140 -58 -199q-27 -27 -69 -41t-81 -14zM361 566h-1
q-23 0 -38 16t-15 38q0 24 15.5 39.5t39.5 15.5t39 -15.5t15 -39.5q0 -23 -15 -38.5t-40 -15.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="370" 
d="M213 230v444h88v-451q0 -234 -204 -234q-56 0 -93 16l12 71q33 -13 74 -13q62 0 92.5 36.5t30.5 130.5zM219 824h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="243" 
d="M-36 -211l-10 69q67 5 96 37q19 21 26.5 59.5t7.5 122.5v407h88v-441q0 -140 -58 -199q-27 -27 -69 -41t-81 -14zM92 686h58l96 -143h-66l-58 94h-2l-58 -94h-64z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="542" 
d="M76 0v674h87v-325h3q23 33 53 72l206 253h108l-244 -286l263 -388h-103l-221 331l-65 -74v-257h-87zM215 -220l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="469" 
d="M161 710v-448h2q20 28 42 55l143 167h105l-187 -199l213 -285h-107l-166 232l-45 -50v-182h-88v710h88zM178 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="469" 
d="M161 484v-222h2q20 28 42 55l143 167h105l-187 -199l213 -285h-107l-166 232l-45 -50v-182h-88v484h88z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="472" 
d="M75 0v674h88v-601h288v-73h-376zM179 827h106l-128 -117h-71z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="236" 
d="M73 0v710h88v-710h-88zM154 856h106l-128 -117h-71z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="472" 
d="M75 0v674h88v-601h288v-73h-376zM187 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="236" 
d="M73 0v710h88v-710h-88zM52 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="472" 
d="M75 0v674h88v-601h288v-73h-376zM260 504l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="244" 
d="M73 0v710h88v-710h-88zM239 522l-38 30q35 44 35 88q0 37 -30 65l62 14q15 -9 25.5 -29.5t10.5 -44.5q0 -11 -1.5 -21t-3.5 -18.5t-6 -17t-7.5 -14.5t-8.5 -13t-8.5 -11t-9 -9.5l-7.5 -7.5t-8 -7z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="472" 
d="M75 0v674h88v-601h288v-73h-376zM341 320h-1q-21 0 -35 15t-14 36q0 20 14.5 35t35.5 15q20 0 34 -15t14 -35q0 -21 -14 -36t-34 -15z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="272" 
d="M73 0v710h88v-710h-88zM253 341h-1q-20 0 -33.5 14.5t-13.5 35.5q0 20 14 35t34 15q19 0 32.5 -14.5t13.5 -35.5t-13 -35.5t-33 -14.5z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="476" 
d="M455 0h-376v269l-84 -61v67l84 62v337h88v-278l134 97v-68l-134 -97v-255h288v-73z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="242" 
d="M165 0h-88v288l-68 -58v69l68 59v352h88v-285l71 61v-70l-71 -61v-355z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="658" 
d="M158 0h-82v674h96l215 -341q78 -126 120 -219l3 1q-10 113 -10 276v283h82v-674h-88l-214 342q-84 133 -125 225l-3 -1q6 -102 6 -278v-288zM371 827h106l-128 -117h-71z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="555" 
d="M73 0v353q0 78 -4 131h78l5 -79h2q20 38 62 64t98 26q28 0 55.5 -9.5t54.5 -31t44 -64t17 -101.5v-289h-88v279q0 64 -27 104t-85 40q-41 0 -73 -26t-45 -64q-6 -17 -6 -41v-292h-88zM319 693h96l-122 -143h-62z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="658" 
d="M158 0h-82v674h96l215 -341q78 -126 120 -219l3 1q-10 113 -10 276v283h82v-674h-88l-214 342q-84 133 -125 225l-3 -1q6 -102 6 -278v-288zM270 -221l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="555" 
d="M73 0v353q0 78 -4 131h78l5 -79h2q20 38 62 64t98 26q28 0 55.5 -9.5t54.5 -31t44 -64t17 -101.5v-289h-88v279q0 64 -27 104t-85 40q-41 0 -73 -26t-45 -64q-6 -17 -6 -41v-292h-88zM222 -221l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5
t-91 -29.5z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="658" 
d="M158 0h-82v674h96l215 -341q78 -126 120 -219l3 1q-10 113 -10 276v283h82v-674h-88l-214 342q-84 133 -125 225l-3 -1q6 -102 6 -278v-288zM365 705h-67l-105 114h74l64 -69h2l63 69h71z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="555" 
d="M73 0v353q0 78 -4 131h78l5 -79h2q20 38 62 64t98 26q28 0 55.5 -9.5t54.5 -31t44 -64t17 -101.5v-289h-88v279q0 64 -27 104t-85 40q-41 0 -73 -26t-45 -64q-6 -17 -6 -41v-292h-88zM311 550h-63l-96 143h67l59 -95h2l59 95h66z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="555" 
d="M73 0v353q0 78 -4 131h78l5 -79h2q20 38 62 64t98 26q28 0 55.5 -9.5t54.5 -31t44 -64t17 -101.5v-289h-88v279q0 64 -27 104t-85 40q-41 0 -73 -26t-45 -64q-6 -17 -6 -41v-292h-88zM56 536l-38 29q37 41 37 82q0 35 -31 59l64 17q14 -9 24.5 -28.5t10.5 -43.5
q0 -62 -64 -112z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="658" 
d="M158 0h-82v674h96l215 -341q78 -126 120 -219l3 1q-10 113 -10 276v283h82v-636q0 -208 -193 -237l-18 68q60 12 91 42t36 83l-218 347q-85 137 -125 226l-3 -1q6 -102 6 -278v-288z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="555" 
d="M73 0v353q0 78 -4 131h78l5 -79h2q20 38 62 64t98 26q28 0 55.5 -9.5t54.5 -31t44 -64t17 -101.5v-230q0 -109 -52 -167.5t-138 -70.5l-17 70q119 24 119 160v228q0 64 -27 104t-85 40q-41 0 -73 -26t-45 -64q-6 -17 -6 -41v-292h-88z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM226 793h240v-55h-240v55z
" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53zM163 643h225v-57h-225v57z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM221 830h52q11 -49 72 -49
q62 0 73 49h50q-1 -47 -32 -76.5t-92 -29.5t-91 29t-32 77z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53zM151 682h52q5 -31 23.5 -50t48.5 -19
q33 0 51 20t21 49h52q0 -56 -34 -90.5t-90 -34.5q-62 0 -93 36.5t-31 88.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM320 830h96l-112 -112h-64
zM467 830h96l-112 -112h-64z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53zM239 693h86l-110 -136h-55zM383 693
h86l-110 -136h-55z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="894" 
d="M857 73v-73h-345q-18 0 -66 -6q-62 -5 -83 -5q-153 0 -240 96.5t-87 246.5q0 163 92 258t240 95q26 0 88 -5q50 -6 64 -6h322v-73h-277v-214h261v-72h-261v-242h292zM477 74v525q-43 15 -107 15q-111 0 -176.5 -76.5t-65.5 -201.5t67 -200.5t184 -75.5q64 0 98 14z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="863" 
d="M824 227h-329q0 -80 44.5 -124.5t116.5 -44.5q66 0 130 27l16 -63q-58 -33 -157 -33q-137 0 -192 114h-2q-26 -56 -77 -85t-110 -29q-98 0 -162 68.5t-64 181.5q0 116 66 186t166 70q62 0 111 -30.5t71 -85.5h2q54 116 177 116q45 0 80 -15.5t56.5 -39.5t35.5 -55.5
t19 -60t5 -56.5q0 -27 -3 -41zM271 429h-1q-72 0 -107 -57.5t-35 -130.5q0 -79 40.5 -132.5t102.5 -53.5q64 0 103 52t39 135q0 76 -36.5 131.5t-105.5 55.5zM494 290h248q1 32 -7.5 61.5t-37 55t-73.5 25.5q-59 0 -92 -43.5t-38 -98.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="538" 
d="M76 0v664q73 15 167 15q134 0 196 -56q53 -47 53 -128q0 -63 -35.5 -107.5t-91.5 -63.5v-3q72 -25 99 -136q37 -156 51 -185h-90q-16 28 -44 161q-15 70 -46 99.5t-90 31.5h-82v-292h-87zM163 603v-245h89q69 0 110.5 35t41.5 93q0 63 -41.5 94t-113.5 31q-56 0 -86 -8z
M302 830h106l-128 -117h-71z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="327" 
d="M73 0v333q0 91 -4 151h77l4 -95h3q16 48 53 77t82 29q8 0 24 -2v-83q-18 2 -30 2q-46 0 -77.5 -31t-40.5 -81q-3 -20 -3 -41v-259h-88zM217 693h96l-122 -143h-62z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="538" 
d="M76 0v664q73 15 167 15q134 0 196 -56q53 -47 53 -128q0 -63 -35.5 -107.5t-91.5 -63.5v-3q72 -25 99 -136q37 -156 51 -185h-90q-16 28 -44 161q-15 70 -46 99.5t-90 31.5h-82v-292h-87zM163 603v-245h89q69 0 110.5 35t41.5 93q0 63 -41.5 94t-113.5 31q-56 0 -86 -8z
M212 -215l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="327" 
d="M73 0v333q0 91 -4 151h77l4 -95h3q16 48 53 77t82 29q8 0 24 -2v-83q-18 2 -30 2q-46 0 -77.5 -31t-40.5 -81q-3 -20 -3 -41v-259h-88zM60 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="538" 
d="M76 0v664q73 15 167 15q134 0 196 -56q53 -47 53 -128q0 -63 -35.5 -107.5t-91.5 -63.5v-3q72 -25 99 -136q37 -156 51 -185h-90q-16 28 -44 161q-15 70 -46 99.5t-90 31.5h-82v-292h-87zM163 603v-245h89q69 0 110.5 35t41.5 93q0 63 -41.5 94t-113.5 31q-56 0 -86 -8z
M299 713h-67l-105 114h74l64 -69h2l63 69h71z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="327" 
d="M73 0v333q0 91 -4 151h77l4 -95h3q16 48 53 77t82 29q8 0 24 -2v-83q-18 2 -30 2q-46 0 -77.5 -31t-40.5 -81q-3 -20 -3 -41v-259h-88zM214 550h-63l-96 143h67l59 -95h2l59 95h66z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="493" 
d="M42 34l23 72q70 -43 155 -43q66 0 103.5 31t37.5 83q0 47 -29 77.5t-98 56.5q-178 61 -178 190q0 80 60.5 131.5t157.5 51.5q90 0 148 -32l-24 -72q-52 32 -127 32q-63 0 -95 -30.5t-32 -70.5q0 -45 30.5 -73t103.5 -56q89 -35 130 -80.5t41 -117.5q0 -83 -61 -138.5
t-174 -55.5q-48 0 -96 12.5t-76 31.5zM300 830h106l-128 -117h-71z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="396" 
d="M40 24l21 66q57 -34 117 -34q45 0 69 20t24 52q0 31 -20 50.5t-69 37.5q-129 47 -129 137q0 60 46 101t121 41q72 0 118 -30l-21 -63q-48 28 -99 28q-38 0 -59 -19t-21 -47q0 -29 20.5 -46t70.5 -37q64 -24 95.5 -58.5t31.5 -86.5q0 -66 -48 -106t-132 -40q-78 0 -136 34
zM244 693h96l-122 -143h-62z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="493" 
d="M42 34l23 72q70 -43 155 -43q66 0 103.5 31t37.5 83q0 47 -29 77.5t-98 56.5q-178 61 -178 190q0 80 60.5 131.5t157.5 51.5q90 0 148 -32l-24 -72q-52 32 -127 32q-63 0 -95 -30.5t-32 -70.5q0 -45 30.5 -73t103.5 -56q89 -35 130 -80.5t41 -117.5q0 -83 -61 -138.5
t-174 -55.5q-48 0 -96 12.5t-76 31.5zM214 827h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="396" 
d="M40 24l21 66q57 -34 117 -34q45 0 69 20t24 52q0 31 -20 50.5t-69 37.5q-129 47 -129 137q0 60 46 101t121 41q72 0 118 -30l-21 -63q-48 28 -99 28q-38 0 -59 -19t-21 -47q0 -29 20.5 -46t70.5 -37q64 -24 95.5 -58.5t31.5 -86.5q0 -66 -48 -106t-132 -40q-78 0 -136 34
zM169 693h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="493" 
d="M42 34l23 72q70 -43 155 -43q66 0 103.5 31t37.5 83q0 47 -29.5 77.5t-98.5 56.5q-177 62 -177 190q0 80 60.5 131.5t157.5 51.5q90 0 148 -32l-24 -72q-52 32 -127 32q-63 0 -95 -30.5t-32 -70.5q0 -45 30.5 -73t102.5 -56q89 -35 130.5 -80.5t41.5 -117.5
q0 -70 -44.5 -122t-129.5 -67l-28 -45q30 -4 50 -23t20 -47q0 -40 -29.5 -60t-73.5 -20q-39 0 -70 18l15 42q28 -14 55 -14q41 0 41 31q0 35 -77 42l40 71h-7q-44 0 -92.5 12.5t-76.5 31.5z" />
    <glyph glyph-name="Scedilla" unicode="&#xf6c1;" horiz-adv-x="493" 
d="M42 34l23 72q70 -43 155 -43q66 0 103.5 31t37.5 83q0 47 -29.5 77.5t-98.5 56.5q-177 62 -177 190q0 80 60.5 131.5t157.5 51.5q90 0 148 -32l-24 -72q-52 32 -127 32q-63 0 -95 -30.5t-32 -70.5q0 -45 30.5 -73t102.5 -56q89 -35 130.5 -80.5t41.5 -117.5
q0 -70 -44.5 -122t-129.5 -67l-28 -45q30 -4 50 -23t20 -47q0 -40 -29.5 -60t-73.5 -20q-39 0 -70 18l15 42q28 -14 55 -14q41 0 41 31q0 35 -77 42l40 71h-7q-44 0 -92.5 12.5t-76.5 31.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="396" 
d="M40 23l21 66q57 -34 117 -34q46 0 69.5 20t23.5 52t-20 51.5t-69 37.5q-129 47 -129 137q0 60 46 101t121 41q72 0 118 -30l-21 -63q-48 28 -99 28q-38 0 -59 -19t-21 -47q0 -29 20 -46t72 -37q64 -24 95 -58.5t31 -87.5q0 -54 -34 -91.5t-95 -48.5l-27 -45
q29 -3 49.5 -22t20.5 -47q0 -42 -29.5 -61.5t-75.5 -19.5q-40 0 -68 18l15 42q26 -14 54 -14q19 0 31.5 7.5t11.5 23.5q0 35 -78 42l40 71h-3q-70 0 -128 33z" />
    <glyph glyph-name="scedilla" unicode="&#xf6c2;" horiz-adv-x="396" 
d="M40 23l21 66q57 -34 117 -34q46 0 69.5 20t23.5 52t-20 51.5t-69 37.5q-129 47 -129 137q0 60 46 101t121 41q72 0 118 -30l-21 -63q-48 28 -99 28q-38 0 -59 -19t-21 -47q0 -29 20 -46t72 -37q64 -24 95 -58.5t31 -87.5q0 -54 -34 -91.5t-95 -48.5l-27 -45
q29 -3 49.5 -22t20.5 -47q0 -42 -29.5 -61.5t-75.5 -19.5q-40 0 -68 18l15 42q26 -14 54 -14q19 0 31.5 7.5t11.5 23.5q0 35 -78 42l40 71h-3q-70 0 -128 33z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="493" 
d="M42 34l23 72q70 -43 155 -43q66 0 103.5 31t37.5 83q0 47 -29 77.5t-98 56.5q-178 61 -178 190q0 80 60.5 131.5t157.5 51.5q90 0 148 -32l-24 -72q-52 32 -127 32q-63 0 -95 -30.5t-32 -70.5q0 -45 30.5 -73t103.5 -56q89 -35 130 -80.5t41 -117.5q0 -83 -61 -138.5
t-174 -55.5q-48 0 -96 12.5t-76 31.5zM294 713h-67l-105 114h74l64 -69h2l63 69h71z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="396" 
d="M40 24l21 66q57 -34 117 -34q45 0 69 20t24 52q0 31 -20 50.5t-69 37.5q-129 47 -129 137q0 60 46 101t121 41q72 0 118 -30l-21 -63q-48 28 -99 28q-38 0 -59 -19t-21 -47q0 -29 20.5 -46t70.5 -37q64 -24 95.5 -58.5t31.5 -86.5q0 -66 -48 -106t-132 -40q-78 0 -136 34
zM235 550h-63l-96 143h67l59 -95h2l59 95h66z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="497" 
d="M204 0v600h-205v74h499v-74h-206v-600h-88zM181 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x21a;" horiz-adv-x="497" 
d="M204 0v600h-205v74h499v-74h-206v-600h-88zM181 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="331" 
d="M93 573l86 27v-116h126v-67h-126v-260q0 -94 66 -94q31 0 51 6l4 -67q-34 -12 -78 -12q-61 0 -95 37q-34 38 -34 127v263h-75v67h75v89zM122 -227l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x21b;" horiz-adv-x="331" 
d="M93 573l86 27v-116h126v-67h-126v-260q0 -94 66 -94q31 0 51 6l4 -67q-34 -12 -78 -12q-61 0 -95 37q-34 38 -34 127v263h-75v67h75v89zM122 -227l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="497" 
d="M204 0v600h-205v74h499v-74h-206v-600h-88zM284 710h-67l-105 114h74l64 -69h2l63 69h71z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="339" 
d="M93 573l86 27v-116h127v-67h-127v-260q0 -47 15.5 -70.5t51.5 -23.5q30 0 51 6l4 -67q-34 -12 -79 -12q-61 0 -95 37q-34 38 -34 127v263h-75v67h75v89zM277 541l-26 35q38 27 38 74q0 34 -30 59l64 13q35 -23 35 -69q0 -45 -26.5 -73t-54.5 -39z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="497" 
d="M498 600h-206v-222h124v-58h-124v-320h-88v320h-125v58h125v222h-205v74h499v-74z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="331" 
d="M305 417h-126v-115h98v-54h-98v-96q0 -89 66 -89q31 0 51 6l4 -67q-34 -12 -78 -12q-61 0 -95 37q-34 38 -34 126v95h-66v54h66v115h-75v67h75v89l86 27v-116h126v-67z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397zM247 715h-47q-1 46 17.5 74t50.5 28q21 0 59 -20q32 -18 48 -18q12 0 18.5 8t8.5 31h46q2 -97 -67 -97q-23 0 -61 19
q-36 19 -47 19q-21 0 -26 -44z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88zM201 567h-48q-1 48 17 76t51 28q20 0 62 -22q30 -17 43 -17q24 0 27 42h47
q2 -101 -68 -101q-22 0 -62 21q-30 18 -43 18q-23 0 -26 -45z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397zM205 788h240v-55h-240v55z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88zM164 643h225v-57h-225v57z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397zM200 827h52q11 -49 72 -49q62 0 73 49h50q-1 -47 -32 -76.5t-92 -29.5t-91 29t-32 77z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88zM152 682h52q5 -31 23.5 -50t48.5 -19q33 0 51 20t21 49h52q0 -56 -34 -90.5t-90 -34.5
q-62 0 -93 36.5t-31 88.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397zM324 873h1q45 0 72 -26t27 -65q0 -38 -28 -63t-72 -25t-72 25.5t-28 62.5q0 39 27.5 65t72.5 26zM323 837h-1
q-21 0 -34 -16t-13 -39q0 -20 13.5 -35.5t34.5 -15.5q22 0 35.5 15t13.5 38q0 22 -13.5 37.5t-35.5 15.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88zM277 723h1q44 0 71.5 -27t27.5 -67q0 -39 -28.5 -65.5t-71.5 -26.5q-44 0 -73 26.5
t-29 65.5q0 40 28 67t74 27zM276 685h-1q-22 0 -35.5 -16.5t-13.5 -39.5q0 -21 14 -37.5t36 -16.5q23 0 37.5 16t14.5 39t-14.5 39t-37.5 16z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="647" 
d="M75 674h88v-400q0 -108 43 -161t114 -53q77 0 120.5 53t43.5 161v400h88v-394q0 -148 -69.5 -219.5t-185.5 -71.5q-111 0 -176.5 69.5t-65.5 218.5v397zM284 827h96l-112 -112h-64zM431 827h96l-112 -112h-64z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="551" 
d="M478 484v-351q0 -82 4 -133h-78l-5 79h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88zM247 693h86l-110 -136h-55zM391 693h86l-110 -136h-55z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="647" 
d="M415 -135l14 -45q-34 -24 -82 -24q-41 0 -65.5 21.5t-24.5 58.5q0 54 58 114h-7q-107 3 -170 72t-63 215v397h88v-399q0 -108 43 -161t114 -53q78 0 121 53t43 161v399h88v-393q0 -124 -50 -194t-136 -88q-61 -54 -61 -101q0 -20 12.5 -31.5t32.5 -11.5q23 0 45 10z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="551" 
d="M478 484v-351q0 -84 4 -133l-21 3q-41 -57 -41 -98q0 -24 14.5 -37.5t36.5 -13.5q25 0 45 9l14 -46q-32 -23 -81 -23q-42 0 -67.5 22.5t-25.5 64.5q0 50 48 122l-5 76h-2q-20 -36 -61 -63t-99 -27q-31 0 -58.5 10t-53 32.5t-40.5 66t-15 103.5v283h88v-267
q0 -155 108 -155q41 0 72 24t44 57q8 19 8 45v296h88z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="846" 
d="M277 0h-91l-171 674h92l80 -341q32 -138 50 -233h2q11 68 55 234l90 340h91l82 -342q36 -154 47 -231h2q13 70 54 233l89 340h89l-191 -674h-91l-85 350q-33 135 -44 221h-2q-14 -89 -52 -220zM390 824h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="736" 
d="M18 484h90l65 -245q28 -117 35 -155h3q10 45 43 154l79 246h74l75 -242q26 -82 43 -158h3q10 60 37 157l69 243h87l-156 -484h-80l-74 231q-29 95 -43 160h-2q-15 -72 -44 -161l-78 -230h-80zM337 693h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="541" 
d="M314 0h-88v286l-214 388h100l95 -186q55 -113 67 -139h2q12 28 68 139l97 186h98l-225 -387v-287zM237 824h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="471" 
d="M9 484h96l105 -286q18 -47 32 -99h2q1 3 12.5 41.5t18.5 59.5l96 284h93l-132 -345q-49 -128 -85.5 -196t-80.5 -107q-55 -46 -108 -56l-22 73q42 13 77 43q45 35 74 98q7 16 7 21q0 8 -7 23zM207 693h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="541" 
d="M314 0h-88v286l-214 388h100l95 -186q55 -113 67 -139h2q12 28 68 139l97 186h98l-225 -387v-287zM188 714h-1q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5zM373 714h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5
t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="553" 
d="M30 0v51l374 547v3h-342v73h455v-53l-372 -545v-3h377v-73h-492zM326 827h106l-128 -117h-71z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="428" 
d="M18 0v51l219 285q38 46 64 76v2h-263v70h369v-55l-217 -282q-35 -46 -62 -75v-2h283v-70h-393zM261 693h96l-122 -143h-62z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="553" 
d="M30 0v51l374 547v3h-342v73h455v-53l-372 -545v-3h377v-73h-492zM281 720h-1q-21 0 -35 15t-14 36q0 20 14.5 35t35.5 15q20 0 34 -15t14 -35q0 -21 -14 -36t-34 -15z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="428" 
d="M18 0v51l219 285q38 46 64 76v2h-263v70h369v-55l-217 -282q-35 -46 -62 -75v-2h283v-70h-393zM217 570h-1q-21 0 -35.5 15.5t-14.5 36.5t15 36t36 15t35 -15t14 -36t-14 -36.5t-35 -15.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="553" 
d="M30 0v51l374 547v3h-342v73h455v-53l-372 -545v-3h377v-73h-492zM322 710h-67l-105 114h74l64 -69h2l63 69h71z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="428" 
d="M18 0v51l219 285q38 46 64 76v2h-263v70h369v-55l-217 -282q-35 -46 -62 -75v-2h283v-70h-393zM260 550h-63l-96 143h67l59 -95h2l59 95h66z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="513" 
d="M111 338v64h99l6 46q5 41 16.5 75.5t32.5 67t57.5 51.5t84.5 19q44 -2 68 -15l-16 -64q-25 10 -52 10q-91 0 -106 -139l-6 -51h120v-64h-128l-25 -204q-12 -106 -51.5 -162t-122.5 -56q-50 0 -77 17l16 62q23 -11 56 -11q44 0 66 38t31 119l22 197h-91z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="788" 
d="M89 0h-90l308 674h421v-73h-286l26 -215h251v-72h-240l30 -241h246v-73h-322l-30 237h-206zM226 308h168l-22 194q-10 80 -12 109h-4q-17 -45 -43 -106zM489 827h106l-128 -117h-71z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="773" 
d="M734 233h-325q-2 -84 41 -130t114 -46q74 0 132 27l16 -62q-63 -33 -159 -33q-64 0 -113.5 28t-70.5 79h-3q-23 -50 -70 -78.5t-109 -28.5q-72 0 -111.5 40.5t-39.5 97.5q0 85 76 131t215 44v20q0 12 -4 27.5t-14.5 35t-35 32.5t-58.5 13q-72 0 -124 -36l-21 58
q68 43 151 43q123 0 156 -102h2q24 48 68 75t101 27q42 0 76 -15.5t54.5 -39.5t34.5 -55t19 -59t5 -54t-3 -39zM329 172v71q-206 4 -206 -109q0 -37 24 -58.5t60 -21.5q45 -1 75.5 23t41.5 60q5 18 5 35zM409 296h244q1 28 -8 57.5t-37 54.5t-69 25q-60 0 -93.5 -43.5
t-36.5 -93.5zM429 693h96l-122 -143h-62z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="689" 
d="M112 -43l-49 39l62 87q-89 97 -89 250q0 158 88 255t223 97q95 0 168 -53l59 81l53 -36l-62 -86q87 -95 87 -248q0 -168 -89 -261t-218 -93q-96 0 -170 54zM176 158l296 410q-55 47 -127 47q-104 0 -162 -82t-58 -196q0 -97 49 -180zM513 515l-295 -408q51 -48 126 -48
q101 0 160 81t59 200q0 98 -47 175h-3zM393 835h106l-128 -117h-71z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="549" 
d="M276 495h1q69 0 122 -34l42 60l39 -27l-43 -62q74 -72 74 -186q0 -124 -71 -190.5t-165 -66.5q-71 0 -123 34l-44 -61l-37 30l42 60q-75 70 -75 187t68 186.5t170 69.5zM161 119l198 281q-34 29 -85 29q-74 0 -112 -56.5t-38 -131.5q0 -72 35 -121zM389 362l-198 -279
q34 -28 81 -28q68 0 110.5 53t42.5 135q0 68 -34 118zM315 693h96l-122 -143h-62z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="493" 
d="M42 34l23 72q70 -43 155 -43q66 0 103.5 31t37.5 83q0 47 -29 77.5t-98 56.5q-178 61 -178 190q0 80 60.5 131.5t157.5 51.5q90 0 148 -32l-24 -72q-52 32 -127 32q-63 0 -95 -30.5t-32 -70.5q0 -45 30.5 -73t103.5 -56q89 -35 130 -80.5t41 -117.5q0 -83 -61 -138.5
t-174 -55.5q-48 0 -96 12.5t-76 31.5zM186 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="396" 
d="M40 24l21 66q57 -34 117 -34q45 0 69 20t24 52q0 31 -20 50.5t-69 37.5q-129 47 -129 137q0 60 46 101t121 41q72 0 118 -30l-21 -63q-48 28 -99 28q-38 0 -59 -19t-21 -47q0 -29 20.5 -46t70.5 -37q64 -24 95.5 -58.5t31.5 -86.5q0 -66 -48 -106t-132 -40q-78 0 -136 34
zM136 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" 
d="M119 693h61l96 -143h-67l-58 94h-2l-58 -94h-66z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" 
d="M183 550h-63l-96 143h67l59 -95h2l59 95h66z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" 
d="M26 682h52q5 -31 23.5 -50t48.5 -19q33 0 51 20t21 49h52q0 -56 -34 -90.5t-90 -34.5q-62 0 -93 36.5t-31 88.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" 
d="M140 570h-1q-21 0 -35.5 15.5t-14.5 36.5t15 36t36 15t35 -15t14 -36t-14 -36.5t-35 -15.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="400" 
d="M201 723h1q44 0 71.5 -27t27.5 -67q0 -39 -28.5 -65.5t-71.5 -26.5q-44 0 -73 26.5t-29 65.5q0 40 28 67t74 27zM200 685h-1q-22 0 -35.5 -16.5t-13.5 -39.5q0 -21 14 -37.5t36 -16.5q23 0 37.5 16t14.5 39t-14.5 39t-37.5 16z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" 
d="M123 4h57q-53 -67 -53 -100q0 -23 13.5 -36.5t36.5 -13.5q26 0 46 10l14 -46q-32 -23 -80 -23q-43 0 -68.5 22.5t-25.5 63.5q0 58 60 123z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" 
d="M75 567h-48q-1 48 17 76t51 28q20 0 62 -22q30 -17 43 -17q24 0 27 42h47q2 -101 -68 -101q-22 0 -62 21q-30 18 -43 18q-23 0 -26 -45z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M123 693h86l-110 -136h-55zM267 693h86l-110 -136h-55z" />
    <glyph glyph-name="uni037E" unicode="&#x37e;" horiz-adv-x="217" 
d="M92 -116l-62 -7q37 131 46 237l97 5q-12 -55 -37 -125.5t-44 -109.5zM119 323h-1q-27 0 -44 19t-17 47q0 29 17.5 47.5t45.5 18.5t45 -18.5t17 -47.5q0 -28 -17.5 -47t-45.5 -19z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="301" 
d="M137 694h89l-83 -143h-59z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" horiz-adv-x="346" 
d="M152 710h76l-49 -157h-48zM51 573h-1q-20 0 -33 14t-13 34t14 33.5t34 13.5q19 0 32 -13.5t13 -33.5t-13 -34t-33 -14zM292 573h-1q-19 0 -32 14t-13 34q0 19 14 33t33 14t32.5 -13.5t13.5 -33.5t-13 -34t-34 -14z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="615" 
d="M427 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM206 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122zM70 674h79l-35 -155h-52z" />
    <glyph glyph-name="anoteleia" unicode="&#x387;" horiz-adv-x="217" 
d="M108 251h-1q-27 0 -45 19.5t-18 47.5q0 30 18 49.5t46 19.5q29 0 46.5 -19t17.5 -50q0 -28 -17.5 -47.5t-46.5 -19.5z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="561" 
d="M494 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM7 674h79l-34 -155h-52z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="722" 
d="M145 674h88v-282h325v282h88v-674h-88v316h-325v-316h-88v674zM7 674h80l-35 -155h-52z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="309" 
d="M145 674h88v-674h-88v674zM7 674h79l-34 -155h-52z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="728" 
d="M380 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM383 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM7 674h80l-35 -155h-52z
" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="707" 
d="M128 609l15 71q38 -3 85 -28t80 -63q85 -95 104 -247h4q21 141 117 245q35 37 77 62.5t76 30.5l15 -71q-66 -25 -133 -102t-98 -196q-18 -72 -18 -138v-173h-88v174q0 72 -14 136q-28 129 -89.5 206t-132.5 93zM7 674h79l-35 -155h-51z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="742" 
d="M565 72v-3h146v-69h-253v55q66 44 108 120.5t42 175.5q0 102 -58 182.5t-159 80.5t-160.5 -79t-59.5 -185q0 -95 43 -175.5t106 -119.5v-55h-253v69h145v3q-57 47 -94.5 120.5t-37.5 164.5q0 143 89.5 235.5t224.5 92.5q137 0 220.5 -91.5t83.5 -228.5q0 -97 -38 -172
t-95 -121zM8 674h79l-35 -155h-52z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="236" 
d="M161 484v-367q0 -34 9.5 -47t37.5 -12l-5 -64q-15 -5 -39 -5q-43 0 -67 26t-24 93v376h88zM107 696h75l-50 -156h-47zM5 561h-1q-19 0 -32 13.5t-13 32.5t14 32.5t33 13.5t31.5 -13t12.5 -33q0 -19 -12.5 -32.5t-32.5 -13.5zM246 561h-1q-19 0 -31.5 13.5t-12.5 32.5
t13.5 32.5t32.5 13.5t32 -13t13 -33q0 -19 -13 -32.5t-33 -13.5z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="542" 
d="M76 2v662q63 15 159 15q125 0 184 -49q57 -42 57 -119q0 -49 -30.5 -89t-83.5 -59v-3q56 -13 97.5 -56t41.5 -112q0 -80 -58 -135q-67 -63 -232 -63q-77 0 -135 8zM163 606v-218h79q67 0 106 32t39 82q0 110 -147 110q-49 0 -77 -6zM163 323v-257q24 -4 73 -4
q75 0 124 31.5t49 99.5q0 65 -49 97.5t-125 32.5h-72z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="451" 
d="M75 674h362v-73h-274v-601h-88v674z" />
    <glyph glyph-name="Delta" unicode="&#x394;" horiz-adv-x="620" 
d="M26 0v50l234 624h99l232 -624v-50h-565zM119 73h375l-137 371q-36 99 -50 150h-3q-9 -35 -44 -137z" />
    <glyph glyph-name="Delta" unicode="&#xe000;" horiz-adv-x="620" 
d="M26 0v50l234 624h99l232 -624v-50h-565zM119 73h375l-137 371q-36 99 -50 150h-3q-9 -35 -44 -137z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="553" 
d="M30 0v51l374 547v3h-342v73h455v-53l-372 -545v-3h377v-73h-492z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="652" 
d="M75 674h88v-282h326v282h88v-674h-88v316h-326v-316h-88v674z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="689" 
d="M341 -11h-1q-135 0 -219.5 94.5t-84.5 247.5q0 158 88.5 256t224.5 98q137 0 220.5 -95t83.5 -247q0 -166 -89 -260t-223 -94zM343 60h1q101 0 159 80t58 200q0 70 -22.5 130.5t-73 102t-120.5 41.5q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5zM209 383
h271v-72h-271v72z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="542" 
d="M76 0v674h87v-325h3q23 33 53 72l206 253h108l-244 -286l263 -388h-103l-221 331l-65 -74v-257h-87z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="609" 
d="M590 0h-94l-156 464q-16 50 -35 128h-3q-12 -56 -33 -127l-153 -465h-91l230 674h103z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="804" 
d="M660 0l-16 296q-11 205 -11 291h-2q-48 -155 -85 -256l-119 -327h-66l-110 321q-50 149 -77 262h-2q-3 -132 -13 -298l-18 -289h-83l47 674h111l115 -326q39 -117 68 -228h2q25 96 72 228l120 326h111l42 -674h-86z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="658" 
d="M158 0h-82v674h96l215 -341q78 -126 120 -219l3 1q-10 113 -10 276v283h82v-674h-88l-214 342q-84 133 -125 225l-3 -1q6 -102 6 -278v-288z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="540" 
d="M54 674h440v-72h-440v72zM87 385h374v-72h-374v72zM43 73h462v-73h-462v73z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="634" 
d="M558 674v-674h-88v600h-307v-600h-88v674h483z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="532" 
d="M76 0v665q74 14 167 14q127 0 189 -56q60 -52 60 -140q0 -91 -53 -144q-69 -74 -206 -74q-46 0 -70 6v-271h-87zM163 603v-261q27 -7 72 -7q79 0 124 37.5t45 105.5q0 65 -42.5 98.5t-116.5 33.5q-56 0 -82 -7z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="552" 
d="M140 77v-4h384v-73h-498v53l234 282l-222 280v59h454v-73h-341v-3l206 -260z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="497" 
d="M204 0v600h-205v74h499v-74h-206v-600h-88z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="588" 
d="M11 609l15 71q38 -3 85 -28t80 -63q85 -95 104 -247h4q21 141 117 245q35 37 77 62.5t76 30.5l15 -71q-66 -25 -133 -102t-98 -196q-18 -72 -18 -138v-173h-88v174q0 72 -14 136q-28 129 -89.5 206t-132.5 93z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="718" 
d="M400 26v-52h-84v52q-115 10 -197.5 88.5t-82.5 218.5q0 143 86.5 225.5t195.5 91.5v50h84v-50q114 -9 197 -90.5t83 -222.5q0 -140 -83 -221t-199 -90zM317 89v498q-73 -6 -132 -73t-59 -179q0 -105 56 -170.5t135 -75.5zM401 587v-498q78 7 134.5 72.5t56.5 174.5
q0 113 -56.5 178.5t-134.5 72.5z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="571" 
d="M546 0h-101l-87 149q-44 71 -77 133h-2q-30 -61 -73 -134l-81 -148h-100l206 341l-198 333h101l89 -158q42 -73 62 -114h3q24 50 61 114l91 158h101l-205 -328z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="685" 
d="M639 674v-195q0 -127 -69.5 -197t-185.5 -76v-206h-83v206q-51 2 -95 17t-80.5 43t-57.5 76t-21 112v220h85v-205q0 -185 169 -194v399h83v-400q75 5 122.5 56t47.5 158v186h85z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="705" 
d="M528 72v-3h146v-69h-252v55q64 44 106.5 122.5t42.5 174.5q0 102 -58 182t-158 80q-101 0 -161 -78.5t-60 -184.5q0 -92 44 -174.5t106 -121.5v-55h-254v69h146v3q-57 47 -95 121t-38 165q0 143 90 235t225 92q137 0 220 -91t83 -228q0 -96 -38 -171t-95 -123z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="705" 
d="M528 72v-3h146v-69h-252v55q64 44 106.5 122.5t42.5 174.5q0 102 -58 182t-158 80q-101 0 -161 -78.5t-60 -184.5q0 -92 44 -174.5t106 -121.5v-55h-254v69h146v3q-57 47 -95 121t-38 165q0 143 90 235t225 92q137 0 220 -91t83 -228q0 -96 -38 -171t-95 -123z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="239" 
d="M28 711h-1q-21 0 -35 14.5t-14 35.5q0 20 14.5 35t35.5 15q20 0 34 -14.5t14 -35.5t-13.5 -35.5t-34.5 -14.5zM211 711h-1q-21 0 -34.5 14.5t-13.5 35.5t14.5 35.5t35.5 14.5q20 0 34 -14.5t14 -35.5t-13.5 -35.5t-35.5 -14.5zM75 674h88v-674h-88v674z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="588" 
d="M207 697h-1q-21 0 -35 14.5t-14 35.5q0 20 14.5 35t35.5 15q20 0 34 -14.5t14 -35.5t-13.5 -35.5t-34.5 -14.5zM390 697h-1q-21 0 -34.5 14.5t-13.5 35.5t14.5 35.5t35.5 14.5q20 0 34 -14.5t14 -35.5t-13.5 -35.5t-35.5 -14.5zM11 609l15 71q38 -3 85 -28t80 -63
q85 -95 104 -247h4q21 141 117 245q35 37 77 62.5t76 30.5l15 -71q-66 -25 -133 -102t-98 -196q-18 -72 -18 -138v-173h-88v174q0 72 -14 136q-28 129 -89.5 206t-132.5 93z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="551" 
d="M286 686h88l-83 -143h-59zM416 484h73q-13 -133 -13 -346q0 -43 11 -62t37 -20l-7 -63q-9 -4 -30 -4q-76 0 -83 102h-3q-54 -102 -165 -102q-87 0 -142.5 67t-55.5 176q0 115 64.5 189t151.5 74q52 0 91 -25.5t58 -70.5h4q3 48 9 85zM127 235v-1q0 -72 35.5 -123.5
t95.5 -51.5q44 0 80.5 36.5t49.5 86.5q5 22 5 68q0 35 -3 52q-9 51 -43.5 87.5t-81.5 36.5q-60 0 -99 -54t-39 -137z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="440" 
d="M256 686h89l-84 -143h-58zM158 259v4q-46 10 -70.5 37.5t-24.5 63.5q0 53 51 92t142 39q92 0 147 -37l-19 -58q-17 12 -52 21.5t-69 9.5q-51 0 -81.5 -20t-30.5 -53q0 -35 37 -52.5t97 -17.5h59v-62h-61q-69 0 -110.5 -20.5t-41.5 -62.5q0 -41 37.5 -64t93.5 -23
q37 0 77.5 11t64.5 25l16 -59q-28 -19 -77.5 -31.5t-99.5 -12.5q-84 0 -144 36t-60 111q0 44 33 78t86 45z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" horiz-adv-x="555" 
d="M301 686h89l-84 -143h-58zM152 405h2q20 38 62 64t98 26q28 0 55.5 -9.5t54.5 -31t44 -64t17 -101.5v-307q0 -136 11 -180h-84q-15 37 -15 177v300q0 64 -27 104t-85 40q-41 0 -73.5 -26t-44.5 -65q-6 -15 -6 -40v-292h-88v353q0 89 -14 131h79q14 -28 14 -79z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="236" 
d="M133 687h90l-84 -143h-59zM161 484v-367q0 -34 9.5 -47t37.5 -12l-5 -64q-15 -5 -39 -5q-43 0 -67 26t-24 93v376h88z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="524" 
d="M360 484h82q14 -26 25 -79.5t11 -109.5q0 -58 -10.5 -108.5t-34 -97t-68.5 -73.5t-108 -27q-84 0 -137 50t-53 146v136q0 129 -10 163h84q14 -16 14 -90v-197q0 -68 32 -101.5t77 -33.5q62 0 95 59.5t33 165.5q0 60 -10.5 116.5t-21.5 80.5zM242 695h75l-49 -156h-47z
M140 560h-1q-19 0 -32 13.5t-13 33.5q0 19 14 32.5t33 13.5t31.5 -13.5t12.5 -33.5q0 -19 -12.5 -32.5t-32.5 -13.5zM382 560h-1q-19 0 -31.5 13.5t-12.5 33.5q0 19 13.5 32.5t32.5 13.5t32 -13.5t13 -33.5q0 -19 -13 -32.5t-33 -13.5z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="551" 
d="M416 484h73q-13 -133 -13 -346q0 -43 11 -62t37 -20l-7 -63q-9 -4 -30 -4q-76 0 -83 102h-3q-54 -102 -165 -102q-87 0 -142.5 67t-55.5 176q0 115 64.5 189t151.5 74q52 0 91 -25.5t58 -70.5h4q3 48 9 85zM127 235v-1q0 -72 35.5 -123.5t95.5 -51.5q44 0 80.5 36.5
t49.5 86.5q5 22 5 68q0 35 -3 52q-9 51 -43.5 87.5t-81.5 36.5q-60 0 -99 -54t-39 -137z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="560" 
d="M159 50h-2v-67q0 -136 12 -181h-83q-15 44 -15 170v461q0 162 75 235q56 53 142 53q76 0 126 -48.5t50 -125.5q0 -54 -26.5 -95.5t-63.5 -59.5v-5q62 -12 105 -62t43 -129q0 -96 -61.5 -151.5t-146.5 -55.5q-96 0 -155 61zM157 443v-306q19 -32 56 -56t84 -24
q60 0 98.5 39.5t38.5 99.5q0 70 -43.5 108.5t-112.5 40.5h-27v60q12 0 18 1q54 4 83.5 41t29.5 87q0 53 -28.5 85t-73.5 32q-123 0 -123 -208z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="464" 
d="M269 -198h-87q6 188 -17 284q-25 109 -75.5 219.5t-95.5 178.5h99q37 -57 81.5 -165.5t65.5 -210.5h4q114 207 114 343v33h83q2 -8 2 -33q0 -169 -171 -425q-6 -8 -6 -24q3 -85 3 -200z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" horiz-adv-x="546" 
d="M466 672l-26 -58q-19 14 -57 27.5t-75 13.5q-39 0 -60.5 -13t-21.5 -32q0 -13 10.5 -26.5t21 -22.5t40.5 -33l82 -65q128 -98 128 -227q0 -110 -68 -178.5t-168 -68.5q-98 0 -166 65.5t-68 177.5q0 87 51 150.5t132 102.5v5l-22 18q-62 51 -62 100q0 44 44.5 78.5
t124.5 34.5q49 0 93 -14.5t67 -34.5zM326 402l-53 42q-146 -77 -146 -212q0 -80 45 -128.5t105 -48.5q61 0 102 47.5t41 127.5q0 100 -94 172z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="440" 
d="M158 259v4q-46 10 -70.5 37.5t-24.5 63.5q0 53 51 92t142 39q93 0 147 -36l-19 -59q-17 12 -52 21.5t-69 9.5q-51 0 -81.5 -20t-30.5 -53q0 -35 37 -52.5t97 -17.5h59v-62h-61q-69 0 -110.5 -20.5t-41.5 -62.5q0 -41 37.5 -64t93.5 -23q38 0 78.5 11t63.5 26l16 -60
q-28 -19 -77.5 -31.5t-99.5 -12.5q-84 0 -144 36t-60 111q0 44 33.5 78.5t85.5 44.5z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="409" 
d="M341 -143l-58 17q10 17 21 53.5t12 56.5q-69 10 -118 27q-159 52 -159 223q0 106 65.5 217.5t179.5 189.5v3h-71q-78 0 -117 9l9 59q39 -5 122 -3h167l13 -61q-120 -61 -199.5 -175t-79.5 -224q0 -75 31.5 -114.5t92.5 -61.5q49 -18 140 -30q9 -2 9 -11q0 -41 -19 -94
t-41 -81z" />
    <glyph glyph-name="eta" unicode="&#x3b7;" horiz-adv-x="555" 
d="M152 405h2q20 38 62 64t98 26q28 0 55.5 -9.5t54.5 -31t44 -64t17 -101.5v-307q0 -136 11 -180h-84q-15 37 -15 177v300q0 64 -27 104t-85 40q-41 0 -73.5 -26t-44.5 -65q-6 -15 -6 -40v-292h-88v353q0 89 -14 131h79q14 -28 14 -79z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" horiz-adv-x="534" 
d="M269 -11h-1q-108 0 -169 95t-61 277q0 158 62 259t172 101q112 0 168.5 -96.5t56.5 -263.5q0 -174 -57.5 -273t-170.5 -99zM125 399h285q0 113 -36.5 185t-106.5 72q-64 0 -103 -75t-39 -182zM411 334h-286q0 -131 40 -205.5t106 -74.5q68 0 104 76t36 204z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="236" 
d="M161 484v-367q0 -34 9.5 -47t37.5 -12l-5 -64q-15 -5 -39 -5q-43 0 -67 26t-24 93v376h88z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="493" 
d="M250 263v-3q33 -8 69 -44t80 -95l88 -121h-102l-76 103q-85 111 -139 112h-9v-215h-88v379q0 82 -11 105h85q14 -17 14 -82v-133h4q51 49 127 127.5t85 87.5h103v-4q-39 -36 -121.5 -114.5t-108.5 -102.5z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="472" 
d="M364 0l-98 277q-15 41 -33 105h-4q-6 -26 -31 -102l-100 -280h-91l175 448q5 14 5 24q0 11 -6 25q-27 73 -56 112.5t-70 39.5q-11 0 -17 -1l8 68q11 4 34 4q65 0 105.5 -54.5t86.5 -173.5l185 -492h-93z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="553" 
d="M403 76h-3q-18 -34 -54 -59.5t-85 -25.5q-76 0 -106 54v-60q0 -134 11 -183h-79q-14 42 -14 178v504h88v-286q0 -61 27 -98.5t82 -37.5q41 0 71.5 23.5t43.5 56.5q8 21 8 46v296h88v-350q0 -41 10 -58.5t36 -18.5l-7 -64q-15 -4 -31 -4q-75 0 -86 87z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="469" 
d="M4 484h93l93 -271q11 -31 41 -127h4q27 53 38.5 77t37 82.5t37.5 106.5t12 87q0 31 -1 45h82q3 -15 3 -35q0 -93 -47.5 -199t-132.5 -250h-83z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="433" 
d="M361 435v-63h-27q-100 -1 -152 -43t-52 -109q0 -59 38.5 -95.5t115.5 -56.5q41 -11 132 -25q10 0 10 -11q0 -38 -19 -93t-41 -82l-57 17q10 17 20.5 54t11.5 56q-39 6 -69.5 12t-67.5 17.5t-64.5 28.5t-51.5 40t-36.5 56.5t-12.5 74.5q0 70 44.5 122.5t111.5 71.5v5
q-55 17 -83 54t-28 87q0 68 55 118t154 50q76 0 122 -27l-18 -61q-42 23 -99 23q-60 0 -93 -32t-33 -77q0 -51 39.5 -80t101.5 -32h48z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="555" 
d="M520 416h-67v-272q0 -98 13 -144h-84q-16 27 -16 136v280h-158q-3 -103 -23 -231t-45 -185h-83q26 69 45.5 192t21.5 224q-66 0 -98 -9l-12 52q37 25 139 25h376z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="563" 
d="M157 60h-2v-78q0 -133 7 -180h-84q-11 41 -11 176v224q0 145 70 221q68 72 167 72q101 0 161.5 -68t60.5 -178q0 -122 -65 -191t-157 -69q-101 0 -147 71zM155 230v-40q0 -20 4 -35q11 -42 48 -69.5t83 -27.5q68 0 107.5 52.5t39.5 136.5q0 74 -37.5 124.5t-102.5 50.5
q-58 0 -100 -48.5t-42 -143.5z" />
    <glyph glyph-name="uni03C2" unicode="&#x3c2;" horiz-adv-x="417" 
d="M408 473l-20 -64q-41 18 -89 18q-77 0 -124.5 -54t-47.5 -131q0 -131 136 -172q52 -15 130 -27q9 -1 9 -14q0 -39 -18.5 -93t-38.5 -80l-59 18q10 16 20.5 53t12.5 56q-51 8 -99 21q-82 23 -132 80.5t-50 149.5q0 105 70.5 183t190.5 78q68 0 109 -22z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" horiz-adv-x="554" 
d="M412 433v-4q42 -30 66 -79.5t24 -104.5q0 -124 -70 -190t-162 -66q-100 0 -166 68t-66 181q0 120 69 188.5t180 68.5h93h53h37h45h27l-3 -66q-56 0 -127 4zM272 55h1q60 0 101 54t41 136q0 71 -36.5 127.5t-105.5 56.5q-70 0 -107.5 -56t-37.5 -132q0 -80 41 -133
t103 -53z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="413" 
d="M321 60l-4 -62q-13 -9 -48 -9q-58 0 -85 36.5t-27 113.5v277h-53q-56 0 -98 -8l-11 51q33 25 131 25h279l-11 -68h-149v-275q0 -82 55 -82q16 0 21 1z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="524" 
d="M360 484h82q14 -26 25 -79.5t11 -109.5q0 -58 -10.5 -108.5t-34 -97t-68.5 -73.5t-108 -27q-84 0 -137 50t-53 146v136q0 129 -10 163h84q14 -16 14 -90v-197q0 -68 32 -101.5t77 -33.5q62 0 95 59.5t33 165.5q0 60 -10.5 116.5t-21.5 80.5z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="642" 
d="M205 499l33 -49q-46 -30 -79 -81t-33 -122q0 -76 40.5 -128.5t115.5 -64.5v294q0 74 35 110.5t89 35.5q76 -1 136.5 -67.5t60.5 -171.5q0 -118 -70 -186.5t-170 -78.5v-188h-81v188q-101 8 -172.5 72t-71.5 184q0 50 16.5 94t42.5 75t53.5 52t54.5 32zM363 362v-309
q72 10 114 63t42 137q0 72 -35.5 124.5t-78.5 52.5q-42 0 -42 -68z" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="457" 
d="M18 484h94l123 -261h4q11 25 50 107l73 154h85l-170 -327l176 -348l-82 -22l-144 297l-4 -1q-1 -3 -18 -41.5t-28 -61.5l-91 -193l-75 23l173 346z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="655" 
d="M295 -198v187q-105 7 -166.5 64t-61.5 188v131q0 80 -8 112h80q13 -18 13 -90v-146q0 -99 39.5 -145t103.5 -50v558h81v-558q66 3 107.5 66.5t41.5 166.5q0 68 -12 123.5t-24 74.5h81q39 -60 39 -191q0 -57 -12.5 -107.5t-39 -94.5t-73 -71.5t-108.5 -30.5v-187h-81z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="694" 
d="M388 363l-2 -191q0 -47 23.5 -78.5t59.5 -31.5q41 0 71 51t30 144q0 70 -22 133.5t-58 97.5v2h83q35 -29 59 -92.5t24 -142.5q0 -128 -54 -197t-128 -69q-45 0 -78.5 23t-48.5 62h-3q-39 -85 -129 -85q-71 0 -124 65t-53 193q0 77 25 143t59 100h82v-2
q-35 -39 -57.5 -102t-22.5 -137q0 -93 31.5 -140t71.5 -47q34 0 57.5 32t22.5 77l-1 192h82z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="236" 
d="M29 557h-1q-21 0 -35 15t-14 36q0 20 14.5 35t35.5 15q20 0 34 -14.5t14 -35.5t-13.5 -36t-34.5 -15zM212 557h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5q20 0 34 -14.5t14 -35.5t-13.5 -36t-35.5 -15zM161 484v-367q0 -34 9.5 -47t37.5 -12l-5 -64q-15 -5 -39 -5
q-44 0 -67.5 26t-23.5 94v375h88z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="524" 
d="M166 557h-1q-21 0 -35 15t-14 36q0 20 14.5 35t35.5 15q20 0 34 -14.5t14 -35.5t-13.5 -36t-34.5 -15zM349 557h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5q20 0 33.5 -14.5t13.5 -35.5t-13 -36t-35 -15zM360 484h82q14 -26 25 -79.5t11 -109.5q0 -58 -10.5 -108.5
t-34 -97t-68.5 -73.5t-108 -27q-84 0 -137 50t-53 146v136q0 129 -10 163h84q14 -16 14 -90v-197q0 -68 32 -101.5t78 -33.5q62 0 95 59.5t33 165.5q0 61 -11 117.5t-22 79.5z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" horiz-adv-x="549" 
d="M289 686h88l-83 -143h-58zM271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="524" 
d="M272 687h89l-83 -143h-59zM360 484h82q14 -26 25 -79.5t11 -109.5q0 -58 -10.5 -108.5t-34 -97t-68.5 -73.5t-108 -27q-84 0 -137 50t-53 146v136q0 129 -10 163h84q14 -16 14 -90v-197q0 -68 32 -101.5t77 -33.5q62 0 95 59.5t33 165.5q0 60 -10.5 116.5t-21.5 80.5z
" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="694" 
d="M361 686h88l-83 -143h-58zM388 363l-2 -191q0 -47 23.5 -78.5t59.5 -31.5q41 0 71 51t30 144q0 70 -22 133.5t-58 97.5v2h83q35 -29 59 -92.5t24 -142.5q0 -128 -54 -197t-128 -69q-45 0 -78.5 23t-48.5 62h-3q-39 -85 -129 -85q-71 0 -124 65t-53 193q0 77 25 143
t59 100h82v-2q-35 -39 -57.5 -102t-22.5 -137q0 -93 31.5 -140t71.5 -47q34 0 57.5 32t22.5 77l-1 192h82z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261zM163 721h-1q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5zM348 721h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5t34.5 -14.5t13.5 -35.5
q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="665" 
d="M-1 674h518v-73h-244v-192h3q62 41 135 41q104 0 162 -84q52 -71 52 -188q0 -162 -93 -241q-56 -63 -140 -76l-15 68q72 18 110 70q47 62 47 171q0 95 -35 146q-39 61 -112 61q-61 0 -114 -39v-338h-88v601h-186v73z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="433" 
d="M75 674h359v-73h-271v-601h-88v674zM284 827h106l-128 -117h-71z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="576" 
d="M544 653l-21 -68q-56 28 -133 28q-111 0 -180 -66t-80 -168h346v-69h-345q3 -106 73 -177t183 -71q77 0 141 27l15 -67q-64 -33 -172 -33q-147 0 -240.5 93.5t-93.5 253.5q0 148 95.5 248.5t254.5 100.5q98 0 157 -32z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="493" 
d="M42 34l23 72q70 -43 155 -43q66 0 103.5 31t37.5 83q0 47 -29 77.5t-98 56.5q-178 61 -178 190q0 80 60.5 131.5t157.5 51.5q90 0 148 -32l-24 -72q-52 32 -127 32q-63 0 -95 -30.5t-32 -70.5q0 -45 30.5 -73t103.5 -56q89 -35 130 -80.5t41 -117.5q0 -83 -61 -138.5
t-174 -55.5q-48 0 -96 12.5t-76 31.5z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="239" 
d="M75 674h88v-674h-88v674zM28 721h-1q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5zM213 721h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="370" 
d="M213 230v444h88v-451q0 -234 -204 -234q-56 0 -93 16l12 71q33 -13 74 -13q62 0 92.5 36.5t30.5 130.5z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="881" 
d="M124 674h377v-254q35 5 82 5q111 0 185 -52.5t74 -156.5q0 -89 -56 -147q-78 -75 -237 -75q-77 0 -135 8v599h-205v-204q0 -66 -3 -113t-12 -102.5t-30.5 -95t-55.5 -62.5q-50 -32 -101 -32l-13 70q43 8 72 37q58 57 58 292v283zM501 353v-287q28 -4 70 -4q77 0 128.5 39
t51.5 112q0 74 -51 109t-130 35q-25 0 -69 -4z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="897" 
d="M75 674h88v-269h267v269h87v-263q35 5 93 5q68 0 122.5 -20t90 -67t35.5 -115q0 -90 -56 -146q-75 -74 -235 -74q-79 0 -137 8v329h-267v-331h-88v674zM517 344v-277q26 -5 71 -5q77 0 128 37.5t51 111.5q0 70 -49.5 103.5t-125.5 33.5q-37 0 -75 -4z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="675" 
d="M-1 674h507v-73h-235v-193h2q76 46 153 46q78 0 125 -47q53 -54 53 -157v-250h-88v239q0 74 -32 110q-33 33 -86 33q-70 0 -127 -43v-339h-88v601h-184v73z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="542" 
d="M76 674h87v-299h27l231 299h104l-255 -309q78 -10 119 -53t70 -121q7 -20 22.5 -62.5t27 -72t24.5 -56.5h-93q-15 27 -62 159q-26 75 -67 112.5t-120 37.5h-28v-309h-87v674zM319 822h106l-128 -117h-71z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="521" 
d="M3 674h99l133 -289q17 -40 49 -118h2q7 19 21 60.5t21 60.5l105 286h92l-141 -348q-80 -194 -126 -255q-65 -83 -152 -83q-32 0 -49 6l8 71q10 -3 32 -3q80 0 133 115q8 18 -3 41zM134 817h67q4 -29 16.5 -45.5t44.5 -16.5t44.5 16.5t16.5 45.5h67q-8 -108 -130 -108
q-118 0 -126 108z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="635" 
d="M75 674h88v-601h310v601h88v-674h-199l-8 -174h-72l-7 174h-200v674z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="612" 
d="M424 212h-239l-70 -212h-90l229 674h105l230 -674h-93zM203 280h203l-67 194q-13 40 -34 123h-3q-15 -66 -33 -122z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="546" 
d="M76 674h378v-73h-291v-181q35 5 83 5q106 0 174 -45q86 -56 86 -164q0 -89 -56 -147q-76 -75 -238 -75q-78 0 -136 8v672zM163 353v-286q25 -5 73 -5q93 0 142 52q37 39 37 99q0 72 -55 110q-48 34 -128 34q-25 0 -69 -4z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="542" 
d="M76 2v662q63 15 159 15q125 0 184 -49q57 -42 57 -119q0 -49 -30.5 -89t-83.5 -59v-3q56 -13 97.5 -56t41.5 -112q0 -80 -58 -135q-67 -63 -232 -63q-77 0 -135 8zM163 606v-218h79q67 0 106 32t39 82q0 110 -147 110q-49 0 -77 -6zM163 323v-257q24 -4 73 -4
q75 0 124 31.5t49 99.5q0 65 -49 97.5t-125 32.5h-72z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="433" 
d="M75 674h359v-73h-271v-601h-88v674z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="630" 
d="M160 674h377v-603l59 -2l-6 -231h-68l-6 162h-425l-6 -162h-68l-6 231l51 2q48 88 66 148q32 109 32 283v172zM241 601v-126q0 -167 -34 -275q-16 -55 -55 -128h297v529h-208z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="492" 
d="M424 388v-72h-261v-243h292v-73h-380v674h365v-73h-277v-213h261z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="801" 
d="M22 674h101l209 -299h27v299h84v-299h27l209 299h101l-239 -310q74 -7 114 -49.5t69 -123.5q7 -21 19.5 -60.5t23.5 -71t23 -59.5h-89q-10 20 -19.5 46.5t-20.5 62.5t-15 49q-27 79 -62.5 115t-110.5 36h-30v-309h-84v309h-31q-75 0 -110.5 -36t-62.5 -115
q-1 -3 -21.5 -67t-33.5 -91h-89q13 28 24 59t24 71.5t20 59.5q28 80 68 122.5t113 51.5z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="492" 
d="M74 570l-24 64q78 51 178 51q89 0 143.5 -44t54.5 -121q0 -61 -39 -105.5t-99 -59.5v-2q72 -8 118 -53t46 -115q0 -98 -71.5 -147t-173.5 -49t-176 44l23 68q72 -41 152 -41q72 0 113 35.5t41 92.5q0 47 -29 77.5t-68 41t-87 10.5h-40v67h40q67 0 113 36t46 88
q0 51 -33 78.5t-88 27.5q-69 0 -140 -44z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="658" 
d="M76 674h82v-284q0 -165 -8 -285l3 -1q40 85 124 223l218 347h87v-674h-82v287q0 177 8 274l-3 1q-35 -78 -125 -227l-211 -335h-93v674z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="658" 
d="M76 674h82v-284q0 -165 -8 -285l3 -1q40 85 124 223l218 347h87v-674h-82v287q0 177 8 274l-3 1q-35 -78 -125 -227l-211 -335h-93v674zM201 816h67q4 -29 16.5 -45.5t44.5 -16.5t44.5 16.5t16.5 45.5h67q-8 -108 -130 -108q-118 0 -126 108z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="542" 
d="M76 674h87v-299h27l231 299h104l-255 -309q78 -10 119 -53t70 -121q7 -20 22.5 -62.5t27 -72t24.5 -56.5h-93q-15 27 -62 159q-26 75 -67 112.5t-120 37.5h-28v-309h-87v674z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="594" 
d="M123 674h396v-674h-88v601h-222v-204q0 -67 -3 -113.5t-12 -102t-30.5 -95t-55.5 -62.5q-50 -32 -104 -32l-11 70q41 6 71 36q59 57 59 292v284z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="804" 
d="M660 0l-16 296q-11 205 -11 291h-2q-48 -155 -85 -256l-119 -327h-66l-110 321q-50 149 -77 262h-2q-3 -132 -13 -298l-18 -289h-83l47 674h111l115 -326q39 -117 68 -228h2q25 96 72 228l120 326h111l42 -674h-86z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="652" 
d="M75 674h88v-282h326v282h88v-674h-88v316h-326v-316h-88v674z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="689" 
d="M340 -11h-1q-135 0 -219 94.5t-84 247.5q0 159 87.5 256.5t224.5 97.5q138 0 221 -95t83 -246q0 -167 -89 -261t-223 -94zM343 60h1q100 0 158 80t58 200q0 110 -56.5 192t-158.5 82q-103 0 -160 -82t-57 -199q0 -112 58.5 -192.5t156.5 -80.5z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="639" 
d="M75 674h489v-674h-88v601h-313v-601h-88v674z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="532" 
d="M76 0v665q74 14 167 14q127 0 189 -56q60 -52 60 -140q0 -91 -53 -144q-69 -74 -206 -74q-46 0 -70 6v-271h-87zM163 603v-261q27 -7 72 -7q79 0 124 37.5t45 105.5q0 65 -42.5 98.5t-116.5 33.5q-56 0 -82 -7z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="580" 
d="M529 92l17 -70q-62 -32 -178 -32q-147 0 -239.5 90t-92.5 251q0 158 97.5 255.5t254.5 97.5q99 0 160 -29l-22 -72q-56 28 -135 28q-120 0 -191.5 -73.5t-71.5 -203.5q0 -125 68.5 -197.5t189.5 -72.5q85 0 143 28z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="497" 
d="M204 0v600h-205v74h499v-74h-206v-600h-88z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="521" 
d="M3 674h99l133 -289q17 -40 49 -118h2q7 19 21 60.5t21 60.5l105 286h92l-141 -348q-80 -194 -126 -255q-65 -83 -152 -83q-32 0 -49 6l8 71q10 -3 32 -3q80 0 133 115q8 18 -3 41z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="725" 
d="M322 701h84v-61q55 -2 104 -20.5t90 -53.5t65 -94t24 -135q0 -77 -24 -135.5t-65.5 -92.5t-90 -52t-105.5 -22v-63h-84v63q-56 4 -104.5 21t-90 50.5t-65.5 91.5t-24 135q0 79 26 139.5t69 94.5t91 52t100 21v61zM321 98v480q-78 -4 -136.5 -63t-58.5 -179
q0 -112 56 -171t139 -67zM405 578v-480q82 6 138 64.5t56 174.5q0 120 -56 178t-138 63z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="571" 
d="M546 0h-101l-87 149q-44 71 -77 133h-2q-23 -47 -73 -134l-81 -148h-100l206 341l-198 333h101l89 -158q42 -73 62 -114h3q24 50 61 114l91 158h101l-205 -328z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="652" 
d="M75 674h88v-602h309v602h88v-603l59 -2l-6 -231h-69l-5 162h-464v674z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="597" 
d="M70 674h88v-216q0 -70 36 -107.5t106 -37.5q75 0 134 39v322h88v-678h-88v288h-2q-73 -42 -162 -42q-37 0 -69.5 9t-63.5 30t-49 62.5t-18 100.5v230z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="847" 
d="M76 674h86v-601h219v601h86v-601h219v601h86v-674h-696v674z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="864" 
d="M76 674h86v-602h219v602h86v-602h219v602h86v-603l59 -2l-6 -231h-69l-5 162h-675v674z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="631" 
d="M-2 674h260v-252q33 5 81 5q106 0 179.5 -53.5t73.5 -157.5q0 -88 -52 -143q-75 -79 -235 -79q-76 0 -134 8v599h-173v73zM258 353v-286q24 -5 69 -5q77 0 125.5 38.5t48.5 112.5q0 73 -50 108.5t-123 35.5q-28 0 -70 -4z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="746" 
d="M76 674h87v-253q30 6 81 6q112 0 185.5 -52.5t73.5 -158.5q0 -86 -52 -143q-75 -79 -240 -79q-77 0 -135 8v672zM163 353v-286q23 -5 70 -5q77 0 128 39t51 112t-52 108.5t-128 35.5q-25 0 -69 -4zM578 674h88v-674h-88v674z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="545" 
d="M76 674h87v-252q33 5 83 5q113 0 186.5 -52.5t73.5 -157.5q0 -87 -52 -144q-75 -79 -242 -79q-78 0 -136 8v672zM163 353v-286q25 -5 72 -5q78 0 129 38.5t51 111.5q0 74 -51 109.5t-130 35.5q-27 0 -71 -4z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="568" 
d="M124 310v70h313q-9 103 -69 168t-161 65q-66 0 -145 -33l-21 66q85 39 171 39q153 0 236 -97.5t83 -250.5q0 -161 -92.5 -254.5t-232.5 -93.5q-98 0 -176 34l19 70q67 -31 146 -31q108 0 173 70t70 178h-314z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="867" 
d="M75 674h88v-290h100q13 140 90.5 220.5t196.5 80.5q129 0 205 -94.5t76 -246.5q0 -169 -82 -262t-208 -93q-123 0 -199 87t-81 234h-98v-310h-88v674zM544 59h1q92 0 144.5 80.5t52.5 200.5q0 111 -51 193t-145 82q-95 0 -147 -82t-52 -199q0 -114 53 -194.5t144 -80.5z
" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="543" 
d="M108 0h-94q14 24 28 61q7 20 19.5 59t21.5 63.5t25 54.5t35 49q24 24 57 35v2q-65 11 -107 55t-42 111q0 87 62 136q64 53 193 53q84 0 161 -14v-665h-87v292h-65q-63 0 -96 -33q-21 -21 -36.5 -56t-31 -87t-21.5 -67q-12 -28 -22 -49zM380 360v243q-30 8 -83 8
q-70 0 -114 -30t-44 -95q0 -59 46.5 -93.5t120.5 -34.5q57 0 74 2z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="482" 
d="M414 297v-181q0 -70 7 -116h-79l-8 61h-3q-21 -31 -60 -51.5t-88 -20.5q-70 0 -109 41t-39 98q0 88 75.5 134.5t215.5 45.5v10q0 19 -3.5 35.5t-14 35.5t-34 30t-58.5 11q-74 0 -127 -36l-20 59q67 42 160 42q185 0 185 -198zM328 163v84q-205 4 -205 -109
q0 -41 23.5 -62.5t58.5 -21.5q45 0 76 23.5t43 57.5q4 12 4 28z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="528" 
d="M443 721l-4 -73q-28 -11 -101 -22q-123 -19 -173 -91q-43 -62 -53 -160h3q23 52 69.5 80.5t99.5 28.5q92 0 149 -63t57 -176q0 -119 -59 -187.5t-165 -68.5q-61 0 -106.5 27.5t-70.5 75t-37 102.5t-12 119q0 169 74 268q34 49 84.5 75.5t124.5 38.5q88 15 120 26z
M401 238v1q0 44 -11 82t-43 68t-81 30q-74 0 -110 -66q-24 -43 -24 -102q0 -44 13 -87t45.5 -76t77.5 -33q68 0 100.5 54t32.5 129z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="500" 
d="M70 0v479q64 11 154 11q216 0 216 -122q0 -43 -28.5 -71.5t-72.5 -38.5v-2q56 -8 89.5 -38t33.5 -81q0 -143 -255 -143q-59 0 -137 6zM155 425v-145h66q55 0 93.5 18.5t38.5 58.5q0 72 -130 72q-28 0 -68 -4zM154 223v-166q38 -3 70 -3q59 0 103.5 18.5t44.5 65.5
q0 48 -41 66.5t-113 18.5h-64z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="385" 
d="M70 484h298v-70h-210v-414h-88v484z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="530" 
d="M125 484h323v-418l49 -1l-5 -218h-68l-4 153h-335l-4 -153h-69l-4 218l41 1q31 46 48 99q28 90 28 194v125zM202 417v-79q0 -100 -25 -181q-13 -44 -38 -90h223v350h-160z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="667" 
d="M14 484h100l161 -211h17v211h84v-211h17l160 211h101l-191 -220q101 -11 145 -128q41 -113 53 -136h-88q-18 32 -44 111q-22 57 -55 80t-83 23h-15v-214h-84v214h-15q-53 0 -85.5 -23t-54.5 -81q-34 -90 -45 -110h-86q10 19 54 135q23 60 57.5 91.5t87.5 37.5z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="431" 
d="M114 221l-1 60h42q55 0 89.5 22.5t34.5 56.5q0 32 -26 51.5t-68 19.5q-60 0 -117 -35l-22 55q73 43 161 43q40 0 75.5 -11.5t62 -41.5t26.5 -74q0 -42 -32.5 -71.5t-76.5 -38.5v-2q57 -6 94.5 -38t37.5 -83q0 -72 -61.5 -108t-147.5 -36q-87 0 -157 39l23 60
q59 -34 128 -34q54 0 86.5 23t32.5 60q0 83 -141 83h-43z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="554" 
d="M70 484h84v-204q0 -90 -6 -195h3q63 122 79 149l151 250h103v-484h-84v206q0 81 6 198h-2q-46 -91 -81 -151q-26 -43 -80.5 -134.5t-70.5 -118.5h-102v484z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="554" 
d="M70 484h84v-204q0 -90 -6 -195h3q63 122 79 149l151 250h103v-484h-84v206q0 81 6 198h-2q-46 -91 -81 -151q-26 -43 -80.5 -134.5t-70.5 -118.5h-102v484zM148 681h64q2 -34 18.5 -55t45.5 -21q59 0 68 76h63q-3 -60 -38.5 -92t-94.5 -32q-62 0 -92.5 34t-33.5 90z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="479" 
d="M73 484h88v-210h20l182 210h107l-211 -218q57 -6 95.5 -43t68.5 -105q40 -99 51 -118h-92q-15 25 -45 99q-25 60 -62 87t-97 27h-17v-213h-88v484z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="507" 
d="M100 484h337v-484h-88v415h-166v-124q0 -112 -16 -175.5t-60 -92.5q-38 -30 -96 -30l-9 69q30 5 48 20q31 23 40.5 70.5t9.5 139.5v192z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="639" 
d="M42 0l37 484h108l87 -244q28 -77 44 -133h2q20 65 58 166l77 211h109l34 -484h-84l-11 231q-1 22 -4 81.5t-4 91.5h-3q-12 -39 -54 -157l-93 -244h-63l-86 240q-25 71 -49 160h-4q-5 -132 -7 -174l-13 -229h-81z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="546" 
d="M70 484h88v-195h229v195h88v-484h-88v219h-229v-219h-88v484z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" horiz-adv-x="549" 
d="M271 -11h-1q-100 0 -166 68.5t-66 180.5q0 118 68.5 187.5t171.5 69.5q104 0 168.5 -69t64.5 -179q0 -126 -72 -192t-168 -66zM273 55h1q63 0 105 53.5t42 135.5q0 71 -36.5 128t-108.5 57t-110 -56t-38 -132q0 -80 41 -133t104 -53z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="541" 
d="M70 484h401v-484h-88v415h-225v-415h-88v484z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="569" 
d="M73 -198v524q0 97 -4 158h79l5 -83h2q55 94 171 94q89 0 147.5 -68.5t58.5 -177.5q0 -124 -64.5 -192t-155.5 -68q-103 0 -149 76h-2v-263h-88zM161 281v-84q0 -21 4 -36q11 -46 47.5 -74.5t83.5 -28.5q69 0 108 51t39 136q0 76 -38 128t-105 52q-46 0 -83.5 -30
t-50.5 -79q-5 -28 -5 -35z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="448" 
d="M403 84l15 -66q-59 -28 -139 -28q-109 0 -175 68t-66 179q0 113 71.5 185.5t188.5 72.5q74 0 122 -26l-20 -67q-44 23 -102 23q-80 0 -125.5 -52.5t-45.5 -130.5q0 -82 47.5 -131.5t120.5 -49.5q56 0 108 23z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="411" 
d="M10 484h390v-69h-151v-415h-88v415h-151v69z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="471" 
d="M9 484h96l105 -286q18 -47 32 -99h2q1 3 12.5 41.5t18.5 59.5l96 284h93l-132 -345q-49 -128 -85.5 -196t-80.5 -107q-55 -46 -108 -56l-22 73q42 13 77 43q45 35 74 98q7 16 7 21q0 8 -7 23z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="641" 
d="M280 709h82l-1 -215q109 -8 176 -74t67 -176q0 -114 -68 -179.5t-175 -73.5l1 -189h-82l1 188q-108 7 -175.5 72.5t-67.5 175.5q0 115 67.5 180.5t175.5 74.5zM281 51v382q-72 -10 -114 -63t-42 -129t42.5 -128.5t113.5 -61.5zM361 433v-382q72 10 114 63t42 129t-42 128
t-114 62z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="463" 
d="M16 484h98l69 -104q27 -41 49 -79h3q18 32 49 80l67 103h96l-165 -234l169 -250h-100l-71 109q-20 30 -52 84h-2q-9 -17 -51 -84l-70 -109h-97l172 247z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="550" 
d="M70 484h88v-415h223v415h88v-418l49 -1l-5 -218h-68l-4 153h-371v484z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="515" 
d="M65 484h88v-164q0 -49 22.5 -75.5t77.5 -26.5q61 0 104 33v233h88v-490h-88v195h-1q-57 -36 -132 -36q-72 0 -115.5 39t-43.5 118v174z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="738" 
d="M70 484h87v-415h169v415h87v-415h168v415h87v-484h-598v484z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="749" 
d="M70 484h87v-415h169v415h87v-415h168v415h87v-418l49 -1l-5 -218h-68l-4 153h-570v484z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="579" 
d="M14 484h231v-163q29 5 83 5q55 0 101 -14t80 -52.5t34 -97.5q0 -69 -51 -112q-65 -55 -205 -55q-44 0 -130 7v413h-143v69zM244 257v-195q34 -4 66 -4q62 0 103.5 24t41.5 77q0 39 -24 63.5t-55 32.5t-70 8q-30 0 -62 -6z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="665" 
d="M71 484h88v-164q36 6 78 6q87 0 148 -39.5t61 -122.5q0 -70 -51 -113q-66 -56 -194 -56q-44 0 -130 7v482zM158 257v-195q23 -4 61 -4q57 0 98 25t41 78q0 54 -39.5 78t-95.5 24q-38 0 -65 -6zM507 484h87v-483h-87v483z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="498" 
d="M71 484h88v-163q33 5 79 5q60 0 107.5 -13t82 -51.5t34.5 -98.5q0 -69 -51 -112q-66 -56 -210 -56q-44 0 -130 7v482zM158 257v-195q34 -4 65 -4q60 0 105.5 24t45.5 78q0 39 -26 63.5t-57.5 32t-69.5 7.5q-31 0 -63 -6z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="464" 
d="M94 216v61h243q-6 66 -48 108t-119 42q-59 0 -115 -27l-20 58q69 37 149 37q112 0 176 -69q66 -68 66 -186q0 -109 -68.5 -180t-187.5 -71q-81 0 -142 33l17 62q62 -26 121 -26q72 0 118 43t52 115h-242z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="705" 
d="M71 484h86v-204h83q11 97 69 156t145 59q95 0 154 -68.5t59 -180.5q0 -115 -62 -186t-156 -71q-88 0 -146 60.5t-65 163.5h-81v-213h-86v484zM450 54h1q62 0 95 57t33 133q0 75 -33 130t-94 55t-95.5 -56t-34.5 -134q0 -76 34 -130.5t94 -54.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="496" 
d="M426 480v-480h-87v203h-65q-51 0 -80 -27q-13 -10 -24 -28t-17 -32.5t-15 -39.5t-13 -36q-7 -18 -20 -40h-94q17 25 30 55q5 10 18 46.5t26 60t32 39.5q24 21 55 28v2q-52 6 -90 37t-38 84q0 69 64 105q57 33 163 33q71 0 155 -10zM339 263v163q-37 5 -73 5
q-33 0 -60.5 -6.5t-49 -26.5t-21.5 -51q0 -42 41 -63.5t88 -21.5q21 0 44 0.5t31 0.5z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="501" 
d="M462 226h-339q1 -84 47.5 -125.5t117.5 -41.5q77 0 134 25l16 -63q-69 -31 -162 -31q-110 0 -174 67t-64 177q0 112 62.5 186.5t165.5 74.5q56 0 97 -22.5t62 -59t30.5 -73t9.5 -73.5q0 -26 -3 -41zM124 289h256q0 23 -5 46t-18 46t-38 37t-60 14q-61 0 -95 -44t-40 -99z
M176 570h-1q-21 0 -35 15t-14 36t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5q0 -21 -14 -36t-35 -15zM362 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="557" 
d="M7 625h82v85h88v-85h226v-63h-226v-141h2q62 69 147 69q89 0 138 -79q53 -78 53 -211q0 -205 -99 -288q-57 -49 -125 -56l-16 69q59 12 93 50q59 67 59 219q0 107 -38 170q-33 55 -92 55t-107 -58q-15 -21 -15 -55v-306h-88v562h-82v63z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="385" 
d="M70 484h298v-70h-210v-414h-88v484zM251 693h96l-122 -143h-62z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="460" 
d="M432 463l-21 -63q-56 27 -110 27q-76 0 -120.5 -42.5t-52.5 -107.5h245v-61h-244q5 -72 52 -115t118 -43q56 0 116 25l17 -63q-62 -31 -147 -31q-114 0 -180.5 70t-66.5 179q0 115 74 186t190 71q76 0 130 -32z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="396" 
d="M40 24l21 66q57 -34 117 -34q45 0 69 20t24 52q0 31 -20 50.5t-69 37.5q-129 47 -129 137q0 60 46 101t121 41q72 0 118 -30l-21 -63q-48 28 -99 28q-38 0 -59 -19t-21 -47q0 -29 20.5 -46t70.5 -37q64 -24 95.5 -58.5t31.5 -86.5q0 -66 -48 -106t-132 -40q-78 0 -136 34
z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM116 566h-1q-23 0 -38 16t-15 38q0 24 15.5 39.5t39.5 15.5t39 -15.5t15 -39.5q0 -23 -15 -38.5t-40 -15.5z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="234" 
d="M161 0h-88v484h88v-484zM24 570h-1q-21 0 -35 15t-14 36t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5q0 -21 -14 -36t-35 -15zM210 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="243" 
d="M-36 -211l-10 69q67 5 96 37q19 21 26.5 59.5t7.5 122.5v407h88v-441q0 -140 -58 -199q-27 -27 -69 -41t-81 -14zM127 566h-1q-23 0 -38 16t-15 38q0 24 15.5 39.5t39.5 15.5t39 -15.5t15 -39.5q0 -23 -15 -38.5t-40 -15.5z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="775" 
d="M105 484h332v-163q33 5 79 5q94 0 158.5 -38.5t64.5 -125.5q0 -69 -48 -110q-66 -57 -211 -57q-45 0 -131 7v413h-162v-122q0 -109 -18 -174.5t-64 -94.5q-35 -30 -95 -30l-8 68q30 5 48 20q32 22 43.5 72.5t11.5 138.5v191zM436 256v-194q23 -4 65 -4q60 0 105 24t45 78
q0 39 -24.5 63.5t-54.5 32t-66 7.5q-41 0 -70 -7z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="782" 
d="M70 484h88v-180h204v180h88v-169q33 5 82 5q39 0 75 -8.5t68.5 -26t51.5 -50t19 -75.5q0 -68 -49 -110q-63 -55 -204 -55q-57 0 -131 6v235h-204v-236h-88v484zM449 253v-192q51 -3 66 -3q36 0 66 8t53.5 31.5t23.5 60.5q0 54 -41.5 77t-101.5 23q-33 0 -66 -5z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="571" 
d="M7 625h82v85h88v-85h202v-63h-202v-156h2q23 36 60 59q42 25 93 25q77 0 123 -53.5t46 -152.5v-284h-88v273q0 65 -26.5 104.5t-85.5 39.5q-42 0 -73.5 -25.5t-44.5 -62.5q-6 -18 -6 -45v-284h-88v562h-82v63z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="479" 
d="M73 484h88v-210h20l182 210h107l-211 -218q57 -6 95.5 -43t68.5 -105q40 -99 51 -118h-92q-15 25 -45 99q-25 60 -62 87t-97 27h-17v-213h-88v484zM281 693h96l-122 -143h-62z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="471" 
d="M9 484h96l105 -286q18 -47 32 -99h2q1 3 12.5 41.5t18.5 59.5l96 284h93l-132 -345q-49 -128 -85.5 -196t-80.5 -107q-55 -46 -108 -56l-22 73q42 13 77 43q45 35 74 98q7 16 7 21q0 8 -7 23zM106 681h64q2 -34 18.5 -55t45.5 -21q59 0 68 76h63q-3 -60 -38.5 -92
t-94.5 -32q-62 0 -92.5 34t-33.5 90z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="538" 
d="M70 484h88v-415h222v415h88v-484h-157l-6 -157h-70l-7 157h-158v484z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="447" 
d="M75 674h292l16 122h65l-11 -195h-274v-601h-88v674z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="392" 
d="M70 484h230l12 109h63l-6 -179h-211v-414h-88v484z" />
    <glyph glyph-name="afii10846" unicode="&#x4d9;" horiz-adv-x="501" 
d="M78 400l-16 63q73 32 163 32q109 0 173 -67.5t64 -177.5q0 -112 -62 -186t-165 -74q-56 0 -97.5 22.5t-62.5 58.5t-30.5 72.5t-9.5 73.5q0 27 3 41h339q-1 85 -46 126.5t-118 41.5q-74 0 -135 -26zM376 195h-256q-1 -53 27.5 -97.5t94.5 -44.5q61 0 94.5 43.5t39.5 98.5z
" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="846" 
d="M277 0h-91l-171 674h92l80 -341q32 -138 50 -233h2q11 68 55 234l90 340h91l82 -342q36 -154 47 -231h2q13 70 54 233l89 340h89l-191 -674h-91l-85 350q-33 135 -44 221h-2q-14 -89 -52 -220zM273 827h106l93 -116h-71z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="736" 
d="M18 484h90l65 -245q28 -117 35 -155h3q10 45 43 154l79 246h74l75 -242q26 -82 43 -158h3q10 60 37 157l69 243h87l-156 -484h-80l-74 231q-29 95 -43 160h-2q-15 -72 -44 -161l-78 -230h-80zM237 693h96l88 -143h-62z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="846" 
d="M277 0h-91l-171 674h92l80 -341q32 -138 50 -233h2q11 68 55 234l90 340h91l82 -342q36 -154 47 -231h2q13 70 54 233l89 340h89l-191 -674h-91l-85 350q-33 135 -44 221h-2q-14 -89 -52 -220zM477 827h106l-128 -117h-71z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="736" 
d="M18 484h90l65 -245q28 -117 35 -155h3q10 45 43 154l79 246h74l75 -242q26 -82 43 -158h3q10 60 37 157l69 243h87l-156 -484h-80l-74 231q-29 95 -43 160h-2q-15 -72 -44 -161l-78 -230h-80zM411 693h96l-122 -143h-62z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="846" 
d="M277 0h-91l-171 674h92l80 -341q32 -138 50 -233h2q11 68 55 234l90 340h91l82 -342q36 -154 47 -231h2q13 70 54 233l89 340h89l-191 -674h-91l-85 350q-33 135 -44 221h-2q-14 -89 -52 -220zM335 721h-1q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5
t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5zM520 721h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="736" 
d="M18 484h90l65 -245q28 -117 35 -155h3q10 45 43 154l79 246h74l75 -242q26 -82 43 -158h3q10 60 37 157l69 243h87l-156 -484h-80l-74 231q-29 95 -43 160h-2q-15 -72 -44 -161l-78 -230h-80zM275 570h-1q-21 0 -35 15t-14 36t14.5 36t36.5 15q20 0 34 -14.5t14 -36.5
q0 -21 -14 -36t-35 -15zM461 570h-1q-21 0 -35 15t-14 36t14.5 36t35.5 15t35 -14.5t14 -36.5q0 -21 -14 -36t-35 -15z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="541" 
d="M314 0h-88v286l-214 388h100l95 -186q55 -113 67 -139h2q12 28 68 139l97 186h98l-225 -387v-287zM132 820h106l93 -116h-71z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="471" 
d="M9 484h96l105 -286q18 -47 32 -99h2q1 3 12.5 41.5t18.5 59.5l96 284h93l-132 -345q-49 -128 -85.5 -196t-80.5 -107q-55 -46 -108 -56l-22 73q42 13 77 43q45 35 74 98q7 16 7 21q0 8 -7 23zM108 693h96l88 -143h-62z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="500" 
d="M30 284h440v-60h-440v60z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1000" 
d="M30 284h940v-60h-940v60z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="207" 
d="M125 458l-90 -10q17 62 44.5 132.5t48.5 105.5l56 6q-41 -114 -59 -234z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="207" 
d="M93 682l90 10q-17 -62 -44.5 -132.5t-48.5 -105.5l-56 -6q41 114 59 234z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="207" 
d="M77 -111l-56 -6q41 114 59 234l90 10q-17 -62 -44.5 -132.5t-48.5 -105.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="354" 
d="M125 458l-90 -10q17 62 44.5 132.5t48.5 105.5l56 6q-41 -114 -59 -234zM273 458l-90 -10q17 62 44.5 132.5t48.5 105.5l56 6q-39 -106 -59 -234z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="354" 
d="M93 682l90 10q-17 -62 -44.5 -132.5t-48.5 -105.5l-56 -6q39 106 59 234zM241 682l90 10q-17 -62 -44.5 -132.5t-48.5 -105.5l-56 -6q41 114 59 234z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="356" 
d="M76 -111l-56 -6q39 106 59 234l90 10q-17 -62 -44.5 -132.5t-48.5 -105.5zM224 -111l-56 -6q41 114 59 234l90 10q-17 -62 -44.5 -132.5t-48.5 -105.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="500" 
d="M209 674h82l-8 -211l174 7v-72l-174 8l8 -456h-82l8 456l-174 -8v72l174 -7z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="500" 
d="M210 674h80l-6 -201l173 7v-72l-173 8v-203l173 8v-72l-173 7l6 -206h-81l6 206l-172 -7v72l173 -8v203l-173 -8v72l173 -7z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="282" 
d="M141 157h-1q-43 0 -73.5 30.5t-30.5 74.5q-1 43 30 74t75 31t74.5 -30.5t30.5 -74.5t-31 -74.5t-74 -30.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1000" 
d="M166 -11h-1q-25 0 -41.5 18t-16.5 45t17 45t43 18t42.5 -17.5t16.5 -45.5q0 -27 -16.5 -45t-43.5 -18zM499 -11h-1q-25 0 -41.5 18t-16.5 45t17 45t43 18t42.5 -17.5t16.5 -45.5q0 -27 -16.5 -45t-43.5 -18zM832 -11h-1q-25 0 -41.5 18t-16.5 45t17 45t43 18t42.5 -17.5
t16.5 -45.5q0 -27 -16.5 -45t-43.5 -18z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1156" 
d="M187 661h1q68 0 108.5 -51t40.5 -142q0 -98 -44 -151.5t-111 -53.5q-65 0 -108 51t-44 144q0 94 44.5 148.5t112.5 54.5zM184 608h-1q-39 0 -61.5 -41.5t-22.5 -104.5q-2 -64 21.5 -105t63.5 -41q42 0 63 40t21 107q0 65 -20.5 105t-63.5 40zM232 -12h-56l382 674h56z
M611 392h1q68 0 108.5 -51t40.5 -143q0 -98 -44 -151.5t-111 -53.5q-65 0 -108 51t-44 145q0 94 44.5 148.5t112.5 54.5zM608 339h-1q-39 0 -61.5 -41.5t-22.5 -105.5q-2 -63 21.5 -104.5t63.5 -41.5q42 0 63 40t21 108q0 64 -20.5 104.5t-63.5 40.5zM976 392h1
q68 0 108.5 -51t40.5 -143q0 -98 -44 -151.5t-111 -53.5q-65 0 -108 51t-44 145q0 94 44.5 148.5t112.5 54.5zM973 339h-1q-39 0 -61.5 -41.5t-22.5 -105.5q-2 -63 21.5 -104.5t63.5 -41.5q42 0 63 40t21 108q0 64 -20.5 104.5t-63.5 40.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="255" 
d="M232 436l-133 -184l134 -184h-71l-133 184l132 184h71z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="255" 
d="M157 252l-134 184h71l133 -184l-133 -184h-70z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="418" 
d="M47 -11h-56l380 672h56z" />
    <glyph glyph-name="fraction" unicode="&#x2215;" horiz-adv-x="418" 
d="M47 -11h-56l380 672h56z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="336" 
d="M166 439h-1q-66 0 -104 53t-38 146q0 92 39.5 146t107.5 54t105.5 -52.5t37.5 -144.5q0 -93 -38 -147.5t-109 -54.5zM167 490h1q36 0 55.5 39t19.5 111q0 147 -75 147q-34 0 -54.5 -39t-20.5 -108q0 -71 20 -110.5t54 -39.5z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="347" 
d="M272 444h-67v101h-189v41l177 247h79v-238h55v-50h-55v-101zM205 595v115q0 4 3 64h-2q-19 -37 -34 -59l-87 -119l1 -1h119z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="308" 
d="M268 831v-55h-155l-13 -85q16 2 31 2q56 0 93 -22q55 -31 55 -100q0 -58 -43 -95.5t-112 -37.5q-65 0 -105 24l14 49q40 -21 89 -21q36 0 62 20.5t26 54.5q0 79 -113 79q-27 0 -57 -4l26 191h202z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="332" 
d="M267 837v-53q-19 2 -40 -1q-58 -8 -92.5 -41.5t-43.5 -80.5h2q37 41 94 41q54 0 88 -34.5t34 -90.5q0 -58 -39 -98t-99 -40q-68 0 -108 46t-40 120q0 109 67 173q51 48 138 57q30 2 39 2zM170 490h1q31 0 50.5 23.5t19.5 59.5q0 37 -20.5 59t-55.5 22q-48 0 -71 -44
q-5 -10 -5 -22q0 -42 21.5 -70t59.5 -28z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="294" 
d="M19 831h263v-43l-172 -344h-70l171 331v1h-192v55z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="328" 
d="M167 838h1q58 0 89.5 -28.5t31.5 -68.5q0 -60 -63 -88v-2q78 -29 78 -98q0 -51 -39.5 -82.5t-102.5 -31.5q-64 0 -101 31t-37 74q0 70 77 100v3q-63 28 -63 86q0 47 36.5 76t92.5 29zM163 486h1q32 0 52 17.5t20 44.5q0 56 -80 76q-65 -19 -65 -72q0 -27 19.5 -46.5
t52.5 -19.5zM165 792h-1q-30 0 -46.5 -16t-16.5 -40q0 -47 70 -65q55 18 55 63q0 23 -15.5 40.5t-45.5 17.5z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="327" 
d="M58 440v53q20 -2 42 1q50 5 84 33q42 34 52 91h-2q-33 -38 -90 -38q-53 0 -87 33.5t-34 86.5q0 57 40 97.5t102 40.5q65 0 102 -45.5t37 -119.5q0 -114 -68 -179q-50 -46 -131 -52q-31 -3 -47 -2zM162 788h-1q-31 0 -51 -24t-20 -61q0 -32 19.5 -53.5t51.5 -21.5
q50 0 71 37q5 7 5 19q0 46 -19 75t-56 29z" />
    <glyph glyph-name="parenleftsuperior" unicode="&#x207d;" horiz-adv-x="180" 
d="M117 868h51q-72 -103 -72 -242q0 -134 72 -241h-51q-77 103 -77 241q0 139 77 242z" />
    <glyph glyph-name="parenrightsuperior" unicode="&#x207e;" horiz-adv-x="180" 
d="M64 385h-51q72 107 72 242q0 137 -72 241h51q77 -103 77 -241q0 -137 -77 -242z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="336" 
d="M166 -151h-1q-66 0 -104 53t-38 146q0 92 39.5 146t107.5 54t105.5 -52.5t37.5 -144.5q0 -93 -38 -147.5t-109 -54.5zM167 -100h1q36 0 55.5 39t19.5 111q0 147 -75 147q-34 0 -54.5 -39t-20.5 -108q0 -71 20 -110.5t54 -39.5z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="244" 
d="M177 -146h-70v328h-2l-72 -35l-11 52l94 44h61v-389z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="311" 
d="M12 -146v37l63 58q68 62 97 100t29 74q0 30 -19.5 49.5t-56.5 19.5q-42 0 -84 -31l-21 46q50 41 123 41q65 0 98.5 -33.5t33.5 -78.5q0 -51 -32 -94t-101 -103l-30 -28v-2h170v-55h-270z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="305" 
d="M45 170l-17 47q48 31 115 31q61 0 91.5 -27t30.5 -67q0 -30 -20.5 -53.5t-56.5 -36.5v-2q41 -5 67 -32t26 -64q0 -49 -42.5 -83t-115.5 -34q-72 0 -115 28l18 50q38 -26 93 -26q43 0 66 20.5t22 46.5q0 35 -31 52.5t-75 17.5h-27v46h28q34 0 62.5 16t28.5 47
q0 20 -16 34.5t-47 14.5q-43 0 -85 -26z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="347" 
d="M272 -146h-67v101h-189v41l177 247h79v-238h55v-50h-55v-101zM205 5v115q0 4 3 64h-2q-19 -37 -34 -59l-87 -119l1 -1h119z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="308" 
d="M268 241v-55h-155l-13 -85q16 2 31 2q56 0 93 -22q55 -31 55 -100q0 -58 -43 -95.5t-112 -37.5q-65 0 -105 24l14 49q40 -21 89 -21q36 0 62 20.5t26 54.5q0 79 -113 79q-27 0 -57 -4l26 191h202z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="332" 
d="M267 247v-53q-19 2 -40 -1q-58 -8 -92.5 -41.5t-43.5 -80.5h2q37 41 94 41q54 0 88 -34.5t34 -90.5q0 -58 -39 -98t-99 -40q-68 0 -108 46t-40 120q0 109 67 173q51 48 138 57q30 2 39 2zM170 -100h1q31 0 50.5 23.5t19.5 59.5q0 37 -20.5 59t-55.5 22q-48 0 -71 -44
q-5 -10 -5 -22q0 -42 21.5 -70t59.5 -28z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="294" 
d="M19 241h263v-43l-172 -344h-70l171 331v1h-192v55z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="328" 
d="M167 248h1q58 0 89.5 -28.5t31.5 -68.5q0 -60 -63 -88v-2q78 -29 78 -98q0 -51 -39.5 -82.5t-102.5 -31.5q-64 0 -101 31t-37 74q0 70 77 100v3q-63 28 -63 86q0 47 36.5 76t92.5 29zM163 -104h1q32 0 52 17.5t20 44.5q0 56 -80 76q-65 -19 -65 -72q0 -27 19.5 -46.5
t52.5 -19.5zM165 202h-1q-30 0 -46.5 -16t-16.5 -40q0 -47 70 -65q55 18 55 63q0 23 -15.5 40.5t-45.5 17.5z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="327" 
d="M58 -150v53q20 -2 42 1q50 5 84 33q42 34 52 91h-2q-33 -38 -90 -38q-53 0 -87 33.5t-34 86.5q0 57 40 97.5t102 40.5q65 0 102 -45.5t37 -119.5q0 -114 -68 -179q-50 -46 -131 -52q-31 -3 -47 -2zM162 198h-1q-31 0 -51 -24t-20 -61q0 -32 19.5 -53.5t51.5 -21.5
q50 0 71 37q5 7 5 19q0 46 -19 75t-56 29z" />
    <glyph glyph-name="parenleftinferior" unicode="&#x208d;" horiz-adv-x="180" 
d="M117 278h51q-72 -103 -72 -242q0 -134 72 -241h-51q-77 103 -77 241q0 139 77 242z" />
    <glyph glyph-name="parenrightinferior" unicode="&#x208e;" horiz-adv-x="180" 
d="M64 -205h-51q72 107 72 242q0 137 -72 241h51q77 -103 77 -241q0 -137 -77 -242z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="513" 
d="M479 95l17 -64q-62 -42 -149 -42q-128 0 -203 90q-59 63 -72 171h-62v50h56v15q0 16 2 44h-59v50h67q18 105 82 169q78 83 197 83q74 0 134 -32l-20 -66q-49 28 -112 28q-81 0 -133 -54q-45 -47 -59 -128h279v-50h-288q-2 -13 -2 -42v-17h290v-50h-283q12 -85 55 -130
q54 -58 141 -58q68 0 122 33z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="504" 
d="M194 512v-216q131 134 131 264q0 93 -62 93q-30 0 -49.5 -35t-19.5 -106zM432 151l43 -40q-69 -121 -200 -121q-74 0 -115 44t-48 118q0 6 -1 8q-25 -18 -55 -37l-26 49q60 42 80 59v265q0 111 43.5 165t114.5 54q60 0 95 -42t35 -111q0 -90 -51 -174.5t-153 -175.5v-14
q1 -134 102 -134q76 0 136 87z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="916" 
d="M728 640h1q72 0 114 -47t42 -125q0 -88 -46 -132.5t-112 -44.5q-70 0 -114 46t-44 126q0 82 45.5 129.5t113.5 47.5zM727 590h-1q-41 0 -63 -38t-22 -88q0 -53 25 -88t61 -35q39 0 62.5 35t23.5 90q0 50 -21.5 87t-64.5 37zM859 197h-263v52h263v-52zM152 0h-78v654h96
l170 -325q61 -117 102 -217l1 1q-9 117 -9 274v267h78v-654h-89l-172 327q-62 122 -102 221h-2q5 -142 5 -279v-269z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="619" 
d="M31 674h227v-45h-87v-227h-54v227h-86v45zM606 402h-54l-10 160q-1 11 -1.5 37t-0.5 39h-3q-2 -7 -10 -37t-13 -44l-49 -151h-56l-48 155q-2 10 -9.5 37.5t-10.5 39.5h-3q0 -28 -2 -76l-10 -160h-52l20 272h82l46 -133q9 -27 18 -69h2q3 14 10 38t8 29l47 135h79z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="817" 
d="M782 316h-605q-5 0 -5 -4v-183q0 -10 9 -21q93 -99 229 -99q145 0 240 111h55q-52 -61 -130 -96t-166 -35q-155 0 -264.5 98.5t-109.5 237.5t109.5 237.5t264.5 98.5t264 -98.5t109 -237.5v-9zM646 340v184q0 12 -10 22q-95 95 -226 95q-133 0 -228 -98q-10 -10 -10 -23
v-180q0 -5 5 -5h464q5 0 5 5z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="530" 
d="M86 614l-28 59q74 54 173 54q109 0 186 -95t77 -259q0 -182 -78.5 -283t-191.5 -101q-95 0 -146.5 61.5t-51.5 148.5q0 102 63 173t155 71q58 0 101.5 -26.5t62.5 -60.5h1q1 10 1 33q0 125 -55.5 195t-131.5 70q-82 0 -137 -40zM232 61h1q64 0 109.5 63t56.5 153
q-14 39 -52.5 67.5t-88.5 28.5q-60 0 -102 -49.5t-42 -122.5q0 -65 32.5 -102.5t85.5 -37.5z" />
    <glyph glyph-name="uni2206" unicode="&#x2206;" horiz-adv-x="569" 
d="M544 0h-518v49l212 607h97l209 -606v-50zM452 72l-119 348q-30 90 -49 158h-3q-18 -70 -43 -145l-124 -361h338z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="615" 
d="M605 577h-93v-667h-83v667h-244v-667h-83v667h-93v73h596v-73z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="507" 
d="M515 -90h-506v53l251 315l-238 313v59h461v-73h-349v-3l222 -293l-234 -294v-4h393v-73z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="596" 
d="M40 297h516v-60h-516v60z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="562" 
d="M578 827l-230 -922h-63l-160 427l-74 -31l-19 47l138 57l123 -345q12 -34 19 -64h1q6 36 14 64l185 767h66z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="728" 
d="M542 117h-1q-26 0 -50 8.5t-50 28.5t-38 31t-39 38q-27 -27 -40.5 -39.5t-39 -31.5t-51.5 -27t-55 -8q-63 0 -103 41.5t-40 106.5q0 60 43.5 103t107.5 43q27 0 51 -8.5t50 -28.5t38 -31t39 -38q27 27 40.5 39.5t39 31.5t51.5 27t55 8q63 0 103 -41t40 -105
q0 -61 -43.5 -104.5t-107.5 -43.5zM542 176h1q36 0 61.5 26.5t25.5 62.5q0 38 -24 62.5t-61 24.5q-26 0 -55.5 -16.5t-43 -29t-42.5 -41.5q3 -3 28 -27.5t35.5 -32.5t32.5 -18.5t42 -10.5zM183 176h1q26 0 54.5 16t43 29.5t42.5 42.5q-6 6 -23 22.5t-26.5 24.5t-25 19.5
t-31 16.5t-32.5 5q-37 0 -62.5 -25.5t-25.5 -61.5q0 -38 24 -63.5t61 -25.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="316" 
d="M306 788l-12 -56q-16 4 -30 4q-30 0 -46 -22q-29 -36 -29 -157q0 -72 6 -233.5t6 -237.5q0 -132 -42 -188q-33 -48 -96 -48q-35 0 -53 10l11 57q18 -6 36 -6q27 0 44 21q31 38 31 154q0 77 -6 240.5t-6 235.5q0 141 52 196q34 37 89 37q27 0 45 -7z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="596" 
d="M533 417l36 -41q-25 -33 -66 -55.5t-85 -22.5q-21 0 -41.5 4t-32 8.5t-30.5 13t-25 10.5t-22.5 10t-27.5 12t-29 8t-35 4q-62 0 -117 -59l-35 42q27 32 68 54.5t85 22.5q20 0 40.5 -4.5t31.5 -8.5t30.5 -12.5t24.5 -10.5q6 -2 22.5 -10t27.5 -12t29 -8t35 -4q59 0 116 59
zM538 218l36 -40q-26 -33 -67.5 -56t-85.5 -23q-20 0 -40.5 4.5t-31.5 8.5t-31 12.5t-24 10.5q-6 2 -23 10t-27.5 12t-28.5 8t-36 4q-62 0 -117 -59l-35 43q26 31 67.5 53.5t85.5 22.5q20 0 40.5 -4t32 -8.5t30.5 -13t25 -10.5q6 -3 22.5 -10.5t27.5 -11.5t28.5 -8t34.5 -4
q64 0 117 59z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="596" 
d="M556 140h-310l-76 -122h-65l79 122h-144v60h182l89 138h-271v60h309l76 122h65l-79 -122h145v-60h-183l-88 -138h271v-60z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="596" 
d="M530 99l-464 193v59l464 192v-66l-382 -155v-2l382 -154v-67zM530 0h-462v58h462v-58z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="596" 
d="M530 292l-464 -193v67l382 154v2l-382 155v66l464 -192v-59zM528 0h-462v58h462v-58z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="522" 
d="M477 325l-182 -356h-69l-181 356l181 355h69zM401 323l-130 267q-7 10 -10 23h-2q-6 -15 -11 -23l-128 -264l131 -267q8 -13 10 -23h2q2 5 6 13.5t4 9.5z" />
    <glyph glyph-name="uniEFED" unicode="&#xefed;" 
d="M151 721h-1q-21 0 -35 15t-14 36q0 20 14.5 35t35.5 15q20 0 34 -15t14 -35q0 -21 -14 -36t-34 -15z" />
    <glyph glyph-name="uniEFEE" unicode="&#xefee;" 
d="M26 832h52q11 -49 72 -49q62 0 73 49h50q-1 -47 -32 -76.5t-92 -29.5t-91 29t-32 77z" />
    <glyph glyph-name="uniEFF1" unicode="&#xeff1;" 
d="M123 4h57q-53 -67 -53 -100q0 -23 13.5 -36.5t36.5 -13.5q26 0 46 10l14 -46q-32 -23 -80 -23q-43 0 -68.5 22.5t-25.5 63.5q0 58 60 123z" />
    <glyph glyph-name="uniEFF2" unicode="&#xeff2;" 
d="M137 2h54l-31 -53q30 -5 50 -24.5t20 -49.5q0 -41 -30 -61t-73 -20q-40 0 -71 17l15 45q26 -14 55 -14q42 0 42 31q0 35 -78 43z" />
    <glyph glyph-name="uniEFF3" unicode="&#xeff3;" horiz-adv-x="400" 
d="M201 877h1q45 0 72 -26t27 -65q0 -38 -28 -63t-72 -25t-72 25.5t-28 62.5q0 39 27.5 65t72.5 26zM200 841h-1q-21 0 -34 -16t-13 -39q0 -20 13.5 -35.5t34.5 -15.5q22 0 35.5 15t13.5 38q0 22 -13.5 37.5t-35.5 15.5z" />
    <glyph glyph-name="uniEFF5" unicode="&#xeff5;" 
d="M73 715h-47q-1 46 17.5 74t50.5 28q21 0 59 -20q32 -18 48 -18q12 0 18.5 8t8.5 31h46q2 -97 -67 -97q-23 0 -61 19q-36 19 -47 19q-21 0 -26 -44z" />
    <glyph glyph-name="uniEFF7" unicode="&#xeff7;" 
d="M115 824h67l105 -114h-74l-63 69h-2l-64 -69h-71z" />
    <glyph glyph-name="uniF629" unicode="&#xf629;" horiz-adv-x="315" 
d="M206 -8h-46v60q-56 7 -90 44t-34 99q0 58 33.5 99t90.5 51v58h46v-57q34 0 64 -14l-13 -50q-26 14 -61 14q-42 0 -67.5 -27t-25.5 -70q0 -46 26 -71.5t65 -25.5q37 0 67 13l11 -47q-26 -14 -66 -17v-59z" />
    <glyph glyph-name="uniF62A" unicode="&#xf62a;" horiz-adv-x="317" 
d="M179 -51h-45v59q-62 2 -99 27l16 50q43 -27 94 -27q31 0 50 14t19 37q0 22 -15.5 36t-52.5 28q-54 19 -80 43.5t-26 62.5q0 39 26 66t71 34v59h45v-56q48 -1 83 -21l-16 -49q-39 21 -82 21q-32 0 -46.5 -13.5t-14.5 -32.5q0 -20 16 -32.5t60 -28.5q51 -19 74.5 -45
t23.5 -66q0 -38 -26.5 -67.5t-74.5 -36.5v-62z" />
    <glyph glyph-name="uniF62B" unicode="&#xf62b;" horiz-adv-x="191" 
d="M18 208h155v-48h-155v48z" />
    <glyph glyph-name="uniF62C" unicode="&#xf62c;" horiz-adv-x="180" 
d="M117 425h51q-72 -103 -72 -242q0 -134 72 -241h-51q-77 103 -77 241q0 139 77 242z" />
    <glyph glyph-name="uniF62D" unicode="&#xf62d;" horiz-adv-x="180" 
d="M64 -58h-51q72 107 72 242q0 137 -72 241h51q77 -103 77 -241q0 -137 -77 -242z" />
    <glyph glyph-name="uniF62E" unicode="&#xf62e;" horiz-adv-x="315" 
d="M206 258h-46v60q-56 7 -90 44t-34 99q0 58 33.5 99t90.5 51v58h46v-57q34 0 64 -14l-13 -50q-26 14 -61 14q-42 0 -67.5 -27t-25.5 -70q0 -46 26 -71.5t65 -25.5q37 0 67 13l11 -47q-26 -14 -66 -17v-59z" />
    <glyph glyph-name="uniF62F" unicode="&#xf62f;" horiz-adv-x="317" 
d="M179 215h-45v59q-62 2 -99 27l16 50q43 -27 94 -27q31 0 50 14t19 37q0 22 -15.5 36t-52.5 28q-54 19 -80 43.5t-26 62.5q0 39 26 66t71 34v59h45v-56q48 -1 83 -21l-16 -49q-39 21 -82 21q-32 0 -46.5 -13.5t-14.5 -32.5q0 -20 16 -32.5t60 -28.5q51 -19 74.5 -45
t23.5 -66q0 -38 -26.5 -67.5t-74.5 -36.5v-62z" />
    <glyph glyph-name="uniF630" unicode="&#xf630;" horiz-adv-x="191" 
d="M18 474h155v-48h-155v48z" />
    <glyph glyph-name="uniF631" unicode="&#xf631;" horiz-adv-x="180" 
d="M117 691h51q-72 -103 -72 -242q0 -134 72 -241h-51q-77 103 -77 241q0 139 77 242z" />
    <glyph glyph-name="uniF632" unicode="&#xf632;" horiz-adv-x="180" 
d="M64 208h-51q72 107 72 242q0 137 -72 241h51q77 -103 77 -241q0 -137 -77 -242z" />
    <glyph glyph-name="uniF633" unicode="&#xf633;" horiz-adv-x="737" 
d="M449 348l21 112q-20 8 -50 8q-66 0 -113 -53.5t-47 -126.5q0 -34 17 -55t49 -21q41 0 77.5 43t45.5 93zM508 71l16 -43q-76 -40 -178 -40q-127 0 -215 86t-88 227q0 159 101.5 271.5t263.5 112.5q129 0 209.5 -81.5t80.5 -207.5q0 -108 -51.5 -173t-125.5 -65
q-33 0 -54 22t-20 66h-3q-54 -88 -142 -88q-45 0 -76.5 34.5t-31.5 91.5q0 96 67.5 166.5t169.5 70.5q63 0 107 -21l-33 -181q-22 -110 29 -112q41 -2 74.5 49t33.5 133q0 112 -64 181t-178 69q-126 0 -213 -92t-87 -240q0 -123 72 -197t185 -74q87 0 151 35z" />
    <glyph glyph-name="uniF634" unicode="&#xf634;" 
d="M93 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="uniF638" unicode="&#xf638;" horiz-adv-x="513" 
d="M145 168l202 374q-32 51 -90 51q-62 0 -98.5 -72t-37.5 -193q-1 -92 24 -160zM371 482l-202 -376q34 -49 86 -49q68 0 102.5 71.5t34.5 199.5q0 88 -21 154zM473 649l-49 -80q53 -85 53 -238q0 -165 -58.5 -253.5t-166.5 -88.5q-75 0 -126 50l-36 -63l-44 31l46 75
q-55 86 -56 241q-1 161 61.5 249.5t164.5 88.5q78 0 128 -49l39 66z" />
    <glyph glyph-name="uniF639" unicode="&#xf639;" horiz-adv-x="513" 
d="M253 -11h-1q-97 0 -156 88t-60 246q0 160 62.5 249t164.5 89t158.5 -87t56.5 -242q0 -165 -59 -254t-166 -89zM256 57h1q65 0 98.5 71.5t33.5 199.5q0 125 -32.5 195t-99.5 70q-60 0 -96 -71.5t-36 -193.5q-1 -126 34.5 -198.5t96.5 -72.5z" />
    <glyph glyph-name="uniF63A" unicode="&#xf63a;" horiz-adv-x="447" 
d="M418 0h-415v54l69 67q136 129 189 200t53 140q0 56 -30 91.5t-98 35.5q-72 0 -138 -55l-28 62q78 66 184 66q97 0 147.5 -56t50.5 -133q0 -83 -53 -159.5t-171 -189.5l-51 -48v-2h291v-73z" />
    <glyph glyph-name="uniF63B" unicode="&#xf63b;" horiz-adv-x="450" 
d="M7 33l24 67q67 -40 144 -40t112.5 38.5t34.5 87.5q-1 64 -49.5 97t-118.5 33h-49v66h49q55 0 100 28.5t45 81.5q0 42 -28.5 70t-85.5 28q-70 0 -130 -43l-24 64q70 50 171 50q90 0 138 -45t48 -110q0 -52 -31 -92.5t-89 -61.5v-2q63 -12 104 -56t41 -110
q0 -82 -63 -138.5t-174 -56.5q-102 0 -169 44z" />
    <glyph glyph-name="uniF63C" unicode="&#xf63c;" horiz-adv-x="503" 
d="M396 0h-83v178h-302v57l290 415h95v-404h91v-68h-91v-178zM100 246h213v217q0 51 3 102h-3q-26 -49 -54 -96l-159 -221v-2z" />
    <glyph glyph-name="uniF63D" unicode="&#xf63d;" horiz-adv-x="460" 
d="M406 650v-74h-248l-25 -167q30 4 53 4q82 0 140 -35q41 -23 67 -66t26 -101q0 -96 -68.5 -159t-173.5 -63q-98 0 -162 40l22 67q61 -36 140 -36q64 0 109 39t44 103q0 65 -46.5 104t-139.5 39q-49 0 -91 -7l42 312h311z" />
    <glyph glyph-name="uniF63E" unicode="&#xf63e;" horiz-adv-x="515" 
d="M418 660v-72q-26 1 -66 -5q-99 -16 -156.5 -79.5t-69.5 -150.5h2q56 76 158 76q87 0 141.5 -58t54.5 -152t-59.5 -162t-157.5 -68q-104 0 -166.5 75.5t-62.5 201.5q0 185 110 293q82 81 208 96q33 5 64 5zM266 57h1q56 0 91 43t35 113q0 69 -36.5 109t-97.5 40
q-40 0 -74.5 -22t-52.5 -58q-9 -18 -9 -38q1 -83 38.5 -135t104.5 -52z" />
    <glyph glyph-name="uniF63F" unicode="&#xf63f;" horiz-adv-x="412" 
d="M1 650h410v-58l-283 -592h-90l281 575v2h-318v73z" />
    <glyph glyph-name="uniF640" unicode="&#xf640;" horiz-adv-x="513" 
d="M262 661h1q90 0 140 -47t50 -113q0 -102 -108 -152v-3q131 -52 131 -168q0 -84 -63 -136.5t-158 -52.5q-99 0 -158.5 50.5t-59.5 124.5q0 120 128 175v3q-52 24 -78 63t-26 83q0 76 57 124.5t144 48.5zM256 53h1q59 0 94.5 32.5t35.5 83.5q0 56 -36.5 89t-106.5 53
q-56 -16 -86.5 -52t-30.5 -83q-2 -51 33.5 -87t95.5 -36zM259 598h-1q-53 0 -83 -30t-30 -74q-1 -46 31.5 -75t92.5 -45q45 15 72 45.5t27 72.5q0 44 -27.5 75t-81.5 31z" />
    <glyph glyph-name="uniF641" unicode="&#xf641;" horiz-adv-x="517" 
d="M96 -10v72q29 -2 71 4q80 9 136 62q69 64 86 173h-3q-57 -70 -154 -70q-86 0 -139 57t-53 143q0 94 63 162t160 68q99 0 157 -74t58 -200q0 -197 -112 -305q-77 -74 -189 -87q-52 -6 -81 -5zM256 594h-1q-55 0 -91 -44.5t-36 -113.5q0 -61 34 -99.5t91 -38.5q87 0 129 68
q7 14 7 32q1 88 -33 142t-100 54z" />
    <glyph glyph-name="uniF642" unicode="&#xf642;" horiz-adv-x="818" 
d="M185 603h1q68 0 108.5 -51t40.5 -143q0 -98 -44 -151.5t-111 -53.5q-65 0 -108 51t-44 145q0 94 44.5 148.5t112.5 54.5zM182 550h-1q-39 0 -61.5 -41.5t-22.5 -105.5q-1 -63 22 -104.5t63 -41.5q42 0 63 40t21 108q0 64 -20.5 104.5t-63.5 40.5zM261 -12h-56l351 620h56
zM640 392h1q68 0 108.5 -51t40.5 -143q0 -98 -44 -151.5t-111 -53.5q-65 0 -108 51t-44 145q0 94 44.5 148.5t112.5 54.5zM637 339h-1q-39 0 -61.5 -41.5t-22.5 -105.5q-1 -63 22 -104.5t63 -41.5q42 0 63 40t21 108q0 64 -20.5 104.5t-63.5 40.5z" />
    <glyph glyph-name="uniF643" unicode="&#xf643;" horiz-adv-x="530" 
d="M265 -11h-1q-96 0 -160 68t-64 185q0 116 64.5 185t160.5 69t160.5 -68.5t64.5 -185.5q0 -115 -64.5 -184t-160.5 -69zM265 56h1q64 0 101.5 52t37.5 134q0 83 -37.5 135t-102.5 52t-102.5 -52t-37.5 -135q0 -82 37.5 -134t102.5 -52z" />
    <glyph glyph-name="uniF644" unicode="&#xf644;" horiz-adv-x="530" 
d="M365 0h-84v411h-2l-186 -82l-15 65l214 91h73v-485z" />
    <glyph glyph-name="uniF645" unicode="&#xf645;" horiz-adv-x="530" 
d="M462 0h-396v48l49 34q119 79 171.5 134.5t52.5 113.5q0 47 -29.5 72.5t-77.5 25.5q-69 0 -136 -49l-24 57q83 62 182 62q77 0 125 -41.5t48 -114.5q0 -65 -44.5 -119.5t-132.5 -116.5l-51 -37v-3q33 4 82 4h181v-70z" />
    <glyph glyph-name="uniF646" unicode="&#xf646;" horiz-adv-x="530" 
d="M62 -94l10 65q50 -16 109 -16q75 0 122 33.5t47 87.5q0 47 -41.5 74.5t-110.5 27.5q-31 0 -55 -2l-2 64l19 1q73 5 118 33t45 73q0 80 -105 80q-60 0 -123 -34l-18 58q26 18 70 31.5t90 13.5q78 0 126 -36.5t48 -97.5q0 -50 -32.5 -86.5t-85.5 -51.5v-3q63 -9 105 -46
t42 -98q0 -84 -73.5 -137.5t-177.5 -53.5q-83 0 -127 20z" />
    <glyph glyph-name="uniF647" unicode="&#xf647;" horiz-adv-x="530" 
d="M497 50h-91v-166h-82v166h-292v57l279 385h95v-375h91v-67zM324 117v200q0 63 3 95l-4 1q-27 -53 -54 -95l-150 -198l1 -3h204z" />
    <glyph glyph-name="uniF648" unicode="&#xf648;" horiz-adv-x="530" 
d="M78 -97l11 63q43 -13 102 -13q68 0 116 34.5t48 94.5q0 44 -31 73q-55 54 -227 43l51 287h287l-11 -72h-217l-29 -150q153 4 220 -61q46 -44 46 -113q0 -91 -70 -147t-182 -56q-72 0 -114 17z" />
    <glyph glyph-name="uniF649" unicode="&#xf649;" horiz-adv-x="530" 
d="M441 606l6 -65q-129 -8 -204 -68.5t-93 -150.5l3 -1q21 30 59.5 51.5t86.5 21.5q79 0 133 -52t54 -139q0 -97 -62.5 -155.5t-145.5 -58.5q-100 0 -161.5 68t-61.5 179q0 157 104 258.5t282 111.5zM278 56h1q48 0 84 37.5t36 104.5q0 61 -33.5 97.5t-88.5 36.5
q-60 0 -101 -43q-32 -33 -32 -83q0 -63 37 -106.5t97 -43.5z" />
    <glyph glyph-name="uniF64A" unicode="&#xf64a;" horiz-adv-x="530" 
d="M454 485v-56q-45 -118 -124 -269.5t-158 -273.5l-83 16q65 94 151 251t124 257v2h-297v73h387z" />
    <glyph glyph-name="uniF64B" unicode="&#xf64b;" horiz-adv-x="530" 
d="M350 320v-3q59 -21 94 -60t35 -91q0 -79 -63 -128t-155 -49q-95 0 -154 46.5t-59 119.5q0 102 125 158v3q-101 43 -101 129q0 71 56.5 116.5t140.5 45.5q83 0 135 -43.5t52 -107.5q0 -86 -106 -136zM268 547q-47 0 -79 -26t-32 -69q0 -76 121 -108q43 13 69.5 40.5
t26.5 67.5q0 45 -31 70t-75 25zM262 52h1q54 0 90 29.5t36 76.5q0 92 -142 129q-51 -17 -81 -50t-30 -74q0 -49 34 -80t92 -31z" />
    <glyph glyph-name="uniF64C" unicode="&#xf64c;" horiz-adv-x="530" 
d="M89 -113l-7 65q125 5 201.5 60.5t98.5 147.5h-2q-17 -27 -56.5 -47t-87.5 -20q-79 0 -132.5 52t-53.5 139q0 91 62 152.5t154 61.5t152.5 -64.5t60.5 -176.5q0 -165 -107.5 -265t-282.5 -105zM259 155h1q36 0 68.5 19t47.5 46q16 21 16 60q0 66 -38 108t-92 42
q-50 0 -87.5 -36.5t-37.5 -102.5t35 -101t87 -35z" />
    <glyph glyph-name="uniF64E" unicode="&#xf64e;" horiz-adv-x="530" 
d="M466 83l12 -61q-51 -33 -138 -33q-108 0 -178 69q-46 46 -62 120h-62v49h55q0 6 -0.5 14t-0.5 10q0 9 2 35h-56v48h64q17 71 63 117q75 75 189 75q67 0 123 -28l-20 -62q-46 24 -104 24q-74 0 -123 -48q-34 -34 -41 -78h248v-48h-258q-2 -22 -2 -29q0 -18 1 -30h259v-49
h-249q11 -46 39 -72q50 -49 126 -49q67 0 113 26z" />
    <glyph glyph-name="uniF64F" unicode="&#xf64f;" horiz-adv-x="530" 
d="M128 282v58h99l6 53q4 32 11.5 60.5t22.5 57.5t35.5 49.5t51.5 33.5t70 13q43 0 69 -15l-16 -64q-27 10 -53 10q-89 0 -106 -139l-7 -59h122v-58h-128l-23 -182q-12 -106 -51.5 -161.5t-122.5 -55.5q-50 0 -77 17l16 62q23 -11 56 -11q44 0 66 38t31 119l20 174h-92z" />
    <glyph glyph-name="uniF650" unicode="&#xf650;" horiz-adv-x="530" 
d="M205 226h104l19 145h-104zM174 -3h-60l23 170h-83v59h92l19 145h-87v59h95l22 165h59l-22 -165h105l22 165h59l-22 -165h82v-59h-91l-18 -145h86v-59h-95l-22 -170h-61l23 170h-104z" />
    <glyph glyph-name="uniF651" unicode="&#xf651;" horiz-adv-x="530" 
d="M479 0h-416v50q50 21 78.5 54.5t28.5 73.5q0 16 -4 44h-97v55h86q-8 42 -8 62q0 86 53 140t139 54q67 0 109 -25l-19 -64q-36 20 -90 20t-81.5 -33.5t-27.5 -92.5q0 -28 7 -61h149v-55h-139q3 -16 3 -32q-1 -76 -63 -117v-2h292v-71z" />
    <glyph glyph-name="uniF652" unicode="&#xf652;" horiz-adv-x="530" 
d="M305 0h-83v115h-152v49h152v60h-152v49h127l-149 242h93l86 -154q23 -43 39 -78h2q4 9 42 80l91 152h91l-160 -242h127v-49h-154v-60h154v-49h-154v-115z" />
    <glyph glyph-name="uniF653" unicode="&#xf653;" horiz-adv-x="530" 
d="M295 -82h-57v80q-91 2 -148 40l24 60q22 -15 60 -25.5t74 -10.5q44 0 75 19.5t31 52.5q0 28 -23 46.5t-77 37.5q-80 26 -116.5 57.5t-36.5 80.5q0 50 39.5 84.5t103.5 42.5v85h57v-83q72 -3 123 -31l-23 -60q-51 30 -115 30q-42 0 -69.5 -17.5t-27.5 -44.5t22.5 -44
t86.5 -38q76 -25 111 -59t35 -83t-40 -88.5t-109 -49.5v-82z" />
    <glyph glyph-name="uniF654" unicode="&#xf654;" horiz-adv-x="530" 
d="M403 129l14 -59q-40 -21 -100 -24v-72h-55v73q-83 9 -132 61t-49 136q0 81 49.5 137t131.5 68v68h55v-66q57 -1 98 -20l-16 -60q-33 18 -94 18q-63 0 -102 -39t-39 -102q0 -66 39 -102.5t98 -36.5q55 0 102 20z" />
    <glyph glyph-name="uniF655" unicode="&#xf655;" horiz-adv-x="336" 
d="M166 -4h-1q-66 0 -104 53t-38 146q0 92 39.5 146t107.5 54t105.5 -52.5t37.5 -144.5q0 -93 -38 -147.5t-109 -54.5zM167 47h1q36 0 55.5 39t19.5 111q0 147 -75 147q-34 0 -54.5 -39t-20.5 -108q0 -71 20 -110.5t54 -39.5z" />
    <glyph glyph-name="uniF656" unicode="&#xf656;" horiz-adv-x="244" 
d="M177 1h-70v328h-2l-72 -35l-11 52l94 44h61v-389z" />
    <glyph glyph-name="uniF657" unicode="&#xf657;" horiz-adv-x="311" 
d="M12 1v37l63 58q68 62 97 100t29 74q0 30 -19.5 49.5t-56.5 19.5q-42 0 -84 -31l-21 46q50 41 123 41q65 0 98.5 -33.5t33.5 -78.5q0 -51 -32 -94t-101 -103l-30 -28v-2h170v-55h-270z" />
    <glyph glyph-name="uniF658" unicode="&#xf658;" horiz-adv-x="305" 
d="M45 317l-17 47q48 31 115 31q61 0 91.5 -27t30.5 -67q0 -30 -20.5 -53.5t-56.5 -36.5v-2q41 -5 67 -32t26 -64q0 -49 -42.5 -83t-115.5 -34q-72 0 -115 28l18 50q38 -26 93 -26q43 0 66 20.5t22 46.5q0 35 -31 52.5t-75 17.5h-27v46h28q34 0 62.5 16t28.5 47
q0 20 -16 34.5t-47 14.5q-43 0 -85 -26z" />
    <glyph glyph-name="uniF659" unicode="&#xf659;" horiz-adv-x="347" 
d="M272 1h-67v101h-189v41l177 247h79v-238h55v-50h-55v-101zM205 152v115q0 4 3 64h-2q-19 -37 -34 -59l-87 -119l1 -1h119z" />
    <glyph glyph-name="uniF65A" unicode="&#xf65a;" horiz-adv-x="308" 
d="M268 388v-55h-155l-13 -85q16 2 31 2q56 0 93 -22q55 -31 55 -100q0 -58 -43 -95.5t-112 -37.5q-65 0 -105 24l14 49q40 -21 89 -21q36 0 62 20.5t26 54.5q0 79 -113 79q-27 0 -57 -4l26 191h202z" />
    <glyph glyph-name="uniF65B" unicode="&#xf65b;" horiz-adv-x="332" 
d="M267 394v-53q-19 2 -40 -1q-58 -8 -92.5 -41.5t-43.5 -80.5h2q37 41 94 41q54 0 88 -34.5t34 -90.5q0 -58 -39 -98t-99 -40q-68 0 -108 46t-40 120q0 109 67 173q51 48 138 57q30 2 39 2zM170 47h1q31 0 50.5 23.5t19.5 59.5q0 37 -20.5 59t-55.5 22q-48 0 -71 -44
q-5 -10 -5 -22q0 -42 21.5 -70t59.5 -28z" />
    <glyph glyph-name="uniF65C" unicode="&#xf65c;" horiz-adv-x="294" 
d="M19 388h263v-43l-172 -344h-70l171 331v1h-192v55z" />
    <glyph glyph-name="uniF65D" unicode="&#xf65d;" horiz-adv-x="328" 
d="M167 395h1q58 0 89.5 -28.5t31.5 -68.5q0 -60 -63 -88v-2q78 -29 78 -98q0 -51 -39.5 -82.5t-102.5 -31.5q-64 0 -101 31t-37 74q0 70 77 100v3q-63 28 -63 86q0 47 36.5 76t92.5 29zM163 43h1q32 0 52 17.5t20 44.5q0 56 -80 76q-65 -19 -65 -72q0 -27 19.5 -46.5
t52.5 -19.5zM165 349h-1q-30 0 -46.5 -16t-16.5 -40q0 -47 70 -65q55 18 55 63q0 23 -15.5 40.5t-45.5 17.5z" />
    <glyph glyph-name="uniF65E" unicode="&#xf65e;" horiz-adv-x="327" 
d="M58 -3v53q20 -2 42 1q50 5 84 33q42 34 52 91h-2q-33 -38 -90 -38q-53 0 -87 33.5t-34 86.5q0 57 40 97.5t102 40.5q65 0 102 -45.5t37 -119.5q0 -114 -68 -179q-50 -46 -131 -52q-31 -3 -47 -2zM162 345h-1q-31 0 -51 -24t-20 -61q0 -32 19.5 -53.5t51.5 -21.5
q50 0 71 37q5 7 5 19q0 46 -19 75t-56 29z" />
    <glyph glyph-name="uniF65F" unicode="&#xf65f;" horiz-adv-x="131" 
d="M48 -67l-48 -5q27 75 39 155l74 6q-30 -99 -65 -156z" />
    <glyph glyph-name="uniF660" unicode="&#xf660;" horiz-adv-x="131" 
d="M65 -5h-1q-19 0 -31 13t-12 32q0 20 12.5 32.5t31.5 12.5q20 0 32 -12.5t12 -32.5q0 -19 -12.5 -32t-31.5 -13z" />
    <glyph glyph-name="uniF661" unicode="&#xf661;" horiz-adv-x="336" 
d="M166 262h-1q-66 0 -104 53t-38 146q0 92 39.5 146t107.5 54t105.5 -52.5t37.5 -144.5q0 -93 -38 -147.5t-109 -54.5zM167 313h1q36 0 55.5 39t19.5 111q0 147 -75 147q-34 0 -54.5 -39t-20.5 -108q0 -71 20 -110.5t54 -39.5z" />
    <glyph glyph-name="uniF662" unicode="&#xf662;" horiz-adv-x="244" 
d="M177 267h-70v328h-2l-72 -35l-11 52l94 44h61v-389z" />
    <glyph glyph-name="uniF663" unicode="&#xf663;" horiz-adv-x="311" 
d="M12 267v37l63 58q68 62 97 100t29 74q0 30 -19.5 49.5t-56.5 19.5q-42 0 -84 -31l-21 46q50 41 123 41q65 0 98.5 -33.5t33.5 -78.5q0 -51 -32 -94t-101 -103l-30 -28v-2h170v-55h-270z" />
    <glyph glyph-name="uniF664" unicode="&#xf664;" horiz-adv-x="305" 
d="M45 583l-17 47q48 31 115 31q61 0 91.5 -27t30.5 -67q0 -30 -20.5 -53.5t-56.5 -36.5v-2q41 -5 67 -32t26 -64q0 -49 -42.5 -83t-115.5 -34q-72 0 -115 28l18 50q38 -26 93 -26q43 0 66 20.5t22 46.5q0 35 -31 52.5t-75 17.5h-27v46h28q34 0 62.5 16t28.5 47
q0 20 -16 34.5t-47 14.5q-43 0 -85 -26z" />
    <glyph glyph-name="uniF665" unicode="&#xf665;" horiz-adv-x="347" 
d="M272 267h-67v101h-189v41l177 247h79v-238h55v-50h-55v-101zM205 418v115q0 4 3 64h-2q-19 -37 -34 -59l-87 -119l1 -1h119z" />
    <glyph glyph-name="uniF666" unicode="&#xf666;" horiz-adv-x="308" 
d="M268 654v-55h-155l-13 -85q16 2 31 2q56 0 93 -22q55 -31 55 -100q0 -58 -43 -95.5t-112 -37.5q-65 0 -105 24l14 49q40 -21 89 -21q36 0 62 20.5t26 54.5q0 79 -113 79q-27 0 -57 -4l26 191h202z" />
    <glyph glyph-name="uniF667" unicode="&#xf667;" horiz-adv-x="332" 
d="M267 660v-53q-19 2 -40 -1q-58 -8 -92.5 -41.5t-43.5 -80.5h2q37 41 94 41q54 0 88 -34.5t34 -90.5q0 -58 -39 -98t-99 -40q-68 0 -108 46t-40 120q0 109 67 173q51 48 138 57q30 2 39 2zM170 313h1q31 0 50.5 23.5t19.5 59.5q0 37 -20.5 59t-55.5 22q-48 0 -71 -44
q-5 -10 -5 -22q0 -42 21.5 -70t59.5 -28z" />
    <glyph glyph-name="uniF668" unicode="&#xf668;" horiz-adv-x="294" 
d="M19 654h263v-43l-172 -344h-70l171 331v1h-192v55z" />
    <glyph glyph-name="uniF669" unicode="&#xf669;" horiz-adv-x="328" 
d="M167 661h1q58 0 89.5 -28.5t31.5 -68.5q0 -60 -63 -88v-2q78 -29 78 -98q0 -51 -39.5 -82.5t-102.5 -31.5q-64 0 -101 31t-37 74q0 70 77 100v3q-63 28 -63 86q0 47 36.5 76t92.5 29zM163 309h1q32 0 52 17.5t20 44.5q0 56 -80 76q-65 -19 -65 -72q0 -27 19.5 -46.5
t52.5 -19.5zM165 615h-1q-30 0 -46.5 -16t-16.5 -40q0 -47 70 -65q55 18 55 63q0 23 -15.5 40.5t-45.5 17.5z" />
    <glyph glyph-name="uniF66A" unicode="&#xf66a;" horiz-adv-x="327" 
d="M58 263v53q20 -2 42 1q50 5 84 33q42 34 52 91h-2q-33 -38 -90 -38q-53 0 -87 33.5t-34 86.5q0 57 40 97.5t102 40.5q65 0 102 -45.5t37 -119.5q0 -114 -68 -179q-50 -46 -131 -52q-31 -3 -47 -2zM162 611h-1q-31 0 -51 -24t-20 -61q0 -32 19.5 -53.5t51.5 -21.5
q50 0 71 37q5 7 5 19q0 46 -19 75t-56 29z" />
    <glyph glyph-name="uniF66B" unicode="&#xf66b;" horiz-adv-x="131" 
d="M48 199l-48 -5q27 75 39 155l74 6q-30 -99 -65 -156z" />
    <glyph glyph-name="uniF66C" unicode="&#xf66c;" horiz-adv-x="131" 
d="M65 261h-1q-19 0 -31 13t-12 32q0 20 12.5 32.5t31.5 12.5q20 0 32 -12.5t12 -32.5q0 -19 -12.5 -32t-31.5 -13z" />
    <glyph glyph-name="uniF6AE" unicode="&#xf6ae;" horiz-adv-x="284" 
d="M195 733h68q-125 -170 -125 -410q0 -226 125 -405h-68q-131 173 -131 406q1 236 131 409z" />
    <glyph glyph-name="uniF6AF" unicode="&#xf6af;" horiz-adv-x="284" 
d="M88 -82h-68q125 177 125 408t-125 407h68q131 -171 131 -407q-1 -233 -131 -408z" />
    <glyph glyph-name="uniF6B0" unicode="&#xf6b0;" horiz-adv-x="284" 
d="M264 -73h-183v798h183v-55h-114v-688h114v-55z" />
    <glyph glyph-name="uniF6B1" unicode="&#xf6b1;" horiz-adv-x="284" 
d="M20 725h183v-798h-183v55h114v688h-114v55z" />
    <glyph glyph-name="uniF6B2" unicode="&#xf6b2;" horiz-adv-x="284" 
d="M28 302v51q48 0 64.5 20.5t16.5 53.5q0 28 -8 84t-8 83q0 66 39.5 98.5t106.5 32.5h20v-55h-17q-80 -1 -80 -86q0 -15 7 -71q8 -50 8 -76q1 -88 -72 -108v-2q73 -18 72 -109q0 -27 -8 -77q-7 -56 -7 -72q0 -86 80 -87h17v-55h-21q-145 0 -145 137q0 33 9 82q7 65 7 81
q0 75 -81 75z" />
    <glyph glyph-name="uniF6B3" unicode="&#xf6b3;" horiz-adv-x="284" 
d="M256 353v-51q-81 0 -81 -75q0 -25 8 -81q8 -54 8 -82q0 -137 -145 -137h-21v55h17q80 1 80 87q0 16 -7 72q-8 50 -8 77q-1 91 72 109v2q-73 20 -72 108q0 26 8 76q7 56 7 71q0 85 -80 86h-17v55h20q67 0 106.5 -32.5t39.5 -98.5q0 -27 -8 -83t-8 -84q0 -33 16.5 -53.5
t64.5 -20.5z" />
    <glyph glyph-name="uniF6B4" unicode="&#xf6b4;" horiz-adv-x="230" 
d="M163 0h-96l14 478h68zM115 683h1q26 0 42 -17.5t16 -44.5t-16 -44.5t-43 -17.5q-26 0 -42.5 17.5t-16.5 44.5t16.5 44.5t42.5 17.5z" />
    <glyph glyph-name="uniF6B5" unicode="&#xf6b5;" horiz-adv-x="406" 
d="M237 683h1q26 0 42 -17.5t16 -45.5q0 -27 -16 -44.5t-43 -17.5q-26 0 -42.5 17.5t-16.5 44.5q0 28 16.5 45.5t42.5 17.5zM197 481h78l2 -26q10 -87 -62 -173q-37 -44 -53.5 -74.5t-16.5 -62.5q0 -39 24.5 -62.5t70.5 -23.5q60 0 102 31l23 -63q-56 -41 -143 -41
q-81 0 -124 43t-43 105q0 46 21.5 84.5t64.5 88.5q61 74 57 148z" />
    <glyph glyph-name="uniF6B6" unicode="&#xf6b6;" horiz-adv-x="419" 
d="M232 511l-133 -184l134 -184h-71l-133 184l132 184h71zM396 511l-133 -184l134 -184h-71l-133 184l132 184h71z" />
    <glyph glyph-name="uniF6B7" unicode="&#xf6b7;" horiz-adv-x="419" 
d="M157 327l-134 184h71l133 -184l-133 -184h-70zM320 327l-134 184h71l133 -184l-133 -184h-70z" />
    <glyph glyph-name="uniF6B8" unicode="&#xf6b8;" horiz-adv-x="255" 
d="M232 511l-133 -184l134 -184h-71l-133 184l132 184h71z" />
    <glyph glyph-name="uniF6B9" unicode="&#xf6b9;" horiz-adv-x="255" 
d="M157 327l-134 184h71l133 -184l-133 -184h-70z" />
    <glyph glyph-name="uniF6BA" unicode="&#xf6ba;" horiz-adv-x="307" 
d="M30 377h247v-64h-247v64z" />
    <glyph glyph-name="uniF6BB" unicode="&#xf6bb;" horiz-adv-x="500" 
d="M30 359h440v-60h-440v60z" />
    <glyph glyph-name="uniF6BC" unicode="&#xf6bc;" horiz-adv-x="1000" 
d="M30 359h940v-60h-940v60z" />
    <glyph glyph-name="uniF6BD" unicode="&#xf6bd;" horiz-adv-x="207" 
d="M103 275h-1q-25 0 -41.5 18t-16.5 44q0 28 17 45.5t43 17.5t42.5 -17.5t16.5 -45.5q0 -27 -16.5 -44.5t-43.5 -17.5z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" 
d="M93 -222l-17 41q75 12 75 65q0 33 -34 61l65 14q40 -26 40 -74q0 -51 -38 -77.5t-91 -29.5z" />
    <glyph glyph-name="Acute" unicode="&#xf6c9;" 
d="M198 827h106l-128 -117h-71z" />
    <glyph glyph-name="Caron" unicode="&#xf6ca;" 
d="M185 710h-67l-105 114h74l64 -69h2l63 69h71z" />
    <glyph glyph-name="Dieresis" unicode="&#xf6cb;" 
d="M58 721h-1q-21 0 -34.5 15t-13.5 36t14 35.5t35 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-34.5 -14.5zM243 721h-1q-21 0 -34.5 15t-13.5 36t14.5 35.5t35.5 14.5t34.5 -14.5t13.5 -35.5q0 -22 -13.5 -36.5t-35.5 -14.5z" />
    <glyph glyph-name="DieresisAcute" unicode="&#xf6cc;" 
d="M14 721h-1q-20 0 -33 14.5t-13 34.5q0 21 14 35.5t34 14.5t33.5 -14.5t13.5 -35.5t-13.5 -35t-34.5 -14zM287 721h-1q-19 0 -32 14.5t-13 34.5q0 21 14 35.5t34 14.5t33.5 -14.5t13.5 -35.5t-13.5 -35t-35.5 -14zM142 828h102l-116 -117h-67z" />
    <glyph glyph-name="DieresisGrave" unicode="&#xf6cd;" 
d="M13 721h-1q-20 0 -33 14.5t-13 34.5q0 21 14 35.5t34 14.5t33.5 -14.5t13.5 -35.5t-13.5 -35t-34.5 -14zM287 721h-1q-19 0 -32 14.5t-13 34.5q0 21 14 35.5t34 14.5t33.5 -14.5t13.5 -35.5t-13.5 -35t-35.5 -14zM56 828h103l81 -117h-67z" />
    <glyph glyph-name="Grave" unicode="&#xf6ce;" 
d="M-2 827h106l93 -116h-71z" />
    <glyph glyph-name="Hungarumlaut" unicode="&#xf6cf;" 
d="M117 827h96l-112 -112h-64zM264 827h96l-112 -112h-64z" />
    <glyph glyph-name="Macron" unicode="&#xf6d0;" 
d="M31 788h240v-55h-240v55z" />
    <glyph glyph-name="cyrBreve" unicode="&#xf6d1;" 
d="M22 834h67q4 -29 16.5 -45.5t44.5 -16.5t44.5 16.5t16.5 45.5h67q-8 -108 -130 -108q-118 0 -126 108z" />
    <glyph glyph-name="cyrFlex" unicode="&#xf6d2;" 
d="M278 726l-67 -1q-4 27 -16.5 43.5t-44.5 16.5q-53 0 -61 -60l-67 1q4 49 36.5 77t93.5 28q59 0 91 -28.5t35 -76.5z" />
    <glyph glyph-name="dblGrave" unicode="&#xf6d3;" 
d="M-45 828h103l91 -117h-66zM129 828h102l92 -117h-67z" />
    <glyph glyph-name="cyrbreve" unicode="&#xf6d4;" horiz-adv-x="301" 
d="M21 681h64q2 -34 18.5 -55t45.5 -21q59 0 68 76h63q-3 -60 -38.5 -92t-94.5 -32q-62 0 -92.5 34t-33.5 90z" />
    <glyph glyph-name="cyrflex" unicode="&#xf6d5;" 
d="M82 563h-63q3 60 38.5 92t94.5 32q62 0 92.5 -34t33.5 -90h-64q-7 76 -64 76q-59 0 -68 -76z" />
    <glyph glyph-name="dblgrave" unicode="&#xf6d6;" 
d="M-2 685h91l80 -142h-55zM149 685h90l81 -142h-56z" />
    <glyph glyph-name="dieresisacute" unicode="&#xf6d7;" horiz-adv-x="301" 
d="M24 573h-1q-20 0 -33.5 14.5t-13.5 34.5t14 34.5t35 14.5q20 0 33.5 -14t13.5 -35t-13.5 -35t-34.5 -14zM270 573h-1q-20 0 -33 14.5t-13 34.5t14 34.5t35 14.5q20 0 33.5 -14t13.5 -35t-13.5 -35t-35.5 -14zM151 701h92l-111 -143h-58z" />
    <glyph glyph-name="dieresisgrave" unicode="&#xf6d8;" horiz-adv-x="301" 
d="M28 573h-1q-20 0 -33.5 14.5t-13.5 34.5t14 34.5t35 14.5q20 0 33.5 -14t13.5 -35t-13.5 -35t-34.5 -14zM269 573h-1q-20 0 -33 14.5t-13 34.5t14 34.5t35 14.5q20 0 33.5 -14t13.5 -35t-13.5 -35t-35.5 -14zM56 701h92l78 -143h-58z" />
    <glyph glyph-name="uniF6DC" unicode="&#xf6dc;" horiz-adv-x="326" 
d="M151 0v568h-2l-113 -61l-17 67l142 76h75v-650h-85z" />
    <glyph glyph-name="centinferior" unicode="&#xf6df;" horiz-adv-x="315" 
d="M206 -155h-46v60q-56 7 -90 44t-34 99q0 58 33.5 99t90.5 51v58h46v-57q34 0 64 -14l-13 -50q-26 14 -61 14q-42 0 -67.5 -27t-25.5 -70q0 -46 26 -71.5t65 -25.5q37 0 67 13l11 -47q-26 -14 -66 -17v-59z" />
    <glyph glyph-name="centsuperior" unicode="&#xf6e0;" horiz-adv-x="315" 
d="M206 435h-46v60q-56 7 -90 44t-34 99q0 58 33.5 99t90.5 51v58h46v-57q34 0 64 -14l-13 -50q-26 14 -61 14q-42 0 -67.5 -27t-25.5 -70q0 -46 26 -71.5t65 -25.5q37 0 67 13l11 -47q-26 -14 -66 -17v-59z" />
    <glyph glyph-name="commainferior" unicode="&#xf6e1;" horiz-adv-x="131" 
d="M48 -214l-48 -5q27 75 39 155l74 6q-30 -99 -65 -156z" />
    <glyph glyph-name="commasuperior" unicode="&#xf6e2;" horiz-adv-x="131" 
d="M48 376l-48 -5q27 75 39 155l74 6q-30 -99 -65 -156z" />
    <glyph glyph-name="dollarinferior" unicode="&#xf6e3;" horiz-adv-x="317" 
d="M179 -198h-45v59q-62 2 -99 27l16 50q43 -27 94 -27q31 0 50 14t19 37q0 22 -15.5 36t-52.5 28q-54 19 -80 43.5t-26 62.5q0 39 26 66t71 34v59h45v-56q48 -1 83 -21l-16 -49q-39 21 -82 21q-32 0 -46.5 -13.5t-14.5 -32.5q0 -20 16 -32.5t60 -28.5q51 -19 74.5 -45
t23.5 -66q0 -38 -26.5 -67.5t-74.5 -36.5v-62z" />
    <glyph glyph-name="dollarsuperior" unicode="&#xf6e4;" horiz-adv-x="317" 
d="M179 392h-45v59q-62 2 -99 27l16 50q43 -27 94 -27q31 0 50 14t19 37q0 22 -15.5 36t-52.5 28q-54 19 -80 43.5t-26 62.5q0 39 26 66t71 34v59h45v-56q48 -1 83 -21l-16 -49q-39 21 -82 21q-32 0 -46.5 -13.5t-14.5 -32.5q0 -20 16 -32.5t60 -28.5q51 -19 74.5 -45
t23.5 -66q0 -38 -26.5 -67.5t-74.5 -36.5v-62z" />
    <glyph glyph-name="hypheninferior" unicode="&#xf6e5;" horiz-adv-x="191" 
d="M18 61h155v-48h-155v48z" />
    <glyph glyph-name="hyphensuperior" unicode="&#xf6e6;" horiz-adv-x="191" 
d="M18 651h155v-48h-155v48z" />
    <glyph glyph-name="periodinferior" unicode="&#xf6e7;" horiz-adv-x="131" 
d="M65 -152h-1q-19 0 -31 13t-12 32q0 20 12.5 32.5t31.5 12.5q20 0 32 -12.5t12 -32.5q0 -19 -12.5 -32t-31.5 -13z" />
    <glyph glyph-name="periodsuperior" unicode="&#xf6e8;" horiz-adv-x="131" 
d="M65 438h-1q-19 0 -31 13t-12 32q0 20 12.5 32.5t31.5 12.5q20 0 32 -12.5t12 -32.5q0 -19 -12.5 -32t-31.5 -13z" />
    <glyph glyph-name="zerooldstyle" unicode="&#xf730;" horiz-adv-x="530" 
d="M265 -11h-1q-96 0 -160 68t-64 185q0 116 64.5 185t160.5 69t160.5 -68.5t64.5 -185.5q0 -115 -64.5 -184t-160.5 -69zM265 56h1q64 0 101.5 52t37.5 134q0 83 -37.5 135t-102.5 52t-102.5 -52t-37.5 -135q0 -82 37.5 -134t102.5 -52z" />
    <glyph glyph-name="oneoldstyle" unicode="&#xf731;" horiz-adv-x="362" 
d="M263 0h-84v408h-2l-135 -62l-16 66l164 73h73v-485z" />
    <glyph glyph-name="twooldstyle" unicode="&#xf732;" horiz-adv-x="448" 
d="M420 0h-396v48l49 34q119 79 171.5 134.5t52.5 113.5q0 47 -29.5 72.5t-77.5 25.5q-69 0 -136 -49l-24 57q83 62 182 62q77 0 125 -41.5t48 -114.5q0 -65 -44.5 -119.5t-132.5 -116.5l-51 -37v-3q33 4 82 4h181v-70z" />
    <glyph glyph-name="threeoldstyle" unicode="&#xf733;" horiz-adv-x="433" 
d="M18 -94l10 65q50 -16 109 -16q75 0 122 33.5t47 87.5q0 47 -41.5 74.5t-110.5 27.5q-31 0 -55 -2l-2 64l19 1q73 5 118 33t45 73q0 80 -105 80q-60 0 -123 -34l-18 58q26 18 70 31.5t90 13.5q78 0 126 -36.5t48 -97.5q0 -50 -32.5 -86.5t-85.5 -51.5v-3q63 -9 105 -46
t42 -98q0 -84 -73.5 -137.5t-177.5 -53.5q-83 0 -127 20z" />
    <glyph glyph-name="fouroldstyle" unicode="&#xf734;" horiz-adv-x="502" 
d="M483 50h-91v-166h-82v166h-292v57l279 385h95v-375h91v-67zM310 117v200q0 63 3 95l-4 1q-27 -53 -54 -95l-150 -198l1 -3h204z" />
    <glyph glyph-name="fiveoldstyle" unicode="&#xf735;" horiz-adv-x="441" 
d="M36 -97l11 63q43 -13 102 -13q68 0 116 34.5t48 94.5q0 44 -31 73q-55 54 -227 43l51 287h287l-11 -72h-217l-29 -150q153 4 220 -61q46 -44 46 -113q0 -91 -70 -147t-182 -56q-72 0 -114 17z" />
    <glyph glyph-name="sixoldstyle" unicode="&#xf736;" horiz-adv-x="520" 
d="M432 606l6 -65q-129 -8 -204 -68.5t-93 -150.5l3 -1q21 30 59.5 51.5t86.5 21.5q79 0 133 -52t54 -139q0 -97 -62.5 -155.5t-145.5 -58.5q-100 0 -161.5 68t-61.5 179q0 157 104 258.5t282 111.5zM269 56h1q48 0 84 37.5t36 104.5q0 61 -33.5 97.5t-88.5 36.5
q-60 0 -101 -43q-32 -33 -32 -83q0 -63 37 -106.5t97 -43.5z" />
    <glyph glyph-name="sevenoldstyle" unicode="&#xf737;" horiz-adv-x="428" 
d="M405 485v-56q-45 -118 -124 -269.5t-158 -273.5l-83 16q65 94 151 251t124 257v2h-297v73h387z" />
    <glyph glyph-name="eightoldstyle" unicode="&#xf738;" horiz-adv-x="513" 
d="M343 320v-3q59 -21 94 -60t35 -91q0 -79 -63 -128t-155 -49q-95 0 -154 46.5t-59 119.5q0 102 125 158v3q-101 43 -101 129q0 71 56.5 116.5t140.5 45.5q83 0 135 -43.5t52 -107.5q0 -86 -106 -136zM261 547q-47 0 -79 -26t-32 -69q0 -76 121 -108q43 13 69.5 40.5
t26.5 67.5q0 45 -31 70t-75 25zM255 52h1q54 0 90 29.5t36 76.5q0 92 -142 129q-51 -17 -81 -50t-30 -74q0 -49 34 -80t92 -31z" />
    <glyph glyph-name="nineoldstyle" unicode="&#xf739;" horiz-adv-x="520" 
d="M82 -113l-7 65q125 5 201.5 60.5t98.5 147.5h-2q-17 -27 -56.5 -47t-87.5 -20q-79 0 -132.5 52t-53.5 139q0 91 62 152.5t154 61.5t152.5 -64.5t60.5 -176.5q0 -165 -107.5 -265t-282.5 -105zM252 155h1q36 0 68.5 19t47.5 46q16 21 16 60q0 66 -37.5 108t-92.5 42
q-50 0 -87.5 -36.5t-37.5 -102.5t35 -101t87 -35z" />
    <hkern u1="&#x20;" u2="&#x38f;" k="12" />
    <hkern u1="&#x20;" u2="&#x38e;" k="30" />
    <hkern u1="&#x20;" u2="&#x38c;" k="12" />
    <hkern u1="&#x20;" u2="&#x386;" k="50" />
    <hkern u1="C" u2="&#x152;" k="24" />
    <hkern u1="F" u2="&#xef;" k="-15" />
    <hkern u1="L" u2="&#x1fe;" k="33" />
    <hkern u1="L" u2="&#xd8;" k="33" />
    <hkern u1="P" u2="&#x127;" k="4" />
    <hkern u1="T" u2="&#x133;" k="12" />
    <hkern u1="T" u2="&#x131;" k="12" />
    <hkern u1="T" u2="&#x12f;" k="12" />
    <hkern u1="T" u2="&#x12d;" k="-52" />
    <hkern u1="T" u2="&#x12b;" k="12" />
    <hkern u1="T" u2="&#x129;" k="12" />
    <hkern u1="T" u2="&#xef;" k="-68" />
    <hkern u1="T" u2="&#xee;" k="12" />
    <hkern u1="T" u2="&#xed;" k="12" />
    <hkern u1="T" u2="&#xec;" k="12" />
    <hkern u1="T" u2="&#xe8;" k="68" />
    <hkern u1="T" u2="&#xe0;" k="36" />
    <hkern u1="T" u2="i" k="12" />
    <hkern u1="V" u2="&#x1fc;" k="59" />
    <hkern u1="V" u2="&#x12d;" k="-50" />
    <hkern u1="V" u2="&#xef;" k="-66" />
    <hkern u1="V" u2="&#xc6;" k="59" />
    <hkern u1="W" u2="&#x1fc;" k="59" />
    <hkern u1="W" u2="&#x12d;" k="-50" />
    <hkern u1="W" u2="&#xef;" k="-66" />
    <hkern u1="W" u2="&#xc6;" k="59" />
    <hkern u1="Y" u2="&#x12d;" k="-29" />
    <hkern u1="Y" u2="&#xf6;" k="64" />
    <hkern u1="Y" u2="&#xef;" k="-52" />
    <hkern u1="Y" u2="&#xeb;" k="64" />
    <hkern u1="Y" u2="&#xe4;" k="48" />
    <hkern u1="r" u2="&#x142;" k="-6" />
    <hkern u1="&#xc7;" u2="&#x152;" k="24" />
    <hkern u1="&#xdd;" u2="&#x12d;" k="-29" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="64" />
    <hkern u1="&#xdd;" u2="&#xef;" k="-52" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="64" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="48" />
    <hkern u1="&#x106;" u2="&#x152;" k="24" />
    <hkern u1="&#x108;" u2="&#x152;" k="24" />
    <hkern u1="&#x10a;" u2="&#x152;" k="24" />
    <hkern u1="&#x10c;" u2="&#x152;" k="24" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="-74" />
    <hkern u1="&#x10f;" u2="&#x2019;" k="-74" />
    <hkern u1="&#x10f;" u2="&#x142;" k="-56" />
    <hkern u1="&#x10f;" u2="&#x140;" k="-56" />
    <hkern u1="&#x10f;" u2="&#x13e;" k="-56" />
    <hkern u1="&#x10f;" u2="&#x13c;" k="-56" />
    <hkern u1="&#x10f;" u2="&#x13a;" k="-56" />
    <hkern u1="&#x10f;" u2="&#x137;" k="-56" />
    <hkern u1="&#x10f;" u2="&#x127;" k="-56" />
    <hkern u1="&#x10f;" u2="&#x125;" k="-56" />
    <hkern u1="&#x10f;" u2="l" k="-56" />
    <hkern u1="&#x10f;" u2="k" k="-56" />
    <hkern u1="&#x10f;" u2="h" k="-56" />
    <hkern u1="&#x10f;" u2="b" k="-56" />
    <hkern u1="&#x139;" u2="&#x1fe;" k="33" />
    <hkern u1="&#x139;" u2="&#xd8;" k="33" />
    <hkern u1="&#x13b;" u2="&#x1fe;" k="33" />
    <hkern u1="&#x13b;" u2="&#xd8;" k="33" />
    <hkern u1="&#x13d;" u2="&#x1fe;" k="33" />
    <hkern u1="&#x13d;" u2="&#xd8;" k="33" />
    <hkern u1="&#x13f;" u2="&#x39f;" k="36" />
    <hkern u1="&#x13f;" u2="&#x398;" k="36" />
    <hkern u1="&#x13f;" u2="&#x1fe;" k="36" />
    <hkern u1="&#x13f;" u2="&#x152;" k="36" />
    <hkern u1="&#x13f;" u2="&#x150;" k="36" />
    <hkern u1="&#x13f;" u2="&#x14e;" k="36" />
    <hkern u1="&#x13f;" u2="&#x14c;" k="36" />
    <hkern u1="&#x13f;" u2="&#x122;" k="36" />
    <hkern u1="&#x13f;" u2="&#x120;" k="36" />
    <hkern u1="&#x13f;" u2="&#x11e;" k="36" />
    <hkern u1="&#x13f;" u2="&#x11c;" k="36" />
    <hkern u1="&#x13f;" u2="&#x10c;" k="36" />
    <hkern u1="&#x13f;" u2="&#x10a;" k="36" />
    <hkern u1="&#x13f;" u2="&#x108;" k="36" />
    <hkern u1="&#x13f;" u2="&#x106;" k="36" />
    <hkern u1="&#x13f;" u2="&#xd8;" k="36" />
    <hkern u1="&#x13f;" u2="&#xd6;" k="36" />
    <hkern u1="&#x13f;" u2="&#xd5;" k="36" />
    <hkern u1="&#x13f;" u2="&#xd4;" k="36" />
    <hkern u1="&#x13f;" u2="&#xd3;" k="36" />
    <hkern u1="&#x13f;" u2="&#xd2;" k="36" />
    <hkern u1="&#x13f;" u2="&#xc7;" k="36" />
    <hkern u1="&#x13f;" u2="Q" k="36" />
    <hkern u1="&#x13f;" u2="O" k="36" />
    <hkern u1="&#x13f;" u2="G" k="36" />
    <hkern u1="&#x13f;" u2="C" k="36" />
    <hkern u1="&#x141;" u2="&#x1fe;" k="33" />
    <hkern u1="&#x141;" u2="&#xd8;" k="33" />
    <hkern u1="&#x142;" u2="&#x201d;" k="-9" />
    <hkern u1="&#x142;" u2="&#x2019;" k="-9" />
    <hkern u1="&#x155;" u2="&#x142;" k="-6" />
    <hkern u1="&#x157;" u2="&#x142;" k="-6" />
    <hkern u1="&#x159;" u2="&#x142;" k="-6" />
    <hkern u1="&#x162;" u2="&#x133;" k="12" />
    <hkern u1="&#x162;" u2="&#x131;" k="12" />
    <hkern u1="&#x162;" u2="&#x12f;" k="12" />
    <hkern u1="&#x162;" u2="&#x12d;" k="-52" />
    <hkern u1="&#x162;" u2="&#x12b;" k="12" />
    <hkern u1="&#x162;" u2="&#x129;" k="12" />
    <hkern u1="&#x162;" u2="&#xef;" k="-68" />
    <hkern u1="&#x162;" u2="&#xee;" k="12" />
    <hkern u1="&#x162;" u2="&#xed;" k="12" />
    <hkern u1="&#x162;" u2="&#xec;" k="12" />
    <hkern u1="&#x162;" u2="&#xe8;" k="68" />
    <hkern u1="&#x162;" u2="&#xe0;" k="36" />
    <hkern u1="&#x162;" u2="i" k="12" />
    <hkern u1="&#x164;" u2="&#x133;" k="12" />
    <hkern u1="&#x164;" u2="&#x131;" k="12" />
    <hkern u1="&#x164;" u2="&#x12f;" k="12" />
    <hkern u1="&#x164;" u2="&#x12d;" k="-52" />
    <hkern u1="&#x164;" u2="&#x12b;" k="12" />
    <hkern u1="&#x164;" u2="&#x129;" k="12" />
    <hkern u1="&#x164;" u2="&#xef;" k="-68" />
    <hkern u1="&#x164;" u2="&#xee;" k="12" />
    <hkern u1="&#x164;" u2="&#xed;" k="12" />
    <hkern u1="&#x164;" u2="&#xec;" k="12" />
    <hkern u1="&#x164;" u2="&#xe8;" k="68" />
    <hkern u1="&#x164;" u2="&#xe0;" k="36" />
    <hkern u1="&#x164;" u2="i" k="12" />
    <hkern u1="&#x165;" u2="&#x201d;" k="-50" />
    <hkern u1="&#x165;" u2="&#x2019;" k="-50" />
    <hkern u1="&#x166;" u2="&#x133;" k="12" />
    <hkern u1="&#x166;" u2="&#x131;" k="12" />
    <hkern u1="&#x166;" u2="&#x12f;" k="12" />
    <hkern u1="&#x166;" u2="&#x12d;" k="-52" />
    <hkern u1="&#x166;" u2="&#x12b;" k="12" />
    <hkern u1="&#x166;" u2="&#x129;" k="12" />
    <hkern u1="&#x166;" u2="&#xef;" k="-68" />
    <hkern u1="&#x166;" u2="&#xee;" k="12" />
    <hkern u1="&#x166;" u2="&#xed;" k="12" />
    <hkern u1="&#x166;" u2="&#xec;" k="12" />
    <hkern u1="&#x166;" u2="&#xe8;" k="68" />
    <hkern u1="&#x166;" u2="&#xe0;" k="36" />
    <hkern u1="&#x166;" u2="i" k="12" />
    <hkern u1="&#x174;" u2="&#x1fc;" k="59" />
    <hkern u1="&#x174;" u2="&#x12d;" k="-50" />
    <hkern u1="&#x174;" u2="&#xef;" k="-66" />
    <hkern u1="&#x174;" u2="&#xc6;" k="59" />
    <hkern u1="&#x178;" u2="&#x12d;" k="-29" />
    <hkern u1="&#x178;" u2="&#xf6;" k="64" />
    <hkern u1="&#x178;" u2="&#xef;" k="-52" />
    <hkern u1="&#x178;" u2="&#xeb;" k="64" />
    <hkern u1="&#x178;" u2="&#xe4;" k="48" />
    <hkern u1="&#x388;" u2="&#x3ca;" k="-26" />
    <hkern u1="&#x388;" u2="&#x390;" k="-42" />
    <hkern u1="&#x389;" u2="&#x3ca;" k="-14" />
    <hkern u1="&#x389;" u2="&#x390;" k="-30" />
    <hkern u1="&#x38a;" u2="&#x3ca;" k="-14" />
    <hkern u1="&#x38a;" u2="&#x390;" k="-30" />
    <hkern u1="&#x38c;" u2="&#x3be;" k="-8" />
    <hkern u1="&#x38e;" u2="&#x3ca;" k="-58" />
    <hkern u1="&#x38e;" u2="&#x3bb;" k="-2" />
    <hkern u1="&#x38e;" u2="&#x3b4;" k="68" />
    <hkern u1="&#x38e;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x38e;" u2="&#x3b0;" k="16" />
    <hkern u1="&#x38e;" u2="&#x390;" k="-84" />
    <hkern u1="&#x38f;" u2="&#x3ca;" k="-8" />
    <hkern u1="&#x38f;" u2="&#x390;" k="-20" />
    <hkern u1="&#x390;" u2="&#x3c0;" k="-28" />
    <hkern u1="&#x390;" u2="&#x3be;" k="-35" />
    <hkern u1="&#x390;" u2="&#x3bb;" k="-54" />
    <hkern u1="&#x390;" u2="&#x3b8;" k="-35" />
    <hkern u1="&#x390;" u2="&#x3b6;" k="-43" />
    <hkern u1="&#x390;" u2="&#x3b2;" k="-32" />
    <hkern u1="&#x391;" u2="&#x3c2;" k="12" />
    <hkern u1="&#x391;" u2="&#x3be;" k="2" />
    <hkern u1="&#x391;" u2="&#x3bd;" k="23" />
    <hkern u1="&#x391;" u2="&#x3b4;" k="9" />
    <hkern u1="&#x392;" u2="&#x3b4;" k="-4" />
    <hkern u1="&#x393;" u2="&#x3cb;" k="36" />
    <hkern u1="&#x393;" u2="&#x3ca;" k="-56" />
    <hkern u1="&#x393;" u2="&#x3b4;" k="48" />
    <hkern u1="&#x393;" u2="&#x3b2;" k="25" />
    <hkern u1="&#x393;" u2="&#x3b0;" k="23" />
    <hkern u1="&#x393;" u2="&#x390;" k="-77" />
    <hkern u1="&#x394;" u2="&#x3be;" k="-4" />
    <hkern u1="&#x395;" u2="&#x3ca;" k="-26" />
    <hkern u1="&#x395;" u2="&#x3be;" k="4" />
    <hkern u1="&#x395;" u2="&#x390;" k="-42" />
    <hkern u1="&#x397;" u2="&#x3ca;" k="-14" />
    <hkern u1="&#x397;" u2="&#x390;" k="-30" />
    <hkern u1="&#x398;" u2="&#x3be;" k="-8" />
    <hkern u1="&#x399;" u2="&#x3ca;" k="-14" />
    <hkern u1="&#x399;" u2="&#x390;" k="-30" />
    <hkern u1="&#x39a;" u2="&#x3ca;" k="-42" />
    <hkern u1="&#x39a;" u2="&#x3c2;" k="4" />
    <hkern u1="&#x39a;" u2="&#x3b2;" k="-6" />
    <hkern u1="&#x39a;" u2="&#x3b0;" k="11" />
    <hkern u1="&#x39a;" u2="&#x390;" k="-63" />
    <hkern u1="&#x39d;" u2="&#x3ca;" k="-14" />
    <hkern u1="&#x39d;" u2="&#x3c1;" k="5" />
    <hkern u1="&#x39d;" u2="&#x3bf;" k="5" />
    <hkern u1="&#x39d;" u2="&#x3b4;" k="5" />
    <hkern u1="&#x39d;" u2="&#x391;" k="13" />
    <hkern u1="&#x39d;" u2="&#x390;" k="-30" />
    <hkern u1="&#x39e;" u2="&#x3ca;" k="-40" />
    <hkern u1="&#x39e;" u2="&#x3b9;" k="-8" />
    <hkern u1="&#x39e;" u2="&#x3b7;" k="-10" />
    <hkern u1="&#x39e;" u2="&#x390;" k="-53" />
    <hkern u1="&#x39f;" u2="&#x3be;" k="-8" />
    <hkern u1="&#x3a0;" u2="&#x3ca;" k="-14" />
    <hkern u1="&#x3a0;" u2="&#x390;" k="-30" />
    <hkern u1="&#x3a1;" u2="&#x3ca;" k="-21" />
    <hkern u1="&#x3a1;" u2="&#x390;" k="-38" />
    <hkern u1="&#x3a1;" u2="&#x127;" k="4" />
    <hkern u1="&#x3a3;" u2="&#x3ca;" k="-15" />
    <hkern u1="&#x3a3;" u2="&#x3c7;" k="7" />
    <hkern u1="&#x3a3;" u2="&#x3b4;" k="15" />
    <hkern u1="&#x3a3;" u2="&#x3aa;" k="-6" />
    <hkern u1="&#x3a3;" u2="&#x390;" k="-35" />
    <hkern u1="&#x3a4;" u2="&#x3cb;" k="22" />
    <hkern u1="&#x3a4;" u2="&#x3ca;" k="-68" />
    <hkern u1="&#x3a4;" u2="&#x3b4;" k="47" />
    <hkern u1="&#x3a4;" u2="&#x3b0;" k="19" />
    <hkern u1="&#x3a4;" u2="&#x3a3;" k="-12" />
    <hkern u1="&#x3a4;" u2="&#x390;" k="-92" />
    <hkern u1="&#x3a4;" u2="&#x133;" k="12" />
    <hkern u1="&#x3a4;" u2="&#x131;" k="12" />
    <hkern u1="&#x3a4;" u2="&#x12f;" k="12" />
    <hkern u1="&#x3a4;" u2="&#x12d;" k="-52" />
    <hkern u1="&#x3a4;" u2="&#x12b;" k="12" />
    <hkern u1="&#x3a4;" u2="&#x129;" k="12" />
    <hkern u1="&#x3a4;" u2="&#xef;" k="-68" />
    <hkern u1="&#x3a4;" u2="&#xee;" k="12" />
    <hkern u1="&#x3a4;" u2="&#xed;" k="12" />
    <hkern u1="&#x3a4;" u2="&#xec;" k="12" />
    <hkern u1="&#x3a4;" u2="&#xe8;" k="68" />
    <hkern u1="&#x3a4;" u2="&#xe0;" k="36" />
    <hkern u1="&#x3a4;" u2="i" k="12" />
    <hkern u1="&#x3a5;" u2="&#x3ca;" k="-58" />
    <hkern u1="&#x3a5;" u2="&#x3bb;" k="-2" />
    <hkern u1="&#x3a5;" u2="&#x3b4;" k="54" />
    <hkern u1="&#x3a5;" u2="&#x3b2;" k="20" />
    <hkern u1="&#x3a5;" u2="&#x3b0;" k="16" />
    <hkern u1="&#x3a5;" u2="&#x390;" k="-84" />
    <hkern u1="&#x3a7;" u2="&#x3ca;" k="-36" />
    <hkern u1="&#x3a7;" u2="&#x390;" k="-51" />
    <hkern u1="&#x3a8;" u2="&#x3aa;" k="-7" />
    <hkern u1="&#x3a9;" u2="&#x3ca;" k="-8" />
    <hkern u1="&#x3a9;" u2="&#x3bd;" k="-27" />
    <hkern u1="&#x3a9;" u2="&#x390;" k="-20" />
    <hkern u1="&#x3a9;" u2="&#xb5;" k="-6" />
    <hkern u1="&#x3ad;" u2="&#x3ca;" k="-20" />
    <hkern u1="&#x3ad;" u2="&#x390;" k="-32" />
    <hkern u1="&#x3af;" u2="&#x3bb;" k="-15" />
    <hkern u1="&#x3b1;" u2="&#x3b6;" k="-14" />
    <hkern u1="&#x3b2;" u2="&#x3ca;" k="-11" />
    <hkern u1="&#x3b2;" u2="&#x390;" k="-25" />
    <hkern u1="&#x3b3;" u2="&#x3b3;" k="-11" />
    <hkern u1="&#x3b4;" u2="&#x3ca;" k="-12" />
    <hkern u1="&#x3b4;" u2="&#x3c1;" k="10" />
    <hkern u1="&#x3b4;" u2="&#x3b3;" k="9" />
    <hkern u1="&#x3b4;" u2="&#x390;" k="-24" />
    <hkern u1="&#x3b6;" u2="&#x3c6;" k="6" />
    <hkern u1="&#x3b6;" u2="&#x3b2;" k="-10" />
    <hkern u1="&#x3b7;" u2="&#x3bd;" k="12" />
    <hkern u1="&#x3b8;" u2="&#x3be;" k="-2" />
    <hkern u1="&#x3b8;" u2="&#x3b6;" k="-14" />
    <hkern u1="&#x3bb;" u2="&#x3bd;" k="22" />
    <hkern u1="&#x3bd;" u2="&#x3c1;" k="19" />
    <hkern u1="&#x3bd;" u2="&#x3b6;" k="-6" />
    <hkern u1="&#x3bf;" u2="&#x3bd;" k="2" />
    <hkern u1="&#x3c0;" u2="&#x3ca;" k="-14" />
    <hkern u1="&#x3c0;" u2="&#x390;" k="-25" />
    <hkern u1="&#x3c3;" u2="&#x3ca;" k="-23" />
    <hkern u1="&#x3c3;" u2="&#x3b6;" k="-15" />
    <hkern u1="&#x3c3;" u2="&#x390;" k="-37" />
    <hkern u1="&#x3c5;" u2="&#x3c1;" k="6" />
    <hkern u1="&#x3c7;" u2="&#x3ca;" k="-12" />
    <hkern u1="&#x3c7;" u2="&#x390;" k="-21" />
    <hkern u1="&#x3ca;" u2="&#x3c0;" k="-15" />
    <hkern u1="&#x3ca;" u2="&#x3be;" k="-20" />
    <hkern u1="&#x3ca;" u2="&#x3bb;" k="-29" />
    <hkern u1="&#x3ca;" u2="&#x3b8;" k="-19" />
    <hkern u1="&#x3ca;" u2="&#x3b6;" k="-22" />
    <hkern u1="&#x3ca;" u2="&#x3b2;" k="-11" />
    <hkern u1="&#x1e80;" u2="&#x1fc;" k="59" />
    <hkern u1="&#x1e80;" u2="&#x12d;" k="-50" />
    <hkern u1="&#x1e80;" u2="&#xef;" k="-66" />
    <hkern u1="&#x1e80;" u2="&#xc6;" k="59" />
    <hkern u1="&#x1e82;" u2="&#x1fc;" k="59" />
    <hkern u1="&#x1e82;" u2="&#x12d;" k="-50" />
    <hkern u1="&#x1e82;" u2="&#xef;" k="-66" />
    <hkern u1="&#x1e82;" u2="&#xc6;" k="59" />
    <hkern u1="&#x1e84;" u2="&#x1fc;" k="59" />
    <hkern u1="&#x1e84;" u2="&#x12d;" k="-50" />
    <hkern u1="&#x1e84;" u2="&#xef;" k="-66" />
    <hkern u1="&#x1e84;" u2="&#xc6;" k="59" />
    <hkern u1="&#x1ef2;" u2="&#x12d;" k="-29" />
    <hkern u1="&#x1ef2;" u2="&#xf6;" k="64" />
    <hkern u1="&#x1ef2;" u2="&#xef;" k="-52" />
    <hkern u1="&#x1ef2;" u2="&#xeb;" k="64" />
    <hkern u1="&#x1ef2;" u2="&#xe4;" k="48" />
    <hkern u1="&#x2018;" u2="&#x133;" k="21" />
    <hkern u1="&#x2018;" u2="&#x131;" k="21" />
    <hkern u1="&#x2018;" u2="&#x12f;" k="21" />
    <hkern u1="&#x2018;" u2="&#x12d;" k="21" />
    <hkern u1="&#x2018;" u2="&#x12b;" k="21" />
    <hkern u1="&#x2018;" u2="&#x129;" k="21" />
    <hkern u1="&#x2018;" u2="&#x127;" k="-9" />
    <hkern u1="&#x2018;" u2="&#xef;" k="21" />
    <hkern u1="&#x2018;" u2="&#xee;" k="21" />
    <hkern u1="&#x2018;" u2="&#xed;" k="21" />
    <hkern u1="&#x2018;" u2="&#xec;" k="21" />
    <hkern u1="&#x2018;" u2="i" k="21" />
    <hkern u1="&#x2019;" u2="&#x133;" k="39" />
    <hkern u1="&#x2019;" u2="&#x131;" k="39" />
    <hkern u1="&#x2019;" u2="&#x12f;" k="39" />
    <hkern u1="&#x2019;" u2="&#x12d;" k="39" />
    <hkern u1="&#x2019;" u2="&#x12b;" k="39" />
    <hkern u1="&#x2019;" u2="&#x129;" k="39" />
    <hkern u1="&#x2019;" u2="&#xef;" k="39" />
    <hkern u1="&#x2019;" u2="&#xee;" k="39" />
    <hkern u1="&#x2019;" u2="&#xed;" k="39" />
    <hkern u1="&#x2019;" u2="&#xec;" k="39" />
    <hkern u1="&#x2019;" u2="i" k="39" />
    <hkern u1="&#x201c;" u2="&#x133;" k="21" />
    <hkern u1="&#x201c;" u2="&#x131;" k="21" />
    <hkern u1="&#x201c;" u2="&#x12f;" k="21" />
    <hkern u1="&#x201c;" u2="&#x12d;" k="21" />
    <hkern u1="&#x201c;" u2="&#x12b;" k="21" />
    <hkern u1="&#x201c;" u2="&#x129;" k="21" />
    <hkern u1="&#x201c;" u2="&#x127;" k="-9" />
    <hkern u1="&#x201c;" u2="&#xef;" k="21" />
    <hkern u1="&#x201c;" u2="&#xee;" k="21" />
    <hkern u1="&#x201c;" u2="&#xed;" k="21" />
    <hkern u1="&#x201c;" u2="&#xec;" k="21" />
    <hkern u1="&#x201c;" u2="i" k="21" />
    <hkern u1="&#x201d;" u2="&#x133;" k="39" />
    <hkern u1="&#x201d;" u2="&#x131;" k="39" />
    <hkern u1="&#x201d;" u2="&#x12f;" k="39" />
    <hkern u1="&#x201d;" u2="&#x12d;" k="39" />
    <hkern u1="&#x201d;" u2="&#x12b;" k="39" />
    <hkern u1="&#x201d;" u2="&#x129;" k="39" />
    <hkern u1="&#x201d;" u2="&#xef;" k="39" />
    <hkern u1="&#x201d;" u2="&#xee;" k="39" />
    <hkern u1="&#x201d;" u2="&#xed;" k="39" />
    <hkern u1="&#x201d;" u2="&#xec;" k="39" />
    <hkern u1="&#x201d;" u2="i" k="39" />
    <hkern u1="&#xf63a;" u2="&#xf63c;" k="28" />
    <hkern u1="&#xf63a;" u2="&#xf63a;" k="-15" />
    <hkern u1="&#xf63d;" u2="&#xf641;" k="17" />
    <hkern u1="&#xf63e;" u2="&#xf641;" k="22" />
    <hkern u1="&#xf63f;" u2="&#xf6dc;" k="-11" />
    <hkern u1="&#xf63f;" u2="&#xf63f;" k="-40" />
    <hkern u1="&#xf63f;" u2="&#xf63e;" k="14" />
    <hkern u1="&#xf63f;" u2="&#xf63c;" k="46" />
    <hkern u1="&#xf63f;" u2="&#xf63a;" k="-9" />
    <hkern u1="&#xf640;" u2="&#xf641;" k="10" />
    <hkern u1="&#xf6dc;" u2="&#xf641;" k="13" />
    <hkern u1="&#xf6dc;" u2="&#xf63c;" k="4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="12" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="6" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-13" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-18" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="guillemotright,guilsinglright"
	k="-14" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="quoteleft,quotedblleft"
	k="-18" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="quoteright,quotedblright"
	k="-17" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="33" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,endash,emdash"
	k="-28" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quoteright,quotedblright"
	k="25" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,period,ellipsis"
	k="12" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="32" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="f,germandbls,f_f_j,f_j,ff,fi,fl,ffi,ffl"
	k="-4" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="6" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="quoteright,quotedblright"
	k="22" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	g2="comma,period,ellipsis"
	k="15" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="50" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
	g2="t,tcommaaccent,tcaron,tbar"
	k="4" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="13" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
	g2="quotedbl,quotesingle"
	k="7" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
	g2="quoteright,quotedblright"
	k="33" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="19" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="-18" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="-4" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-13" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="-17" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="-17" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="colon,semicolon"
	k="-10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="quoteright,quotedblright"
	k="-24" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="comma,period,ellipsis"
	k="-14" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="31" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="quoteright,quotedblright"
	k="22" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="comma,period,ellipsis"
	k="8" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="7" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="7" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="x"
	k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="hyphen,endash,emdash"
	k="-15" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="quotedbl,quotesingle"
	k="11" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="quoteright,quotedblright"
	k="33" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="comma,period,ellipsis"
	k="24" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="14" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="f,germandbls,f_f_j,f_j,ff,fi,fl,ffi,ffl"
	k="-30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-24" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-9" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="-4" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="-4" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="x"
	k="-18" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="colon,semicolon"
	k="-9" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotright,guilsinglright"
	k="-12" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotleft,guilsinglleft"
	k="6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quoteright,quotedblright"
	k="-30" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,ellipsis"
	k="53" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="24" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="quoteright,quotedblright"
	k="17" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="comma,period,ellipsis"
	k="11" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="30" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="4" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-12" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="colon,semicolon"
	k="-24" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="hyphen,endash,emdash"
	k="4" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="quoteleft,quotedblleft"
	k="-27" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="quoteright,quotedblright"
	k="-30" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="comma,period,ellipsis"
	k="39" />
    <hkern g1="x"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="22" />
    <hkern g1="x"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="15" />
    <hkern g1="x"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="x"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-13" />
    <hkern g1="x"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-15" />
    <hkern g1="x"
	g2="hyphen,endash,emdash"
	k="6" />
    <hkern g1="x"
	g2="quoteright,quotedblright"
	k="-25" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="7" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-24" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotright,guilsinglright"
	k="-18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="quoteright,quotedblright"
	k="-29" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="77" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="J,Jcircumflex"
	k="-19" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="M,Mu"
	k="4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="28" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="53" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="X,Chi"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="79" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="-4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="f,germandbls,f_f_j,f_j,ff,fi,fl,ffi,ffl"
	k="7" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="j,jcircumflex"
	k="4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="t,tcommaaccent,tcaron,tbar"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="21" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="z,zacute,zdotaccent,zcaron"
	k="-14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="parenright,bracketright,braceright"
	k="9" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="quotedbl,quotesingle"
	k="59" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="quoteleft,quotedblleft"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="quoteright,quotedblright"
	k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="Delta"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="Omega"
	k="-4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="Phi"
	k="21" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="Psi"
	k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="Upsilon,Upsilondieresis"
	k="99" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="chi"
	k="27" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="epsilontonos,epsilon"
	k="-7" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="gamma,nu"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="lambda"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="11" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="rho"
	k="13" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="tau"
	k="26" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="theta"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Alphatonos,Alpha,Lambda"
	g2="zeta,xi"
	k="4" />
    <hkern g1="B,Beta"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="9" />
    <hkern g1="B,Beta"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-4" />
    <hkern g1="B,Beta"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="B,Beta"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="-4" />
    <hkern g1="B,Beta"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-4" />
    <hkern g1="B,Beta"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="B,Beta"
	g2="quoteright,quotedblright"
	k="-8" />
    <hkern g1="B,Beta"
	g2="comma,period,ellipsis"
	k="13" />
    <hkern g1="B,Beta"
	g2="Psi"
	k="11" />
    <hkern g1="B,Beta"
	g2="Upsilon,Upsilondieresis"
	k="18" />
    <hkern g1="B,Beta"
	g2="epsilontonos,epsilon"
	k="-4" />
    <hkern g1="B,Beta"
	g2="gamma,nu"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-28" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="J,Jcircumflex"
	k="-4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="21" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="-4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="parenright,bracketright,braceright"
	k="-17" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="quoteright,quotedblright"
	k="-19" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="J,Jcircumflex"
	k="-18" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-9" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="comma,period,ellipsis"
	k="4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="Delta"
	k="-12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="Xi"
	k="-7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="epsilontonos,epsilon"
	k="-17" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="lambda"
	k="-11" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="tau"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi"
	g2="pi"
	k="-13" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="85" />
    <hkern g1="F"
	g2="M,Mu"
	k="18" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="78" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="45" />
    <hkern g1="F"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="16" />
    <hkern g1="F"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="31" />
    <hkern g1="F"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="35" />
    <hkern g1="F"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="21" />
    <hkern g1="F"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="17" />
    <hkern g1="F"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="26" />
    <hkern g1="F"
	g2="colon,semicolon"
	k="13" />
    <hkern g1="F"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="F"
	g2="guillemotleft,guilsinglleft"
	k="15" />
    <hkern g1="F"
	g2="quoteleft,quotedblleft"
	k="7" />
    <hkern g1="F"
	g2="comma,period,ellipsis"
	k="97" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="f,germandbls,f_f_j,f_j,ff,fi,fl,ffi,ffl"
	k="-10" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="j,jcircumflex"
	k="-8" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-16" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-9" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="-11" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="-10" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="-10" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="x"
	k="-5" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="tau"
	k="-5" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="zeta,xi"
	k="-6" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis"
	g2="pi"
	k="-8" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-21" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="J,Jcircumflex"
	k="-38" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="17" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-15" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="-10" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-20" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="-16" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="5" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="6" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="16" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="-10" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="-10" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="colon,semicolon"
	k="-22" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="guillemotleft,guilsinglleft"
	k="5" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="hyphen,endash,emdash"
	k="18" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="parenright,bracketright,braceright"
	k="-23" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="quoteleft,quotedblleft"
	k="4" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="quoteright,quotedblright"
	k="-9" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="comma,period,ellipsis"
	k="-17" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Delta"
	k="-12" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Omega"
	k="-14" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Phi"
	k="27" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Xi"
	k="-26" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Sigma"
	k="-24" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="epsilontonos,epsilon"
	k="-19" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="gamma,nu"
	k="14" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="lambda"
	k="-17" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="tau"
	k="15" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="18" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="mu,etatonos,beta,eta,kappa"
	k="-5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="88" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="J,Jcircumflex"
	k="-11" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="36" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="59" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="84" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="t,tcommaaccent,tcaron,tbar"
	k="4" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="guillemotleft,guilsinglleft"
	k="38" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="hyphen,endash,emdash"
	k="42" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="quotedbl,quotesingle"
	k="98" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="quoteleft,quotedblleft"
	k="117" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="quoteright,quotedblright"
	k="103" />
    <hkern g1="M,Mu"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="10" />
    <hkern g1="M,Mu"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="10" />
    <hkern g1="M,Mu"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="-6" />
    <hkern g1="M,Mu"
	g2="j,jcircumflex"
	k="-10" />
    <hkern g1="M,Mu"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="-6" />
    <hkern g1="M,Mu"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-7" />
    <hkern g1="M,Mu"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="-12" />
    <hkern g1="M,Mu"
	g2="hyphen,endash,emdash"
	k="-6" />
    <hkern g1="M,Mu"
	g2="quoteleft,quotedblleft"
	k="4" />
    <hkern g1="M,Mu"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="M,Mu"
	g2="Psi"
	k="11" />
    <hkern g1="M,Mu"
	g2="Upsilon,Upsilondieresis"
	k="22" />
    <hkern g1="M,Mu"
	g2="pi"
	k="-9" />
    <hkern g1="M,Mu"
	g2="mu,etatonos,beta,eta,kappa"
	k="-4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="25" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="X,Chi"
	k="27" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="28" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="f,germandbls,f_f_j,f_j,ff,fi,fl,ffi,ffl"
	k="-16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="-6" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="-4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-17" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="x"
	k="5" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="guillemotleft,guilsinglleft"
	k="-13" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="hyphen,endash,emdash"
	k="-14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="parenright,bracketright,braceright"
	k="7" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="quoteright,quotedblright"
	k="-9" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="comma,period,ellipsis"
	k="34" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="Delta"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="Omega"
	k="-4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="Upsilon,Upsilondieresis"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="Xi"
	k="8" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="Sigma"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="gamma,nu"
	k="-16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="lambda"
	k="16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="rho"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="tau"
	k="-11" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="theta"
	k="-18" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="zeta,xi"
	k="-9" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute,Omicrontonos,Theta,Omicron"
	g2="pi"
	k="-12" />
    <hkern g1="P,Rho"
	g2="J,Jcircumflex"
	k="74" />
    <hkern g1="P,Rho"
	g2="M,Mu"
	k="12" />
    <hkern g1="P,Rho"
	g2="X,Chi"
	k="13" />
    <hkern g1="P,Rho"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="9" />
    <hkern g1="P,Rho"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="84" />
    <hkern g1="P,Rho"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="30" />
    <hkern g1="P,Rho"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="26" />
    <hkern g1="P,Rho"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="24" />
    <hkern g1="P,Rho"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="25" />
    <hkern g1="P,Rho"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="22" />
    <hkern g1="P,Rho"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-6" />
    <hkern g1="P,Rho"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="P,Rho"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-4" />
    <hkern g1="P,Rho"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="5" />
    <hkern g1="P,Rho"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="16" />
    <hkern g1="P,Rho"
	g2="colon,semicolon"
	k="11" />
    <hkern g1="P,Rho"
	g2="guillemotleft,guilsinglleft"
	k="23" />
    <hkern g1="P,Rho"
	g2="hyphen,endash,emdash"
	k="17" />
    <hkern g1="P,Rho"
	g2="quoteleft,quotedblleft"
	k="-6" />
    <hkern g1="P,Rho"
	g2="quoteright,quotedblright"
	k="-21" />
    <hkern g1="P,Rho"
	g2="comma,period,ellipsis"
	k="140" />
    <hkern g1="P,Rho"
	g2="Delta"
	k="85" />
    <hkern g1="P,Rho"
	g2="Psi"
	k="-5" />
    <hkern g1="P,Rho"
	g2="Upsilon,Upsilondieresis"
	k="6" />
    <hkern g1="P,Rho"
	g2="epsilontonos,epsilon"
	k="25" />
    <hkern g1="P,Rho"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="25" />
    <hkern g1="P,Rho"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="10" />
    <hkern g1="P,Rho"
	g2="mu,etatonos,beta,eta,kappa"
	k="10" />
    <hkern g1="P,Rho"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-9" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-16" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="X,Chi"
	k="-5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="-5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="-12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="-4" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-19" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="-9" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="-10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quoteright,quotedblright"
	k="-9" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-38" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="J,Jcircumflex"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="27" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="X,Chi"
	k="-22" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="-28" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="75" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="6" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="64" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="64" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="71" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="53" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="z,zacute,zdotaccent,zcaron"
	k="51" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="7" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="x"
	k="34" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="colon,semicolon"
	k="26" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="guillemotright,guilsinglright"
	k="36" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="guillemotleft,guilsinglleft"
	k="51" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="hyphen,endash,emdash"
	k="51" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="parenright,bracketright,braceright"
	k="-60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="quotedbl,quotesingle"
	k="-17" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="quoteright,quotedblright"
	k="-34" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="comma,period,ellipsis"
	k="60" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="Delta"
	k="70" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="Omega"
	k="26" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="Phi"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="Psi"
	k="-9" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="Upsilon,Upsilondieresis"
	k="-12" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="Sigma"
	k="-5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="epsilontonos,epsilon"
	k="44" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="lambda"
	k="-9" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="61" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="rho"
	k="66" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="39" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="zeta,xi"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="mu,etatonos,beta,eta,kappa"
	k="33" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="33" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,Tau"
	g2="omega,omegatonos"
	k="46" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="34" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="4" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="f,germandbls,f_f_j,f_j,ff,fi,fl,ffi,ffl"
	k="-7" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-4" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="x"
	k="7" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,period,ellipsis"
	k="27" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-33" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="23" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-16" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="57" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="33" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="9" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="33" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="25" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-9" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="18" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="4" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="5" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="16" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="16" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="11" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="25" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="14" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="parenright,bracketright,braceright"
	k="-55" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quotedbl,quotesingle"
	k="-19" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quoteleft,quotedblleft"
	k="-8" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quoteright,quotedblright"
	k="-27" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,ellipsis"
	k="55" />
    <hkern g1="X,Chi"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-8" />
    <hkern g1="X,Chi"
	g2="J,Jcircumflex"
	k="-4" />
    <hkern g1="X,Chi"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="29" />
    <hkern g1="X,Chi"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-9" />
    <hkern g1="X,Chi"
	g2="X,Chi"
	k="15" />
    <hkern g1="X,Chi"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="-5" />
    <hkern g1="X,Chi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="9" />
    <hkern g1="X,Chi"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="6" />
    <hkern g1="X,Chi"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="11" />
    <hkern g1="X,Chi"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="8" />
    <hkern g1="X,Chi"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="19" />
    <hkern g1="X,Chi"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="X,Chi"
	g2="hyphen,endash,emdash"
	k="21" />
    <hkern g1="X,Chi"
	g2="quoteleft,quotedblleft"
	k="6" />
    <hkern g1="X,Chi"
	g2="Delta"
	k="4" />
    <hkern g1="X,Chi"
	g2="Phi"
	k="41" />
    <hkern g1="X,Chi"
	g2="Psi"
	k="13" />
    <hkern g1="X,Chi"
	g2="Upsilon,Upsilondieresis"
	k="16" />
    <hkern g1="X,Chi"
	g2="Xi"
	k="-11" />
    <hkern g1="X,Chi"
	g2="gamma,nu"
	k="29" />
    <hkern g1="X,Chi"
	g2="lambda"
	k="-11" />
    <hkern g1="X,Chi"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="21" />
    <hkern g1="X,Chi"
	g2="rho"
	k="11" />
    <hkern g1="X,Chi"
	g2="tau"
	k="17" />
    <hkern g1="X,Chi"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="18" />
    <hkern g1="X,Chi"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="12" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-34" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="54" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="M,Mu"
	k="10" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="36" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-29" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="81" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="13" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis"
	k="7" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="70" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="40" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="76" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="52" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="t,tcommaaccent,tcaron,tbar"
	k="19" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="53" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="29" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="25" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="9" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="13" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="x"
	k="26" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="33" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="49" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="hyphen,endash,emdash"
	k="51" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="parenright,bracketright,braceright"
	k="-56" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="quotedbl,quotesingle"
	k="-8" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="quoteleft,quotedblleft"
	k="5" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="quoteright,quotedblright"
	k="-15" />
    <hkern g1="Y,Yacute,Ydieresis,Ygrave"
	g2="comma,period,ellipsis"
	k="95" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="J,Jcircumflex"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="21" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="X,Chi"
	k="6" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="12" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="7" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="9" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="hyphen,endash,emdash"
	k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="quoteleft,quotedblleft"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="Upsilon,Upsilondieresis"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="Xi"
	k="-5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="Sigma"
	k="7" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="lambda"
	k="-10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="11" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="-8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="-5" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="quoteright,quotedblright"
	k="4" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="parenright,bracketright,braceright"
	k="-42" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="quoteright,quotedblright"
	k="-8" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="comma,period,ellipsis"
	k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="j,jcircumflex"
	k="4" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="-6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="8" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="quoteleft,quotedblleft"
	k="8" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="quoteright,quotedblright"
	k="12" />
    <hkern g1="f,ff"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="12" />
    <hkern g1="f,ff"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="13" />
    <hkern g1="f,ff"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="9" />
    <hkern g1="f,ff"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-11" />
    <hkern g1="f,ff"
	g2="colon,semicolon"
	k="-32" />
    <hkern g1="f,ff"
	g2="guillemotright,guilsinglright"
	k="-13" />
    <hkern g1="f,ff"
	g2="parenright,bracketright,braceright"
	k="-103" />
    <hkern g1="f,ff"
	g2="quotedbl,quotesingle"
	k="-56" />
    <hkern g1="f,ff"
	g2="quoteleft,quotedblleft"
	k="-49" />
    <hkern g1="f,ff"
	g2="quoteright,quotedblright"
	k="-66" />
    <hkern g1="f,ff"
	g2="comma,period,ellipsis"
	k="33" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="6" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="6" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-9" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
	g2="hyphen,endash,emdash"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
	g2="quoteleft,quotedblleft"
	k="-7" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
	g2="quoteright,quotedblright"
	k="-21" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="34" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="12" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Delta"
	k="-4" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Omega"
	k="-7" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Phi"
	k="4" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Upsilon,Upsilondieresis"
	k="53" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Sigma"
	k="4" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="gamma,nu"
	k="-13" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="lambda"
	k="-7" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="tau"
	k="-14" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="pi"
	k="-18" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Omegatonos"
	k="-11" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Omicrontonos"
	k="-11" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Upsilontonos"
	k="52" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="55" />
    <hkern g1="guillemotright,guilsinglright"
	g2="J,Jcircumflex"
	k="17" />
    <hkern g1="guillemotright,guilsinglright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="-11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="27" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X,Chi"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="guillemotright,guilsinglright"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="-12" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="50" />
    <hkern g1="hyphen,endash,emdash"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="-14" />
    <hkern g1="hyphen,endash,emdash"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="11" />
    <hkern g1="hyphen,endash,emdash"
	g2="X,Chi"
	k="22" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="4" />
    <hkern g1="hyphen,endash,emdash"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="-14" />
    <hkern g1="hyphen,endash,emdash"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="-18" />
    <hkern g1="hyphen,endash,emdash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="5" />
    <hkern g1="hyphen,endash,emdash"
	g2="Omega"
	k="-6" />
    <hkern g1="hyphen,endash,emdash"
	g2="Phi"
	k="-13" />
    <hkern g1="hyphen,endash,emdash"
	g2="Psi"
	k="13" />
    <hkern g1="hyphen,endash,emdash"
	g2="Upsilon,Upsilondieresis"
	k="54" />
    <hkern g1="hyphen,endash,emdash"
	g2="Sigma"
	k="24" />
    <hkern g1="hyphen,endash,emdash"
	g2="zeta,xi"
	k="-13" />
    <hkern g1="hyphen,endash,emdash"
	g2="omega,omegatonos"
	k="-12" />
    <hkern g1="hyphen,endash,emdash"
	g2="Omegatonos"
	k="-11" />
    <hkern g1="hyphen,endash,emdash"
	g2="Omicrontonos"
	k="-11" />
    <hkern g1="hyphen,endash,emdash"
	g2="Upsilontonos"
	k="53" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-48" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="J,Jcircumflex"
	k="-17" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="12" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-50" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="X,Chi"
	k="-11" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="-41" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="11" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="j,jcircumflex"
	k="-56" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Delta"
	k="16" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Phi"
	k="23" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Psi"
	k="-6" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Upsilon,Upsilondieresis"
	k="-11" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="chi"
	k="-4" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="lambda"
	k="-8" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="rho"
	k="23" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="tau"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="zeta,xi"
	k="13" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="omega,omegatonos"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Omegatonos"
	k="-22" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Omicrontonos"
	k="-19" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Upsilontonos"
	k="-11" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Epsilontonos,Etatonos,Iotatonos"
	k="-19" />
    <hkern g1="comma,period,ellipsis"
	g2="quotedbl,quotesingle"
	k="102" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteleft,quotedblleft"
	k="104" />
    <hkern g1="comma,period,ellipsis"
	g2="quoteright,quotedblright"
	k="113" />
    <hkern g1="comma,period,ellipsis"
	g2="Upsilon,Upsilondieresis"
	k="85" />
    <hkern g1="d,l,dcaron,dcroat,lacute,lcommaaccent,lcaron,ldot,lslash,fl,ffl"
	g2="quoteright,quotedblright"
	k="-6" />
    <hkern g1="d,l,dcaron,dcroat,lacute,lcommaaccent,lcaron,ldot,lslash,fl,ffl"
	g2="comma,period,ellipsis"
	k="10" />
    <hkern g1="j,ij,jcircumflex,f_f_j,f_j"
	g2="quoteright,quotedblright"
	k="-7" />
    <hkern g1="j,ij,jcircumflex,f_f_j,f_j"
	g2="comma,period,ellipsis"
	k="11" />
    <hkern g1="quotedbl,quotesingle"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-17" />
    <hkern g1="quotedbl,quotesingle"
	g2="J,Jcircumflex"
	k="59" />
    <hkern g1="quotedbl,quotesingle"
	g2="M,Mu"
	k="5" />
    <hkern g1="quotedbl,quotesingle"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-17" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="61" />
    <hkern g1="quotedbl,quotesingle"
	g2="f,germandbls,f_f_j,f_j,ff,fi,fl,ffi,ffl"
	k="-25" />
    <hkern g1="quotedbl,quotesingle"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="8" />
    <hkern g1="quotedbl,quotesingle"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-24" />
    <hkern g1="quotedbl,quotesingle"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-21" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,period,ellipsis"
	k="113" />
    <hkern g1="quotedbl,quotesingle"
	g2="Delta"
	k="88" />
    <hkern g1="quotedbl,quotesingle"
	g2="Omega"
	k="21" />
    <hkern g1="quotedbl,quotesingle"
	g2="Phi"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="epsilontonos,epsilon"
	k="58" />
    <hkern g1="quotedbl,quotesingle"
	g2="gamma,nu"
	k="6" />
    <hkern g1="quotedbl,quotesingle"
	g2="rho"
	k="76" />
    <hkern g1="quotedbl,quotesingle"
	g2="tau"
	k="-7" />
    <hkern g1="quotedbl,quotesingle"
	g2="theta"
	k="5" />
    <hkern g1="quotedbl,quotesingle"
	g2="zeta,xi"
	k="18" />
    <hkern g1="quotedbl,quotesingle"
	g2="omega,omegatonos"
	k="49" />
    <hkern g1="quotedbl,quotesingle"
	g2="Omegatonos"
	k="-18" />
    <hkern g1="quotedbl,quotesingle"
	g2="Omicrontonos"
	k="-18" />
    <hkern g1="quotedbl,quotesingle"
	g2="Upsilontonos"
	k="-18" />
    <hkern g1="quotedbl,quotesingle"
	g2="Epsilontonos,Etatonos,Iotatonos"
	k="-18" />
    <hkern g1="quoteleft,quotedblleft"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-40" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J,Jcircumflex"
	k="82" />
    <hkern g1="quoteleft,quotedblleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="16" />
    <hkern g1="quoteleft,quotedblleft"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-28" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="-19" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="68" />
    <hkern g1="quoteleft,quotedblleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="35" />
    <hkern g1="quoteleft,quotedblleft"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="62" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="65" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="33" />
    <hkern g1="quoteleft,quotedblleft"
	g2="t,tcommaaccent,tcaron,tbar"
	k="-5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="27" />
    <hkern g1="quoteleft,quotedblleft"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-4" />
    <hkern g1="quoteleft,quotedblleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="-6" />
    <hkern g1="quoteleft,quotedblleft"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="23" />
    <hkern g1="quoteleft,quotedblleft"
	g2="x"
	k="-5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="quoteleft,quotedblleft"
	k="58" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,period,ellipsis"
	k="125" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Delta"
	k="78" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Omega"
	k="44" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Phi"
	k="22" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Upsilon,Upsilondieresis"
	k="-19" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Xi"
	k="-4" />
    <hkern g1="quoteleft,quotedblleft"
	g2="epsilontonos,epsilon"
	k="77" />
    <hkern g1="quoteleft,quotedblleft"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="65" />
    <hkern g1="quoteleft,quotedblleft"
	g2="rho"
	k="105" />
    <hkern g1="quoteleft,quotedblleft"
	g2="theta"
	k="8" />
    <hkern g1="quoteleft,quotedblleft"
	g2="zeta,xi"
	k="29" />
    <hkern g1="quoteleft,quotedblleft"
	g2="mu,etatonos,beta,eta,kappa"
	k="23" />
    <hkern g1="quoteleft,quotedblleft"
	g2="omega,omegatonos"
	k="63" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Omegatonos"
	k="-31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Omicrontonos"
	k="-31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Upsilontonos"
	k="-31" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Epsilontonos,Etatonos,Iotatonos"
	k="-31" />
    <hkern g1="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,fi,ffi"
	g2="quoteright,quotedblright"
	k="-7" />
    <hkern g1="quoteright,quotedblright"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-39" />
    <hkern g1="quoteright,quotedblright"
	g2="J,Jcircumflex"
	k="78" />
    <hkern g1="quoteright,quotedblright"
	g2="M,Mu"
	k="9" />
    <hkern g1="quoteright,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="24" />
    <hkern g1="quoteright,quotedblright"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-31" />
    <hkern g1="quoteright,quotedblright"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="-21" />
    <hkern g1="quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="90" />
    <hkern g1="quoteright,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="8" />
    <hkern g1="quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute"
	k="18" />
    <hkern g1="quoteright,quotedblright"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="50" />
    <hkern g1="quoteright,quotedblright"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="77" />
    <hkern g1="quoteright,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="72" />
    <hkern g1="quoteright,quotedblright"
	g2="t,tcommaaccent,tcaron,tbar"
	k="11" />
    <hkern g1="quoteright,quotedblright"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="7" />
    <hkern g1="quoteright,quotedblright"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="39" />
    <hkern g1="quoteright,quotedblright"
	g2="quoteright,quotedblright"
	k="58" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,ellipsis"
	k="154" />
    <hkern g1="quoteright,quotedblright"
	g2="space"
	k="86" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="83" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="J,Jcircumflex"
	k="-6" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="52" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="94" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-12" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="j,jcircumflex"
	k="-5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="v,w,y,yacute,ydieresis,wcircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="z,zacute,zdotaccent,zcaron"
	k="-11" />
    <hkern g1="space"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="41" />
    <hkern g1="space"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="36" />
    <hkern g1="space"
	g2="Y,Yacute,Ydieresis,Ygrave"
	k="46" />
    <hkern g1="space"
	g2="quoteleft,quotedblleft"
	k="83" />
    <hkern g1="space"
	g2="quotesinglbase,quotedblbase"
	k="113" />
    <hkern g1="Delta"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="75" />
    <hkern g1="Delta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="13" />
    <hkern g1="Delta"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="-6" />
    <hkern g1="Delta"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="Delta"
	g2="parenright,bracketright,braceright"
	k="21" />
    <hkern g1="Delta"
	g2="quotedbl,quotesingle"
	k="83" />
    <hkern g1="Delta"
	g2="quoteright,quotedblright"
	k="60" />
    <hkern g1="Delta"
	g2="comma,period,ellipsis"
	k="-5" />
    <hkern g1="Delta"
	g2="Delta"
	k="-7" />
    <hkern g1="Delta"
	g2="Phi"
	k="18" />
    <hkern g1="Delta"
	g2="Psi"
	k="72" />
    <hkern g1="Delta"
	g2="Upsilon,Upsilondieresis"
	k="92" />
    <hkern g1="Delta"
	g2="chi"
	k="29" />
    <hkern g1="Delta"
	g2="epsilontonos,epsilon"
	k="-16" />
    <hkern g1="Delta"
	g2="gamma,nu"
	k="19" />
    <hkern g1="Delta"
	g2="lambda"
	k="-17" />
    <hkern g1="Delta"
	g2="rho"
	k="-4" />
    <hkern g1="Delta"
	g2="tau"
	k="13" />
    <hkern g1="Delta"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="5" />
    <hkern g1="Delta"
	g2="zeta,xi"
	k="-5" />
    <hkern g1="Delta"
	g2="pi"
	k="-10" />
    <hkern g1="Gamma"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-19" />
    <hkern g1="Gamma"
	g2="M,Mu"
	k="16" />
    <hkern g1="Gamma"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="36" />
    <hkern g1="Gamma"
	g2="X,Chi"
	k="-4" />
    <hkern g1="Gamma"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="116" />
    <hkern g1="Gamma"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="Gamma"
	g2="guillemotright,guilsinglright"
	k="60" />
    <hkern g1="Gamma"
	g2="hyphen,endash,emdash"
	k="68" />
    <hkern g1="Gamma"
	g2="parenright,bracketright,braceright"
	k="-13" />
    <hkern g1="Gamma"
	g2="quotedbl,quotesingle"
	k="-9" />
    <hkern g1="Gamma"
	g2="quoteright,quotedblright"
	k="4" />
    <hkern g1="Gamma"
	g2="comma,period,ellipsis"
	k="119" />
    <hkern g1="Gamma"
	g2="Delta"
	k="113" />
    <hkern g1="Gamma"
	g2="Omega"
	k="31" />
    <hkern g1="Gamma"
	g2="Phi"
	k="40" />
    <hkern g1="Gamma"
	g2="Psi"
	k="-11" />
    <hkern g1="Gamma"
	g2="Upsilon,Upsilondieresis"
	k="-15" />
    <hkern g1="Gamma"
	g2="Sigma"
	k="-10" />
    <hkern g1="Gamma"
	g2="epsilontonos,epsilon"
	k="80" />
    <hkern g1="Gamma"
	g2="gamma,nu"
	k="41" />
    <hkern g1="Gamma"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="83" />
    <hkern g1="Gamma"
	g2="rho"
	k="107" />
    <hkern g1="Gamma"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="51" />
    <hkern g1="Gamma"
	g2="mu,etatonos,beta,eta,kappa"
	k="54" />
    <hkern g1="Gamma"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="54" />
    <hkern g1="Omegatonos,Omega"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="23" />
    <hkern g1="Omegatonos,Omega"
	g2="colon,semicolon"
	k="-11" />
    <hkern g1="Omegatonos,Omega"
	g2="hyphen,endash,emdash"
	k="-11" />
    <hkern g1="Omegatonos,Omega"
	g2="quotedbl,quotesingle"
	k="21" />
    <hkern g1="Omegatonos,Omega"
	g2="quoteright,quotedblright"
	k="4" />
    <hkern g1="Omegatonos,Omega"
	g2="comma,period,ellipsis"
	k="-11" />
    <hkern g1="Omegatonos,Omega"
	g2="Omega"
	k="-10" />
    <hkern g1="Omegatonos,Omega"
	g2="Phi"
	k="-6" />
    <hkern g1="Omegatonos,Omega"
	g2="Psi"
	k="4" />
    <hkern g1="Omegatonos,Omega"
	g2="Upsilon,Upsilondieresis"
	k="24" />
    <hkern g1="Omegatonos,Omega"
	g2="chi"
	k="-13" />
    <hkern g1="Omegatonos,Omega"
	g2="gamma,nu"
	k="-24" />
    <hkern g1="Omegatonos,Omega"
	g2="lambda"
	k="-20" />
    <hkern g1="Omegatonos,Omega"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="-10" />
    <hkern g1="Omegatonos,Omega"
	g2="tau"
	k="-23" />
    <hkern g1="Omegatonos,Omega"
	g2="theta"
	k="-18" />
    <hkern g1="Omegatonos,Omega"
	g2="zeta,xi"
	k="-13" />
    <hkern g1="Omegatonos,Omega"
	g2="pi"
	k="-27" />
    <hkern g1="Omegatonos,Omega"
	g2="mu,etatonos,beta,eta,kappa"
	k="-6" />
    <hkern g1="Omegatonos,Omega"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-5" />
    <hkern g1="Phi"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="23" />
    <hkern g1="Phi"
	g2="X,Chi"
	k="34" />
    <hkern g1="Phi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="30" />
    <hkern g1="Phi"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="18" />
    <hkern g1="Phi"
	g2="hyphen,endash,emdash"
	k="-11" />
    <hkern g1="Phi"
	g2="parenright,bracketright,braceright"
	k="6" />
    <hkern g1="Phi"
	g2="quotedbl,quotesingle"
	k="18" />
    <hkern g1="Phi"
	g2="comma,period,ellipsis"
	k="59" />
    <hkern g1="Phi"
	g2="Delta"
	k="20" />
    <hkern g1="Phi"
	g2="Omega"
	k="-5" />
    <hkern g1="Phi"
	g2="Upsilon,Upsilondieresis"
	k="44" />
    <hkern g1="Phi"
	g2="Xi"
	k="8" />
    <hkern g1="Phi"
	g2="Sigma"
	k="26" />
    <hkern g1="Phi"
	g2="lambda"
	k="19" />
    <hkern g1="Phi"
	g2="rho"
	k="14" />
    <hkern g1="Phi"
	g2="tau"
	k="-8" />
    <hkern g1="Phi"
	g2="theta"
	k="-18" />
    <hkern g1="Phi"
	g2="zeta,xi"
	k="-14" />
    <hkern g1="Psi"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-9" />
    <hkern g1="Psi"
	g2="M,Mu"
	k="24" />
    <hkern g1="Psi"
	g2="X,Chi"
	k="8" />
    <hkern g1="Psi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="78" />
    <hkern g1="Psi"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="13" />
    <hkern g1="Psi"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis"
	k="7" />
    <hkern g1="Psi"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="Psi"
	g2="comma,period,ellipsis"
	k="93" />
    <hkern g1="Psi"
	g2="Delta"
	k="75" />
    <hkern g1="Psi"
	g2="Omega"
	k="11" />
    <hkern g1="Psi"
	g2="Phi"
	k="9" />
    <hkern g1="Psi"
	g2="Upsilon,Upsilondieresis"
	k="-5" />
    <hkern g1="Psi"
	g2="Sigma"
	k="4" />
    <hkern g1="Psi"
	g2="epsilontonos,epsilon"
	k="25" />
    <hkern g1="Psi"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="22" />
    <hkern g1="Psi"
	g2="rho"
	k="42" />
    <hkern g1="Psi"
	g2="omega,omegatonos"
	k="13" />
    <hkern g1="Sigma"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="40" />
    <hkern g1="Sigma"
	g2="colon,semicolon"
	k="-11" />
    <hkern g1="Sigma"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="Sigma"
	g2="hyphen,endash,emdash"
	k="34" />
    <hkern g1="Sigma"
	g2="quotedbl,quotesingle"
	k="15" />
    <hkern g1="Sigma"
	g2="quoteright,quotedblright"
	k="35" />
    <hkern g1="Sigma"
	g2="comma,period,ellipsis"
	k="-11" />
    <hkern g1="Sigma"
	g2="chi"
	k="6" />
    <hkern g1="Sigma"
	g2="gamma,nu"
	k="21" />
    <hkern g1="Sigma"
	g2="lambda"
	k="-6" />
    <hkern g1="Sigma"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="19" />
    <hkern g1="Sigma"
	g2="rho"
	k="9" />
    <hkern g1="Sigma"
	g2="tau"
	k="17" />
    <hkern g1="Sigma"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="10" />
    <hkern g1="Sigma"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="4" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,Tau"
	k="-11" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="M,Mu"
	k="20" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Theta,Omicron"
	k="34" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="X,Chi"
	k="5" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute,Alpha,Lambda"
	k="102" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="colon,semicolon"
	k="38" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="guillemotright,guilsinglright"
	k="32" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="hyphen,endash,emdash"
	k="53" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="parenright,bracketright,braceright"
	k="-11" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Delta"
	k="98" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Omega"
	k="22" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Phi"
	k="33" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Psi"
	k="-8" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Upsilon,Upsilondieresis"
	k="-5" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="epsilontonos,epsilon"
	k="65" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="gamma,nu"
	k="19" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="lambda"
	k="5" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="78" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="rho"
	k="91" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="35" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="pi"
	k="14" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="mu,etatonos,beta,eta,kappa"
	k="37" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="34" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="lambda"
	k="-8" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="rho"
	k="7" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="zeta,xi"
	k="-12" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="pi"
	k="-9" />
    <hkern g1="beta"
	g2="hyphen,endash,emdash"
	k="-5" />
    <hkern g1="beta"
	g2="parenright,bracketright,braceright"
	k="9" />
    <hkern g1="beta"
	g2="quotedbl,quotesingle"
	k="33" />
    <hkern g1="beta"
	g2="quoteright,quotedblright"
	k="25" />
    <hkern g1="beta"
	g2="comma,period,ellipsis"
	k="25" />
    <hkern g1="beta"
	g2="gamma,nu"
	k="9" />
    <hkern g1="beta"
	g2="rho"
	k="9" />
    <hkern g1="beta"
	g2="zeta,xi"
	k="-4" />
    <hkern g1="chi"
	g2="parenright,bracketright,braceright"
	k="5" />
    <hkern g1="chi"
	g2="quoteright,quotedblright"
	k="-11" />
    <hkern g1="chi"
	g2="comma,period,ellipsis"
	k="13" />
    <hkern g1="chi"
	g2="epsilontonos,epsilon"
	k="12" />
    <hkern g1="chi"
	g2="lambda"
	k="20" />
    <hkern g1="chi"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="21" />
    <hkern g1="chi"
	g2="rho"
	k="10" />
    <hkern g1="chi"
	g2="tau"
	k="-20" />
    <hkern g1="chi"
	g2="pi"
	k="-15" />
    <hkern g1="chi"
	g2="mu,etatonos,beta,eta,kappa"
	k="-5" />
    <hkern g1="chi"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="4" />
    <hkern g1="epsilontonos,epsilon"
	g2="guillemotright,guilsinglright"
	k="-11" />
    <hkern g1="epsilontonos,epsilon"
	g2="gamma,nu"
	k="-6" />
    <hkern g1="epsilontonos,epsilon"
	g2="lambda"
	k="-6" />
    <hkern g1="epsilontonos,epsilon"
	g2="rho"
	k="4" />
    <hkern g1="epsilontonos,epsilon"
	g2="pi"
	k="-8" />
    <hkern g1="etatonos,eta"
	g2="quoteright,quotedblright"
	k="33" />
    <hkern g1="etatonos,eta"
	g2="gamma,nu"
	k="16" />
    <hkern g1="etatonos,eta"
	g2="rho"
	k="4" />
    <hkern g1="etatonos,eta"
	g2="tau"
	k="4" />
    <hkern g1="etatonos,eta"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="6" />
    <hkern g1="gamma,nu"
	g2="guillemotright,guilsinglright"
	k="-11" />
    <hkern g1="gamma,nu"
	g2="hyphen,endash,emdash"
	k="-11" />
    <hkern g1="gamma,nu"
	g2="quotedbl,quotesingle"
	k="-8" />
    <hkern g1="gamma,nu"
	g2="quoteright,quotedblright"
	k="-13" />
    <hkern g1="gamma,nu"
	g2="comma,period,ellipsis"
	k="48" />
    <hkern g1="gamma,nu"
	g2="epsilontonos,epsilon"
	k="8" />
    <hkern g1="gamma,nu"
	g2="gamma,nu"
	k="-10" />
    <hkern g1="gamma,nu"
	g2="lambda"
	k="14" />
    <hkern g1="gamma,nu"
	g2="rho"
	k="23" />
    <hkern g1="gamma,nu"
	g2="tau"
	k="-18" />
    <hkern g1="gamma,nu"
	g2="theta"
	k="-23" />
    <hkern g1="gamma,nu"
	g2="zeta,xi"
	k="-5" />
    <hkern g1="gamma,nu"
	g2="pi"
	k="-16" />
    <hkern g1="kappa"
	g2="colon,semicolon"
	k="-11" />
    <hkern g1="kappa"
	g2="guillemotright,guilsinglright"
	k="-11" />
    <hkern g1="kappa"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="kappa"
	g2="quoteright,quotedblright"
	k="-6" />
    <hkern g1="kappa"
	g2="comma,period,ellipsis"
	k="-11" />
    <hkern g1="kappa"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="15" />
    <hkern g1="kappa"
	g2="rho"
	k="8" />
    <hkern g1="kappa"
	g2="zeta,xi"
	k="10" />
    <hkern g1="kappa"
	g2="pi"
	k="-5" />
    <hkern g1="lambda"
	g2="parenright,bracketright,braceright"
	k="6" />
    <hkern g1="lambda"
	g2="quotedbl,quotesingle"
	k="85" />
    <hkern g1="lambda"
	g2="quoteright,quotedblright"
	k="60" />
    <hkern g1="lambda"
	g2="chi"
	k="33" />
    <hkern g1="lambda"
	g2="epsilontonos,epsilon"
	k="-5" />
    <hkern g1="lambda"
	g2="gamma,nu"
	k="34" />
    <hkern g1="lambda"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="6" />
    <hkern g1="lambda"
	g2="tau"
	k="27" />
    <hkern g1="lambda"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="10" />
    <hkern g1="lambda"
	g2="pi"
	k="4" />
    <hkern g1="lambda"
	g2="mu,etatonos,beta,eta,kappa"
	k="5" />
    <hkern g1="lambda"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="8" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="hyphen,endash,emdash"
	k="-11" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="parenright,bracketright,braceright"
	k="20" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="quotedbl,quotesingle"
	k="49" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="comma,period,ellipsis"
	k="27" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="-6" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="chi"
	k="24" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="gamma,nu"
	k="8" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="lambda"
	k="7" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="rho"
	k="8" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="tau"
	k="5" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="zeta,xi"
	k="-8" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="omega,omegatonos"
	k="-6" />
    <hkern g1="pi"
	g2="guillemotright,guilsinglright"
	k="-15" />
    <hkern g1="pi"
	g2="parenright,bracketright,braceright"
	k="22" />
    <hkern g1="pi"
	g2="quotedbl,quotesingle"
	k="13" />
    <hkern g1="pi"
	g2="comma,period,ellipsis"
	k="20" />
    <hkern g1="pi"
	g2="gamma,nu"
	k="-7" />
    <hkern g1="pi"
	g2="lambda"
	k="11" />
    <hkern g1="pi"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="5" />
    <hkern g1="pi"
	g2="rho"
	k="14" />
    <hkern g1="pi"
	g2="tau"
	k="-9" />
    <hkern g1="pi"
	g2="pi"
	k="-6" />
    <hkern g1="pi"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="8" />
    <hkern g1="sigma"
	g2="guillemotright,guilsinglright"
	k="-22" />
    <hkern g1="sigma"
	g2="hyphen,endash,emdash"
	k="-17" />
    <hkern g1="sigma"
	g2="quoteright,quotedblright"
	k="-13" />
    <hkern g1="sigma"
	g2="comma,period,ellipsis"
	k="31" />
    <hkern g1="sigma"
	g2="chi"
	k="-5" />
    <hkern g1="sigma"
	g2="gamma,nu"
	k="-14" />
    <hkern g1="sigma"
	g2="lambda"
	k="10" />
    <hkern g1="sigma"
	g2="rho"
	k="14" />
    <hkern g1="sigma"
	g2="tau"
	k="-23" />
    <hkern g1="sigma"
	g2="theta"
	k="-14" />
    <hkern g1="sigma"
	g2="zeta,xi"
	k="-14" />
    <hkern g1="sigma"
	g2="pi"
	k="-14" />
    <hkern g1="sigma"
	g2="mu,etatonos,beta,eta,kappa"
	k="-5" />
    <hkern g1="sigma"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-5" />
    <hkern g1="tau"
	g2="guillemotright,guilsinglright"
	k="-22" />
    <hkern g1="tau"
	g2="hyphen,endash,emdash"
	k="17" />
    <hkern g1="tau"
	g2="quoteright,quotedblright"
	k="-19" />
    <hkern g1="tau"
	g2="comma,period,ellipsis"
	k="26" />
    <hkern g1="tau"
	g2="chi"
	k="-5" />
    <hkern g1="tau"
	g2="gamma,nu"
	k="-8" />
    <hkern g1="tau"
	g2="lambda"
	k="12" />
    <hkern g1="tau"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="12" />
    <hkern g1="tau"
	g2="rho"
	k="24" />
    <hkern g1="tau"
	g2="tau"
	k="-6" />
    <hkern g1="theta"
	g2="hyphen,endash,emdash"
	k="-17" />
    <hkern g1="theta"
	g2="comma,period,ellipsis"
	k="32" />
    <hkern g1="theta"
	g2="lambda"
	k="5" />
    <hkern g1="theta"
	g2="zeta,xi"
	k="-18" />
    <hkern g1="xi"
	g2="colon,semicolon"
	k="-24" />
    <hkern g1="xi"
	g2="guillemotright,guilsinglright"
	k="-22" />
    <hkern g1="xi"
	g2="parenright,bracketright,braceright"
	k="-37" />
    <hkern g1="xi"
	g2="quotedbl,quotesingle"
	k="-7" />
    <hkern g1="xi"
	g2="comma,period,ellipsis"
	k="-26" />
    <hkern g1="xi"
	g2="epsilontonos,epsilon"
	k="-8" />
    <hkern g1="xi"
	g2="lambda"
	k="-38" />
    <hkern g1="xi"
	g2="pi"
	k="-19" />
    <hkern g1="zeta"
	g2="colon,semicolon"
	k="-14" />
    <hkern g1="zeta"
	g2="guillemotright,guilsinglright"
	k="-18" />
    <hkern g1="zeta"
	g2="hyphen,endash,emdash"
	k="16" />
    <hkern g1="zeta"
	g2="parenright,bracketright,braceright"
	k="-50" />
    <hkern g1="zeta"
	g2="quotedbl,quotesingle"
	k="-29" />
    <hkern g1="zeta"
	g2="comma,period,ellipsis"
	k="-19" />
    <hkern g1="zeta"
	g2="epsilontonos,epsilon"
	k="-6" />
    <hkern g1="zeta"
	g2="lambda"
	k="-33" />
    <hkern g1="zeta"
	g2="rho"
	k="-12" />
    <hkern g1="zeta"
	g2="tau"
	k="8" />
    <hkern g1="afii10018"
	g2="afii10041"
	k="41" />
    <hkern g1="afii10018"
	g2="afii10021"
	k="10" />
    <hkern g1="afii10018"
	g2="afii10051"
	k="44" />
    <hkern g1="afii10018"
	g2="afii10025,afii10047"
	k="7" />
    <hkern g1="afii10018"
	g2="afii10038"
	k="9" />
    <hkern g1="afii10018"
	g2="afii10058,afii10029"
	k="-5" />
    <hkern g1="afii10018"
	g2="afii10060,afii10036,afii10044"
	k="42" />
    <hkern g1="afii10018"
	g2="afii10057"
	k="4" />
    <hkern g1="afii10018"
	g2="afii10053,afii10032,afii10035"
	k="12" />
    <hkern g1="afii10018"
	g2="afii10062,afii10037"
	k="17" />
    <hkern g1="afii10018"
	g2="afii10024"
	k="8" />
    <hkern g1="afii10018"
	g2="afii10017"
	k="7" />
    <hkern g1="afii10018"
	g2="afii10054"
	k="6" />
    <hkern g1="afii10018"
	g2="afii10039"
	k="20" />
    <hkern g1="afii10018"
	g2="afii10089"
	k="9" />
    <hkern g1="afii10018"
	g2="afii10069"
	k="4" />
    <hkern g1="afii10018"
	g2="afii10099"
	k="12" />
    <hkern g1="afii10018"
	g2="afii10084,afii10092"
	k="33" />
    <hkern g1="afii10018"
	g2="afii10105"
	k="7" />
    <hkern g1="afii10018"
	g2="afii10108"
	k="6" />
    <hkern g1="afii10018"
	g2="afii10085,afii10110"
	k="20" />
    <hkern g1="afii10018"
	g2="afii10087"
	k="12" />
    <hkern g1="afii10051"
	g2="afii10041"
	k="45" />
    <hkern g1="afii10051"
	g2="afii10021"
	k="-11" />
    <hkern g1="afii10051"
	g2="afii10051"
	k="72" />
    <hkern g1="afii10051"
	g2="afii10025,afii10047"
	k="-5" />
    <hkern g1="afii10051"
	g2="afii10038"
	k="7" />
    <hkern g1="afii10051"
	g2="afii10058,afii10029"
	k="-18" />
    <hkern g1="afii10051"
	g2="afii10060,afii10036,afii10044"
	k="80" />
    <hkern g1="afii10051"
	g2="afii10057"
	k="-10" />
    <hkern g1="afii10051"
	g2="afii10053,afii10032,afii10035"
	k="6" />
    <hkern g1="afii10051"
	g2="afii10062,afii10037"
	k="10" />
    <hkern g1="afii10051"
	g2="afii10024"
	k="-12" />
    <hkern g1="afii10051"
	g2="afii10049"
	k="-11" />
    <hkern g1="afii10051"
	g2="afii10089"
	k="13" />
    <hkern g1="afii10051"
	g2="afii10069"
	k="-11" />
    <hkern g1="afii10051"
	g2="afii10099"
	k="8" />
    <hkern g1="afii10051"
	g2="afii10077,afii10106"
	k="-10" />
    <hkern g1="afii10051"
	g2="afii10084,afii10092"
	k="20" />
    <hkern g1="afii10051"
	g2="afii10108"
	k="10" />
    <hkern g1="afii10051"
	g2="afii10085,afii10110"
	k="13" />
    <hkern g1="afii10051"
	g2="afii10097"
	k="-8" />
    <hkern g1="afii10051"
	g2="afii10072"
	k="-10" />
    <hkern g1="afii10054"
	g2="afii10041"
	k="25" />
    <hkern g1="afii10054"
	g2="afii10021"
	k="5" />
    <hkern g1="afii10054"
	g2="afii10058,afii10029"
	k="-6" />
    <hkern g1="afii10054"
	g2="afii10060,afii10036,afii10044"
	k="7" />
    <hkern g1="afii10054"
	g2="afii10057"
	k="4" />
    <hkern g1="afii10054"
	g2="afii10062,afii10037"
	k="4" />
    <hkern g1="afii10054"
	g2="afii10049"
	k="-5" />
    <hkern g1="afii10054"
	g2="afii10089"
	k="16" />
    <hkern g1="afii10054"
	g2="afii10099"
	k="7" />
    <hkern g1="afii10054"
	g2="afii10077,afii10106"
	k="-4" />
    <hkern g1="afii10054"
	g2="afii10084,afii10092"
	k="25" />
    <hkern g1="afii10054"
	g2="afii10105"
	k="4" />
    <hkern g1="afii10054"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-5" />
    <hkern g1="afii10054"
	g2="afii10085,afii10110"
	k="11" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10041"
	k="6" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10089"
	k="8" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10073,afii10095,afii10846"
	k="-5" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10077,afii10106"
	k="-5" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10084,afii10092"
	k="4" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10097"
	k="-4" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10072"
	k="-5" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="comma,period,ellipsis"
	k="-5" />
    <hkern g1="afii10038"
	g2="afii10041"
	k="8" />
    <hkern g1="afii10038"
	g2="afii10021"
	k="27" />
    <hkern g1="afii10038"
	g2="afii10051"
	k="31" />
    <hkern g1="afii10038"
	g2="afii10025,afii10047"
	k="9" />
    <hkern g1="afii10038"
	g2="afii10058,afii10029"
	k="5" />
    <hkern g1="afii10038"
	g2="afii10060,afii10036,afii10044"
	k="21" />
    <hkern g1="afii10038"
	g2="afii10057"
	k="21" />
    <hkern g1="afii10038"
	g2="afii10062,afii10037"
	k="24" />
    <hkern g1="afii10038"
	g2="afii10024"
	k="17" />
    <hkern g1="afii10038"
	g2="afii10017"
	k="21" />
    <hkern g1="afii10038"
	g2="afii10039"
	k="37" />
    <hkern g1="afii10038"
	g2="afii10030"
	k="6" />
    <hkern g1="afii10038"
	g2="afii10065"
	k="4" />
    <hkern g1="afii10038"
	g2="afii10069"
	k="14" />
    <hkern g1="afii10038"
	g2="afii10099"
	k="-5" />
    <hkern g1="afii10038"
	g2="afii10073,afii10095,afii10846"
	k="-4" />
    <hkern g1="afii10038"
	g2="afii10084,afii10092"
	k="-10" />
    <hkern g1="afii10038"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-6" />
    <hkern g1="afii10038"
	g2="afii10108"
	k="-6" />
    <hkern g1="afii10038"
	g2="afii10085,afii10110"
	k="-8" />
    <hkern g1="afii10038"
	g2="afii10097"
	k="-8" />
    <hkern g1="afii10030"
	g2="afii10041"
	k="18" />
    <hkern g1="afii10030"
	g2="afii10021"
	k="-9" />
    <hkern g1="afii10030"
	g2="afii10058,afii10029"
	k="-11" />
    <hkern g1="afii10030"
	g2="afii10062,afii10037"
	k="4" />
    <hkern g1="afii10030"
	g2="afii10017"
	k="8" />
    <hkern g1="afii10030"
	g2="afii10049"
	k="-5" />
    <hkern g1="afii10030"
	g2="afii10065"
	k="-4" />
    <hkern g1="afii10030"
	g2="afii10089"
	k="15" />
    <hkern g1="afii10030"
	g2="afii10069"
	k="-5" />
    <hkern g1="afii10030"
	g2="afii10073,afii10095,afii10846"
	k="-7" />
    <hkern g1="afii10030"
	g2="afii10077,afii10106"
	k="-6" />
    <hkern g1="afii10030"
	g2="afii10097"
	k="-7" />
    <hkern g1="afii10030"
	g2="afii10072"
	k="-8" />
    <hkern g1="afii10034"
	g2="afii10041"
	k="7" />
    <hkern g1="afii10034"
	g2="afii10021"
	k="66" />
    <hkern g1="afii10034"
	g2="afii10025,afii10047"
	k="21" />
    <hkern g1="afii10034"
	g2="afii10038"
	k="11" />
    <hkern g1="afii10034"
	g2="afii10058,afii10029"
	k="29" />
    <hkern g1="afii10034"
	g2="afii10057"
	k="87" />
    <hkern g1="afii10034"
	g2="afii10053,afii10032,afii10035"
	k="8" />
    <hkern g1="afii10034"
	g2="afii10062,afii10037"
	k="12" />
    <hkern g1="afii10034"
	g2="afii10024"
	k="23" />
    <hkern g1="afii10034"
	g2="afii10017"
	k="84" />
    <hkern g1="afii10034"
	g2="afii10039"
	k="26" />
    <hkern g1="afii10034"
	g2="afii10030"
	k="18" />
    <hkern g1="afii10034"
	g2="afii10065"
	k="26" />
    <hkern g1="afii10034"
	g2="afii10066"
	k="5" />
    <hkern g1="afii10034"
	g2="afii10089"
	k="11" />
    <hkern g1="afii10034"
	g2="afii10069"
	k="48" />
    <hkern g1="afii10034"
	g2="afii10099"
	k="-9" />
    <hkern g1="afii10034"
	g2="afii10102"
	k="20" />
    <hkern g1="afii10034"
	g2="afii10077,afii10106"
	k="24" />
    <hkern g1="afii10034"
	g2="afii10084,afii10092"
	k="-6" />
    <hkern g1="afii10034"
	g2="afii10105"
	k="19" />
    <hkern g1="afii10034"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="28" />
    <hkern g1="afii10034"
	g2="afii10108"
	k="-9" />
    <hkern g1="afii10034"
	g2="afii10097"
	k="9" />
    <hkern g1="afii10034"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="15" />
    <hkern g1="afii10034"
	g2="afii10086"
	k="28" />
    <hkern g1="afii10034"
	g2="afii10078"
	k="15" />
    <hkern g1="afii10034"
	g2="afii10103"
	k="15" />
    <hkern g1="afii10034"
	g2="colon,semicolon"
	k="21" />
    <hkern g1="afii10034"
	g2="comma,period,ellipsis"
	k="139" />
    <hkern g1="afii10034"
	g2="hyphen,endash,emdash"
	k="13" />
    <hkern g1="afii10034"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="afii10053,afii10035"
	g2="afii10041"
	k="12" />
    <hkern g1="afii10053,afii10035"
	g2="afii10021"
	k="-21" />
    <hkern g1="afii10053,afii10035"
	g2="afii10051"
	k="-36" />
    <hkern g1="afii10053,afii10035"
	g2="afii10025,afii10047"
	k="-19" />
    <hkern g1="afii10053,afii10035"
	g2="afii10038"
	k="25" />
    <hkern g1="afii10053,afii10035"
	g2="afii10058,afii10029"
	k="-35" />
    <hkern g1="afii10053,afii10035"
	g2="afii10060,afii10036,afii10044"
	k="-35" />
    <hkern g1="afii10053,afii10035"
	g2="afii10053,afii10032,afii10035"
	k="20" />
    <hkern g1="afii10053,afii10035"
	g2="afii10062,afii10037"
	k="-18" />
    <hkern g1="afii10053,afii10035"
	g2="afii10024"
	k="-15" />
    <hkern g1="afii10053,afii10035"
	g2="afii10017"
	k="-4" />
    <hkern g1="afii10053,afii10035"
	g2="afii10039"
	k="-7" />
    <hkern g1="afii10053,afii10035"
	g2="afii10049"
	k="-15" />
    <hkern g1="afii10053,afii10035"
	g2="afii10065"
	k="5" />
    <hkern g1="afii10053,afii10035"
	g2="afii10066"
	k="14" />
    <hkern g1="afii10053,afii10035"
	g2="afii10089"
	k="59" />
    <hkern g1="afii10053,afii10035"
	g2="afii10069"
	k="-11" />
    <hkern g1="afii10053,afii10035"
	g2="afii10099"
	k="-13" />
    <hkern g1="afii10053,afii10035"
	g2="afii10073,afii10095,afii10846"
	k="-10" />
    <hkern g1="afii10053,afii10035"
	g2="afii10077,afii10106"
	k="-16" />
    <hkern g1="afii10053,afii10035"
	g2="afii10084,afii10092"
	k="33" />
    <hkern g1="afii10053,afii10035"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="16" />
    <hkern g1="afii10053,afii10035"
	g2="afii10108"
	k="-14" />
    <hkern g1="afii10053,afii10035"
	g2="afii10085,afii10110"
	k="25" />
    <hkern g1="afii10053,afii10035"
	g2="afii10072"
	k="-15" />
    <hkern g1="afii10053,afii10035"
	g2="afii10086"
	k="18" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10041"
	k="7" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10021"
	k="50" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10051"
	k="-52" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10025,afii10047"
	k="-13" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10038"
	k="42" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10058,afii10029"
	k="25" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10060,afii10036,afii10044"
	k="-41" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10057"
	k="64" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10053,afii10032,afii10035"
	k="35" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10062,afii10037"
	k="-40" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10024"
	k="-20" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10017"
	k="81" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10049"
	k="5" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10030"
	k="8" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10065"
	k="80" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10066"
	k="26" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10089"
	k="88" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10069"
	k="101" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10099"
	k="-33" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10102"
	k="40" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10073,afii10095,afii10846"
	k="60" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10077,afii10106"
	k="88" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10084,afii10092"
	k="61" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10105"
	k="23" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="92" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10108"
	k="-33" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10085,afii10110"
	k="46" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10097"
	k="78" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10072"
	k="61" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10087"
	k="57" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="67" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10086"
	k="86" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10078"
	k="79" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10103"
	k="15" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="parenright,bracketright,braceright"
	k="-30" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="colon,semicolon"
	k="59" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="comma,period,ellipsis"
	k="68" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="hyphen,endash,emdash"
	k="73" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="guillemotright,guilsinglright"
	k="51" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="guillemotleft,guilsinglleft"
	k="36" />
    <hkern g1="afii10039"
	g2="afii10041"
	k="18" />
    <hkern g1="afii10039"
	g2="afii10021"
	k="-11" />
    <hkern g1="afii10039"
	g2="afii10051"
	k="-15" />
    <hkern g1="afii10039"
	g2="afii10025,afii10047"
	k="-6" />
    <hkern g1="afii10039"
	g2="afii10038"
	k="36" />
    <hkern g1="afii10039"
	g2="afii10058,afii10029"
	k="-24" />
    <hkern g1="afii10039"
	g2="afii10060,afii10036,afii10044"
	k="-17" />
    <hkern g1="afii10039"
	g2="afii10053,afii10032,afii10035"
	k="29" />
    <hkern g1="afii10039"
	g2="afii10062,afii10037"
	k="-10" />
    <hkern g1="afii10039"
	g2="afii10024"
	k="-4" />
    <hkern g1="afii10039"
	g2="afii10017"
	k="5" />
    <hkern g1="afii10039"
	g2="afii10049"
	k="-12" />
    <hkern g1="afii10039"
	g2="afii10065"
	k="6" />
    <hkern g1="afii10039"
	g2="afii10066"
	k="18" />
    <hkern g1="afii10039"
	g2="afii10089"
	k="51" />
    <hkern g1="afii10039"
	g2="afii10069"
	k="-11" />
    <hkern g1="afii10039"
	g2="afii10099"
	k="-8" />
    <hkern g1="afii10039"
	g2="afii10073,afii10095,afii10846"
	k="-5" />
    <hkern g1="afii10039"
	g2="afii10077,afii10106"
	k="-14" />
    <hkern g1="afii10039"
	g2="afii10084,afii10092"
	k="20" />
    <hkern g1="afii10039"
	g2="afii10105"
	k="4" />
    <hkern g1="afii10039"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="20" />
    <hkern g1="afii10039"
	g2="afii10108"
	k="-9" />
    <hkern g1="afii10039"
	g2="afii10085,afii10110"
	k="24" />
    <hkern g1="afii10039"
	g2="afii10097"
	k="-4" />
    <hkern g1="afii10039"
	g2="afii10072"
	k="-5" />
    <hkern g1="afii10039"
	g2="afii10086"
	k="21" />
    <hkern g1="afii10039"
	g2="colon,semicolon"
	k="4" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10041"
	k="51" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10021"
	k="8" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10051"
	k="87" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10025,afii10047"
	k="11" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10038"
	k="4" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10060,afii10036,afii10044"
	k="72" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10057"
	k="5" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10053,afii10032,afii10035"
	k="4" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10062,afii10037"
	k="47" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10017"
	k="6" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10054"
	k="8" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10039"
	k="23" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10030"
	k="6" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10089"
	k="10" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10069"
	k="4" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10099"
	k="8" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10102"
	k="5" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10084,afii10092"
	k="24" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10105"
	k="9" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10108"
	k="6" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10085,afii10110"
	k="15" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10087"
	k="12" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="parenright,bracketright,braceright"
	k="31" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="quoteright,quotedblright"
	k="48" />
    <hkern g1="afii10023,afii10022"
	g2="afii10041"
	k="13" />
    <hkern g1="afii10023,afii10022"
	g2="afii10021"
	k="-20" />
    <hkern g1="afii10023,afii10022"
	g2="afii10025,afii10047"
	k="-9" />
    <hkern g1="afii10023,afii10022"
	g2="afii10058,afii10029"
	k="-26" />
    <hkern g1="afii10023,afii10022"
	g2="afii10060,afii10036,afii10044"
	k="-5" />
    <hkern g1="afii10023,afii10022"
	g2="afii10057"
	k="-18" />
    <hkern g1="afii10023,afii10022"
	g2="afii10062,afii10037"
	k="-9" />
    <hkern g1="afii10023,afii10022"
	g2="afii10049"
	k="-7" />
    <hkern g1="afii10023,afii10022"
	g2="afii10066"
	k="4" />
    <hkern g1="afii10023,afii10022"
	g2="afii10089"
	k="13" />
    <hkern g1="afii10023,afii10022"
	g2="afii10069"
	k="-12" />
    <hkern g1="afii10023,afii10022"
	g2="afii10073,afii10095,afii10846"
	k="-8" />
    <hkern g1="afii10023,afii10022"
	g2="afii10077,afii10106"
	k="-14" />
    <hkern g1="afii10023,afii10022"
	g2="afii10084,afii10092"
	k="14" />
    <hkern g1="afii10023,afii10022"
	g2="afii10105"
	k="4" />
    <hkern g1="afii10023,afii10022"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="7" />
    <hkern g1="afii10023,afii10022"
	g2="afii10108"
	k="-4" />
    <hkern g1="afii10023,afii10022"
	g2="afii10085,afii10110"
	k="6" />
    <hkern g1="afii10023,afii10022"
	g2="afii10097"
	k="-7" />
    <hkern g1="afii10023,afii10022"
	g2="afii10072"
	k="-8" />
    <hkern g1="afii10023,afii10022"
	g2="afii10086"
	k="7" />
    <hkern g1="afii10057"
	g2="afii10021"
	k="10" />
    <hkern g1="afii10057"
	g2="afii10057"
	k="25" />
    <hkern g1="afii10057"
	g2="afii10062,afii10037"
	k="-5" />
    <hkern g1="afii10057"
	g2="afii10017"
	k="5" />
    <hkern g1="afii10057"
	g2="afii10049"
	k="-5" />
    <hkern g1="afii10057"
	g2="afii10089"
	k="6" />
    <hkern g1="afii10057"
	g2="afii10069"
	k="9" />
    <hkern g1="afii10057"
	g2="afii10099"
	k="-9" />
    <hkern g1="afii10057"
	g2="afii10105"
	k="4" />
    <hkern g1="afii10057"
	g2="afii10108"
	k="-8" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10041"
	k="17" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10021"
	k="-30" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10051"
	k="-19" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10025,afii10047"
	k="-13" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10038"
	k="21" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10058,afii10029"
	k="-36" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10060,afii10036,afii10044"
	k="-29" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10057"
	k="-25" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10053,afii10032,afii10035"
	k="19" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10062,afii10037"
	k="-19" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10024"
	k="-17" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10017"
	k="-5" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10039"
	k="-9" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10049"
	k="-23" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10065"
	k="-12" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10066"
	k="9" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10089"
	k="38" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10069"
	k="-24" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10099"
	k="-15" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10102"
	k="-4" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10073,afii10095,afii10846"
	k="-13" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10077,afii10106"
	k="-28" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10084,afii10092"
	k="23" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10108"
	k="-13" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10085,afii10110"
	k="19" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10097"
	k="-11" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10072"
	k="-15" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10087"
	k="-12" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10078"
	k="-7" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10103"
	k="-4" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="hyphen,endash,emdash"
	k="19" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10041"
	k="7" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10021"
	k="21" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10051"
	k="14" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10025,afii10047"
	k="14" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10058,afii10029"
	k="4" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10060,afii10036,afii10044"
	k="8" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10057"
	k="11" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10062,afii10037"
	k="21" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10024"
	k="17" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10017"
	k="15" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10039"
	k="28" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10049"
	k="-4" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10089"
	k="-4" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10069"
	k="9" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10099"
	k="-5" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10073,afii10095,afii10846"
	k="-7" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10084,afii10092"
	k="-13" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10108"
	k="-8" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10085,afii10110"
	k="-12" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10097"
	k="-5" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10087"
	k="5" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10078"
	k="-5" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10041"
	k="17" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10021"
	k="-13" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10051"
	k="13" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10025,afii10047"
	k="-6" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10038"
	k="12" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10058,afii10029"
	k="-14" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10060,afii10036,afii10044"
	k="11" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10057"
	k="-12" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10053,afii10032,afii10035"
	k="9" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10062,afii10037"
	k="4" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10024"
	k="-5" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10049"
	k="-7" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10065"
	k="-5" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10066"
	k="6" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10089"
	k="19" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10069"
	k="-14" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10099"
	k="4" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10073,afii10095,afii10846"
	k="-8" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10077,afii10106"
	k="-14" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10084,afii10092"
	k="18" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10105"
	k="-35" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="6" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10108"
	k="4" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10085,afii10110"
	k="6" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10097"
	k="-8" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10072"
	k="-9" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10087"
	k="-4" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10086"
	k="7" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="comma,period,ellipsis"
	k="-20" />
    <hkern g1="afii10060"
	g2="afii10041"
	k="45" />
    <hkern g1="afii10060"
	g2="afii10051"
	k="84" />
    <hkern g1="afii10060"
	g2="afii10038"
	k="7" />
    <hkern g1="afii10060"
	g2="afii10060,afii10036,afii10044"
	k="70" />
    <hkern g1="afii10060"
	g2="afii10057"
	k="4" />
    <hkern g1="afii10060"
	g2="afii10053,afii10032,afii10035"
	k="6" />
    <hkern g1="afii10060"
	g2="afii10062,afii10037"
	k="23" />
    <hkern g1="afii10060"
	g2="afii10049"
	k="-5" />
    <hkern g1="afii10060"
	g2="afii10089"
	k="18" />
    <hkern g1="afii10060"
	g2="afii10099"
	k="10" />
    <hkern g1="afii10060"
	g2="afii10084,afii10092"
	k="27" />
    <hkern g1="afii10060"
	g2="afii10105"
	k="14" />
    <hkern g1="afii10060"
	g2="afii10108"
	k="7" />
    <hkern g1="afii10060"
	g2="afii10085,afii10110"
	k="19" />
    <hkern g1="afii10060"
	g2="afii10097"
	k="-4" />
    <hkern g1="afii10062,afii10037"
	g2="afii10021"
	k="50" />
    <hkern g1="afii10062,afii10037"
	g2="afii10051"
	k="-52" />
    <hkern g1="afii10062,afii10037"
	g2="afii10025,afii10047"
	k="-11" />
    <hkern g1="afii10062,afii10037"
	g2="afii10038"
	k="11" />
    <hkern g1="afii10062,afii10037"
	g2="afii10058,afii10029"
	k="16" />
    <hkern g1="afii10062,afii10037"
	g2="afii10060,afii10036,afii10044"
	k="-42" />
    <hkern g1="afii10062,afii10037"
	g2="afii10057"
	k="68" />
    <hkern g1="afii10062,afii10037"
	g2="afii10053,afii10032,afii10035"
	k="11" />
    <hkern g1="afii10062,afii10037"
	g2="afii10062,afii10037"
	k="-24" />
    <hkern g1="afii10062,afii10037"
	g2="afii10024"
	k="-14" />
    <hkern g1="afii10062,afii10037"
	g2="afii10017"
	k="57" />
    <hkern g1="afii10062,afii10037"
	g2="afii10039"
	k="-4" />
    <hkern g1="afii10062,afii10037"
	g2="afii10030"
	k="9" />
    <hkern g1="afii10062,afii10037"
	g2="afii10065"
	k="49" />
    <hkern g1="afii10062,afii10037"
	g2="afii10066"
	k="14" />
    <hkern g1="afii10062,afii10037"
	g2="afii10089"
	k="34" />
    <hkern g1="afii10062,afii10037"
	g2="afii10069"
	k="68" />
    <hkern g1="afii10062,afii10037"
	g2="afii10099"
	k="-37" />
    <hkern g1="afii10062,afii10037"
	g2="afii10102"
	k="41" />
    <hkern g1="afii10062,afii10037"
	g2="afii10073,afii10095,afii10846"
	k="20" />
    <hkern g1="afii10062,afii10037"
	g2="afii10077,afii10106"
	k="33" />
    <hkern g1="afii10062,afii10037"
	g2="afii10084,afii10092"
	k="-12" />
    <hkern g1="afii10062,afii10037"
	g2="afii10105"
	k="12" />
    <hkern g1="afii10062,afii10037"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="53" />
    <hkern g1="afii10062,afii10037"
	g2="afii10108"
	k="-34" />
    <hkern g1="afii10062,afii10037"
	g2="afii10085,afii10110"
	k="8" />
    <hkern g1="afii10062,afii10037"
	g2="afii10097"
	k="41" />
    <hkern g1="afii10062,afii10037"
	g2="afii10072"
	k="17" />
    <hkern g1="afii10062,afii10037"
	g2="afii10087"
	k="16" />
    <hkern g1="afii10062,afii10037"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="24" />
    <hkern g1="afii10062,afii10037"
	g2="afii10086"
	k="50" />
    <hkern g1="afii10062,afii10037"
	g2="afii10078"
	k="35" />
    <hkern g1="afii10062,afii10037"
	g2="afii10103"
	k="8" />
    <hkern g1="afii10062,afii10037"
	g2="parenright,bracketright,braceright"
	k="-24" />
    <hkern g1="afii10062,afii10037"
	g2="colon,semicolon"
	k="37" />
    <hkern g1="afii10062,afii10037"
	g2="comma,period,ellipsis"
	k="106" />
    <hkern g1="afii10062,afii10037"
	g2="hyphen,endash,emdash"
	k="39" />
    <hkern g1="afii10062,afii10037"
	g2="guillemotright,guilsinglright"
	k="19" />
    <hkern g1="afii10062,afii10037"
	g2="guillemotleft,guilsinglleft"
	k="27" />
    <hkern g1="afii10019,afii10025"
	g2="afii10041"
	k="21" />
    <hkern g1="afii10019,afii10025"
	g2="afii10021"
	k="4" />
    <hkern g1="afii10019,afii10025"
	g2="afii10051"
	k="10" />
    <hkern g1="afii10019,afii10025"
	g2="afii10038"
	k="10" />
    <hkern g1="afii10019,afii10025"
	g2="afii10060,afii10036,afii10044"
	k="6" />
    <hkern g1="afii10019,afii10025"
	g2="afii10057"
	k="5" />
    <hkern g1="afii10019,afii10025"
	g2="afii10053,afii10032,afii10035"
	k="8" />
    <hkern g1="afii10019,afii10025"
	g2="afii10062,afii10037"
	k="14" />
    <hkern g1="afii10019,afii10025"
	g2="afii10024"
	k="5" />
    <hkern g1="afii10019,afii10025"
	g2="afii10039"
	k="9" />
    <hkern g1="afii10019,afii10025"
	g2="afii10049"
	k="-6" />
    <hkern g1="afii10019,afii10025"
	g2="afii10089"
	k="20" />
    <hkern g1="afii10019,afii10025"
	g2="afii10069"
	k="4" />
    <hkern g1="afii10019,afii10025"
	g2="afii10077,afii10106"
	k="-4" />
    <hkern g1="afii10019,afii10025"
	g2="afii10084,afii10092"
	k="6" />
    <hkern g1="afii10019,afii10025"
	g2="afii10105"
	k="5" />
    <hkern g1="afii10019,afii10025"
	g2="afii10085,afii10110"
	k="4" />
    <hkern g1="afii10019,afii10025"
	g2="afii10087"
	k="10" />
    <hkern g1="afii10065"
	g2="afii10089"
	k="14" />
    <hkern g1="afii10065"
	g2="afii10099"
	k="10" />
    <hkern g1="afii10065"
	g2="afii10077,afii10106"
	k="-6" />
    <hkern g1="afii10065"
	g2="afii10084,afii10092"
	k="7" />
    <hkern g1="afii10065"
	g2="afii10105"
	k="7" />
    <hkern g1="afii10065"
	g2="afii10108"
	k="8" />
    <hkern g1="afii10065"
	g2="afii10085,afii10110"
	k="11" />
    <hkern g1="afii10065"
	g2="afii10097"
	k="-7" />
    <hkern g1="afii10099"
	g2="afii10089"
	k="9" />
    <hkern g1="afii10099"
	g2="afii10069"
	k="-4" />
    <hkern g1="afii10099"
	g2="afii10099"
	k="8" />
    <hkern g1="afii10099"
	g2="afii10077,afii10106"
	k="-8" />
    <hkern g1="afii10099"
	g2="afii10084,afii10092"
	k="12" />
    <hkern g1="afii10099"
	g2="afii10085,afii10110"
	k="8" />
    <hkern g1="afii10099"
	g2="afii10097"
	k="-5" />
    <hkern g1="afii10102"
	g2="afii10089"
	k="14" />
    <hkern g1="afii10102"
	g2="afii10069"
	k="-4" />
    <hkern g1="afii10102"
	g2="afii10099"
	k="11" />
    <hkern g1="afii10102"
	g2="afii10105"
	k="4" />
    <hkern g1="afii10102"
	g2="afii10108"
	k="8" />
    <hkern g1="afii10102"
	g2="afii10097"
	k="-5" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10089"
	k="11" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10099"
	k="9" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10077,afii10106"
	k="-4" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10105"
	k="7" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10108"
	k="5" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10085,afii10110"
	k="4" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10097"
	k="-4" />
    <hkern g1="afii10086"
	g2="afii10089"
	k="6" />
    <hkern g1="afii10086"
	g2="afii10069"
	k="4" />
    <hkern g1="afii10086"
	g2="afii10099"
	k="6" />
    <hkern g1="afii10086"
	g2="afii10073,afii10095,afii10846"
	k="4" />
    <hkern g1="afii10086"
	g2="afii10077,afii10106"
	k="-5" />
    <hkern g1="afii10086"
	g2="afii10084,afii10092"
	k="5" />
    <hkern g1="afii10086"
	g2="afii10105"
	k="9" />
    <hkern g1="afii10086"
	g2="afii10108"
	k="4" />
    <hkern g1="afii10086"
	g2="afii10085,afii10110"
	k="17" />
    <hkern g1="afii10086"
	g2="afii10072"
	k="8" />
    <hkern g1="afii10086"
	g2="afii10087"
	k="18" />
    <hkern g1="afii10078"
	g2="afii10089"
	k="11" />
    <hkern g1="afii10078"
	g2="afii10069"
	k="-11" />
    <hkern g1="afii10078"
	g2="afii10099"
	k="5" />
    <hkern g1="afii10078"
	g2="afii10073,afii10095,afii10846"
	k="-5" />
    <hkern g1="afii10078"
	g2="afii10077,afii10106"
	k="-13" />
    <hkern g1="afii10078"
	g2="afii10084,afii10092"
	k="4" />
    <hkern g1="afii10078"
	g2="afii10085,afii10110"
	k="5" />
    <hkern g1="afii10078"
	g2="afii10097"
	k="-5" />
    <hkern g1="afii10083,afii10101"
	g2="afii10066"
	k="-6" />
    <hkern g1="afii10083,afii10101"
	g2="afii10089"
	k="6" />
    <hkern g1="afii10083,afii10101"
	g2="afii10069"
	k="-19" />
    <hkern g1="afii10083,afii10101"
	g2="afii10073,afii10095,afii10846"
	k="-13" />
    <hkern g1="afii10083,afii10101"
	g2="afii10077,afii10106"
	k="-21" />
    <hkern g1="afii10083,afii10101"
	g2="afii10084,afii10092"
	k="-16" />
    <hkern g1="afii10083,afii10101"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="6" />
    <hkern g1="afii10083,afii10101"
	g2="afii10085,afii10110"
	k="-14" />
    <hkern g1="afii10083,afii10101"
	g2="afii10097"
	k="-9" />
    <hkern g1="afii10083,afii10101"
	g2="afii10072"
	k="-13" />
    <hkern g1="afii10083,afii10101"
	g2="afii10087"
	k="-9" />
    <hkern g1="afii10083,afii10101"
	g2="afii10086"
	k="7" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10065"
	k="14" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10066"
	k="-4" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10089"
	k="9" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10069"
	k="37" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10102"
	k="6" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10073,afii10095,afii10846"
	k="-5" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10077,afii10106"
	k="14" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10084,afii10092"
	k="-20" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10105"
	k="10" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="20" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10085,afii10110"
	k="-14" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10097"
	k="6" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10072"
	k="-12" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10087"
	k="-6" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="7" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10086"
	k="20" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10078"
	k="6" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10103"
	k="7" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="parenright,bracketright,braceright"
	k="23" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="comma,period,ellipsis"
	k="76" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="hyphen,endash,emdash"
	k="34" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="guillemotleft,guilsinglleft"
	k="14" />
    <hkern g1="afii10087"
	g2="afii10089"
	k="8" />
    <hkern g1="afii10087"
	g2="afii10069"
	k="-18" />
    <hkern g1="afii10087"
	g2="afii10073,afii10095,afii10846"
	k="-15" />
    <hkern g1="afii10087"
	g2="afii10077,afii10106"
	k="-24" />
    <hkern g1="afii10087"
	g2="afii10084,afii10092"
	k="-12" />
    <hkern g1="afii10087"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="14" />
    <hkern g1="afii10087"
	g2="afii10085,afii10110"
	k="-8" />
    <hkern g1="afii10087"
	g2="afii10097"
	k="-17" />
    <hkern g1="afii10087"
	g2="afii10072"
	k="-17" />
    <hkern g1="afii10087"
	g2="afii10087"
	k="-4" />
    <hkern g1="afii10087"
	g2="afii10086"
	k="15" />
    <hkern g1="afii10087"
	g2="guillemotleft,guilsinglleft"
	k="16" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10066"
	k="5" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10089"
	k="33" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10099"
	k="14" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10102"
	k="5" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10077,afii10106"
	k="-4" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10084,afii10092"
	k="52" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10105"
	k="10" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10108"
	k="9" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10085,afii10110"
	k="36" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10087"
	k="6" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="quoteright,quotedblright"
	k="57" />
    <hkern g1="afii10103"
	g2="afii10089"
	k="9" />
    <hkern g1="afii10103"
	g2="afii10077,afii10106"
	k="-4" />
    <hkern g1="afii10103"
	g2="afii10105"
	k="4" />
    <hkern g1="afii10103"
	g2="afii10097"
	k="-5" />
    <hkern g1="afii10070,afii10071"
	g2="afii10066"
	k="-5" />
    <hkern g1="afii10070,afii10071"
	g2="afii10089"
	k="6" />
    <hkern g1="afii10070,afii10071"
	g2="afii10099"
	k="4" />
    <hkern g1="afii10070,afii10071"
	g2="afii10077,afii10106"
	k="-5" />
    <hkern g1="afii10070,afii10071"
	g2="afii10084,afii10092"
	k="4" />
    <hkern g1="afii10070,afii10071"
	g2="afii10105"
	k="4" />
    <hkern g1="afii10070,afii10071"
	g2="afii10085,afii10110"
	k="4" />
    <hkern g1="afii10070,afii10071"
	g2="afii10097"
	k="-6" />
    <hkern g1="afii10070,afii10071"
	g2="afii10087"
	k="4" />
    <hkern g1="afii10070,afii10071"
	g2="afii10086"
	k="-5" />
    <hkern g1="afii10105"
	g2="afii10089"
	k="8" />
    <hkern g1="afii10105"
	g2="afii10069"
	k="-6" />
    <hkern g1="afii10105"
	g2="afii10097"
	k="-5" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10065"
	k="-10" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10089"
	k="15" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10069"
	k="-25" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10073,afii10095,afii10846"
	k="-11" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10077,afii10106"
	k="-27" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10084,afii10092"
	k="-17" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="6" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10085,afii10110"
	k="-7" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10097"
	k="-18" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10072"
	k="-20" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10087"
	k="-13" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10086"
	k="8" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="guillemotleft,guilsinglleft"
	k="9" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10089"
	k="10" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10069"
	k="7" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10099"
	k="10" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10077,afii10106"
	k="-4" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10084,afii10092"
	k="8" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10105"
	k="8" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10108"
	k="6" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10085,afii10110"
	k="10" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10072"
	k="8" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10087"
	k="13" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10066"
	k="7" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10089"
	k="16" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10069"
	k="-19" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10099"
	k="13" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10077,afii10106"
	k="-9" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10084,afii10092"
	k="8" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10105"
	k="-27" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="4" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10108"
	k="8" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10097"
	k="-9" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10072"
	k="-8" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10087"
	k="-4" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10086"
	k="5" />
    <hkern g1="afii10108"
	g2="afii10089"
	k="18" />
    <hkern g1="afii10108"
	g2="afii10069"
	k="-4" />
    <hkern g1="afii10108"
	g2="afii10099"
	k="14" />
    <hkern g1="afii10108"
	g2="afii10077,afii10106"
	k="-6" />
    <hkern g1="afii10108"
	g2="afii10084,afii10092"
	k="15" />
    <hkern g1="afii10108"
	g2="afii10105"
	k="10" />
    <hkern g1="afii10108"
	g2="afii10085,afii10110"
	k="10" />
    <hkern g1="afii10108"
	g2="afii10097"
	k="-5" />
    <hkern g1="afii10085,afii10110"
	g2="afii10065"
	k="7" />
    <hkern g1="afii10085,afii10110"
	g2="afii10066"
	k="-8" />
    <hkern g1="afii10085,afii10110"
	g2="afii10069"
	k="20" />
    <hkern g1="afii10085,afii10110"
	g2="afii10102"
	k="5" />
    <hkern g1="afii10085,afii10110"
	g2="afii10073,afii10095,afii10846"
	k="-5" />
    <hkern g1="afii10085,afii10110"
	g2="afii10077,afii10106"
	k="4" />
    <hkern g1="afii10085,afii10110"
	g2="afii10084,afii10092"
	k="-22" />
    <hkern g1="afii10085,afii10110"
	g2="afii10105"
	k="5" />
    <hkern g1="afii10085,afii10110"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="9" />
    <hkern g1="afii10085,afii10110"
	g2="afii10085,afii10110"
	k="-6" />
    <hkern g1="afii10085,afii10110"
	g2="afii10072"
	k="-10" />
    <hkern g1="afii10085,afii10110"
	g2="afii10087"
	k="-4" />
    <hkern g1="afii10085,afii10110"
	g2="afii10086"
	k="9" />
    <hkern g1="afii10085,afii10110"
	g2="afii10078"
	k="5" />
    <hkern g1="afii10085,afii10110"
	g2="parenright,bracketright,braceright"
	k="19" />
    <hkern g1="afii10085,afii10110"
	g2="comma,period,ellipsis"
	k="50" />
    <hkern g1="afii10085,afii10110"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="afii10085,afii10110"
	g2="guillemotleft,guilsinglleft"
	k="8" />
    <hkern g1="afii10067,afii10073"
	g2="afii10066"
	k="4" />
    <hkern g1="afii10067,afii10073"
	g2="afii10089"
	k="16" />
    <hkern g1="afii10067,afii10073"
	g2="afii10099"
	k="11" />
    <hkern g1="afii10067,afii10073"
	g2="afii10077,afii10106"
	k="-7" />
    <hkern g1="afii10067,afii10073"
	g2="afii10084,afii10092"
	k="10" />
    <hkern g1="afii10067,afii10073"
	g2="afii10105"
	k="9" />
    <hkern g1="afii10067,afii10073"
	g2="afii10108"
	k="9" />
    <hkern g1="afii10067,afii10073"
	g2="afii10085,afii10110"
	k="11" />
    <hkern g1="afii10067,afii10073"
	g2="afii10097"
	k="-5" />
    <hkern g1="afii10067,afii10073"
	g2="afii10072"
	k="-4" />
    <hkern g1="afii10104"
	g2="afii10089"
	k="9" />
    <hkern g1="afii10104"
	g2="afii10099"
	k="-59" />
    <hkern g1="afii10104"
	g2="afii10077,afii10106"
	k="-4" />
    <hkern g1="afii10104"
	g2="afii10084,afii10092"
	k="-9" />
    <hkern g1="afii10104"
	g2="afii10105"
	k="-7" />
    <hkern g1="afii10104"
	g2="afii10108"
	k="-52" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10041"
	k="26" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10021"
	k="-21" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10051"
	k="-34" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10058,afii10029"
	k="13" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10060,afii10036,afii10044"
	k="-35" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10057"
	k="15" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10062,afii10037"
	k="-11" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10049"
	k="11" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10089"
	k="49" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10069"
	k="-18" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10099"
	k="-5" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10084,afii10092"
	k="23" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10105"
	k="-27" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10108"
	k="-6" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10085,afii10110"
	k="16" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10097"
	k="13" />
    <hkern g1="colon,semicolon"
	g2="afii10021"
	k="-6" />
    <hkern g1="colon,semicolon"
	g2="afii10051"
	k="6" />
    <hkern g1="colon,semicolon"
	g2="afii10069"
	k="-6" />
    <hkern g1="colon,semicolon"
	g2="afii10084,afii10092"
	k="-12" />
    <hkern g1="colon,semicolon"
	g2="afii10105"
	k="5" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10041"
	k="13" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10021"
	k="15" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10051"
	k="41" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10058,afii10029"
	k="9" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10060,afii10036,afii10044"
	k="33" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10057"
	k="26" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10062,afii10037"
	k="33" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10024"
	k="16" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10039"
	k="29" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10049"
	k="8" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10089"
	k="8" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10069"
	k="17" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10099"
	k="9" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10077,afii10106"
	k="11" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10084,afii10092"
	k="12" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10105"
	k="5" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10085,afii10110"
	k="11" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10097"
	k="6" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10072"
	k="15" />
    <hkern g1="hyphen,endash,emdash"
	g2="afii10087"
	k="6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10051"
	k="19" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10060,afii10036,afii10044"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10039"
	k="5" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10084,afii10092"
	k="-5" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10041"
	k="15" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10021"
	k="5" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10051"
	k="33" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10058,afii10029"
	k="4" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10060,afii10036,afii10044"
	k="16" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10057"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10062,afii10037"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10024"
	k="8" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10049"
	k="7" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10089"
	k="5" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10069"
	k="4" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10099"
	k="9" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10077,afii10106"
	k="4" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10084,afii10092"
	k="7" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10105"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10108"
	k="9" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10085,afii10110"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10097"
	k="5" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10072"
	k="6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10087"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10051"
	k="-66" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10038"
	k="11" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10058,afii10029"
	k="18" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10060,afii10036,afii10044"
	k="-67" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10057"
	k="80" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10053,afii10032,afii10035"
	k="11" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10062,afii10037"
	k="-10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10017"
	k="77" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10049"
	k="11" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10069"
	k="57" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10099"
	k="-26" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10077,afii10106"
	k="38" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10084,afii10092"
	k="-20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10105"
	k="9" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10108"
	k="-28" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10085,afii10110"
	k="7" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10097"
	k="33" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10021"
	k="12" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10051"
	k="-6" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10058,afii10029"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10060,afii10036,afii10044"
	k="-10" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10057"
	k="17" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10017"
	k="18" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10089"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10069"
	k="16" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10099"
	k="-5" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10105"
	k="6" />
    <hkern g1="quoteright,quotedblright"
	g2="afii10108"
	k="-5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10041"
	k="32" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10051"
	k="10" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10060,afii10036,afii10044"
	k="12" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10062,afii10037"
	k="6" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10024"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10017"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10054"
	k="8" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10039"
	k="8" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10049"
	k="6" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10099"
	k="6" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10084,afii10092"
	k="-7" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10105"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10108"
	k="5" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10085,afii10110"
	k="6" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10097"
	k="6" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10072"
	k="6" />
    <hkern g1="quotesinglbase,quotedblbase"
	g2="afii10087"
	k="7" />
  </font>
</defs></svg>
