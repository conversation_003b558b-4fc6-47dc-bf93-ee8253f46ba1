/* CSS RESET ELEMENTS --------------------------------------------------------------------------------------*/
body,img,h1,h2,h3,h4,h5,h6 { border:none; list-style:none; margin:0; padding:0; }
body{ 
	background: #ffffff url(../images/background.png) ;
	color:#333;
  font-family: Arial,helvetica,sans-serif; 
  font-size:8.5pt; 
  line-height:1.33em;
  margin:0 0 0 0; 
  padding:0 0 0 0; 
}

/*----------------------------------------------------------------------------------------------------------*/
a{color:#af580e; text-decoration:none;}
a:hover{text-decoration:underline;}
/*----------------------------------------------------------------------------------------------------------*/
.l{text-align:left;}
.r{text-align:right;}
.c{text-align:center;}
.b{ font-weight:bold;}
/*----------------------------------------------------------------------------------------------------------
 * logo = w:460 h:170
 
 border-right:1px solid #FFF;
 background-color:red;
 
 * */
#main{ margin:auto auto; width:1012px; padding-top:5px; }
#header{ height:167px; width:100%; }
  #header .left{ background:url(../images/MAJLogo163.png) no-repeat; float:left; width:490px; height:163px;}
  #header .right{ float:right; width:522px; height:163px; }
  #header .right #authArea{ color:#FFF; text-align:right; width:100%; height:123px; }
  #header .right #authArea #authContent{ color:#FFF; float:right; margin:5px 10px 0 0; }
  #header .right #authArea #authContent a{ color:#FFF; font-weight:bold; font-size:10pt; text-decoration:none; }
  
  #header .right #iconArea{ color:#FFF; width:100%; height:40px; }
    .iconBox{ background:url(../images/icon_bg.png) no-repeat; height:40px; position:relative; width:552px; }
  #header .right #iconArea #iconContent{ width:210px; height:27px; float:right; margin:7px 15px 0 0; }
	#header .right #iconArea #iconContent ul{ padding:0; margin:0px; }
	#header .right #iconArea #iconContent li{ display:block; float:left; padding:0 3px 0 0; margin:0 3px 0 0; }
  
/*----------------------------------------------------------------------------------------------------------*/
#logo{ cursor:pointer; height:163px; width:460px; }
/*----------------------------------------------------------------------------------------------------------*/
#mainNav{ width:1012px; padding:0; margin:0; }
	.mainNavBG{ background:url(../images/mainNavBG.png) no-repeat; height:77px; position:relative; width:1012px; }
  #mainNav ul{ height:25px; list-style:none; padding:35px 0 0 20px; margin:0; position:relative; text-align:center; }
	#mainNav li{ display:block; float:left; }
	#mainNav a{ color:#ffffff; font-size:14pt; font-weight:bold; letter-spacing:1px; position:relative; text-decoration:none; }
  #mainNav a:hover{ text-decoration:none; }
  .navLink{color:#ffffff;}
  .borderRight{ border-right:2px solid #FFF; padding:0 10px 0 0; margin:0 10px 0 0; }

/*----------------------------------------------------------------------------------------------------------*/
#container{ float:left; height:auto; position:relative; overflow:hidden; width:100%; }
#mastHead{ width:100%; height:324px; background-color:#fff; }
  #mastHeadImage{ float:left; width:432px; height:324px; background-color:#fff; }
  #mastHeadImage p{ border:none; list-style:none; margin:0; padding:0; }
  #mastHeadContent{ float:right; width:580px; height:324px; background-color:#fff; }
  #mastHeadContentArea{ padding:20px;}
/*----------------------------------------------------------------------------------------------------------*/
#cols{ overflow:hidden; }/*  background:red; height:auto; position:relative;  */
  #cols .column{ width:25%; float:left;  position:relative;}
	  #cols #columnContent1{ background-color:#bebabb; color:#000; height:265px; }
	  #cols #columnContent1 p { margin:0; }
      #cols #columnContent1 .columnTitle{ background-color:#1f1b1d; color:#fff; font-size: 18pt; font-weight:bold; padding:20px; }
      #cols #columnContent1 .BodyText{  }
      #cols #columnContent1 p { padding:15px 15px 5px 15px; }
      #cols #columnContent1 .colFooter{ position:absolute; bottom:0; }
      
      
    #cols #columnContent2{ background-color:#cfb9bb; color:#000; height:265px; }
	  #cols #columnContent2 p { margin:0; }
      #cols #columnContent2 .columnTitle{ background-color:#65171e; color:#fff; font-size: 18pt; font-weight:bold; padding:20px; }
      #cols #columnContent2 p { padding:15px 15px 5px 15px; }
      #cols #columnContent2 .colFooter{ position:absolute; bottom:0; }
      
    #cols #columnContent3{ background-color:#e1baba; color:#000; height:265px; }
	  #cols #columnContent3 p { margin:0; }
     #cols  #columnContent3 .columnTitle{ background-color:#a42224; color:#fff; font-size: 18pt; font-weight:bold; padding:20px; }
      #cols #columnContent3 p { padding:15px 15px 5px 15px; }
      #cols #columnContent3 .colFooter{ position:absolute; bottom:0; }
      
    #cols #columnContent4{ background-color:#f8d3ba; color:#000; height:265px; }
	  #cols #columnContent4 p { margin:0; }
      #cols #columnContent4 .columnTitle{ background-color:#f07e35; color:#fff; font-size: 18pt; font-weight:bold; padding:20px; }
      #cols #columnContent4 p { padding:15px 15px 5px 15px; }
      #cols #columnContent4 .colFooter{ position:absolute; bottom:0; }
      
#containerBottom{ background:url(../images/container_bottom.png) no-repeat; height:15px; width:100%; }
/*----------------------------------------------------------------------------------------------------------*/
#content{background:#fff; height:auto; position:relative; overflow:hidden;  padding:20px;  }
#ncLeft{ float:left; width:58%; height:auto; position:relative; overflow:hidden; padding:5px; }
#ncRight{ float:right; width:40%; height:auto; position:relative; overflow:hidden; text-align:center; }
#contentFull{ background:#fff; height:auto; position:relative; overflow:hidden;  padding:20px; }
/*FOOTER: --------------------------------------------------------------------------------------------------*/
#footer{  height:auto; overflow:hidden; width:100%; }
  #footer a{ text-decoration:underline; color:#ffffff; font-size:8pt;  }
  #footer a.hover{   }
#footerNav { height:auto; overflow:hidden; width:100%; margin-bottom:10px;}
  #footerNav ul{ list-style:none; }
	#footerNav li{display:block; float:left; padding:0 10px 0 0; margin:0 10px 0 0; position:relative; }
	#footerNav a{ color:#ffffff; font-size:9pt; font-weight:bold; letter-spacing:1px; position:relative; text-decoration:none; }
  #footerNav a:hover{ color:#db5a08; text-decoration:underline; }
  #footerNav .navLink{color:#ffffff;}
  #footerNav .borderRight{ border-right:1px solid #FFF; }
#footerContent { color:#5b5b5b;  height:auto; overflow:hidden; width:100%; margin:0 0 10px 40px;}
/*----------------------------------------------------------------------------------------------------------*/
#login                   { float:left; height:82px; left:-304px; position:relative; top:140px; width:312px; }
#loginform               { color:#4a4d38; font-family:arial; font-size:14px; }
#loginform a             { color:#4a4d38; font-family:arial; font-size:12px; }
#loginform a.logout      { color:#b35b1a; }
#loginform a.logout:hover{ text-decoration:underline; }
.loginBox{ border:1px solid #4b4b4b; }
.loginPageTitle{ background-color:#362a2d; color:#fff; font-size:14pt;}
.loginPage{ background-color:#f4f4f4; font-size:10pt;}
input.loginTextBox      { background:#fff; border:1px solid #4b4b4b; color:#4b4b4b; line-height:18px; font-size:9pt; height:18px; padding-left:2px; width:130px; }

/*----------------------------------------------------------------------------------------------------------*/
.clear{ clear:both; }
