<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct"><!--- DISPLAY THE FOLLOWING IN DIRECT MODE --->
	<cfinclude template="directMode.cfm" />
<cfelse>
	<cfoutput>
		<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
		<html xmlns="http://www.w3.org/1999/xhtml">
			<head>
				<cfinclude template="head.cfm">
			</head>
			<body>
				<div id="main">
					<cfinclude template="header.cfm">
					<cfinclude template="mainNav.cfm">
					<div class="clear" />
					<div id="container">
						
							<div id="contentFull">#application.objCMS.renderZone(zone='Main',event=event, mode='div')#</div>
						
					</div>
					<!--- END: container *********************************************************************************************** --->
					<!--- <div id="clear" /> --->
					<div class="clear" />
					<div id="containerBottom"></div>
					<cfinclude template="footer.cfm">
				</div>
				<cfinclude template="toolBar.cfm" />
				<!--- END: main ****************************************************************************************************** --->
			</body>
		</html>
	</cfoutput>
</cfif>