<!--- include headerPanel to include all start scripts --->
<cfinclude template="headerPanel.cfm">

<body leftmargin="0" topmargin="0">
	<cfinclude template="jssalesscript.cfm">
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct">
		<div class="bodyText" style="margin:6px;">
		<cfoutput>#application.objCMS.renderZone(zone='Main',event=event)#</cfoutput>
		</div>
	<cfelse>
	
		<cfinclude template="mastHead.cfm">
		
		
		
		<div id="blue_content"> 
			<!-- or 'blue_content_js' if Javascript Menu is called for -->
			<!-- JavaScript Menu -->			
			<div id="blue_main_area" class="wrap">
				<!-- ZONE C -->
				<cfoutput>#application.objCMS.renderZone(zone='C',event=event)#</cfoutput>
			</div>
		</div>

		<div id="white_content">
			<div class="wrap white_content_area">
				
					<!-- Main -->
					<cfoutput>#application.objCMS.renderZone(zone='Main',event=event)#</cfoutput>
				
			</div>
		</div>
		
		
		
		<div id="grey_content">
			<div id="grey_boxes" class="wrap">
				<div id="zoneE">				
					<div id="gb1">
						<div class="grey_box">
							<img src="/images/home_memberCentral_35.jpg">
							<p class="grey_box_header">Request Demo</p>
							<p class="grey_box_content">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod.</p>
						</div>
					</div>					
					<div id="gb2">
						<div class="grey_box">
							<img src="/images/home_memberCentral_37.jpg">
							<p class="grey_box_header">Questions or Comments</p>
							<p class="grey_box_content">Need help? Have a suggestion? We want to hear from you. Call 737-210-1642 or email <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
						</div>
					</div>					
					<div id="gb3">
						<div class="grey_box">
							<img id="mlist_icon" src="/images/home_memberCentral_39.jpg">
							<p class="grey_box_header">Receive email updates</p>
							<!--- CustomPage??? where does this form go --->
							<cfform action="" method="post">
								<input id="email" class="inputtext" type="text" name="email" value="Enter your email...">
								<input id="submit" class="subscribe_button" type="submit" value="">
							</cfform>							
							<!--- will probably need some custom js validation here --->
						</div>
					</div>					
				</div>			
				<div id="zoneF">
					<p><a href="">Policy</a> | <a href="">Terms of Use</a> | <a href="">Contact Us</a></p>
					<p>� 2009 MemberCentral. All rights reserved.</p>
				</div>				
			</div>			
		</div>
		
	</cfif>
	
	<!--- toolbar --->
	<cfif application.objCMS.getZoneItemCount(zone='ToolBar',event=event)>
		<center>
		<div class="bodyText" style="margin:6px;"><cfoutput>#application.objCMS.renderZone(zone='ToolBar',event=event)#</cfoutput></div>
		</center>
	</cfif>
	<cfinclude template="adRoll.cfm" />
</body>
</html>