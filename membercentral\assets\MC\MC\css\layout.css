@charset "utf-8";
/* CSS Document */

@font-face {
	font-family: 'Myriad Pro';
	src: url('fonts/MyriadPro-Regular/MyriadPro-Regular.eot');
	src: url('fonts/MyriadPro-Regular/MyriadPro-Regular.eot?#iefix') format('embedded-opentype'),
		url('fonts/MyriadPro-Regular/MyriadPro-Regular.woff') format('woff'),
		url('fonts/MyriadPro-Regular/MyriadPro-Regular.ttf') format('truetype'),
		url('fonts/MyriadPro-Regular/MyriadPro-Regular.svg#MyriadPro-Regular') format('svg');
	font-weight: normal;
	font-style: normal;
}



@font-face {
	font-family: 'Raleway-Medium';
	src: url('fonts/Raleway-Medium/Raleway-Medium.eot');
	src: url('fonts/Raleway-Medium/Raleway-Medium.eot?#iefix') format('embedded-opentype'),
		url('fonts/Raleway-Medium/Raleway-Medium.woff') format('woff'),
		url('fonts/Raleway-Medium/Raleway-Medium.ttf') format('truetype'),
		url('fonts/Raleway-Medium/Raleway-Medium.svg#Raleway-Medium') format('svg');
	font-weight: 500;
	font-style: normal;
}

@font-face {
	font-family: 'Raleway-Bold';
	src: url('fonts/Raleway-Bold/Raleway-Bold.eot');
	src: url('fonts/Raleway-Bold/Raleway-Bold.eot?#iefix') format('embedded-opentype'),
		url('fonts/Raleway-Bold/Raleway-Bold.woff') format('woff'),
		url('fonts/Raleway-Bold/Raleway-Bold.ttf') format('truetype'),
		url('fonts/Raleway-Bold/Raleway-Bold.svg#Raleway-Bold') format('svg');
	font-weight: bold;
	font-style: normal;
}


@font-face {
	font-family: 'Raleway-LightItalic';
	src: url('fonts/Raleway-LightItalic/Raleway-LightItalic.eot');
	src: url('fonts/Raleway-LightItalic/Raleway-LightItalic.eot?#iefix') format('embedded-opentype'),
		url('fonts/Raleway-LightItalic/Raleway-LightItalic.woff') format('woff'),
		url('fonts/Raleway-LightItalic/Raleway-LightItalic.ttf') format('truetype'),
		url('fonts/Raleway-LightItalic/Raleway-LightItalic.svg#Raleway-LightItalic') format('svg');
	font-weight: 300;
	font-style: italic;
}





body {font-size: 14px;font-family: 'Raleway-Medium';}
h1{font-family: 'Raleway-Medium';font-size: 24px;color:#66C8D8;font-weight:400}
h2{font-family: 'Raleway-Medium';font-size: 31.5px;color:#333333;font-weight:700}
h3{font-family: 'Raleway-Medium';font-size: 24.5px;color:#333333;font-weight:700}
h4{font-family: 'Raleway-Medium';font-size: 17.5px;color:#333333;font-weight:700}
h5{font-family: 'Raleway-Medium';font-size: 14px;color:#333333;font-weight:700}
h6{font-family: 'Raleway-Medium';font-size: 16px;color:#333333;font-weight:700}
address{font-size: 14px;font-family: 'Raleway-Medium';color:#333333;}
a h1, h1 a {color:#66C8D8 }
a h1:hover, h1 a:hover { color:#1389CA}
header { padding: 19px 0; background: #fff; position: fixed; top: 0; left: 0; right: 0; z-index: 556}
.banner { padding-top: 95px; max-height: 650px; overflow: hidden}
.banner img, .freedemo-img img { width: 100%;}
.fixHeader .banner { padding-top: 77px;}
.banner { position: relative}
.banner .container {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 35px;
    z-index: 55;
}
.fixHeader header { padding: 8px 0; -webkit-box-shadow: 0 0 5px #bbb; -moz-box-shadow: 0 0 5px #bbb; -ms-box-shadow: 0 0 5px #bbb; box-shadow: 0 0 5px #bbb;}

.nav-menu li {
    display: inline-block;
    list-style: none;
    margin: 0 13px;
    font-size: 20px;
}
.nav-menu li a { color: #373737; display: block; padding: 11px 0;}
.nav-menu ul { display: inline-block; margin-right: 30px;}
.free-demo { display: inline-block; margin-top: 10px;}
.free-demo a {
    background: #33b5cb;
    font-size: 18px;
    display: block;
    text-decoration: none;
    color: #fff;
    width: 135px;
    height: 33px;
    text-align: center;
    line-height: 33px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    	border-radius: 5px;
}
.free-demo a:hover, .free-demo a:focus { background: #0573ac}
.banner-text a:hover, .banner-text a:focus,
.form-inner input[type="submit"].btn:hover,
.form-inner input[type="submit"].btn:focus { background: #e1540f;}

a, a:hover, a:focus { text-decoration: none;}
a:hover{
    color: #66C8D8;
}
.nav-menu li:hover>a, .nav-menu li a:focus, .nav-menu li.active a {color: #33b5cb}
.nav-menu { text-align: right;}
.nav-menu li { position: relative}
.nav-menu li:hover .caret { border-top-color: #33b5cb}

/*-----------*/
.submenu {
    background: #fff;
    text-align: left; 
}
.nav-menu ul .submenu-inr {
    margin: 0;
    padding: 10px 0;
    /*min-width: 150px;*/
    
}
.nav-menu ul .submenu-inr li { display: block; margin: 0;}
.nav-menu ul .submenu-inr li a {
    white-space: nowrap;
    padding: 5px 15px;
    display: block;
    font-size: 17px;
}

.nav-menu ul li.parent .caret {
    position: absolute;
    top: 20px;
    right: 0;
}
/*----------*/
.banner-inner { position: relative;}
.banner-text { color: #fff; text-align: center;}
.banner-text h1 { font-size: 48px; font-weight: normal; margin-bottom: 20px;color: #fff;}
.banner-text p {
    font-size: 30px;
    line-height: 36px;
    margin-bottom: 0;
    font-family: 'Raleway-Medium';
	min-height: 70px;
}
.banner-text a {
    background: #fa5e11;
    color: #fff;
    display: inline-block;
    height: 63px;
    width: 265px;
    font-size: 28px;
    line-height: 63px;
    margin-top: 35px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    	border-radius: 10px;
}

.top-three-part { margin: 40px 0 10px;}
.top-three-part h1 {
    font-size: 32px;
    font-weight: normal;
    text-align: center;
    margin: 0 auto 20px;
    max-width: 950px;
    color: #545454;
}
.top-three-part .single-block {
    text-align: center;
}
.top-three-part .single-block h2 {
    font-family: 'Raleway-Bold';
    font-size: 22px;
    color: #33b6cb;
    margin-bottom: 0;
}
.top-three-part .single-block p {
    font-size: 19px;
    font-family: 'Raleway-Medium';
    line-height: 26px;
    color: #545454;
}
.meet-staff .container {
    background: #e8e8e8;
}
.head-matter ul { margin: 0; padding-right: 30px;}
.head-matter ul li {
    font-size: 22px;
    line-height: 27px;
    margin-bottom: 20px;
    list-style: none;
    padding-left: 45px;
    position: relative;
}
.head-matter ul li:after {
    content: "";
    position: absolute;
    top: -3px;
    left: -9px;
    width: 42px;
    height: 42px;
    background: url(../images/list-img.jpg) center top no-repeat;
}
.simply-billing .head-matter ul li:after { background: url(../images/list-img-wht.jpg) center top no-repeat;}

.head-matter h1 { font-size: 32px; margin-bottom: 30px; font-weight: normal; margin-top: 50px;}
.meet-staff img { width: 620px; max-width: inherit; margin-top:-1px;}
.simply-billing .right-img { height: 476px; position: relative; text-align: right; float: right;}
.engage-member, .meet-staff { background: #e8e8e8;}
.simply-billing {
    position: relative;
}
.simply-billing .right-img img {
    position: absolute;
    top: 50%;
    right: 70px;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    	transform: translateY(-50%);    
}

.engage-member .container { position: relative;}
.engage-member .head-matter { position: absolute; right: 0;}
.client-inner ul { margin: 6px 0}
.client-inner li {
    list-style: none;
    width: 185px;
    height: 185px;
    float: left;
    position: relative;
    display: inline-block;
    text-align: center;
    vertical-align: top;
    border-left: 1px solid #f6f6f6;
    border-top: 1px solid #f6f6f6;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    	box-sizing: border-box;
}


.client-inner li img {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%,-50%,0);
    -moz-transform: translate3d(-50%,-50%,0);
    -ms-transform: translate3d(-50%,-50%,0);
    -o-transform: translate3d(-50%,-50%,0);
    	transform: translate3d(-50%,-50%,0);
}


.client-inner li:after { content: ""; position: absolute; top: -18px; left: -18px; z-index: 55; width: 36px; height: 36px; background: #fff url(../images/client-plus.jpg) center center no-repeat;}

.client-inner li:nth-child(1) { border: 0}
.client-inner li:nth-child(2),
.client-inner li:nth-child(3) { border-top: 0}
.client-inner li:nth-child(4),
.client-inner li:nth-child(7) { border-left: 0}
.client-inner li:nth-child(1):after,
.client-inner li:nth-child(2):after,
.client-inner li:nth-child(3):after,
.client-inner li:nth-child(4):after,
.client-inner li:nth-child(7):after { display: none;}

footer {  background: #eff1f2;}
footer .container { background: #eff1f2; padding-top: 50px;}
footer .container .span1 { text-align: right; padding-top: 15px;}
footer .single-block {
    display: inline-block;
    vertical-align: top;
    width: 195px;
}
footer .single-block h4,
.member-central h4 {
    text-transform: uppercase;
    font-weight: normal;
    font-family: 'Raleway-Bold';
    font-size: 12px;
    color: #2c2c2b;
    letter-spacing: 1px;
}
footer .single-block ul, .member-central ul {
    margin: 0 0 20px;
}
footer .single-block li, .member-central li {
    list-style: none;
    font-size: 13px;
}

.member-central li { margin-bottom: 5px}
.member-central li,
.member-central li a { color: #000;}
footer .single-block li a {
    color: #5b5c5c;
    
}

.left-links ul { margin: 50px 0 20px; font-size: 13px}
.left-links ul li { display: inline-block; margin: 0 15px;}
.left-links ul li, .left-links ul li a {
    color: #828283;
}
.left-links ul li a { text-decoration: underline;}
.social-icns ul {
    text-align: right;
    margin: 40px 30px 0 0;
}
.social-icns li {
    display: inline-block;
    list-style: none;
	margin: 0 9px
}
.social-icns li a {
    width: 30px;
    height: 30px;
    background: url(../images/social-icns.jpg) no-repeat;
    display: block;
    border-radius: 50%;
}
.social-icns li a:hover, .social-icns li a:focus { opacity: .7}
footer .single-block li a:hover, footer .single-block li a:focus,
.left-links ul li a:hover, .left-links ul li a:focus { color: #000}
.social-icns li.fb a { background-position: -3px -4px;}
.social-icns li.twtr a { background-position: -52px -4px;}
.social-icns li.gplus a { background-position: -100px -4px;}
.social-icns li.lin a { background-position: -149px -4px;}

.span3.member-central h4, .span3.member-central ul { margin-left: 50px;}

.item-inner { text-align: center; font-family: 'Raleway-Medium'; margin: 30px 0;}
.item-inner img { margin: 0 auto 30px;}

.item-inner h1, .item-inner h2  { font-weight: normal;}
.item-inner h1 { font-size: 24px; margin-bottom: 0;}
.item-inner h2 { font-size: 22px; margin: -8px 0 10px;}
.item-inner p {
    font-size: 16px;
    line-height: 26px;
}

.owl-carousel img{
	width:auto!important;
}
.owl-carousel .owl-dots .owl-dot{
	display:inline-block;
}
.owl-carousel .owl-dots{
	text-align:center;
}


.owl-carousel .owl-dots .owl-dot span,.owl-theme .owl-dots .owl-dot span {
    width: 17px;
    height: 17px;
    margin: 5px 7px;
    border: 1px solid #b6b6b6;
    display: block;
    -webkit-backface-visibility: visible;
    -webkit-transition: opacity 200ms ease;
    -moz-transition: opacity 200ms ease;
    -ms-transition: opacity 200ms ease;
    -o-transition: opacity 200ms ease;
    transition: opacity 200ms ease;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    border-radius: 30px;
	float:left;
}
.owl-theme .owl-dots .owl-dot:hover span,
.owl-theme .owl-dots .owl-dot.active span { background: #404040; border-color: #777;}


a, a:hover, a:focus, input, input:hover, input:focus,
.nav-menu,
.openMenu .nav-menu,
.overlay-bg,
.openMenu .overlay-bg,
header,
.fixHeader header,
.nav-menu ul li .submenu,
.nav-menu ul li:hover .submenu,
.nav-menu ul li .submenu-inr,
.nav-menu ul li:hover .submenu-inr,
.banner,
.fixHeader .banner { 
    -webkit-transition: all 300ms;
    -moz-transition: all 300ms;
    -ms-transition: all 300ms;
    -o-transition: all 300ms;
    	transition: all 300ms;
}

.free-demo-form .form-horizontal .control-group {
    margin-bottom: 10px;
}
.free-demo-form .form-horizontal .control-label {
    float: none;
    width: auto;
    padding-top: 0;
    text-align: left;
    display: block;
	font-family: 'Raleway-Medium';
	font-size: 15px;
	padding-left: 10px;
}
.free-demo-form { position: relative}
.free-demo-form .container {
    position: absolute;
    top: 30px;
    bottom: 30px;
    left: 30px; right: 30px;
}
.freedemo-img { max-height: 533px; overflow: hidden}
.free-demo-form .container .row, .free-demo-form .container .span12 { height: 100%} 

.free-demo-form .form-horizontal .controls {
    margin-left: 0;
}

.form-inner {
    max-width: 425px;
    margin: 0 auto;
	height: 100%;
    background: rgba(37, 174, 198, 0.8);
    text-align: center;
    color: #fff;
}
.free-demo-form form {
    width: 185px;
    margin: 0 auto;
    padding-bottom: 15px
}

.form-inner h1 {
	font-weight: normal;
    margin: 0 0 15px;
    padding: 20px 0 5px;
    font-size: 27px;
    border-bottom: 2px solid;
    display: inline-block;    
}

.form-inner input {
    width: 170px;
    height: 27px;
    border: 0;
	font-family: 'Raleway-Medium';
    font-size: 15px;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    	box-shadow: none;
}

.form-inner input[type="submit"].btn { width: 155px; height: 40px; background: #fa5e11; margin-top: 20px }



.img-right .block-image { float: right; margin-left: 70px; padding-top: 12px;}
.img-left .block-image { float: left; margin-right: 100px; padding-top: 62px;}
.img-left .container { margin-bottom: 40px;}
.img-matter .container {
    border-bottom: 1px solid #efefef;
}

.matter-cntnr {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    	transform: translateY(-50%);
}

.img-right .matter-cntnr  {width: 490px; left: 0;}
.img-left .matter-cntnr  {width: 465px; right: 0;}


.single-block { font-family: 'Raleway-Medium'; margin-bottom: 35px;}
.single-block h1 {
    color: #66c8d8;
    font-size: 24px;
    font-weight: normal;
}

.single-block h6 {
    font-size: 16px;
    font-weight: normal;
    margin-bottom: 15px;
}


.ams-soft .container {
    color: #fff;text-align: center;
    padding: 56px 0 20px;
}

.ams-soft { background: #636363;}


.single-block-ams p { max-width: 250px; margin: 0 auto; padding: 5px 0 20px; text-align: left; font-family: 'Raleway-Medium'; color: #e6e6e6;}
.single-block-ams h3 {
    font-size: 18px;
    font-weight: normal;
    line-height: 23px;
    max-width: 250px;
    margin-left: auto;
    margin-right: auto;
    font-family: 'Raleway-Medium';
}
.single-block-ams a {
    display: inline-block;
    padding: 5px 27px;
    border: 2px solid #ee5932;
    color: #ee5932;
    text-transform: uppercase;
    margin-top: 10px;
    font-weight: bold;
    letter-spacing: 1px;
    
}

.single-block-ams a:hover, .single-block-ams a:focus {
    background: #ee5932;
    color: #fff;
    font-weight: normal;	
}

.blog-page.banner .container { bottom: 29%; }

.blog-three-block ul { margin: 0 -13px; overflow: hidden}
.blog-three-block li {
    float: left;
    width: 33.333%;
    list-style: none;
    text-align: center;
    font-family: 'Raleway-Medium';
    
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    	box-sizing: border-box;
}

.single-blog {
    padding: 40px 40px 20px;
    border: 1px solid;
    margin: 0 13px 26px;
    
}

.blog-three-block li h3 {
    font-size: 16px;
    margin-bottom: 0;
    line-height: 20px;
}

.blog-three-block li h4 {
    font-size: 16px;
    margin-top: 0;
	font-weight: normal
}
.blog-three-block li p { font-size: 16px; line-height: 26px; padding-bottom: 60px; margin: 20px 0 0; background: #fff url(../images/blog-icn.jpg) bottom center no-repeat}

.blogtesti-slider {
    border: 1px solid #000;
    margin-bottom: 26px;
    padding: 90px 0 80px;
    background: #fff url(../images/blog-quote-icn-top.png) 60px 80px no-repeat;
}
.blogtesti-slider > div {background: url(../images/blog-quote-icn-btm.png) right bottom no-repeat;}
.item-inner-blog {
    text-align: center;
    font-family: 'Raleway-Medium';
    max-width: 770px;
    margin: 0 auto;
}
.item-inner-blog h1 {
    font-size: 32px;
    font-weight: normal;
}
.item-inner-blog h2 {
    font-size: 24px;
    font-weight: normal;
	margin-bottom: 50px;
}
.item-inner-blog p {
    font-size: 28px;
    line-height: 44px;
    margin: 30px 0 50px;
    font-family: 'Raleway-LightItalic';
}

.blogtesti-slider .owl-theme .owl-dots .owl-dot span {
    width: 7px;
    height: 7px;
    margin: 5px 7px;
    border: 2px solid #0ba8c0;
    display: block;
   
}

.blogtesti-slider .owl-theme .owl-dots .owl-dot:hover span, 
.blogtesti-slider .owl-theme .owl-dots .owl-dot.active span {
    background: #33b6cb;
    border-color: #0ba8c0;
}

.other-member { text-align: center; padding: 50px 0;}
.other-member h1 {
    font-size: 24px;
    font-weight: normal;
    letter-spacing: 1px;
}

.other-member-imgs ul { margin: 0;}
.other-member-imgs li { list-style: none; display: inline-block; margin-left: 55px; margin-bottom: 15px;}
.other-member-imgs li:first-child { margin-left: 0}
.other-member-imgs { margin: 35px 0 105px;}
.top-heading h1 {
    font-size: 32px;
    font-weight: normal;
    text-align: center;
    margin: 40px 0 30px;
    color: #545454;
}

.behind-story-block {
    max-width: 945px;
    margin: 50px auto 90px;
    position: relative
}
.text-container p {
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 25px;
}
.text-container { margin-right: 490px;}
.right-image-cntnr {
    float: right;
    vertical-align: top;
    position: absolute;
    top: 50%;
    right: 0;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    	transform: translateY(-50%);
}

.single-cmnt { max-width: 750px; margin: 0 auto 35px;}
.left-img-cmnt { float: left; width: 200px;}
.cmnt-mtr { margin-left: 200px;}
.cmnt-mtr h4 { margin-top: 0; font-size: 18px;}
.cmnt-mtr h4 a { color: #545454;}
.cmnt-mtr h4 a span {
    color: #6c9f41;
    font-size: 16px;
    font-weight: normal;
}
.cmnt-mtr p { font-size: 16px;}
.our-team { border-top: 1px solid #efefef; border-bottom: 1px solid #efefef; padding-bottom: 50px;}
.clinet-testimonials .top-heading h1 { margin: 30px 0 0; }
.our-team .top-heading h1 { margin: 30px 0 60px;}





/*-------------*/
header .container {
    position: relative;
}

.menu-icn-inner span:before, .menu-icn-inner span:after {
    content: "";
    position: absolute;
    left: 0;
    height: 3px;
    width: 30px;
    background: #0353A4;
    display: block;
}
.menu-icn-inner span:before { top: -10px}
.menu-icn-inner span:after { bottom: -10px;}

.menu-icn-inner span {
    height: 3px;
    width: 30px;
    background: #0353A4;
    display: block;
    position: relative;
}
.menu-icn-inner {
    height: 14px;
    width: 30px;
    padding-top: 11px;
    
}
.menu-icn {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    margin-top: 15px;
    display: none
}
.close-icn { text-align: right; border-bottom: 1px solid #e0e0e0; display: none}	
.close-icn-inr {
    width: 30px;
    height: 30px;
    display: inline-block;
    margin: 10px 10px 5px;
    cursor: pointer;
}
.close-icn-inr span { position: relative;  margin-top: 25px; display: block;}
.close-icn-inr span:after, .close-icn-inr span:before {
    content: "";
    height: 2px;
    background: #33b5cb;
    position: absolute;
    width: 100%;
    left:0;
    top: -11px;
   
}


.close-icn-inr span:before { -webkit-transform: rotate(45deg); -moz-transform: rotate(45deg); -ms-transform: rotate(45deg); -o-transform: rotate(45deg); transform: rotate(45deg);}
.close-icn-inr span:after { -webkit-transform: rotate(-45deg); -moz-transform: rotate(-45deg); -ms-transform: rotate(-45deg); -o-transform: rotate(-45deg); transform: rotate(-45deg);}


.overlay-bg { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,.5); z-index: 3; visibility: hidden; opacity: 0; filter: alpha(opacity=0);}
.custom-height{margin-top: 40px;}
.custom-height .top-heading h1{margin-top: 0px ;}
#tblSiteLogin{background-color:#cccccc;}

.free-demo-form input[type="submit"].btn:hover,
.free-demo-form input[type="submit"].btn:focus { background: #e1540f !important;}